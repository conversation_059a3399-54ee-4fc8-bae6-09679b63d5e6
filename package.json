{"name": "awegood-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@hookform/error-message": "^2.0.1", "@hookform/resolvers": "^3.3.2", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-tooltip": "^1.0.7", "@tanstack/react-table": "^8.10.7", "@tiptap/extension-character-count": "^2.3.1", "@tiptap/extension-highlight": "^2.2.4", "@tiptap/extension-link": "^2.3.1", "@tiptap/extension-list-item": "^2.2.4", "@tiptap/extension-placeholder": "^2.2.4", "@tiptap/extension-task-item": "^2.2.4", "@tiptap/extension-task-list": "^2.2.4", "@tiptap/extension-text-style": "^2.2.4", "@tiptap/pm": "^2.2.4", "@tiptap/react": "^2.2.4", "@tiptap/starter-kit": "^2.2.4", "@types/crypto-js": "^4.2.2", "@types/file-saver": "^2.0.7", "@types/recharts": "^1.8.29", "@unpic/react": "^0.0.38", "axios": "^1.5.1", "bignumber.js": "^9.1.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "^0.2.0", "crypto-js": "^4.2.0", "date-fns": "^2.30.0", "events": "^3.3.0", "file-saver": "^2.0.5", "framer-motion": "^10.16.4", "graphql-request": "^6.1.0", "jszip": "^3.10.1", "lodash": "^4.17.21", "lodash.isequal": "^4.5.0", "lodash.xor": "^4.5.0", "lucide-react": "^0.288.0", "luxon": "^3.4.3", "prosekit": "^0.5.2", "qs": "^6.11.2", "radash": "^12.1.0", "react": "^18.2.0", "react-day-picker": "^8.9.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.47.0", "react-router-dom": "^6.17.0", "react-textarea-autosize": "^8.5.3", "recharts": "^2.12.7", "swr": "^2.2.4", "tailwind-merge": "^1.14.0", "tailwindcss-animate": "^1.0.7", "use-debounce": "^9.0.4", "usehooks-ts": "^3.1.0", "valtio": "^1.13.2", "vaul": "^1.1.1", "zod": "^3.22.4"}, "devDependencies": {"@tailwindcss/typography": "^0.5.12", "@types/lodash.isequal": "^4.5.8", "@types/lodash.xor": "^4.5.9", "@types/luxon": "^3.3.3", "@types/node": "^20.8.7", "@types/qs": "^6.9.9", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react-swc": "^3.3.2", "autoprefixer": "^10.4.16", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.31", "rollup-plugin-visualizer": "^5.12.0", "tailwindcss": "^3.3.3", "typescript": "^5.0.2", "vite": "^4.4.5"}}