import { createBrowser<PERSON>outer, Navigate, Outlet, RouterProvider } from 'react-router-dom'
import { SWRConfig } from 'swr'
import ErrorPage from './error-page'
import DashboardLayout from './components/DashboardLayout'
import { BreadcrumbLink } from './components/ui/breadcrumb'
// import Orders from "./routes/orders"
import Products from './routes/products'
import ProductDetails from './routes/products/detail'
import Categories from './routes/categories'
import Customers from './routes/customers'
import Events from './routes/events'
import EventDetails from './routes/events/details'
import EventPurchaseDetails from './routes/event-purchases/details'
import Exhibitors from './routes/exhibitors'
import ExhibitorDetails from './routes/exhibitors/details'
import Settings from './routes/settings'
import Dashboard from './routes/dashboard'
import Teams from './routes/teams'
import Campaigns from './routes/campaigns'
import Profile from './routes/profile'
import AuthPage from './routes/auth'
import useAuth, { AuthProvider } from './hooks/useAuth'
import { fetcher } from './network/request'
import { Toaster } from './components/ui/toaster'
import CampaignDetails from './routes/campaigns/details'
import CategoryDetails from './routes/categories/details'
import CustomerDetails from './routes/customers/details'
import Brands from './routes/brands'
import BrandDetails from './routes/brands/details'
import EventProductDetails from './components/Event/UpdateEventProductForm'
import MediaRoom from './routes/media-room'
import SubCategoryDetails from './routes/categories/details2'
import SubCategory2Details from './routes/categories/details3'
import Inquiries from './routes/inquiries'
import InquiryDetails from './routes/inquiries/details'
import Roles from './routes/roles'
import Careers from './routes/careers'
import JobApplicationDetails from './routes/careers/details'
import Statistics from '@/routes/statistics'
import CampaignStatistics from '@/routes/campaigns/statistics'
import TablePageSizeProvider from './components/Table/TablePageSizeProvider'
import Memories from './routes/memories'
import MemoryDetails from './routes/memories/details'
// import MemoryMediaDetails from './routes/memory-medias/details'
// import MemoryMedias from './routes/memory-medias'
import MemoryCollections from './routes/memory-collections'
import MemoryCollectionDetails from './routes/memory-collections/details'

const AdminRoutes = () => {
  const { role } = useAuth()
  if (role != 'admin' && role != 'super_admin') {
    return <Navigate to="/dashboard" replace />
  }

  return <Outlet />
}

const VendorRoutes = () => {
  const { role } = useAuth()
  if (role != 'vendor') {
    return <Navigate to="/dashboard" replace />
  }

  return <Outlet />
}

const router = createBrowserRouter([
  {
    element: <AuthProvider />,
    children: [
      // public
      {
        path: '/',
        element: <AuthPage />,
        errorElement: <ErrorPage />
      },
      {
        element: <DashboardLayout />,
        errorElement: <ErrorPage />,
        handle: {
          crumb: () => <BreadcrumbLink to="/dashboard">Dashboard</BreadcrumbLink>
        },
        children: [
          // { path: "/orders", element: <Orders /> }, // disabled for now
          // common
          { path: '/dashboard', element: <Dashboard /> },
          { path: '/media-room', element: <MediaRoom /> },
          {
            path: '/statistics',
            handle: {
              crumb: () => <BreadcrumbLink to="/statistics">Statistics</BreadcrumbLink>
            },
            children: [
              {
                index: true,
                element: <Statistics />
              },
              {
                path: '/statistics/campaigns/:campaign_id',
                element: <CampaignStatistics />,
                handle: {
                  crumb: (params: { [key: string]: string }) => (
                    <BreadcrumbLink to={`/statistics/campaigns/${params.campaign_id}`}>
                      {params.campaign_id}
                    </BreadcrumbLink>
                  )
                }
              }
            ]
          },
          {
            path: '/products',
            handle: {
              crumb: () => <BreadcrumbLink to="/products">Products</BreadcrumbLink>
            },
            children: [
              {
                index: true,
                element: <Products />
              },
              {
                path: '/products/:product_id',
                element: <ProductDetails />,
                handle: {
                  crumb: (params: { [key: string]: string }) => {
                    return (
                      <BreadcrumbLink to={`/products/${params.product_id}`}>
                        {params.product_id}
                      </BreadcrumbLink>
                    )
                  }
                }
              }
            ]
          },
          {
            path: '/categories',
            handle: {
              crumb: () => <BreadcrumbLink to="/categories">Categories</BreadcrumbLink>
            },
            children: [
              {
                index: true,
                element: <Categories />
              },
              {
                path: '/categories/:parent_category_id',
                handle: {
                  crumb: (params: { [key: string]: string }) => (
                    <BreadcrumbLink to={`/categories/${params.parent_category_id}`}>
                      {params.parent_category_id}
                    </BreadcrumbLink>
                  )
                },
                children: [
                  {
                    index: true,
                    element: <CategoryDetails />
                  },
                  {
                    path: '/categories/:parent_category_id/sub-category/:sub_category_id',
                    handle: {
                      crumb: (params: { [key: string]: string }) => (
                        <BreadcrumbLink
                          to={`/categories/${params.parent_category_id}/sub-category/${params.sub_category_id}`}
                        >
                          {params.sub_category_id}
                        </BreadcrumbLink>
                      )
                    },
                    children: [
                      {
                        index: true,
                        element: <SubCategoryDetails />
                      },
                      {
                        path: '/categories/:parent_category_id/sub-category/:sub_category_id/sub-category2/:sub_category_id2',
                        element: <SubCategory2Details />,
                        handle: {
                          crumb: (params: { [key: string]: string }) => (
                            <BreadcrumbLink
                              to={`/categories/${params.parent_category_id}/sub-category/${params.sub_category_id}/sub-category2/${params.sub_category_id2}`}
                            >
                              {params.sub_category_id2}
                            </BreadcrumbLink>
                          )
                        }
                      }
                    ]
                  }
                ]
              }
            ]
          },
          {
            path: '/brands',
            handle: {
              crumb: () => <BreadcrumbLink to="/brands">Brands</BreadcrumbLink>
            },
            children: [
              {
                index: true,
                element: <Brands />
              },
              {
                path: '/brands/:brand_id',
                element: <BrandDetails />,
                handle: {
                  crumb: (params: { [key: string]: string }) => (
                    <BreadcrumbLink to={`/brands/${params.brand_id}`}>
                      {params.brand_id}
                    </BreadcrumbLink>
                  )
                }
              }
            ]
          },
          {
            path: '/events',
            handle: {
              crumb: () => <BreadcrumbLink to="/events">Events</BreadcrumbLink>
            },
            children: [
              {
                index: true,
                element: <Events />
              },
              {
                path: '/events/:event_id',
                handle: {
                  crumb: (params: { [key: string]: string }) => (
                    <BreadcrumbLink to={`/events/${params.event_id}`}>
                      {params.event_id}
                    </BreadcrumbLink>
                  )
                },
                children: [
                  {
                    index: true,
                    element: <EventDetails />
                  },
                  {
                    path: '/events/:event_id/event_products',
                    handle: {
                      crumb: (params: { [key: string]: string }) => (
                        <BreadcrumbLink to={`/events/${params.event_id}`}>
                          Event Products
                        </BreadcrumbLink>
                      )
                    },
                    children: [
                      {
                        index: true,
                        element: <EventDetails />
                      },
                      {
                        path: '/events/:event_id/event_products/:event_product_id',
                        handle: {
                          crumb: (params: { [key: string]: string }) => (
                            <BreadcrumbLink
                              to={`/events/${params.event_id}/event_products/${params.event_product_id}`}
                            >
                              {params.event_product_id}
                            </BreadcrumbLink>
                          )
                        },
                        element: <EventProductDetails />
                      }
                    ]
                  },
                  {
                    path: '/events/:event_id/campaigns/:campaign_id',
                    handle: {
                      crumb: (params: { [key: string]: string }) => (
                        <BreadcrumbLink to={`/events/${params.event_id}#deals`}>
                          Deals
                        </BreadcrumbLink>
                      )
                    },
                    children: [
                      {
                        index: true,
                        element: <CampaignDetails />,
                        handle: {
                          crumb: (params: { [key: string]: string }) => (
                            <BreadcrumbLink
                              to={`/events/${params.event_id}/campaigns/${params.campaign_id}`}
                            >
                              {params.campaign_id}
                            </BreadcrumbLink>
                          )
                        }
                      }
                    ]
                  },
                  {
                    path: '/events/:event_id/event_purchases',
                    handle: {
                      crumb: (params: { [key: string]: string }) => (
                        <BreadcrumbLink to={`/events/${params.event_id}#purchases`}>
                          Event Purchases
                        </BreadcrumbLink>
                      )
                    },
                    children: [
                      {
                        index: true,
                        element: <EventDetails />
                      },
                      {
                        path: '/events/:event_id/event_purchases/:event_purchase_id',
                        handle: {
                          crumb: (params: { [key: string]: string }) => (
                            <BreadcrumbLink
                              to={`/events/${params.event_id}/event_purchases/${params.event_purchase_id}`}
                            >
                              {params.event_purchase_id}
                            </BreadcrumbLink>
                          )
                        },
                        element: <EventPurchaseDetails />
                      }
                    ]
                  }
                ]
              }
            ]
          },
          {
            path: '/inquiries',
            handle: {
              crumb: () => <BreadcrumbLink to="/inquiries">Inquiries</BreadcrumbLink>
            },
            children: [
              {
                index: true,
                element: <Inquiries />
              },
              {
                path: '/inquiries/:inquiry_id',
                element: <InquiryDetails />,
                handle: {
                  crumb: (params: { [key: string]: string }) => (
                    <BreadcrumbLink to={`/inquiries/${params.inquiry_id}`}>
                      {params.inquiry_id}
                    </BreadcrumbLink>
                  )
                }
              }
            ]
          },
          {
            path: '/job-applications',
            handle: {
              crumb: () => <BreadcrumbLink to="/job-applications">Job Applications</BreadcrumbLink>
            },
            children: [
              {
                index: true,
                element: <Careers />
              },
              {
                path: '/job-applications/:application_id',
                element: <JobApplicationDetails />,
                handle: {
                  crumb: (params: { [key: string]: string }) => (
                    <BreadcrumbLink to={`/job-applications/${params.application_id}`}>
                      {params.application_id}
                    </BreadcrumbLink>
                  )
                }
              }
            ]
          },
          {
            path: '/settings',
            element: <Settings />,
            handle: {
              crumb: () => <BreadcrumbLink to="/settings">Settings</BreadcrumbLink>
            }
          },
          {
            element: <AdminRoutes />, // only for admins
            children: [
              {
                path: '/friends',
                handle: {
                  crumb: () => <BreadcrumbLink to="/friends">Friends</BreadcrumbLink>
                },
                children: [
                  {
                    index: true,
                    element: <Customers />
                  },
                  {
                    path: '/friends/:customer_id',
                    element: <CustomerDetails />,
                    handle: {
                      crumb: (params: { [key: string]: string }) => (
                        <BreadcrumbLink to={`/friends/${params.customer_id}`}>
                          {params.customer_id}
                        </BreadcrumbLink>
                      )
                    }
                  }
                ]
              },
              {
                path: '/companies',
                handle: {
                  crumb: () => <BreadcrumbLink to="/companies">Companies</BreadcrumbLink>
                },
                children: [
                  {
                    index: true,
                    element: <Exhibitors />
                  },
                  {
                    path: '/companies/:exhibitor_id',
                    element: <ExhibitorDetails />,
                    handle: {
                      crumb: (params: { [key: string]: string }) => (
                        <BreadcrumbLink to={`/companies/${params.exhibitor_id}`}>
                          {params.exhibitor_id}
                        </BreadcrumbLink>
                      )
                    }
                  }
                ]
              },
              {
                path: '/campaigns',
                handle: {
                  crumb: () => <BreadcrumbLink to="/campaigns">Campaigns</BreadcrumbLink>
                },
                children: [
                  {
                    index: true,
                    element: <Campaigns />
                  },
                  {
                    path: '/campaigns/:campaign_id',
                    element: <CampaignDetails />,
                    handle: {
                      crumb: (params: { [key: string]: string }) => (
                        <BreadcrumbLink to={`/campaigns/${params.campaign_id}`}>
                          {params.campaign_id}
                        </BreadcrumbLink>
                      )
                    }
                  }
                ]
              },
              {
                path: '/memories',
                handle: {
                  crumb: () => <BreadcrumbLink to="/memories">Memories</BreadcrumbLink>
                },
                children: [
                  {
                    index: true,
                    element: <Memories />
                  },
                  {
                    path: '/memories/:memory_id',
                    element: <MemoryDetails />,
                    handle: {
                      crumb: (params: { [key: string]: string }) => (
                        <BreadcrumbLink to={`/memories/${params.memory_id}`}>
                          {params.memory_id}
                        </BreadcrumbLink>
                      )
                    }
                  }
                ]
              },
              // {
              //   path: '/memory-medias',
              //   handle: {
              //     crumb: () => <BreadcrumbLink to="/memory-medias">Memory Medias</BreadcrumbLink>
              //   },
              //   children: [
              //     {
              //       index: true,
              //       element: <MemoryMedias />
              //     },
              //     {
              //       path: '/memory-medias/:memory_media_id',
              //       element: <MemoryMediaDetails />,
              //       handle: {
              //         crumb: (params: { [key: string]: string }) => (
              //           <BreadcrumbLink to={`/memory-medias/${params.memory_media_id}`}>
              //             {params.memory_media_id}
              //           </BreadcrumbLink>
              //         )
              //       }
              //     }
              //   ]
              // },
              {
                path: '/memory-collections',
                handle: {
                  crumb: () => (
                    <BreadcrumbLink to="/memory-collections">Memory Collection</BreadcrumbLink>
                  )
                },
                children: [
                  {
                    index: true,
                    element: <MemoryCollections />
                  },
                  {
                    path: '/memory-collections/:memory_collection_id',
                    element: <MemoryCollectionDetails />,
                    handle: {
                      crumb: (params: { [key: string]: string }) => (
                        <BreadcrumbLink to={`/memory-collections/${params.memory_collection_id}`}>
                          {params.memory_collection_id}
                        </BreadcrumbLink>
                      )
                    }
                  }
                ]
              },
              {
                path: '/roles',
                element: <Roles />,
                handle: {
                  crumb: () => <BreadcrumbLink to="/roles">Roles</BreadcrumbLink>
                }
              }
            ]
          },
          {
            element: <VendorRoutes />, // only for vendors
            children: [
              {
                path: '/teams',
                element: <Teams />,
                handle: {
                  crumb: () => <BreadcrumbLink to="/teams">Teams</BreadcrumbLink>
                }
              }
            ]
          },
          {
            element: <Profile />,
            path: '/profile',
            handle: {
              crumb: () => <BreadcrumbLink to="/profile">Profile</BreadcrumbLink>
            }
          }
        ]
      }
    ]
  }
])

function App() {
  return (
    <SWRConfig value={{ fetcher, revalidateOnFocus: false, errorRetryCount: 1 }}>
      {/* table page context provider */}
      <TablePageSizeProvider>
        <RouterProvider router={router} />
        <Toaster />
      </TablePageSizeProvider>
    </SWRConfig>
  )
}

export default App
