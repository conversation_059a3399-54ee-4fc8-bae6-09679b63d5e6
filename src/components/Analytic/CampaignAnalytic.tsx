import CampaignAnalyticTable from '@/components/Analytic/CampaignAnalyticTable'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import AnalyticService from '@/network/services/analytic' // CustomerAnalytics
import { DateTime } from 'luxon'
// import { <PERSON>, Pie<PERSON>hart } from 'recharts'
import useSWR from 'swr'

const CampaignAnalytic = ({ startsAt, endsAt }: { startsAt?: DateTime; endsAt?: DateTime }) => {
  // const { data: freebiesData } = useSWR(
  //   serialize(CampaignService.getCampaigns, {
  //     type: 'freebies'
  //   })
  // )
  // const freebies = CampaignService.toRow(freebiesData)
  const { data: campaignOverview } = useSWR(AnalyticService.getCampaignsOverview(startsAt, endsAt))
  // const { data } = useSWR<CampaignWishlistAnalytics[] | undefined | null>(
  //   freebies.length > 0
  //     ? AnalyticService.getCampaignWishlistAnaytic(freebies.map((e) => e.id.toString()))
  //     : null
  // )

  return (
    <div className="space-y-4">
      <div className="flex flex-row mb-4">
        <Card>
          <CardHeader className="flex flex-row space-y-0 items-center justify-between">
            <CardTitle>Total Submissions</CardTitle>
          </CardHeader>
          <CardContent>
            <div>{campaignOverview?.total_submissions}</div>
          </CardContent>
        </Card>
      </div>

      <CampaignAnalyticTable type="freebies" />

      {/* {data?.map((campaign) => {
        const campaignName = freebies.find((e) => e.id == campaign.campaign_id)?.title
        return (
          <CampaignAnalyticCard
            analytic={campaign}
            campaignName={campaignName ?? ''}
            key={campaign.campaign_id}
          />
        )
      })} */}
    </div>
  )
}

export default CampaignAnalytic
