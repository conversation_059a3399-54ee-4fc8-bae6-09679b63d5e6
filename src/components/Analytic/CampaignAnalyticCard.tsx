import CustomTooltip from '@/components/Charts/CustomTooltip'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle, Content, Title } from '@/components/ui/card'
import { ChartColors } from '@/constants/colors'
import // CustomerAnalytics
'@/network/services/analytic'
import { CampaignWishlistAnalytics } from '@/types/Analytics'
import { useMemo } from 'react'
import { useNavigate } from 'react-router-dom'
import { Cell, Pie, PieChart, ResponsiveContainer, Tooltip } from 'recharts'

const CampaignAnalyticCard = ({
  analytic,
  campaignName
}: {
  analytic: CampaignWishlistAnalytics
  campaignName: string
}) => {
  const nav = useNavigate()
  const total = useMemo(() => {
    return {
      by_product: analytic?.by_product.reduce(
        (prev, curr) => (prev += parseInt(`${curr.count}`)),
        0
      ),
      by_category: analytic?.by_category.reduce(
        (prev, curr) => (prev += parseInt(`${curr.count}`)),
        0
      ),
      by_collection: analytic?.by_collection.reduce(
        (prev, curr) => (prev += parseInt(`${curr.count}`)),
        0
      )
    }
  }, [analytic])

  return (
    <Card className="h-fit">
      <CardHeader className="flex flex-row space-y-0 items-center justify-between">
        <CardTitle>{campaignName}</CardTitle>
        <Button
          onClick={() => {
            nav(`/statistics/campaigns/${analytic.campaign_id}`)
          }}
        >
          View Details
        </Button>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 xl:grid-cols-2">
          <div>
            <Title>Product Wishlist Analysis</Title>
            <Content>
              {analytic.by_product ? (
                <div className="h-[250px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Tooltip content={<CustomTooltip total={total.by_product} />} />
                      <Pie
                        data={analytic.by_product.map((item) => ({
                          name: item.medusa_product_title,
                          value: parseInt(`${item.count}`)
                        }))}
                        dataKey="value"
                        nameKey="name"
                        cx="50%"
                        cy="50%"
                        outerRadius={50}
                        fill="#8884d8"
                        label={({ x, y, cx, index }) => (
                          <text
                            x={x}
                            y={y}
                            fill="black"
                            textAnchor={x > cx ? 'start' : 'end'}
                            dominantBaseline="central"
                          >
                            {`${analytic.by_product[index].medusa_product_title ?? 'NULL'} (${(
                              (parseInt(`${analytic.by_product[index].count}`) /
                                (total.by_product ?? 1)) *
                              100
                            ).toFixed(1)}%)`}
                          </text>
                        )}
                      >
                        {analytic.by_product.map((_, index) => (
                          <Cell key={`k-${index}`} fill={ChartColors[index % ChartColors.length]} />
                        ))}
                      </Pie>
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              ) : (
                <>Data Not Available</>
              )}
            </Content>
          </div>

          <div>
            <Title>Brand Wishlist Analysis</Title>
            <Content>
              {analytic.by_product ? (
                <div className="h-[250px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Tooltip content={<CustomTooltip total={total.by_collection} />} />
                      <Pie
                        data={analytic.by_collection.map((item) => ({
                          name: item.medusa_collection_title,
                          value: parseInt(`${item.count}`)
                        }))}
                        dataKey="value"
                        nameKey="name"
                        cx="50%"
                        cy="50%"
                        outerRadius={50}
                        fill="#8884d8"
                        label={({ x, y, cx, index }) => (
                          <text
                            x={x}
                            y={y}
                            fill="black"
                            textAnchor={x > cx ? 'start' : 'end'}
                            dominantBaseline="central"
                          >
                            {`${
                              analytic.by_collection[index].medusa_collection_title ?? 'NULL'
                            } (${(
                              (parseInt(`${analytic.by_collection[index].count}`) /
                                (total.by_collection ?? 1)) *
                              100
                            ).toFixed(1)}%)`}
                          </text>
                        )}
                      >
                        {analytic.by_collection.map((_, index) => (
                          <Cell key={`k-${index}`} fill={ChartColors[index % ChartColors.length]} />
                        ))}
                      </Pie>
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              ) : (
                <>Data Not Available</>
              )}
            </Content>
          </div>

          <div>
            <Title>Category Wishlist Analysis</Title>
            <Content>
              {analytic.by_product ? (
                <div className="h-[250px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Tooltip content={<CustomTooltip total={total.by_category} />} />
                      <Pie
                        data={analytic.by_category.map((item) => ({
                          name: item.medusa_category_name,
                          value: parseInt(`${item.count}`)
                        }))}
                        dataKey="value"
                        nameKey="name"
                        cx="50%"
                        cy="50%"
                        outerRadius={50}
                        fill="#8884d8"
                        label={({ x, y, cx, index }) => (
                          <text
                            x={x}
                            y={y}
                            fill="black"
                            textAnchor={x > cx ? 'start' : 'end'}
                            dominantBaseline="central"
                          >
                            {`${analytic.by_category[index].medusa_category_name ?? 'NULL'} (${(
                              (parseInt(`${analytic.by_category[index].count}`) /
                                (total.by_category ?? 1)) *
                              100
                            ).toFixed(1)}%)`}
                          </text>
                        )}
                      >
                        {analytic.by_category.map((_, index) => (
                          <Cell key={`k-${index}`} fill={ChartColors[index % ChartColors.length]} />
                        ))}
                      </Pie>
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              ) : (
                <>Data Not Available</>
              )}
            </Content>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default CampaignAnalyticCard
