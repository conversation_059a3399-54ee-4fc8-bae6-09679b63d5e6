import { Button } from '@/components/ui/button'
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  CardFooter,
  CardContentLabel,
  CardContentTitle
} from '@/components/ui/card'
import { toast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import CampaignService from '@/network/services/campaign'
import { Campaign } from '@/types/Campaign'
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem
} from '@/components/ui/dropdown-menu'
import { FC, useMemo } from 'react'
import useSWR, { mutate } from 'swr'
import SettingService from '@/network/services/setting'
import ReadOnlyEditorComponent from '@/components/Editor/ReadOnlyEditor'

const CampaignStatusBadge: FC<{ campaign: Campaign }> = ({ campaign }) => {
  const color = useMemo(() => {
    return campaign.published ? 'bg-success' : 'bg-destructive'
  }, [campaign.published])

  const updateStatus = async (campaign: Campaign, published: boolean) => {
    try {
      await CampaignService.updateCampaign(campaign.id, {
        ...campaign,
        published
      })
      mutate(
        (key) => typeof key === 'string' && key.startsWith(CampaignService.getCampaign(campaign.id))
      )
      toast({
        title: `Campaign ${published ? 'publised' : 'set to pending'}`,
        variant: 'success'
      })
    } catch (error) {
      console.log(error)
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="h-8">
          <div className="flex items-center space-x-2">
            <div className={cn('h-1.5 w-1.5 self-center rounded-full', color)} />
            <span className="capitalize text-xs">
              {campaign.published ? 'Published' : 'Pending'}
            </span>
          </div>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" forceMount>
        {!campaign.published ? (
          <DropdownMenuGroup>
            <DropdownMenuItem
              onClick={async (event) => {
                event.stopPropagation()
                updateStatus(campaign, true)
              }}
              className="space-x-2"
            >
              <div className={cn('h-1.5 w-1.5 self-center rounded-full', 'bg-success')} />
              <span className="capitalize text-xs">Publish</span>
            </DropdownMenuItem>
          </DropdownMenuGroup>
        ) : (
          <DropdownMenuGroup>
            <DropdownMenuItem
              onClick={async (event) => {
                event.stopPropagation()
                updateStatus(campaign, false)
              }}
              className="space-x-2"
            >
              <div className={cn('h-1.5 w-1.5 self-center rounded-full', 'bg-destructive')} />
              <span className="capitalize text-xs">Unpublish</span>
            </DropdownMenuItem>
          </DropdownMenuGroup>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

export const CampaignAnalyticDetailCard: FC<{ campaign: Campaign }> = ({ campaign }) => {
  const { data, error, isLoading } = useSWR(SettingService.getSetting('freebies'))

  if (!data || error || isLoading) {
    return <></>
  }

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <CardTitle>{campaign.title}</CardTitle>
          <div className="flex flex-row space-x-2">
            <CampaignStatusBadge {...{ campaign }} />
          </div>
        </CardHeader>
        <CardContent className="flex flex-col space-y-4">
          <div className="grid grid-cols-[200px_1fr] gap-2">
            <CardContentTitle>Slug</CardContentTitle>
            <CardContentLabel>{campaign.slug}</CardContentLabel>
            <CardContentTitle>Description</CardContentTitle>
            <CardContentLabel>
              <ReadOnlyEditorComponent className="text-right" content={campaign.abstract} />
            </CardContentLabel>
            <CardContentTitle>Gender</CardContentTitle>
            <CardContentLabel>
              <ReadOnlyEditorComponent
                className="text-right capitalize"
                content={campaign.gender}
              />
            </CardContentLabel>
            <CardContentTitle>{campaign.event_id ? 'Event' : 'Category'}</CardContentTitle>
            <CardContentLabel>
              {campaign.event_id ? campaign.event?.title : campaign?.category ?? '-'}
            </CardContentLabel>
            <CardContentTitle>Rewards</CardContentTitle>
            <CardContentLabel>{campaign.rewards ? 'View in form' : '-'}</CardContentLabel>
            <CardContentTitle>Long Description</CardContentTitle>
            <CardContentLabel>{campaign.description ? 'View in form' : '-'}</CardContentLabel>
            <CardContentTitle>Application</CardContentTitle>
            <CardContentLabel>{campaign.application ? 'View in form' : '-'}</CardContentLabel>
            <CardContentTitle>Mission</CardContentTitle>
            <CardContentLabel>{campaign.mission ? 'View in form' : '-'}</CardContentLabel>
            <CardContentTitle>Redemption</CardContentTitle>
            <CardContentLabel>
              {(campaign.redemption?.length ?? 0) > 0 ? 'View in form' : '-'}
            </CardContentLabel>
            <CardContentTitle>Terms & Conditions</CardContentTitle>
            <CardContentLabel>
              {campaign.terms_and_conditions ? 'View in form' : '-'}
            </CardContentLabel>
            <CardContentTitle>User Agreements</CardContentTitle>
            <CardContentLabel>
              {campaign.metadata?.user_agreements?.map((user_agreement: string, i: number) => {
                return (
                  <ReadOnlyEditorComponent
                    key={i}
                    className="text-right"
                    content={user_agreement}
                  />
                )
              }) ?? '-'}
            </CardContentLabel>
            <CardContentTitle>Deal Wishlist Description</CardContentTitle>
            <CardContentLabel>
              {campaign.metadata?.deal_wishlist_description ?? '-'}
            </CardContentLabel>
            {/* TODO: */}
            {/* terms */}
            {/* rank */}
            {/* if have event */}
          </div>

          <div>
            <p className="text-md font-medium mb-2">Fields</p>
            <div className="grid grid-cols-2 gap-1">
              {campaign.fields?.map((field) => {
                return (
                  <>
                    <CardContentTitle>{field.name}</CardContentTitle>
                    <CardContentLabel>{field.type}</CardContentLabel>
                  </>
                )
              })}
            </div>
          </div>
        </CardContent>
        <CardFooter></CardFooter>
      </Card>
    </>
  )
}
