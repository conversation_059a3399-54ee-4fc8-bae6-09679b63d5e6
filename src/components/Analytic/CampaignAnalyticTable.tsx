import CampaignService from '@/network/services/campaign'
import { ColumnDef, Row } from '@tanstack/react-table'
import { DataTable, FilterColumn } from '@/components/Table/DataTable'
import { Campaign } from '@/types/Campaign'
import { useNavigate } from 'react-router-dom'
import { IDataResponse, serialize } from '@/network/request'
import { CheckIcon, Cross2Icon } from '@radix-ui/react-icons'
import ReadOnlyEditorComponent from '../Editor/ReadOnlyEditor'
import { DateTime } from 'luxon'

interface CampaignAnalyticTableProps extends React.HTMLAttributes<HTMLDivElement> {
  type: 'deal' | 'freebies'
  eventId?: number
}

function CampaignAnalyticTable({ type, eventId }: CampaignAnalyticTableProps) {
  const nav = useNavigate()
  const columns: ColumnDef<Campaign>[] = [
    {
      accessorKey: 'title',
      header: 'Title'
    },
    {
      accessorKey: 'category',
      header: 'Category'
    },
    {
      accessorKey: 'abstract',
      header: 'Description',
      cell: (props) => {
        return (
          <ReadOnlyEditorComponent
            className="max-h-20 max-w-[300px] overflow-hidden"
            content={props.getValue<string>()}
          />
        )
      }
    },
    {
      accessorKey: 'rank',
      header: 'Rank'
    },
    {
      accessorKey: 'total_submissions',
      header: 'Submission Count'
    },
    {
      accessorKey: 'published',
      header: 'Published',
      cell: (props) => {
        return props.getValue<boolean>() ? (
          <CheckIcon className="h-4 w-4 text-success" />
        ) : (
          <Cross2Icon className="h-4 w-4 text-destructive" />
        )
      }
    },
    {
      accessorKey: 'created_at',
      header: 'Created At',
      cell: (props) => DateTime.fromISO(props.getValue<string>()).toFormat('yyyy-MM-dd HH:mm:ss')
    }
  ]

  const columnFilter: FilterColumn[] = [
    { columnKey: 'title', header: 'Title', dataType: 'string' },
    {
      columnKey: 'created_at',
      header: 'Created At',
      dataType: 'date'
    }
    // {
    //   columnKey: 'published',
    //   header: 'Published',
    //   dataType: 'faceted',
    //   options: [
    //     { value: 'true', label: 'TRUE', icon: undefined },
    //     { value: 'false', label: 'FALSE', icon: undefined }
    //   ]
    // }
  ]

  const typeBasedColumns =
    type == 'freebies'
      ? columns.filter((_, index) => index != 3)
      : [...columns].filter((_, index) => index != 1)

  return (
    <DataTable<Campaign, unknown, IDataResponse<Campaign>>
      columns={typeBasedColumns}
      filterColumns={columnFilter}
      swrService={
        type == 'freebies'
          ? serialize(CampaignService.getCampaigns, {
              type: 'freebies'
            })
          : eventId != null
          ? serialize(CampaignService.getCampaigns, {
              event: eventId
            })
          : serialize(CampaignService.getCampaigns, {
              type: 'deals'
            })
      }
      pageParam="page"
      limitParam="limit"
      sortParam="sort"
      sortColumns={['title', 'published', 'rank', 'created_at']}
      toRow={CampaignService.toRow}
      toPaginate={CampaignService.toPaginate}
      onRowClick={(row: Row<Campaign>) => {
        nav(`/statistics/campaigns/${row.original.id}`)
      }}
    />
  )
}

export default CampaignAnalyticTable
