import CustomTooltip from '@/components/Charts/CustomTooltip'
import { Card, CardContent, CardHeader, CardTitle, Content, Title } from '@/components/ui/card'
import { ChartColors } from '@/constants/colors'
import AnalyticService from '@/network/services/analytic'
import { CustomerAnalytics } from '@/types/Analytics'
import { DateTime } from 'luxon'
import { useMemo, useState } from 'react'
import {
  Bar,
  BarChart,
  CartesianGrid,
  Cell,
  Legend,
  Pie,
  PieChart,
  ReferenceLine,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis
} from 'recharts'
import useSWR from 'swr'

const CustomerAnalytic = ({
  startsAt,
  endsAt,
  mode
}: {
  startsAt?: DateTime | null
  endsAt?: DateTime | null
  mode: 'general' | 'past30'
}) => {
  const [comparisonDate] = useState(DateTime.now().set({ day: -1 }))
  const { data } = useSWR<CustomerAnalytics>(
    AnalyticService.getCustomerAnalytic(startsAt?.toJSDate(), endsAt?.toJSDate())
  )

  const { data: comparisonData } = useSWR<CustomerAnalytics>(
    mode == 'past30'
      ? AnalyticService.getCustomerAnalytic(undefined, comparisonDate.toJSDate())
      : null
  )

  console.log(comparisonDate.toJSDate())

  const total = useMemo(() => {
    return {
      gender: data?.gender.reduce((prev, curr) => (prev += parseInt(`${curr.count}`)), 0),
      address: data?.address.reduce((prev, curr) => (prev += parseInt(`${curr.count}`)), 0),
      age: data?.age.reduce((prev, curr) => (prev += parseInt(`${curr.count}`)), 0),
      stage: data?.stage.reduce((prev, curr) => (prev += parseInt(`${curr.count}`)), 0)
    }
  }, [data])

  // const handleExport = async () => {
  //   try {
  //     const { data: result } = await AnalyticService.exportCustomerAnalytic(
  //       startsAt?.toJSDate(),
  //       endsAt?.toJSDate()
  //     )
  //     if (result.download_url) {
  //       window.open(result.download_url, '_blank')
  //       return
  //     }
  //     console.log('error, no download url')
  //   } catch (e) {
  //     console.log('error export customer analytics', e)
  //   }
  // }

  return (
    <div>
      <div className="flex flex-row mb-4 items-end">
        <Card>
          <CardHeader className="flex flex-row space-y-0 items-center justify-between">
            <CardTitle>Total Friends</CardTitle>
          </CardHeader>
          <CardContent>
            <div>{data?.total_customer}</div>
          </CardContent>
        </Card>

        <Card className="ml-4">
          <CardHeader className="flex flex-row space-y-0 items-center justify-between">
            <CardTitle>Past 30 Days Grow</CardTitle>
          </CardHeader>
          <CardContent>
            <div>{data?.grow_30_days}</div>
          </CardContent>
        </Card>

        {/* {mode == 'general' && (
          <Button className="ml-4" onClick={handleExport}>
            Export To CSV
          </Button>
        )} */}
      </div>

      <Card className="h-fit">
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <CardTitle>
            Friends Analytic
            {mode == 'past30' &&
              ` (${DateTime.now().toFormat('yyyy LLL')} vs ${DateTime.now()
                .minus({ month: 1 })
                .toFormat('yyyy LLL')})`}
          </CardTitle>
        </CardHeader>
        {mode == 'general' && (
          <CardContent className="grid grid-cols-1 xl:grid-cols-2">
            <div className="h-[250px]">
              <Title>Gender</Title>
              <Content>
                {data?.gender && data.gender.length > 0 ? (
                  <div className="h-[250px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Tooltip content={<CustomTooltip total={total.gender} />} />
                        <Pie
                          data={data.gender.map((item) => ({
                            name: item.gender,
                            value: parseInt(`${item.count}`)
                          }))}
                          dataKey="value"
                          nameKey="name"
                          cx="50%"
                          cy="50%"
                          outerRadius={50}
                          fill="#8884d8"
                          label={({ x, y, cx, index }) => (
                            <text
                              x={x}
                              y={y}
                              fill="black"
                              textAnchor={x > cx ? 'start' : 'end'}
                              dominantBaseline="central"
                            >
                              {`${data.gender[index].gender ?? 'NULL'} (${(
                                (parseInt(`${data.gender[index].count}`) / (total.gender ?? 1)) *
                                100
                              ).toFixed(1)}%)`}
                            </text>
                          )}
                        >
                          {data.gender.map((_, index) => (
                            <Cell
                              key={`k-${index}`}
                              fill={ChartColors[index % ChartColors.length]}
                            />
                          ))}
                        </Pie>
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                ) : (
                  <div className="text-start">No Data</div>
                )}
              </Content>
            </div>

            <div className="h-[250px]">
              <Title>Stage</Title>
              <Content>
                {data?.stage && data.stage.length > 0 ? (
                  <div className="h-[250px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Tooltip content={<CustomTooltip total={total.stage} />} />
                        <Pie
                          data={data.stage.map((item) => ({
                            name: item.current_stage,
                            value: parseInt(`${item.count}`)
                          }))}
                          dataKey="value"
                          nameKey="name"
                          cx="50%"
                          cy="50%"
                          outerRadius={50}
                          fill="#8884d8"
                          label={({ x, y, cx, index }) => (
                            <text
                              x={x}
                              y={y}
                              fill="black"
                              textAnchor={x > cx ? 'start' : 'end'}
                              dominantBaseline="central"
                            >
                              {`${data.stage[index].current_stage ?? 'NULL'} (${(
                                (parseInt(`${data.stage[index].count}`) / (total.stage ?? 1)) *
                                100
                              ).toFixed(1)}%)`}
                            </text>
                          )}
                        >
                          {data.stage.map((_, index) => (
                            <Cell
                              key={`k-${index}`}
                              fill={ChartColors[index % ChartColors.length]}
                            />
                          ))}
                        </Pie>
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                ) : (
                  <div className="text-start">No Data</div>
                )}
              </Content>
            </div>

            <div className="h-[250px]">
              <Title>Address</Title>
              <Content>
                {data?.address && data.address.length > 0 ? (
                  <div className="h-[250px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Tooltip content={<CustomTooltip total={total.address} />} />
                        <Pie
                          data={data.address.map((item) => ({
                            name: item.province,
                            value: parseInt(`${item.count}`)
                          }))}
                          dataKey="value"
                          nameKey="name"
                          cx="50%"
                          cy="50%"
                          outerRadius={50}
                          fill="#8884d8"
                          label={({ x, y, cx, index }) => (
                            <text
                              x={x}
                              y={y}
                              fill="black"
                              textAnchor={x > cx ? 'start' : 'end'}
                              dominantBaseline="central"
                            >
                              {`${data.address[index].province ?? 'NULL'} (${(
                                (parseInt(`${data.address[index].count}`) / (total.address ?? 1)) *
                                100
                              ).toFixed(1)}%)`}
                            </text>
                          )}
                        >
                          {data.address.map((_, index) => (
                            <Cell
                              key={`k-${index}`}
                              fill={ChartColors[index % ChartColors.length]}
                            />
                          ))}
                        </Pie>
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                ) : (
                  <div className="text-start">No Data</div>
                )}
              </Content>
            </div>

            <div className="h-[250px]">
              <Title>Age group</Title>
              <Content>
                {data?.age && data.age.length > 0 ? (
                  <div className="h-[250px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Tooltip content={<CustomTooltip total={total.age} />} />
                        <Pie
                          data={data.age.map((item) => ({
                            name: item.age_group,
                            value: parseInt(`${item.count}`)
                          }))}
                          dataKey="value"
                          nameKey="name"
                          cx="50%"
                          cy="50%"
                          outerRadius={50}
                          fill="#8884d8"
                          label={({ x, y, cx, index }) => (
                            <text
                              x={x}
                              y={y}
                              fill="black"
                              textAnchor={x > cx ? 'start' : 'end'}
                              dominantBaseline="central"
                            >
                              {`${data.age[index].age_group ?? 'NULL'} (${(
                                (parseInt(`${data.age[index].count}`) / (total.age ?? 1)) *
                                100
                              ).toFixed(1)}%)`}
                            </text>
                          )}
                        >
                          {data.age.map((_, index) => (
                            <Cell
                              key={`k-${index}`}
                              fill={ChartColors[index % ChartColors.length]}
                            />
                          ))}
                        </Pie>
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                ) : (
                  <div className="text-start">No Data</div>
                )}
              </Content>
            </div>
          </CardContent>
        )}

        {mode == 'past30' && (
          <CardContent className="grid grid-cols-1 xl:grid-cols-2">
            <div className="h-[250px]">
              <Title>Gender</Title>
              <Content>
                {data?.gender && data.gender.length > 0 ? (
                  <div className="h-[250px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        height={250}
                        data={data.gender.map((item) => {
                          const comparison = comparisonData?.gender.find(
                            (e) => e.gender == item.gender
                          )
                          return {
                            name: item.gender ?? 'Unknown',
                            value:
                              parseInt(`${item.count}`) - parseInt(`${comparison?.count ?? '0'}`)
                          }
                        })}
                        margin={{
                          top: 5,
                          right: 30,
                          left: 20,
                          bottom: 5
                        }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <ReferenceLine y={0} stroke="#000" />
                        <Bar dataKey="value" fill="#8884d8" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                ) : (
                  <div className="text-start">No Data</div>
                )}
              </Content>
            </div>

            <div className="h-[250px]">
              <Title>Stage</Title>
              <Content>
                {data?.stage && data.stage.length > 0 ? (
                  <div className="h-[250px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        height={250}
                        data={data.stage.map((item) => {
                          const comparison = comparisonData?.stage.find(
                            (e) => e.current_stage == item.current_stage
                          )
                          return {
                            name: item.current_stage ?? 'Unknown',
                            value:
                              parseInt(`${item.count}`) - parseInt(`${comparison?.count ?? '0'}`)
                          }
                        })}
                        margin={{
                          top: 5,
                          right: 30,
                          left: 20,
                          bottom: 5
                        }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <ReferenceLine y={0} stroke="#000" />
                        <Bar dataKey="value" fill="#8884d8" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                ) : (
                  <div className="text-start">No Data</div>
                )}
              </Content>
            </div>

            <div className="h-[250px]">
              <Title>Address</Title>
              <Content>
                {data?.address && data.address.length > 0 ? (
                  <div className="h-[250px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        height={250}
                        data={data.address.map((item) => {
                          const comparison = comparisonData?.address.find(
                            (e) => e.province == item.province
                          )
                          return {
                            name: item.province ?? 'Unknown',
                            value:
                              parseInt(`${item.count}`) - parseInt(`${comparison?.count ?? '0'}`)
                          }
                        })}
                        margin={{
                          top: 5,
                          right: 30,
                          left: 20,
                          bottom: 5
                        }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <ReferenceLine y={0} stroke="#000" />
                        <Bar dataKey="value" fill="#8884d8" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                ) : (
                  <div className="text-start">No Data</div>
                )}
              </Content>
            </div>

            <div className="h-[250px]">
              <Title>Age group</Title>
              <Content>
                {data?.age && data.age.length > 0 ? (
                  <div className="h-[250px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        height={250}
                        data={data.age.map((item) => {
                          const comparison = comparisonData?.age.find(
                            (e) => e.age_group == item.age_group
                          )
                          return {
                            name: item.age_group ?? 'Unknown',
                            value:
                              parseInt(`${item.count}`) - parseInt(`${comparison?.count ?? '0'}`)
                          }
                        })}
                        margin={{
                          top: 5,
                          right: 30,
                          left: 20,
                          bottom: 5
                        }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <ReferenceLine y={0} stroke="#000" />
                        <Bar dataKey="value" fill="#8884d8" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                ) : (
                  <div className="text-start">No Data</div>
                )}
              </Content>
            </div>
          </CardContent>
        )}
      </Card>
    </div>
  )
}

export default CustomerAnalytic
