import CampaignAnalyticCard from '@/components/Analytic/CampaignAnalyticCard'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import { serialize } from '@/network/request'
import AnalyticService from '@/network/services/analytic' // CustomerAnalytics
import CampaignService from '@/network/services/campaign'
import { CampaignWishlistAnalytics } from '@/types/Analytics'
import { DateTime } from 'luxon'
// import { Pie, PieChart } from 'recharts'
import useSWR from 'swr'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'
import { Link } from 'react-router-dom'
import { Button } from '@/components/ui/button'

const DealAnalytic = ({}: { startsAt?: DateTime; endsAt?: DateTime }) => {
  const { data: agHoursData } = useSWR(
    serialize(CampaignService.getCampaigns, {
      type: 'ag_hours'
    })
  )

  const { data: agDealsData } = useSWR(
    serialize(CampaignService.getCampaigns, {
      type: 'ag_deals'
    })
  )

  const { data: othersData } = useSWR(
    serialize(CampaignService.getCampaigns, {
      type: 'others'
    })
  )
  const hours = CampaignService.toRow(agHoursData)
  const deals = CampaignService.toRow(agDealsData)
  const others = CampaignService.toRow(othersData)

  const { data: hoursAnalytics } = useSWR<CampaignWishlistAnalytics[] | undefined | null>(
    hours.length > 0
      ? AnalyticService.getCampaignWishlistAnaytic(hours.map((e) => e.id.toString()))
      : null
  )
  const { data: dealsAnalytics } = useSWR<CampaignWishlistAnalytics[] | undefined | null>(
    deals.length > 0
      ? AnalyticService.getCampaignWishlistAnaytic(deals.map((e) => e.id.toString()))
      : null
  )
  const { data: othersAnalytics } = useSWR<CampaignWishlistAnalytics[] | undefined | null>(
    others.length > 0
      ? AnalyticService.getCampaignWishlistAnaytic(others.map((e) => e.id.toString()))
      : null
  )

  return (
    <div className="h-fit">
      <Tabs defaultValue="deals" className="space-y-4">
        <TabsList>
          <TabsTrigger value="deals">AG Sales</TabsTrigger>
          <TabsTrigger value="hours">AG Hours</TabsTrigger>
          <TabsTrigger value="others">Others</TabsTrigger>
        </TabsList>

        <TabsContent value="deals">
          <div className="space-y-4">
            {dealsAnalytics?.map((deal) => {
              const campaignName = deals.find((e) => e.id == deal.campaign_id)?.title
              return (
                <CampaignAnalyticCard
                  analytic={deal}
                  campaignName={campaignName ?? ''}
                  key={deal.campaign_id}
                />
              )
            })}
          </div>
        </TabsContent>

        <TabsContent value="hours">
          <div className="space-y-4">
            {hoursAnalytics?.map((hour) => {
              const campaignName = hours.find((e) => e.id == hour.campaign_id)?.title
              return (
                <CampaignAnalyticCard
                  analytic={hour}
                  campaignName={campaignName ?? ''}
                  key={hour.campaign_id}
                />
              )
            })}
          </div>
        </TabsContent>

        <TabsContent value="others">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {othersAnalytics?.map((other, index) => {
                return (
                  <TableRow key={index}>
                    <TableCell>{others.find((e) => e.id == other.campaign_id)?.title}</TableCell>
                    <TableCell>
                      <Button asChild>
                        <Link to={`/statistics/campaigns/${other.campaign_id}`}>View Details</Link>
                      </Button>
                    </TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
          </Table>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default DealAnalytic
