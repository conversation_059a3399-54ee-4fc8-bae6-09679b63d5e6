import CustomTooltip from '@/components/Charts/CustomTooltip'
import { Card, CardContent, CardHeader, CardTitle, Content, Title } from '@/components/ui/card'
import { ChartColors } from '@/constants/colors'
import { IDataResponse } from '@/network/request'
import AnalyticService from '@/network/services/analytic'
import EventService from '@/network/services/event'
import { PurchaseAnalytics } from '@/types/Analytics'
import { GeneralEventInfo } from '@/types/Event'
import { ColumnDef, Row } from '@tanstack/react-table'
import { DateTime } from 'luxon'
import { useEffect, useMemo, useState } from 'react'
import {
  Bar,
  BarChart,
  CartesianGrid,
  Cell,
  Legend,
  Pie,
  PieChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis
} from 'recharts'
import useSWR from 'swr'
import { DataTable, FilterColumn } from '../Table/DataTable'
import { Button } from '../ui/button'

const PurchaseAnalytic = () => {
  const [selectedEvent, setSelectedEvent] = useState<GeneralEventInfo | null>()

  // Event Tables
  // column declaration for DataTable
  const columns: ColumnDef<GeneralEventInfo>[] = [
    {
      accessorKey: 'title',
      header: 'Title'
    },
    {
      accessorKey: 'from',
      header: 'From',
      cell: (props) => <span>{props.getValue<DateTime>().toFormat('yyyy-MM-dd')}</span>
    },
    {
      accessorKey: 'to',
      header: 'To',
      cell: (props) => <span>{props.getValue<DateTime>().toFormat('yyyy-MM-dd')}</span>
    },
    {
      accessorKey: 'location',
      header: 'Venue'
    }
  ]

  // Declaration of which column can be filtered
  const columnFilter: FilterColumn[] = [
    {
      columnKey: 'title',
      header: 'Title',
      dataType: 'string'
    },
    {
      columnKey: 'from',
      header: 'From',
      dataType: 'date'
    },
    {
      columnKey: 'to',
      header: 'To',
      dataType: 'date'
    }
  ]

  const EventTable = () => {
    return (
      <DataTable<GeneralEventInfo, unknown, IDataResponse<GeneralEventInfo>>
        columns={columns}
        filterColumns={columnFilter}
        swrService={EventService.getEvents}
        pageParam="page"
        limitParam="limit"
        toRow={EventService.toRow as () => GeneralEventInfo[]}
        toPaginate={EventService.toPaginate}
        onRowClick={(row: Row<GeneralEventInfo>) => {
          setSelectedEvent(row.original)
        }}
      />
    )
  }
  // Event Tables

  const { data, mutate } = useSWR<PurchaseAnalytics>(
    selectedEvent ? () => AnalyticService.getPurchaseAnalytic(selectedEvent.id) : null
  )

  useEffect(() => {
    if (selectedEvent) {
      mutate()
    }
  }, [selectedEvent, mutate])

  const total = useMemo(() => {
    return {
      brand_sales_count: data?.brands.reduce(
        (prev, curr) => (prev += parseInt(`${curr.sales_count}`)),
        0
      ),
      brand_revenue: data?.brands.reduce(
        (prev, curr) => (prev += parseInt(`${curr.total_revenue}`)),
        0
      ),
      category_sales_count: data?.categories.reduce(
        (prev, curr) => (prev += parseInt(`${curr.sales_count}`)),
        0
      ),
      category_revenue: data?.categories.reduce(
        (prev, curr) => (prev += parseInt(`${curr.total_revenue}`)),
        0
      )
    }
  }, [data])

  // data from backend sorted with revenue, hence client sort with sales count manually
  const sortedWithSalesCount = useMemo(() => {
    const clonedData = data && (JSON.parse(JSON.stringify(data)) as PurchaseAnalytics)

    if (data) {
      return {
        brand_sorted: clonedData?.brands?.sort((a, b) => b.sales_count - a.sales_count),
        category_sorted: clonedData?.categories?.sort((a, b) => b.sales_count - a.sales_count)
      }
    }
  }, [data])

  const handleExport = async () => {
    if (selectedEvent) {
      try {
        const { data: result } = await AnalyticService.exportPurchaseAnalytic(selectedEvent.id)
        const csv_links = [result.download_url_category, result.download_url_brand]

        for (const link of csv_links) {
          try {
            // Create a hidden anchor element
            const a = document.createElement('a')
            a.href = link ?? ''
            a.download = '' // This will use the filename from the server
            a.style.display = 'none'

            // Add to document, click it, and remove it
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)

            // Wait a bit before the next download
            await new Promise((resolve) => setTimeout(resolve, 1000))
          } catch (error) {
            console.error('Error downloading file:', error)
          }
        }
        return
      } catch (e) {
        console.log('error export purchase analytics', e)
      }
    }
  }

  return (
    <>
      <div>
        <Card className="h-fit">
          <CardHeader className="flex flex-row space-y-0 items-center justify-between">
            <CardTitle>Choose the event</CardTitle>
          </CardHeader>

          <CardContent className="grid grid-cols-1 xl:grid-cols-1">
            <div>
              <Title>Events</Title>
              <Content>
                <div className="space-y-4">
                  <EventTable />
                </div>
              </Content>
            </div>
          </CardContent>
        </Card>
      </div>
      {selectedEvent && data && (
        <div>
          <div className="flex flex-row mb-4 items-end space-x-3">
            <Card>
              <CardHeader className="flex flex-row space-y-0 items-center justify-between">
                <CardTitle>Total Revenue</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="font-bold">RM {data?.total_revenue}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row space-y-0 items-center justify-between">
                <CardTitle>Event</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="font-bold">
                  {selectedEvent?.title} @ {selectedEvent?.location}
                </div>
              </CardContent>
            </Card>

            <Button onClick={handleExport}>Export To CSV</Button>
          </div>

          <Card className="h-fit">
            <CardHeader className="flex flex-row space-y-0 items-center justify-between">
              <CardTitle>Purchase Analytic</CardTitle>
            </CardHeader>

            <CardContent className="grid grid-rows-2 space-y-10">
              {/* Bar Chart */}
              <div className="grid grid-cols-1 xl:grid-cols-1 ">
                <div className="h-[450px]">
                  <Title>Top 10 Brands Total Revenue</Title>
                  <Content>
                    {data?.brands && data.brands.length > 0 ? (
                      <div className="h-[450px]">
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart width={730} height={250} data={data.brands}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="brand_name" />
                            <YAxis />
                            <Tooltip />
                            <Legend />
                            <Bar dataKey="total_revenue" fill="#56db5c" />
                          </BarChart>
                        </ResponsiveContainer>
                      </div>
                    ) : (
                      <div className="text-start">No Data</div>
                    )}
                  </Content>
                </div>

                <div className="h-[450px]">
                  <Title>Top 10 Brands Sales Count</Title>
                  <Content>
                    {data?.brands && data.brands.length > 0 ? (
                      <div className="h-[450px]">
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart
                            width={730}
                            height={250}
                            data={sortedWithSalesCount?.brand_sorted}
                          >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="brand_name" />
                            <YAxis />
                            <Tooltip />
                            <Legend />
                            <Bar dataKey="sales_count" fill="#3ddff5" />
                          </BarChart>
                        </ResponsiveContainer>
                      </div>
                    ) : (
                      <div className="text-start">No Data</div>
                    )}
                  </Content>
                </div>
              </div>

              <div className="grid grid-cols-1 xl:grid-cols-1 ">
                <div className="h-[450px]">
                  <Title>Top 10 Category Total Revenue</Title>
                  <Content>
                    {data?.categories && data.categories.length > 0 ? (
                      <div className="h-[450px]">
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart width={730} height={250} data={data.categories}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="category_name" />
                            <YAxis />
                            <Tooltip />
                            <Legend />
                            <Bar dataKey="total_revenue" fill="#56db5c" />
                          </BarChart>
                        </ResponsiveContainer>
                      </div>
                    ) : (
                      <div className="text-start">No Data</div>
                    )}
                  </Content>
                </div>

                <div className="h-[450px]">
                  <Title>Top 10 Category Sales Count</Title>
                  <Content>
                    {data?.categories && data.categories.length > 0 ? (
                      <div className="h-[450px]">
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart
                            width={730}
                            height={250}
                            data={sortedWithSalesCount?.category_sorted}
                          >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="category_name" />
                            <YAxis />
                            <Tooltip />
                            <Legend />
                            <Bar dataKey="sales_count" fill="#3ddff5" />
                          </BarChart>
                        </ResponsiveContainer>
                      </div>
                    ) : (
                      <div className="text-start">No Data</div>
                    )}
                  </Content>
                </div>
              </div>

              {/* Pie Chart */}
              <div className="grid grid-cols-1 xl:grid-cols-1 ">
                <div className="h-[500px]">
                  <Title>Top 10 Brands Sales Count</Title>
                  <Content>
                    {data?.brands && data.brands.length > 0 ? (
                      <div className="h-[500px]">
                        <ResponsiveContainer width="100%" height="100%">
                          <PieChart>
                            <Tooltip content={<CustomTooltip total={total.brand_sales_count} />} />
                            <Pie
                              data={data.brands.map((brand) => ({
                                name: brand.brand_name,
                                value: parseInt(`${brand.sales_count}`)
                              }))}
                              dataKey="value"
                              nameKey="name"
                              cx="50%"
                              cy="50%"
                              outerRadius="80%"
                              fill="#8884d8"
                              label={({ x, y, cx, index }) => (
                                <text
                                  x={x}
                                  y={y}
                                  fill="black"
                                  textAnchor={x > cx ? 'start' : 'end'}
                                  dominantBaseline="central"
                                >
                                  {`${data.brands[index].brand_name ?? 'NULL'} (${(
                                    (parseInt(`${data.brands[index].sales_count}`) /
                                      (total.brand_sales_count ?? 1)) *
                                    100
                                  ).toFixed(1)}%)`}
                                </text>
                              )}
                            >
                              {data.brands.map((_, index) => (
                                <Cell
                                  key={`k-${index}`}
                                  fill={ChartColors[index % ChartColors.length]}
                                />
                              ))}
                            </Pie>
                          </PieChart>
                        </ResponsiveContainer>
                      </div>
                    ) : (
                      <div className="text-start">No Data</div>
                    )}
                  </Content>
                </div>

                <div className="h-[500px]">
                  <Title>Top 10 Category Sales Count</Title>
                  <Content>
                    {data?.categories && data.categories.length > 0 ? (
                      <div className="h-[500px]">
                        <ResponsiveContainer width="100%" height="100%">
                          <PieChart>
                            <Tooltip
                              content={<CustomTooltip total={total.category_sales_count} />}
                            />
                            <Pie
                              data={data.categories.map((category) => ({
                                name: category.category_name,
                                value: parseInt(`${category.sales_count}`)
                              }))}
                              dataKey="value"
                              nameKey="name"
                              cx="50%"
                              cy="50%"
                              outerRadius="80%"
                              fill="#8884d8"
                              label={({ x, y, cx, index }) => (
                                <text
                                  x={x}
                                  y={y}
                                  fill="black"
                                  textAnchor={x > cx ? 'start' : 'end'}
                                  dominantBaseline="central"
                                >
                                  {`${data.categories[index].category_name ?? 'NULL'} (${(
                                    (parseInt(`${data.categories[index].sales_count}`) /
                                      (total.category_sales_count ?? 1)) *
                                    100
                                  ).toFixed(1)}%)`}
                                </text>
                              )}
                            >
                              {data.categories.map((_, index) => (
                                <Cell
                                  key={`k-${index}`}
                                  fill={ChartColors[index % ChartColors.length]}
                                />
                              ))}
                            </Pie>
                          </PieChart>
                        </ResponsiveContainer>
                      </div>
                    ) : (
                      <div className="text-start">No Data</div>
                    )}
                  </Content>
                </div>

                <div className="h-[500px]">
                  <Title>Top 10 Brands Total Revenue</Title>
                  <Content>
                    {data?.brands && data.brands.length > 0 ? (
                      <div className="h-[500px]">
                        <ResponsiveContainer width="100%" height="100%">
                          <PieChart>
                            <Tooltip content={<CustomTooltip total={total.brand_revenue} />} />
                            <Pie
                              data={data.brands.map((brand) => ({
                                name: brand.brand_name,
                                value: parseInt(`${brand.total_revenue}`)
                              }))}
                              dataKey="value"
                              nameKey="name"
                              cx="50%"
                              cy="50%"
                              outerRadius="80%"
                              fill="#8884d8"
                              label={({ x, y, cx, index }) => (
                                <text
                                  x={x}
                                  y={y}
                                  fill="black"
                                  textAnchor={x > cx ? 'start' : 'end'}
                                  dominantBaseline="central"
                                >
                                  {`${data.brands[index].brand_name ?? 'NULL'} (${(
                                    (parseInt(`${data.brands[index].total_revenue}`) /
                                      (total.brand_revenue ?? 1)) *
                                    100
                                  ).toFixed(1)}%)`}
                                </text>
                              )}
                            >
                              {data.brands.map((_, index) => (
                                <Cell
                                  key={`k-${index}`}
                                  fill={ChartColors[index % ChartColors.length]}
                                />
                              ))}
                            </Pie>
                          </PieChart>
                        </ResponsiveContainer>
                      </div>
                    ) : (
                      <div className="text-start">No Data</div>
                    )}
                  </Content>
                </div>

                <div className="h-[500px]">
                  <Title>Top 10 Category Total Revenue</Title>
                  <Content>
                    {data?.categories && data.categories.length > 0 ? (
                      <div className="h-[500px]">
                        <ResponsiveContainer width="100%" height="100%">
                          <PieChart>
                            <Tooltip content={<CustomTooltip total={total.category_revenue} />} />
                            <Pie
                              data={data.categories.map((category) => ({
                                name: category.category_name,
                                value: parseInt(`${category.total_revenue}`)
                              }))}
                              dataKey="value"
                              nameKey="name"
                              cx="50%"
                              cy="50%"
                              outerRadius="80%"
                              fill="#8884d8"
                              label={({ x, y, cx, index }) => (
                                <text
                                  x={x}
                                  y={y}
                                  fill="black"
                                  textAnchor={x > cx ? 'start' : 'end'}
                                  dominantBaseline="central"
                                >
                                  {`${data.categories[index].category_name ?? 'NULL'} (${(
                                    (parseInt(`${data.categories[index].total_revenue}`) /
                                      (total.category_revenue ?? 1)) *
                                    100
                                  ).toFixed(1)}%)`}
                                </text>
                              )}
                            >
                              {data.categories.map((_, index) => (
                                <Cell
                                  key={`k-${index}`}
                                  fill={ChartColors[index % ChartColors.length]}
                                />
                              ))}
                            </Pie>
                          </PieChart>
                        </ResponsiveContainer>
                      </div>
                    ) : (
                      <div className="text-start">No Data</div>
                    )}
                  </Content>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </>
  )
}

export default PurchaseAnalytic
