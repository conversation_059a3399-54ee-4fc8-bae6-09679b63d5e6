import * as React from 'react'
import { useForm } from 'react-hook-form'

import { cn } from '@/lib/utils'
import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import useAuth from '@/hooks/useAuth'
import { LoginState } from '@/network/services/auth'

interface UserAuthFormProps extends React.HTMLAttributes<HTMLDivElement> {}

export function UserAuthForm({ className }: UserAuthFormProps) {
  const form = useForm<LoginState>({ shouldUseNativeValidation: false })
  const { login, loading, error } = useAuth()
  const onSubmit = form.handleSubmit((values) => {
    try {
      login(values)
    } catch (error) {
      console.error(error)
    }
  })

  return (
    <Form {...form}>
      <form onSubmit={onSubmit} className={cn('grid gap-2', className)}>
        <FormField
          control={form.control}
          name="email"
          rules={{ required: 'Please enter your email' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="password"
          rules={{ required: 'Please enter your password' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Password</FormLabel>
              <FormControl>
                <Input {...field} type="password" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        {error != null && <p className="text-destructive">Please try again</p>}
        <Button type="submit" disabled={form.formState.isSubmitting || loading} className="mt-2">
          {(form.formState.isSubmitting || loading) && (
            <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
          )}
          Sign In
        </Button>
      </form>
    </Form>
  )
}
