import { Button } from '@/components/ui/button'
import { Form } from '@/components/ui/form'
import {
  Sheet,
  Sheet<PERSON>lose,
  <PERSON>et<PERSON>ontent,
  <PERSON>etHeader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger
} from '@/components/ui/sheet'
import { CreateProductCategoryInput } from '@/types/ProductCategory'
import { PlusCircleIcon } from 'lucide-react'
import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import useAuth from '@/hooks/useAuth'
import { CreateProductBrandForm } from './CreateProductBrandForm'

export const AddBrandButton = () => {
  const { role } = useAuth()
  const [isSheetOpen, setIsSheetOpen] = useState(false)
  const form = useForm<CreateProductCategoryInput>({
    shouldUseNativeValidation: false
  })

  useEffect(() => {
    if (form.formState.isSubmitSuccessful) {
      form.reset()
      setIsSheetOpen(false)
    }
  }, [form.formState.isSubmitSuccessful])

  // only vendor can create brand
  if (role !== 'vendor') {
    return <></>
  }

  return (
    <div className="flex justify-end">
      <Sheet
        open={isSheetOpen}
        onOpenChange={(isOpen) => {
          setIsSheetOpen(isOpen)
        }}
      >
        <SheetTrigger asChild>
          <Button>
            <PlusCircleIcon size="16" className="mr-2" />
            Create Product Brand
          </Button>
        </SheetTrigger>
        <SheetContent side="bottom" className="w-full h-full">
          <SheetHeader className="max-w-4xl mx-auto pt-10 pb-6">
            <SheetTitle className="flex justify-between items-center">
              New Product Brand
              <div className="flex space-x-2">
                <SheetClose>
                  <Button
                    variant="outline"
                    onClick={() => {
                      form.reset()
                    }}
                  >
                    Cancel
                  </Button>
                </SheetClose>
                <Button
                  type="submit"
                  form="create-product-category-form"
                  disabled={form.formState.isSubmitting}
                  onClick={() => {
                    form.clearErrors()
                  }}
                >
                  Propose
                </Button>
              </div>
            </SheetTitle>
          </SheetHeader>
          <Form {...form}>
            <CreateProductBrandForm />
          </Form>
        </SheetContent>
      </Sheet>
    </div>
  )
}
