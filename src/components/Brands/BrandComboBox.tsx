import { SearchProductCollections, useProductCollections } from '@/hooks/products/useProducts'
import { cn } from '@/lib/utils'
import { ChevronsUpDown } from 'lucide-react'
import { isEmpty } from 'radash'
import { FC, useState } from 'react'
import { useFormContext } from 'react-hook-form'
import { FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Search } from '@/components/Form/Search'

const POPOVER_WIDTH = 'w-[250px]'

// multiple select
export const BrandComboBox: FC<{
  productCollections: SearchProductCollections[]
  name: string
}> = ({ productCollections, name }) => {
  const [open, setOpen] = useState(false)
  const form = useFormContext()

  const handleSetActive = (productBrand: SearchProductCollections) => {
    const newProductCollections = [...productCollections, productBrand]
    form.setValue(name, newProductCollections)
  }

  const handleSetInactive = (productCollection: SearchProductCollections) => {
    const newProductCollections = [...productCollections]
    const findIndex = newProductCollections.findIndex((c) => c.id == productCollection.id)
    newProductCollections.splice(findIndex, 1)
    form.setValue(name, newProductCollections)
  }

  const displayName = !isEmpty(productCollections)
    ? productCollections.length == 1
      ? productCollections[0].title
      : productCollections.length
    : 'Choose a brand'

  return (
    <FormItem className="flex flex-col">
      <FormLabel>Brands</FormLabel>

      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <FormControl>
            <Button
              variant="outline"
              role="combobox"
              className={cn(
                'w-[200px] justify-between',
                isEmpty(productCollections) && 'text-muted-foreground',
                POPOVER_WIDTH
              )}
            >
              {(productCollections.length ?? 0) > 1 ? (
                <Badge variant="outline">{displayName}</Badge>
              ) : (
                <>{displayName}</>
              )}

              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </FormControl>
        </PopoverTrigger>

        <PopoverContent side="bottom" className={cn('p-0', POPOVER_WIDTH)}>
          <Search<SearchProductCollections, SearchProductCollections[]>
            multiple
            fn={useProductCollections}
            renderFn={(pc: SearchProductCollections) => pc.title}
            valueFn={(pc: SearchProductCollections) => pc.id}
            compareFn={(pc: SearchProductCollections) => {
              if (isEmpty(productCollections)) {
                return false
              }

              const findIndex = productCollections?.findIndex((productCollection) => {
                return productCollection.id == pc.id
              })

              return findIndex != -1
            }}
            selectedResult={productCollections}
            onSelectResult={handleSetActive}
            onDeselectResult={handleSetInactive}
          />
        </PopoverContent>
      </Popover>
      <FormMessage />
    </FormItem>
  )
}
