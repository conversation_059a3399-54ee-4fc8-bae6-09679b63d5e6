import { ColumnDef, Row, createColumnHelper } from '@tanstack/react-table'
import { DataTable, FilterColumn } from '@/components/Table/DataTable'
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem
} from '@/components/ui/dropdown-menu'
import { useToast } from '@/components/ui/use-toast'
import { Button } from '@/components/ui/button'
import { MonitorCheckIcon, MonitorXIcon, MoreHorizontal, StoreIcon, Trash2Icon } from 'lucide-react'
import { FC } from 'react'
import { useNavigate } from 'react-router-dom'
import { mutate } from 'swr'
import { cn, statusToColor } from '@/lib/utils'
import ProductCollectionService, {
  ProductCollectionResponse
} from '@/network/services/product_collection'
import { ProductCollection } from '@/types/ProductCollection'
import { DropdownMenuGroup } from '@radix-ui/react-dropdown-menu'
import useAuth from '@/hooks/useAuth'
import { serialize } from '@/network/request'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger
} from '../ui/alert-dialog'

const columnHelper = createColumnHelper<ProductCollection>()

// column for admins
const columns: ColumnDef<ProductCollection>[] = [
  {
    accessorKey: 'rank',
    header: 'Rank',
    cell: (rank) => rank.getValue() ?? '-'
  },
  {
    accessorKey: 'title',
    header: 'Name'
  },
  {
    accessorKey: 'store.name',
    header: 'Company'
  },
  {
    accessorKey: 'description',
    header: 'Description'
  },
  {
    accessorKey: 'thumbnail_url',
    header: 'Thumbnail',
    cell: (props) => {
      return <img className="w-[80px]" src={props.getValue<string>()} />
    }
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: (props) => {
      const value = props.getValue<string>()
      const color = statusToColor(value)

      return (
        <div className="flex items-center space-x-2">
          <div className={cn('h-1.5 w-1.5 self-center rounded-full', color)} />
          <span className="capitalize">{value}</span>
        </div>
      )
    }
  },
  columnHelper.display({
    id: 'actions',
    cell: (props) => <RowActions row={props.row} />
  })
]

const columnFilter: FilterColumn[] = [
  {
    columnKey: 'title',
    header: 'Search',
    dataType: 'string'
  }
]

const RowActions: FC<{ row: Row<ProductCollection> }> = ({ row }) => {
  const nav = useNavigate()
  const productBrandId = row.original.id
  const { toast } = useToast()

  const updateBrandStatus = async (newStatus: string) => {
    try {
      const { data: response } = await ProductCollectionService.updateProductCollection(
        row.original.id,
        {
          status: newStatus
        }
      )

      if (response != null) {
        mutate(
          (key) =>
            typeof key === 'string' &&
            key.startsWith(ProductCollectionService.getProductCollections)
        )
        toast({
          title: `Brand ${newStatus}`,
          variant: 'success'
        })
      }

      console.log(response)
    } catch (e) {
      console.log(e)

      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  }

  return (
    <div className="w-full flex justify-end">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0" onClick={(e) => e.stopPropagation()}>
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuGroup>
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation()
                nav(`/companies/${row.original.store.id}`)
              }}
              className="space-x-2"
            >
              <StoreIcon size="16" />
              <span>Go to Exhibitor</span>
            </DropdownMenuItem>

            <DropdownMenuItem className="space-x-2" disabled={row.original.status !== 'proposed'}>
              <AlertDialog key="publish-brand">
                <AlertDialogTrigger
                  type="button"
                  onClick={(e) => e.stopPropagation()}
                  className="flex space-x-2 w-full"
                >
                  <MonitorCheckIcon size="16" />
                  <span>Publish</span>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>
                      Publish {row.original.title} from {row.original.store?.name}?
                    </AlertDialogTitle>
                    <AlertDialogDescription className="text-red-500">
                      *NOTE: this action cannot be undone
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={(e) => {
                        e.stopPropagation()
                        updateBrandStatus('published')
                      }}
                    >
                      Confirm
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </DropdownMenuItem>

            <DropdownMenuItem
              // onClick={(e) => {
              //   e.stopPropagation()
              //   updateBrandStatus('rejected')
              // }}
              className="space-x-2"
              disabled={row.original.status !== 'proposed'}
            >
              <AlertDialog key="reject-brand">
                <AlertDialogTrigger
                  type="button"
                  onClick={(e) => e.stopPropagation()}
                  className="flex space-x-2 w-full"
                >
                  <MonitorXIcon size="16" />
                  <span>Reject</span>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>
                      Reject {row.original.title} from {row.original.store?.name}?
                    </AlertDialogTitle>
                    <AlertDialogDescription className="text-red-500">
                      *NOTE: this action cannot be undone
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={(e) => {
                        e.stopPropagation()
                        updateBrandStatus('published')
                      }}
                    >
                      Confirm
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </DropdownMenuItem>

            <DropdownMenuItem
              onClick={async (event) => {
                event.stopPropagation()

                try {
                  await ProductCollectionService.deleteProductCollection(productBrandId)
                  mutate(
                    (key) =>
                      typeof key === 'string' &&
                      key.startsWith(ProductCollectionService.getProductCollections)
                  )
                  toast({
                    description: 'Product brand deleted',
                    variant: 'destructive'
                  })
                } catch (error) {
                  console.log(error)
                }
              }}
              className="space-x-2"
            >
              <AlertDialog key="reject-brand">
                <AlertDialogTrigger
                  type="button"
                  onClick={(e) => e.stopPropagation()}
                  className="flex space-x-2 w-full"
                >
                  <Trash2Icon size="16" />
                  <span>Delete</span>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>
                      Delete {row.original.title} from {row.original.store?.name}?
                    </AlertDialogTitle>
                    <AlertDialogDescription className="text-red-500">
                      *NOTE: this action cannot be undone
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={async (event) => {
                        event.stopPropagation()

                        try {
                          await ProductCollectionService.deleteProductCollection(productBrandId)
                          mutate(
                            (key) =>
                              typeof key === 'string' &&
                              key.startsWith(ProductCollectionService.getProductCollections)
                          )
                          toast({
                            description: 'Product collection deleted',
                            variant: 'destructive'
                          })
                        } catch (error) {
                          console.log(error)
                        }
                      }}
                    >
                      Confirm
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </DropdownMenuItem>
          </DropdownMenuGroup>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}

const BrandTable = () => {
  const nav = useNavigate()
  const { role } = useAuth()
  // remove index 1 (store) & 5 (action)
  const roleBasedColumns =
    role == 'vendor' ? [...columns].filter((_, index) => index != 1 && index != 5) : columns

  return (
    <DataTable<ProductCollection, unknown, ProductCollectionResponse>
      columns={roleBasedColumns}
      filterColumns={columnFilter}
      swrService={serialize(
        ProductCollectionService.getProductCollections,
        { expand: ['store'] },
        'comma'
      )}
      pageParam="page"
      limitParam="limit"
      filterParam="q"
      sortParam="sort"
      sortColumns={['rank', 'title', 'status']}
      toRow={ProductCollectionService.toRow}
      toPaginate={ProductCollectionService.toPaginate}
      onRowClick={(row: Row<ProductCollection>) => {
        nav(`/brands/${row.original.id}`)
      }}
    />
  )
}

export default BrandTable
