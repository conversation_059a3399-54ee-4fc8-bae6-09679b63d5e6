/* eslint-disable @typescript-eslint/ban-ts-comment */
import * as React from 'react'
import { useFormContext } from 'react-hook-form'
import { mutate } from 'swr'

import { cn } from '@/lib/utils'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { toast } from '@/components/ui/use-toast'
import { isAxiosError } from 'axios'
import { Input } from '@/components/ui/input'
import { CreateProductCollectionInput } from '@/types/ProductCollection'
import { Dropzone } from '../Form/Dropzone'
import { FileWithPath } from 'react-dropzone'
import { Image } from '@unpic/react'
import FileService from '@/network/services/file'
import ProductCollectionService from '@/network/services/product_collection'
import OrSeparator from '../ui/orSeparator'
import TextEditorComponent from '../Editor/TextEditor'

interface ProductCategoryFormProps extends React.HTMLAttributes<HTMLDivElement> {}

export function CreateProductBrandForm({ className }: ProductCategoryFormProps) {
  const form = useFormContext<CreateProductCollectionInput>()
  const thumbnail = form.watch('thumbnail') as FileWithPath & { preview: string }
  const thumbnailUrl = form.watch('thumbnail_url')

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      // upload media
      if (values.thumbnail) {
        // only upload if no url provided
        if (!values.thumbnail_url) {
          const { data } = await FileService.upload([values.thumbnail])
          // set media url
          values.thumbnail_url = data.uploads[0].url
        }
        delete values.thumbnail
      }

      if (values.thumbnail_url) {
        delete values.thumbnail
      }

      await ProductCollectionService.createProductCollection(values)
      mutate(
        (key) =>
          typeof key === 'string' && key.startsWith(ProductCollectionService.getProductCollections)
      )
      toast({
        title: 'Product brand created',
        variant: 'success'
      })
    } catch (error) {
      console.error(error)

      if (isAxiosError(error) && error.response?.data.type == 'duplicate_error') {
        // likely is handle duplicated
        if (error.response.data.message.startsWith('Product_collection with handle')) {
          form.setError('handle', {
            message: error.response.data.message.replace('_', ' ')
          })
        }
      } else {
        form.setError('root', {})
      }

      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <form
      id="create-product-category-form"
      onSubmit={onSubmit}
      className={cn('flex flex-col space-y-8 max-w-4xl mx-auto', className)}
    >
      <div className="grid grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="title"
          rules={{ required: 'Please enter the title' }}
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Title</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="handle"
          rules={{ required: 'Please enter the handle' }}
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Handle</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          rules={{ required: 'Please enter the description' }}
          render={({ field }) => (
            <FormItem className="flex flex-col col-span-2">
              <FormLabel>Description</FormLabel>
              <FormControl>
                <TextEditorComponent content={field.value} onChange={field.onChange} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="thumbnail_url"
          rules={{
            required:
              thumbnail || thumbnailUrl ? false : 'Please provide the campaign thumbnail url'
          }}
          render={({ field }) => (
            <>
              <FormLabel>Thumbnail</FormLabel>
              <FormItem className="flex flex-col col-span-2">
                <FormLabel>Image URL</FormLabel>
                <FormControl>
                  <Input placeholder="Image Url" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            </>
          )}
        />

        <div className="col-span-2">
          <OrSeparator />
        </div>

        <FormField
          control={form.control}
          name="thumbnail"
          rules={{
            required: thumbnail || thumbnailUrl ? false : 'Please provide the campaign thumbnail'
          }}
          render={({ field }) => (
            <FormItem className="flex flex-col col-span-2">
              <FormLabel>Upload Image</FormLabel>
              <FormControl>
                <Dropzone
                  multiple={false}
                  onDrop={(acceptedFiles) => {
                    const preview = Object.assign(acceptedFiles[0], {
                      preview: URL.createObjectURL(acceptedFiles[0])
                    })

                    form.setValue('thumbnail', preview as File, {
                      shouldValidate: true
                    })
                  }}
                  {...field}
                />
              </FormControl>
              {thumbnail && (
                <Image layout="constrained" width={320} height={320} src={thumbnail.preview} />
              )}
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </form>
  )
}
