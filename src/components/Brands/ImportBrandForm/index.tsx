/* eslint-disable @typescript-eslint/ban-ts-comment */
import * as React from 'react'

import { cn } from '@/lib/utils'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Dropzone } from '@/components/Form/Dropzone'
import { useFormContext } from 'react-hook-form'
import FileService from '@/network/services/file'
import { CreateBatchJobInput } from '@/types/BatchJob'
import BatchJobService from '@/network/services/batch-job'
import { toast } from '@/components/ui/use-toast'
import { isAxiosError } from 'axios'
import { FileWithPath } from 'react-dropzone'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from '@/components/ui/accordion'
import { Separator } from '@/components/ui/separator'

interface ImportBrandForm extends React.HTMLAttributes<HTMLDivElement> {}

export function ImportBrandForm({ className }: ImportBrandForm) {
  const form = useFormContext<CreateBatchJobInput & { file?: File }>()
  const file = form.watch('file') as FileWithPath

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      if (!values.file) {
        return
      }

      const { data } = await FileService.uploadPrivate([values.file])
      const fileKey = data.uploads[0].key
      values.context.fileKey = fileKey
      values.type = 'product-collection-import'

      delete values.file

      await BatchJobService.createBatchJob(values)

      toast({
        title: 'File uploaded',
        variant: 'success'
      })
    } catch (error) {
      console.error(error)
      if (isAxiosError(error) && error.response?.data.type == 'duplicate_error') {
        // TODO: need to handle errors here
      } else {
        form.setError('root', {})
      }

      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <>
      <form
        id="import-brand-form"
        onSubmit={onSubmit}
        className={cn('mx-auto flex max-w-4xl flex-col space-y-8', className)}
      >
        <FormField
          control={form.control}
          name="file"
          rules={{ required: 'Please upload the csv file' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Upload CSV file</FormLabel>
              <FormControl>
                <Dropzone
                  multiple={false}
                  onDrop={(acceptedFiles) => {
                    const file = Object.assign(acceptedFiles[0])

                    form.setValue('file', file as File, {
                      shouldValidate: true
                    })
                  }}
                  description="Drop your csv file here, or click to browse"
                  accept=".csv"
                  files={file ? [file] : []}
                  {...field}
                />
              </FormControl>
              <FormDescription>Please follow the format exactly</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
      </form>
      <div className="pt-5 pb-5">
        <Separator className={cn('mx-auto flex max-w-4xl flex-col space-y-8', className)} />
      </div>
      <div className={cn('mx-auto flex max-w-4xl flex-col space-y-2', className)}>
        <h1 className="font-semibold">Guidelines</h1>

        <Accordion type="multiple" className="w-full">
          <AccordionItem value="create-brand">
            <AccordionTrigger>A. Create New Brand</AccordionTrigger>
            <AccordionContent>
              <div className="space-y-5">
                'Brand Title', 'Brand Handle', 'Brand Company Id' are{' '}
                <span className="font-semibold text-red-900">compulsory</span> fields. 'Brand
                Handle' are used to uniquely identify each brand row for both create/update process,
                hence id column is not needed, though the Brand Id will be generated automatically
                by the system.
                <br />
                <div className="bg-gray-300 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md">
                  1. Please ensure 'Brand Handle' are unique. If the handle already exist, it will
                  be treated as update, else it will be treated as create new brand row.
                </div>
                <div className="bg-gray-300 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md">
                  2. 'Brand Status' are default to 'draft' when leave blank.
                </div>
                <div className="bg-gray-300 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md">
                  3. Please ensure 'Brand Company Id' is an existing company id.
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="update-brand">
            <AccordionTrigger>B. Update Brand</AccordionTrigger>
            <AccordionContent>
              <div className="space-y-5">
                'Brand Title', 'Brand Handle', 'Brand Company Id' are{' '}
                <span className="font-semibold text-red-900">compulsory</span> fields.
                <br />
                <div className="bg-gray-300 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md">
                  1. All fields can be updated except 'Brand Handle'.
                </div>
                <div className="bg-gray-300 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md">
                  2. 'Brand Status' are default to 'draft' when leave blank.
                </div>
                <div className="bg-gray-300 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md">
                  3. Please ensure 'Brand Company Id' is an existing company id.
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
    </>
  )
}
