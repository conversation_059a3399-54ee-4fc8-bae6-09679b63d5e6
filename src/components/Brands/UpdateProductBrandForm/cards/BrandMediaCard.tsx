import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { Label } from '@/components/ui/label'
import { getUrlMediaType } from '@/lib/utils'
import { ProductCollection } from '@/types/ProductCollection'
import { Image } from '@unpic/react'
import { EditIcon, MoreHorizontal } from 'lucide-react'
import { capitalize } from 'radash'
import { FC, useState } from 'react'
import UpdateBrandMediaDialog from './UpdateBrandMediaDialog'

const BrandMediaCard: FC<{ brand: ProductCollection }> = ({ brand }) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0">
          <CardTitle>Media</CardTitle>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" forceMount>
              <DropdownMenuGroup>
                <DropdownMenuItem
                  onClick={async (event) => {
                    event.stopPropagation()
                    setIsDialogOpen(true)
                  }}
                  className="space-x-2"
                >
                  <EditIcon size="16" />
                  <span>Edit</span>
                </DropdownMenuItem>
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>
        </CardHeader>
        <CardContent className="space-y-4">
          {brand.images?.map((image) => {
            const mediaType = getUrlMediaType(image.url)

            return (
              <span key={image.url} className="grid grid-cols-[150px_1fr] items-center space-x-2">
                {mediaType == 'video' ? (
                  <video controls src={image.url} height={150} width={150} className="rounded-md" />
                ) : (
                  <Image layout="constrained" width={150} height={150} src={image.url} />
                )}
                <Label className="text-ellipsis text-xs font-normal text-gray-500">
                  {capitalize(mediaType ?? '')}
                </Label>
              </span>
            )
          })}
        </CardContent>
      </Card>

      <UpdateBrandMediaDialog {...{ isDialogOpen, setIsDialogOpen, brand }} />
    </>
  )
}

export default BrandMediaCard
