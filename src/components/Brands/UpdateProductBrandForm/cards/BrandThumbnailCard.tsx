import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Image } from '@unpic/react'
import { EditIcon, MoreHorizontal, Trash2Icon } from 'lucide-react'
import { FC, useState } from 'react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { ProductCollection } from '@/types/ProductCollection'
import UpdateBrandThumbnailDialog from './UpdateBrandThumbnailDialog'

const BrandThumbnailCard: FC<{ brand: ProductCollection }> = ({ brand }) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <CardTitle>Thumbnail</CardTitle>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" forceMount>
              <DropdownMenuGroup>
                <DropdownMenuItem
                  onClick={async (event) => {
                    event.stopPropagation()
                    setIsDialogOpen(true)
                  }}
                  className="space-x-2"
                >
                  <EditIcon size="16" />
                  <span>Edit</span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={async (event) => {
                    event.stopPropagation()
                  }}
                  className="space-x-2"
                >
                  <Trash2Icon size="16" />
                  <span>Delete</span>
                </DropdownMenuItem>
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>
        </CardHeader>
        <CardContent>
          {brand.thumbnail_url && (
            <Image layout="constrained" width={150} height={150} src={brand.thumbnail_url} />
          )}
        </CardContent>
      </Card>

      <UpdateBrandThumbnailDialog {...{ isDialogOpen, setIsDialogOpen, brand }} />
    </>
  )
}

export default BrandThumbnailCard
