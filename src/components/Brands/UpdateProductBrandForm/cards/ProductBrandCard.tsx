import { Button } from '@/components/ui/button'
import {
  <PERSON>,
  CardHeader,
  CardTitle,
  CardContent,
  Card<PERSON>ooter,
  CardContentLabel,
  CardContentTitle
} from '@/components/ui/card'
import { ProductCollection, UpdateProductCollectionInput } from '@/types/ProductCollection'
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem
} from '@/components/ui/dropdown-menu'
import { EditIcon, MoreHorizontal } from 'lucide-react'
import { FC, useMemo, useState } from 'react'
import useAuth from '@/hooks/useAuth'
import { cn, statusToColor } from '@/lib/utils'
import { mutate } from 'swr'
import { toast } from '@/components/ui/use-toast'
import { UpdateProductBrandInfoForm } from '../forms/UpdateProductBrandInfoForm'
import ProductCollectionService from '@/network/services/product_collection'
import { NavLink } from 'react-router-dom'

const ProductCategoryStatusBadge: FC<{ brand: ProductCollection }> = ({ brand }) => {
  const { role } = useAuth()
  const color = useMemo(() => {
    return statusToColor(brand.status)
  }, [brand.status])

  const updateStatus = async (newStatus: string) => {
    try {
      const { data: response } = await ProductCollectionService.updateProductCollection(brand.id, {
        status: newStatus
      } as UpdateProductCollectionInput)

      if (response != null) {
        mutate(
          (key) =>
            typeof key === 'string' &&
            key.startsWith(ProductCollectionService.getProductCollection(brand.id))
        )
        toast({
          title: `Brand ${newStatus}`,
          variant: 'success'
        })
      }
    } catch (e) {
      console.log(e)

      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="h-8">
          <div className="flex items-center space-x-2">
            <div className={cn('h-1.5 w-1.5 self-center rounded-full', color)} />
            <span className="capitalize text-xs">{brand.status}</span>
          </div>
        </Button>
      </DropdownMenuTrigger>
      {role !== 'vendor' && brand.status == 'proposed' && (
        <DropdownMenuContent align="end" forceMount>
          <DropdownMenuGroup>
            <DropdownMenuItem
              onClick={async (event) => {
                event.stopPropagation()
                updateStatus('published')
              }}
              className="space-x-2"
            >
              <div
                className={cn('h-1.5 w-1.5 self-center rounded-full', statusToColor('published'))}
              />
              <span className="capitalize text-xs">Publish</span>
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={async (event) => {
                event.stopPropagation()
                updateStatus('rejected')
              }}
              className="space-x-2"
            >
              <div
                className={cn('h-1.5 w-1.5 self-center rounded-full', statusToColor('rejected'))}
              />
              <span className="capitalize text-xs">Reject</span>
            </DropdownMenuItem>
          </DropdownMenuGroup>
        </DropdownMenuContent>
      )}
    </DropdownMenu>
  )
}

export const ProductBrandCard: FC<{ brand: ProductCollection }> = ({ brand }) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const { role } = useAuth()

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <CardTitle>{brand.title}</CardTitle>
          <div className="flex flex-row space-x-2">
            <ProductCategoryStatusBadge {...{ brand }} />
            {role === 'super_admin' && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-8 w-8 p-0">
                    <span className="sr-only">Open menu</span>
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" forceMount>
                  <DropdownMenuGroup>
                    <DropdownMenuItem
                      onClick={async (event) => {
                        event.stopPropagation()
                        setIsDialogOpen(true)
                      }}
                      className="space-x-2"
                    >
                      <EditIcon size="16" />
                      <span>Edit product brand</span>
                    </DropdownMenuItem>
                  </DropdownMenuGroup>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        </CardHeader>
        <CardContent className="flex flex-col space-y-4">
          <div className="grid grid-cols-[200px_1fr] gap-2">
            <CardContentTitle>Handle</CardContentTitle>
            <CardContentLabel>{brand.handle}</CardContentLabel>
            <CardContentTitle>Description</CardContentTitle>
            <CardContentLabel>{brand.description}</CardContentLabel>
            {role !== 'vendor' && (
              <>
                <CardContentTitle>Company</CardContentTitle>
                <CardContentLabel className="flex flex-col space-y-2">
                  <p>{brand.store?.name ?? ''}</p>
                  <NavLink to={`/companies/${brand.store.id}`} className="space-y-4">
                    <Button size="sm">Go to Company</Button>
                  </NavLink>
                </CardContentLabel>
              </>
            )}
            <CardContentTitle>Instagram</CardContentTitle>
            <CardContentLabel>{brand.metadata.instagram ?? '-'}</CardContentLabel>
            <CardContentTitle>Facebook</CardContentTitle>
            <CardContentLabel>{brand.metadata?.facebook ?? '-'}</CardContentLabel>
            <CardContentTitle>Tiktok</CardContentTitle>
            <CardContentLabel>{brand.metadata?.tiktok ?? '-'}</CardContentLabel>
            <CardContentTitle>Website</CardContentTitle>
            <CardContentLabel>{brand.metadata?.website ?? '-'}</CardContentLabel>
            <CardContentTitle>More Awesome Brands</CardContentTitle>
            <CardContentLabel>
              {brand.related_collections?.map((col) => (
                <p>{col.title}</p>
              ))}
            </CardContentLabel>
          </div>
        </CardContent>
        <CardFooter></CardFooter>
      </Card>

      <UpdateProductBrandInfoForm {...{ isDialogOpen, setIsDialogOpen, brand }} />
    </>
  )
}
