import {
  Card,
  CardHeader,
  CardT<PERSON>le,
  CardContent,
  CardFooter,
  CardDescription
} from '@/components/ui/card'
import { FC } from 'react'
import { Product } from '@/types/Product'
import { ProductCollection } from '@/types/ProductCollection'
import { AddProductButton } from '@/components/Product/AddProductButton'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'
import { cn, statusToColor } from '@/lib/utils'

const SimilarBrandedProductCard: FC<{ brand: ProductCollection; products: Product[] }> = ({
  products
}) => {
  return (
    <>
      <Card>
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <div className="flex flex-col space-y-2">
            <CardTitle>Products</CardTitle>
            <CardDescription>Products under this brand</CardDescription>
          </div>
          <div className="flex flex-row space-x-2">
            {/* TODO: pass in the current brand into default value of form */}
            <AddProductButton />
          </div>
        </CardHeader>
        <CardContent className="flex flex-col space-y-4">
          {/* TODO better looking table */}
          <SimilarBrandProductTable products={products} />
        </CardContent>
        <CardFooter></CardFooter>
      </Card>
    </>
  )
}

const SimilarBrandProductTable: FC<{ products: Product[] }> = ({ products }) => {
  console.log('similar prod', products)

  return (
    <div className="grid grid-cols-1 gap-1">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Description</TableHead>
            <TableHead>Thumbnail</TableHead>
            <TableHead>Status</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {products?.map((product, index) => {
            return (
              <TableRow key={index}>
                <TableCell>{product.title}</TableCell>
                <TableCell>{product.description}</TableCell>
                <TableCell align="center">
                  <span className="flex flex-row items-center gap-4">
                    <img
                      className="max-h-[86px]"
                      src={product?.thumbnail ?? ''}
                      alt={'similar-brand-product-' + index}
                    />
                  </span>
                </TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <div
                      className={cn(
                        'h-1.5 w-1.5 self-center rounded-full',
                        statusToColor(product.status)
                      )}
                    />
                    <span className="capitalize">{product.status}</span>
                  </div>
                </TableCell>
              </TableRow>
            )
          })}
        </TableBody>
      </Table>
    </div>
  )
}

export default SimilarBrandedProductCard
