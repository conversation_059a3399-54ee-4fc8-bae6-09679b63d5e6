import { Icons } from '@/components/icons'
import { But<PERSON> } from '@/components/ui/button'
import { DialogHeader } from '@/components/ui/dialog'
import { toast } from '@/components/ui/use-toast'
import { bytesToSize, cn, getUrlMediaType } from '@/lib/utils'
import { Dialog, DialogContent, DialogTitle, DialogClose } from '@/components/ui/dialog'
import { FC, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { mutate } from 'swr'
import { Form, FormField, FormItem, FormControl, FormMessage } from '@/components/ui/form'
import { Dropzone } from '@/components/Form/Dropzone'
import { FileWithPath } from 'react-dropzone'
import { Image } from '@unpic/react'
import { Label } from '@/components/ui/label'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { MoreHorizontal, Trash2Icon } from 'lucide-react'
import { ProductCollection, UpdateProductCollectionInput } from '@/types/ProductCollection'
import FileService from '@/network/services/file'
import ProductCollectionService from '@/network/services/product_collection'
import { title } from 'radash'

const UpdateBrandMediaDialog: FC<{
  isDialogOpen: boolean
  setIsDialogOpen: (value: boolean) => void
  brand: ProductCollection
}> = ({ isDialogOpen, setIsDialogOpen, brand }) => {
  const form = useForm<UpdateProductCollectionInput>({
    shouldUseNativeValidation: false,
    defaultValues: {
      images: brand.images?.map((image) => image.url) ?? [],
      image_files: undefined
    }
  })
  const images = form.watch('images') ?? []
  const media = form.watch('image_files') ?? []

  useEffect(() => {
    if (isDialogOpen || brand) {
      form.reset({
        images: brand.images?.map((image) => image.url) ?? [],
        image_files: []
      })
    }
  }, [brand, isDialogOpen])

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      if (values.image_files && (values.image_files?.length ?? 0) > 0) {
        const { data } = await FileService.upload(values.image_files)
        for (const index in data.uploads) {
          const file = data.uploads[index]
          values.images?.push(file.url)
        }
      }
      delete values.image_files

      await ProductCollectionService.updateProductCollection(brand.id, values)
      setIsDialogOpen(false)
      mutate(
        (key) =>
          typeof key === 'string' &&
          key.startsWith(ProductCollectionService.getProductCollection(brand.id))
      )
      toast({
        title: 'Brand media updated',
        variant: 'success'
      })
    } catch (error) {
      console.error(error)
      form.setError('root', {})
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <Dialog
      open={isDialogOpen}
      onOpenChange={(isOpen) => {
        setIsDialogOpen(isOpen)
      }}
    >
      <DialogContent className="max-w-2xl max-h-screen overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex justify-between items-center">
            Edit Media
            <div className="flex space-x-2">
              <DialogClose asChild>
                <Button variant="outline">Cancel</Button>
              </DialogClose>
              <Button
                type="submit"
                form="update-media-form"
                disabled={form.formState.isSubmitting}
                onClick={() => {
                  form.clearErrors()
                }}
              >
                {form.formState.isSubmitting && (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                )}
                Save
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form
            encType="multipart/form-data"
            id="update-media-form"
            onSubmit={onSubmit}
            className={cn('flex flex-col space-y-8')}
          >
            <FormField
              control={form.control}
              name="images"
              // rules={{ required: 'Please insert the image' }}
              render={({ field }) => (
                <>
                  <FormItem>
                    {/* <FormDescription>Replace the thumbnail</FormDescription> */}
                    <FormControl>
                      <Dropzone
                        accept={{ 'image/*': ['.jpeg', '.jpg', '.png'], 'video/*': ['.mp4'] }}
                        description="Drop your videos or images here, or click to browse"
                        multiple={true}
                        onDrop={(acceptedFiles) => {
                          console.log('ondrop')
                          const mediaTmp = media ? [...media] : []

                          for (const index in acceptedFiles) {
                            const file = acceptedFiles[index]
                            const findIndex = media?.findIndex(
                              (f) => (f as FileWithPath)?.path == file.path
                            )

                            if ((findIndex ?? -1) == -1) {
                              const preview = Object.assign(file, {
                                preview: URL.createObjectURL(file)
                              })

                              mediaTmp.push(preview)
                            }
                          }

                          form.setValue('image_files', mediaTmp)
                        }}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>

                  {(images.length > 0 || media.length > 0) && (
                    <div className="flex flex-col space-y-4 mt-2">
                      {images?.map((file, index) => {
                        const mediaType = getUrlMediaType(file)

                        return (
                          <div
                            key={index}
                            className="grid grid-cols-[150px_1fr_36px] space-x-2 items-center"
                          >
                            {mediaType == 'image' && (
                              <Image
                                key={index}
                                src={file ?? ''}
                                height={150}
                                width={150}
                                objectFit="contain"
                                className="rounded-md"
                              />
                            )}
                            {mediaType == 'video' && (
                              <video
                                // controls
                                key={index}
                                src={file ?? ''}
                                height={150}
                                width={150}
                                className="rounded-md"
                              />
                            )}
                            <div className="flex flex-col">
                              <Label className="text-xs font-normal">
                                {title(mediaType)} uploaded
                              </Label>
                            </div>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <span className="sr-only">Open menu</span>
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    const imagesTmp = [...images]
                                    imagesTmp.splice(index, 1)
                                    form.setValue('images', imagesTmp, {
                                      shouldValidate: true
                                    })
                                  }}
                                  className="space-x-2"
                                >
                                  <Trash2Icon size="16" />
                                  <span>Delete</span>
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        )
                      })}
                      {media?.map((file, index) => {
                        return (
                          <div
                            key={index}
                            className="grid grid-cols-[150px_1fr_36px] space-x-2 items-center"
                          >
                            {file?.type.startsWith('image') && (
                              <Image
                                key={index}
                                src={file.preview ?? ''}
                                height={150}
                                width={150}
                                objectFit="contain"
                                className="rounded-md"
                              />
                            )}
                            {file?.type.startsWith('video') && (
                              <video
                                // controls
                                key={index}
                                src={file.preview ?? ''}
                                height={150}
                                width={150}
                                className="rounded-md"
                              />
                            )}
                            <div className="flex flex-col">
                              <Label className="text-xs font-normal">{file?.path}</Label>
                              {file?.size && (
                                <Label className="text-xs font-normal text-gray-500">
                                  {bytesToSize(file.size)}
                                </Label>
                              )}
                            </div>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <span className="sr-only">Open menu</span>
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    const mediaTmp = media ? [...media] : []
                                    mediaTmp.splice(index, 1)
                                    form.setValue('image_files', mediaTmp)
                                  }}
                                  className="space-x-2"
                                >
                                  <Trash2Icon size="16" />
                                  <span>Delete</span>
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        )
                      })}
                    </div>
                  )}
                </>
              )}
            />
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}

export default UpdateBrandMediaDialog
