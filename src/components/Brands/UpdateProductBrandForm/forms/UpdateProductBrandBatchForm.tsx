/* eslint-disable @typescript-eslint/ban-ts-comment */
import { cn } from '@/lib/utils'
import { ProductCategoryBatchProduct } from '@/types/ProductCategory'
import { useForm } from 'react-hook-form'
import { Form, FormField } from '@/components/ui/form'
import {
  DialogHeader,
  Dialog,
  DialogContent,
  DialogTitle,
  DialogClose
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { ProductComboBox } from '@/components/Product/ProductComboBox'
import { Icons } from '@/components/icons'
import { Product } from '@/types/Product'
import { ProductCollection } from '@/types/ProductCollection'

interface UpdateProductBrandBatchFormProps extends React.HTMLAttributes<HTMLDivElement> {
  isDialogOpen: boolean
  setIsDialogOpen: (value: boolean) => void
  brand: ProductCollection
  products: Product[]
}

export function UpdateProductBrandBatchForm({
  isDialogOpen,
  setIsDialogOpen,
  products,
  className
}: UpdateProductBrandBatchFormProps) {
  const form = useForm<ProductCategoryBatchProduct>({
    defaultValues: {
      product_ids: products.map((product) => ({
        id: product.id
      }))
    }
  })

  const onSubmit = form.handleSubmit(async (
    // values
  ) => {
    // try {
    //   const initialValues = products.map((prod) => {
    //     return { id: prod.id }
    //   })
    //   const toSubmit = values.product_ids.map((prod) => {
    //     return { id: prod.id }
    //   })

    //   // find difference from initial and add or delete
    //   const toAdd = diff(toSubmit, initialValues, (item) => item.id)
    //   const toRemove = diff(initialValues, toSubmit, (item) => item.id)

    //   if (!isEmpty(toAdd)) {
    //     await ProductCategoryService.addProductsToProductCategory(category.id, {
    //       product_ids: toAdd
    //     })
    //   }

    //   if (!isEmpty(toRemove)) {
    //     await ProductCategoryService.deleteProductsFromProductCategory(category.id, {
    //       product_ids: toRemove
    //     })
    //   }

    //   mutate(ProductCategoryService.getProductCategory(category.id))
    //   mutate(serialize(ProductService.getProducts, { category_id: [category.id] }))
    //   toast({
    //     title: 'Product(s) updated successfully',
    //     variant: 'success'
    //   })
    //   setIsDialogOpen(false)
    // } catch (error) {
    //   console.error(error)

    //   if (isAxiosError(error) && error.response?.data.type == 'duplicate_error') {
    //     // likely is handle duplicated
    //   } else {
    //     form.setError('root', {})
    //   }

    //   toast({
    //     title: 'Action failed, please try again',
    //     variant: 'destructive'
    //   })
    // }
  })

  return (
    <Dialog
      open={isDialogOpen}
      onOpenChange={(isOpen) => {
        setIsDialogOpen(isOpen)
      }}
    >
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle className="flex justify-between items-center">
            Add products to brand
            <div className="flex space-x-2">
              <DialogClose>
                <Button variant="outline">Cancel</Button>
              </DialogClose>
              <Button
                type="submit"
                form="product-brand-batch-form"
                disabled={form.formState.isSubmitting}
                onClick={() => {
                  form.clearErrors()
                }}
              >
                {form.formState.isSubmitting && (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                )}
                Save
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form
            id="product-brand-batch-form"
            onSubmit={onSubmit}
            className={cn('flex flex-col space-y-8', className)}
          >
            <FormField
              control={form.control}
              name="product_ids"
              render={({ field }) => {
                // @ts-ignore
                return <ProductComboBox products={field.value ?? []} name={field.name} />
              }}
            />
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
