import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import {
  <PERSON>alog,
  DialogClose,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { toast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import ProductCollectionService from '@/network/services/product_collection'
import { ProductCollection, UpdateProductCollectionInput } from '@/types/ProductCollection'
import { isAxiosError } from 'axios'
import isEqual from 'lodash.isequal'
import { useForm } from 'react-hook-form'
import { mutate } from 'swr'
import { BrandComboBox } from '../../BrandComboBox'
import TextEditorComponent from '@/components/Editor/TextEditor'

interface UpdateSimilarBrandedDialogProps extends React.HTMLAttributes<HTMLDivElement> {
  isDialogOpen: boolean
  setIsDialogOpen: (value: boolean) => void
  brand: ProductCollection
}

export function UpdateProductBrandInfoForm({
  isDialogOpen,
  setIsDialogOpen,
  brand,
  className
}: UpdateSimilarBrandedDialogProps) {
  const form = useForm<UpdateProductCollectionInput>({
    defaultValues: {
      title: brand.title,
      rank: brand.rank,
      handle: brand.handle,
      description: brand.description,
      related_collections: brand.related_collections ?? [],
      metadata: brand.metadata ?? { facebook: '', instagram: '', tiktok: '', website: '' }
    }
  })

  const onSubmit = form.handleSubmit(async (values) => {
    console.log('edit brand', values)

    // if (values.related_collections) {
    //   values.related_collections = (
    //     values.related_collections as unknown as ProductCollection[]
    //   ).map((col) => col.id)
    // }

    // block if no changes
    if (
      isEqual(values, {
        title: brand.title,
        handle: brand.handle,
        description: brand.description,
        related_collections: brand.related_collections ?? []
      })
    ) {
      console.log('same')
      toast({
        title: 'No changes applied',
        variant: 'destructive'
      })

      return
    }

    try {
      await ProductCollectionService.updateProductCollection(brand.id, {
        ...values,
        rank: values.rank ? parseInt(values.rank as any) : undefined
      })
      mutate(
        (key) =>
          typeof key === 'string' &&
          key.startsWith(ProductCollectionService.getProductCollection(brand.id))
      )
      toast({
        title: 'Product brand updated',
        variant: 'success'
      })
      setIsDialogOpen(false)
    } catch (error) {
      console.error(error)

      if (isAxiosError(error) && error.response?.data.type == 'duplicate_error') {
        // likely is handle duplicated
        if (error.response.data.message.startsWith('Product_collection with handle')) {
          form.setError('handle', {
            message: error.response.data.message.replace('_', ' ')
          })
        }
      } else {
        form.setError('root', {})
      }

      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <Dialog
      open={isDialogOpen}
      onOpenChange={(isOpen) => {
        setIsDialogOpen(isOpen)
      }}
    >
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle className="flex justify-between items-center">
            Update Product Brand
            <div className="flex space-x-2">
              <DialogClose asChild>
                <Button type="button" variant="outline">
                  Cancel
                </Button>
              </DialogClose>
              <Button
                type="submit"
                form="update-product-brand-form"
                disabled={form.formState.isSubmitting}
                onClick={() => {
                  form.clearErrors()
                }}
              >
                {form.formState.isSubmitting && (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                )}
                Save
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form
            id="update-product-brand-form"
            onSubmit={onSubmit}
            className={cn('flex flex-col space-y-8', className)}
          >
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="title"
                rules={{ required: 'Please enter the name' }}
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Title</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="rank"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Rank</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="handle"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Handle</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="metadata.instagram"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Instagram</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="metadata.facebook"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Facebook</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="metadata.tiktok"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Tiktok</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="metadata.website"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Website</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                // rules={{ required: "Please enter the description" }}
                render={({ field }) => (
                  <FormItem className="flex flex-col col-span-2">
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <TextEditorComponent content={field.value} onChange={field.onChange} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="related_collections"
                // rules={{ required: "Please enter the description" }}
                render={({ field }) => (
                  // @ts-ignore
                  <BrandComboBox productCollections={field.value ?? []} name={field.name} />
                )}
              />
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
