/* eslint-disable @typescript-eslint/ban-ts-comment */
import * as React from 'react'

import { Product } from '@/types/Product'
import { ProductBrandCard } from './cards/ProductBrandCard'
import SimilarBrandedProductCard from './cards/SimilarBrandedProductCard'
import { ProductCollection } from '@/types/ProductCollection'
import BrandThumbnailCard from './cards/BrandThumbnailCard'
import BrandMediaCard from './cards/BrandMediaCard'
import BrandLogoCard from '@/components/Brands/UpdateProductBrandForm/cards/BrandLogoCard'

interface UpdateProductBrandFormProps extends React.HTMLAttributes<HTMLDivElement> {
  initialValue: ProductCollection
  products: Product[]
}

export function UpdateProductBrandForm({ initialValue, products }: UpdateProductBrandFormProps) {
  return (
    <div className="flex flex-col space-y-4">
      <div className="grid lg:grid-cols-[4fr_2fr] gap-4">
        <ProductBrandCard brand={initialValue} />
        <div className="space-y-4">
          <BrandThumbnailCard brand={initialValue} />
          <BrandLogoCard brand={initialValue} />
          <BrandMediaCard brand={initialValue} />
        </div>
      </div>

      <div className="grid grid-cols-1 gap-4">
        <SimilarBrandedProductCard brand={initialValue} products={products} />
      </div>
    </div>
  )
}
