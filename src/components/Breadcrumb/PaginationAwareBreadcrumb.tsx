import { ReactNode, cloneElement, isValidElement, ReactElement } from 'react'
import { useLocation } from 'react-router-dom'
import { Link, LinkProps } from 'react-router-dom'

interface PaginationAwareBreadcrumbProps {
  children: ReactNode
}

/**
 * A component that wraps breadcrumb links and adds pagination state
 * @param children The breadcrumb link to wrap
 * @returns A breadcrumb link with pagination state preserved
 */
// Helper function to get the base path without query parameters
function getBasePath(path: string): string {
  return path.split('?')[0]
}

// Helper function to get pagination state from session storage
function getPaginationState(path: string) {
  const basePath = getBasePath(path)
  const storedState = sessionStorage.getItem(`pagination_${basePath}`)

  if (!storedState) return null

  try {
    return JSON.parse(storedState)
  } catch (e) {
    return null
  }
}

export function PaginationAwareBreadcrumb({ children }: PaginationAwareBreadcrumbProps) {
  // Always call hooks at the top level, before any conditional returns
  const location = useLocation()

  // Process the breadcrumb with pagination
  const processBreadcrumb = () => {
    // If the child is not a valid element, return it as is
    if (!isValidElement(children)) {
      return children
    }

    // If the child doesn't have a Link component as a child, return it as is
    const linkElement = children.props.children as ReactElement
    if (!isValidElement(linkElement) || linkElement.type !== Link) {
      return children
    }

    // Get the target URL from the Link component
    const targetUrl = (linkElement.props as LinkProps).to
    if (typeof targetUrl !== 'string') {
      return children
    }

    // Get pagination parameters from the current URL
    const searchParams = new URLSearchParams(location.search)
    let pageIndex = searchParams.get('page')
    let pageSize = searchParams.get('size')

    // If not in URL, try to get from session storage
    if (!pageIndex || !pageSize) {
      const storedState = getPaginationState(targetUrl)
      if (storedState) {
        pageIndex = pageIndex || storedState.pageIndex
        pageSize = pageSize || storedState.pageSize
      }
    }

    // If there are no pagination parameters, return the original breadcrumb
    if (!pageIndex && !pageSize) {
      return children
    }

    // Create a URL with pagination parameters
    let breadcrumbUrl = targetUrl
    const params = new URLSearchParams()

    if (pageIndex) {
      params.set('page', pageIndex)
    }

    if (pageSize) {
      params.set('size', pageSize)
    }

    // Add parameters to URL if any exist
    if (params.toString()) {
      breadcrumbUrl = `${targetUrl}?${params.toString()}`
    }

    // Create a new Link component with the updated URL
    const newLinkElement = cloneElement(linkElement, {
      to: breadcrumbUrl
    } as Partial<LinkProps>)

    // Create a new breadcrumb link with the updated Link component
    return cloneElement(children, {
      children: newLinkElement
    } as { children: ReactElement })
  }

  // Process the breadcrumb after all hooks have been called
  return processBreadcrumb()
}
