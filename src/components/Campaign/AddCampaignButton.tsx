import CreateCampaignForm from '@/components/Campaign/CreateCampaignForm'
import { Button } from '@/components/ui/button'
import { Form } from '@/components/ui/form'
import {
  Sheet,
  SheetClose,
  Sheet<PERSON>ontent,
  She<PERSON><PERSON>eader,
  Sheet<PERSON><PERSON>le,
  SheetTrigger
} from '@/components/ui/sheet'
import { Campaign } from '@/types/Campaign'
import { CreateCampaignInput } from '@/types/CreateCampaign'
import { CopyPlusIcon, PlusCircleIcon } from 'lucide-react'
import { title } from 'radash'
import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'

interface AddCampaignButtonProps extends React.HTMLAttributes<HTMLDivElement> {
  type: 'deal' | 'freebies'
  eventId?: number
  campaign?: Campaign
}

function AddCampaignButton({ type, eventId, campaign }: AddCampaignButtonProps) {
  const [isSheetOpen, setIsSheetOpen] = useState(false)

  const form = useForm<CreateCampaignInput>({
    shouldUseNativeValidation: false,
    // TODO: need to allow duplicate thumbnail
    defaultValues: campaign
      ? { ...campaign, published: false }
      : {
          title: '',
          description: '',
          abstract: '',
          terms_and_conditions: '',
          event_id: eventId
        }
  })

  useEffect(() => {
    if (form.formState.isSubmitSuccessful) {
      form.reset()
      setIsSheetOpen(false)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [form.formState.isSubmitSuccessful])

  return (
    <div className="flex justify-end">
      <Sheet
        open={isSheetOpen}
        onOpenChange={(isOpen) => {
          setIsSheetOpen(isOpen)
        }}
      >
        <SheetTrigger asChild>
          <Button size="sm">
            {campaign ? (
              <CopyPlusIcon size="16" className="mr-2" />
            ) : (
              <PlusCircleIcon size="16" className="mr-2" />
            )}
            {campaign ? 'Duplicate' : `Create ${title(type)}`}
          </Button>
        </SheetTrigger>
        <SheetContent side="bottom" className="w-full h-full">
          <SheetHeader className="max-w-4xl mx-auto pt-10 pb-6">
            <SheetTitle className="flex justify-between items-center">
              <span>{campaign ? `Duplicate ${campaign.title}` : `New ${title(type)}`}</span>
              <div className="flex space-x-2">
                <SheetClose asChild>
                  <Button
                    variant="outline"
                    onClick={() => {
                      form.reset()
                    }}
                  >
                    Cancel
                  </Button>
                </SheetClose>
                <Button
                  type="submit"
                  form="create-campaign-form"
                  disabled={form.formState.isSubmitting}
                  onClick={() => {
                    form.clearErrors()
                  }}
                >
                  Save
                </Button>
              </div>
            </SheetTitle>
          </SheetHeader>
          <Form {...form}>
            <CreateCampaignForm type={type} setIsDialogOpen={setIsSheetOpen} />
          </Form>
        </SheetContent>
      </Sheet>
    </div>
  )
}

export default AddCampaignButton
