import { ColumnDef, Row } from '@tanstack/react-table'
import { DataTable } from '@/components/Table/DataTable'
import { useNavigate } from 'react-router-dom'
import { IDataResponse } from '@/network/request'
import { DateTime } from 'luxon'
import { CampaignSubmission } from '@/types/CampaignSubmission'
import CampaignSubmissionService from '@/network/services/campaign_submission'

interface CampaignSubmissionTableProps extends React.HTMLAttributes<HTMLDivElement> {
  campaignId: number | string
}

function CampaignSubmissionTable({ campaignId }: CampaignSubmissionTableProps) {
  const nav = useNavigate()
  const columns: ColumnDef<CampaignSubmission>[] = [
    {
      header: 'Email',
      cell: ({
        cell: {
          row: { original }
        }
      }) => original.customer?.email ?? 'N/A'
    },
    {
      cell: ({
        cell: {
          row: { original }
        }
      }) =>
        original.customer
          ? `${original.customer.first_name} ${original.customer.last_name}`
          : 'N/A',
      header: 'Name'
    },

    {
      cell: ({
        cell: {
          row: { original }
        }
      }) => original.customer?.current_stage ?? 'N/A',
      header: 'Stage'
    },
    {
      cell: ({
        cell: {
          row: { original }
        }
      }) => original.customer?.mobile_number ?? 'N/A',
      header: 'Mobile Number'
    },

    {
      accessorKey: 'created_at',
      header: 'Submission Date',
      cell: (props) => DateTime.fromISO(props.getValue<string>()).toFormat('yyyy-MM-dd HH:mm:ss')
    }
  ]

  // const columnFilter: FilterColumn[] = [
  //   { columnKey: 'title', header: 'Title', dataType: 'string' },
  //   {
  //     columnKey: 'created_at',
  //     header: 'Created At',
  //     dataType: 'date'
  //   }
  // ]

  return (
    <DataTable<CampaignSubmission, unknown, IDataResponse<CampaignSubmission>>
      columns={columns}
      // filterColumns={columnFilter}
      swrService={CampaignSubmissionService.getCampaignSubmissions(campaignId)}
      pageParam="page"
      limitParam="limit"
      sortParam="sort"
      sortColumns={['title', 'published', 'rank', 'created_at']}
      toRow={CampaignSubmissionService.toRow}
      toPaginate={CampaignSubmissionService.toPaginate}
      onRowClick={(row: Row<CampaignSubmission>) => {
        nav(`/friends/${row.original.medusa_customer_id}`)
      }}
    />
  )
}

export default CampaignSubmissionTable
