import CampaignService from '@/network/services/campaign'
import { ColumnDef, Row, createColumnHelper } from '@tanstack/react-table'
import { DataTable, FilterColumn } from '@/components/Table/DataTable'
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem
} from '@/components/ui/dropdown-menu'
import { useToast } from '@/components/ui/use-toast'
import { Button } from '@/components/ui/button'
import { Campaign } from '@/types/Campaign'
import { EditIcon, MonitorCheckIcon, MoreHorizontal, Trash2Icon } from 'lucide-react'
import { FC, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { mutate } from 'swr'
import { IDataResponse, serialize } from '@/network/request'
import { CheckIcon, Cross2Icon } from '@radix-ui/react-icons'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger
} from '../ui/alert-dialog'
import ReadOnlyEditorComponent from '../Editor/ReadOnlyEditor'
import UpdateCampaignRankDialog from './UpdateCampaignForm/UpdateCampaignRankDialog'
import { DateTime } from 'luxon'

const RowActions: FC<{ row: Row<Campaign>; type: 'deal' | 'freebies' }> = ({ row, type }) => {
  const campaignId = row.original.id
  const { toast } = useToast()
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  return (
    <div className="w-full flex justify-end">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" onClick={(e) => e.stopPropagation()}>
          <DropdownMenuItem disabled={row.original.published} className="space-x-2">
            <AlertDialog key="publish-campaign">
              <AlertDialogTrigger
                onClick={(e) => e.stopPropagation()}
                className="flex space-x-2 w-full"
              >
                <MonitorCheckIcon size="16" />
                <span>Publish</span>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Publish {row.original.title}</AlertDialogTitle>
                  <AlertDialogDescription className="text-red-500">
                    *NOTE: this action cannot be undone, unable to delete after published
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Close</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={async (event) => {
                      event.stopPropagation()

                      try {
                        await CampaignService.updateCampaign(campaignId, {
                          published: true
                        })
                        mutate(
                          (key) =>
                            typeof key === 'string' && key.startsWith(CampaignService.getCampaigns)
                        )
                        toast({
                          title: 'Campaign published',
                          variant: 'success'
                        })
                      } catch (error) {
                        console.log(error)
                      }
                    }}
                  >
                    Confirm
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </DropdownMenuItem>
          {/* <DropdownMenuItem
            disabled={!row.original.published}
            onClick={async (event) => {
              event.stopPropagation()

              try {
                await CampaignService.updateCampaign(campaignId, {
                  published: false
                })
                mutate(
                  (key) => typeof key === 'string' && key.startsWith(CampaignService.getCampaigns)
                )
                toast({
                  title: 'Campaign set to pending',
                  variant: 'success'
                })
              } catch (error) {
                if (isAxiosError(error)) {
                  console.log(error?.response?.data)
                } else {
                  console.log(error)
                }
              }
            }}
            className="space-x-2"
          >
            <MonitorCheckIcon size="16" />
            <span>Unpublish</span>
          </DropdownMenuItem> */}
          {type === 'deal' && (
            <DropdownMenuItem
              onClick={async (event) => {
                event.stopPropagation()
                setIsDialogOpen(true)
              }}
              className="space-x-2"
            >
              <EditIcon size="16" />
              <span>Edit Rank</span>
            </DropdownMenuItem>
          )}
          <DropdownMenuItem disabled={row.original.published} className="space-x-2">
            <AlertDialog key="delete-campaign">
              <AlertDialogTrigger
                onClick={(e) => e.stopPropagation()}
                className="flex space-x-2 w-full"
              >
                <Trash2Icon size="16" />
                <span>Delete</span>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Delete {row.original.title}</AlertDialogTitle>
                  <AlertDialogDescription className="text-red-500">
                    *NOTE: this action cannot be undone
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Close</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={async (event) => {
                      event.stopPropagation()

                      try {
                        await CampaignService.deleteCampaign(campaignId)
                        mutate(
                          (key) =>
                            typeof key === 'string' && key.startsWith(CampaignService.getCampaigns)
                        )
                        toast({
                          description: 'Campaign deleted',
                          variant: 'destructive'
                        })
                      } catch (error) {
                        console.log(error)
                        toast({
                          description: 'Action failed, please try again',
                          variant: 'destructive'
                        })
                      }
                    }}
                  >
                    Confirm
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {type === 'deal' && (
        <UpdateCampaignRankDialog
          isDialogOpen={isDialogOpen}
          setIsDialogOpen={setIsDialogOpen}
          campaign={row.original}
        />
      )}
    </div>
  )
}

interface CampaignTableProps extends React.HTMLAttributes<HTMLDivElement> {
  type: 'deal' | 'freebies'
  eventId?: number
}

function CampaignTable({ type, eventId }: CampaignTableProps) {
  const nav = useNavigate()

  const columnHelper = createColumnHelper<Campaign>()
  const columns: ColumnDef<Campaign>[] = [
    {
      accessorKey: 'title',
      header: 'Title'
    },
    {
      accessorKey: 'category',
      header: 'Category'
    },
    {
      accessorKey: 'abstract',
      header: 'Description',
      cell: (props) => {
        return (
          <ReadOnlyEditorComponent
            className="max-h-20 max-w-[300px] overflow-hidden"
            content={props.getValue<string>()}
          />
        )
      }
    },
    {
      accessorKey: 'rank',
      header: 'Rank'
    },
    {
      accessorKey: 'published',
      header: 'Published',
      cell: (props) => {
        return props.getValue<boolean>() ? (
          <CheckIcon className="h-4 w-4 text-success" />
        ) : (
          <Cross2Icon className="h-4 w-4 text-destructive" />
        )
      }
    },
    {
      accessorKey: 'created_at',
      header: 'Created At',
      cell: (props) => DateTime.fromISO(props.getValue<string>()).toFormat('yyyy-MM-dd HH:mm:ss')
    },
    columnHelper.display({
      id: 'actions',
      cell: (props) => <RowActions row={props.row} type={type} />
    })
  ]

  const columnFilter: FilterColumn[] = [
    { columnKey: 'title', header: 'Title', dataType: 'string' },
    {
      columnKey: 'created_at',
      header: 'Created At',
      dataType: 'date'
    }
    // {
    //   columnKey: 'published',
    //   header: 'Published',
    //   dataType: 'faceted',
    //   options: [
    //     { value: 'true', label: 'TRUE', icon: undefined },
    //     { value: 'false', label: 'FALSE', icon: undefined }
    //   ]
    // }
  ]

  const typeBasedColumns =
    type == 'freebies'
      ? columns.filter((_, index) => index != 3)
      : [...columns].filter((_, index) => index != 1)

  return (
    <DataTable<Campaign, unknown, IDataResponse<Campaign>>
      columns={typeBasedColumns}
      filterColumns={columnFilter}
      swrService={
        type == 'freebies'
          ? serialize(CampaignService.getCampaigns, {
              type: 'freebies'
            })
          : eventId != null
          ? serialize(CampaignService.getCampaigns, {
              event: eventId
            })
          : serialize(CampaignService.getCampaigns, {
              type: 'deals'
            })
      }
      pageParam="page"
      limitParam="limit"
      sortParam="sort"
      sortColumns={['title', 'published', 'rank', 'created_at']}
      toRow={CampaignService.toRow}
      toPaginate={CampaignService.toPaginate}
      onRowClick={(row: Row<Campaign>) => {
        if (type == 'deal') {
          nav(`campaigns/${row.original.id}`)
        } else {
          nav(`/campaigns/${row.original.id}`)
        }
      }}
    />
  )
}

export default CampaignTable
