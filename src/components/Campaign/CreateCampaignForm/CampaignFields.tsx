import { FormControl, FormField, FormItem, FormLabel } from '@/components/ui/form'
import { useFormContext } from 'react-hook-form'
import { CreateCampaignInput } from '@/types/CreateCampaign'
import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { PlusCircleIcon } from 'lucide-react'
import { AddCampaignFieldDialog } from './AddCampaignFieldDialog'
import { CampaignFieldCard } from './CampaignFieldCard'

const CampaignFields = () => {
  const form = useFormContext<CreateCampaignInput>()

  return (
    <div className="grid grid-cols-2 gap-4">
      <FormField
        control={form.control}
        name="fields"
        // rules={{ required: 'Please enter the fields' }}
        render={() => (
          <FormItem className="flex flex-col col-span-2">
            <FormLabel>Fields</FormLabel>
            <FormControl>
              <CampaignFieldsList />
            </FormControl>
          </FormItem>
        )}
      />
    </div>
  )
}

const CampaignFieldsList = () => {
  const form = useFormContext<CreateCampaignInput>()
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const fields = form.watch('fields')

  return (
    <div className="grid gap-4">
      {/* dialog to add field option */}
      <AddCampaignFieldDialog isDialogOpen={isDialogOpen} setIsDialogOpen={setIsDialogOpen} />
      <Button variant="outline" type="button" onClick={() => setIsDialogOpen(true)}>
        <PlusCircleIcon size="16" className="mr-2" />
        Create new field
      </Button>
      {fields && fields.length > 0 && (
        <div className="grid grid-cols-[2fr_2fr] gap-4">
          {fields.map((field, index) => {
            return <CampaignFieldCard field={field} index={index} />
          })}
        </div>
      )}
    </div>
  )
}

export default CampaignFields
