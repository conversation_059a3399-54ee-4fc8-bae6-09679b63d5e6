import { Input } from '@/components/ui/input'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { useFieldArray, useFormContext } from 'react-hook-form'
import { CreateCampaignInput } from '@/types/CreateCampaign'
import { Dropzone } from '@/components/Form/Dropzone'
import { FileWithPath } from 'react-dropzone'
import { Image } from '@unpic/react'
import EditorComponent from '@/components/Editor/Editor'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import React from 'react'
import { Trash2Icon } from 'lucide-react'
import { Button } from '@/components/ui/button'
import OrSeparator from '@/components/ui/orSeparator'

interface CampaignGeneralInformationProps extends React.HTMLAttributes<HTMLDivElement> {
  type: 'deal' | 'freebies'
  categories: any[]
}

function CampaignGeneralInformation({ type, categories }: CampaignGeneralInformationProps) {
  const form = useFormContext<CreateCampaignInput>()
  const thumbnail = form.watch('thumbnail') as FileWithPath & { preview: string }
  const thumbnailUrl = form.watch('thumbnail_url')

  const {
    fields: userAgreements,
    append: appendAgreement,
    remove: removeAgreement
  } = useFieldArray({ name: 'metadata.user_agreements', control: form.control })

  return (
    <div className="grid gap-4">
      <FormField
        control={form.control}
        name="title"
        rules={{ required: 'Please enter the title' }}
        render={({ field }) => (
          <FormItem className="flex flex-col col-span-2">
            <FormLabel>Title</FormLabel>
            <FormControl>
              <Input {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="slug"
        render={({ field }) => (
          <FormItem className="flex flex-col col-span-2">
            <FormLabel>Slug</FormLabel>
            <FormControl>
              <Input {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* <FormField
        control={form.control}
        name="description"
        rules={{ required: 'Please enter the description' }}
        render={({ field }) => (
          <FormItem className="flex flex-col col-span-2">
            <FormLabel>Description</FormLabel>
            <FormControl>
              <Textarea {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      /> */}

      <FormField
        control={form.control}
        name="abstract"
        rules={{ required: 'Please enter the description' }}
        render={({ field }) => (
          <FormItem className="flex flex-col col-span-2">
            <FormLabel>Description</FormLabel>
            <FormControl>
              <EditorComponent content={field.value} onChange={field.onChange} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* <FormField
        control={form.control}
        name="terms_and_conditions"
        rules={{ required: 'Please enter the terms & conditions' }}
        render={({ field }) => (
          <FormItem className="flex flex-col col-span-2">
            <FormLabel>Terms & Conditions</FormLabel>
            <FormControl>
              <Textarea {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      /> */}

      <FormField
        control={form.control}
        name="thumbnail_url"
        rules={{
          required: thumbnail || thumbnailUrl ? false : 'Please provide the campaign thumbnail url'
        }}
        render={({ field }) => (
          <FormItem className="flex flex-col col-span-2">
            <FormLabel>Thumbnail</FormLabel>
            <FormLabel>Image URL</FormLabel>
            <FormControl>
              <Input placeholder="Image Url" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <div className="col-span-2">
        <OrSeparator />
      </div>

      <FormField
        control={form.control}
        name="thumbnail"
        rules={{
          required: thumbnail || thumbnailUrl ? false : 'Please provide the campaign thumbnail'
        }}
        render={({ field }) => (
          <FormItem className="flex flex-col col-span-2">
            <FormLabel>Upload Image</FormLabel>
            <FormControl>
              <Dropzone
                multiple={false}
                onDrop={(acceptedFiles) => {
                  const preview = Object.assign(acceptedFiles[0], {
                    preview: URL.createObjectURL(acceptedFiles[0])
                  })

                  form.setValue('thumbnail', preview as File, {
                    shouldValidate: true
                  })
                }}
                {...field}
              />
            </FormControl>
            {thumbnail && (
              <Image layout="constrained" width={320} height={320} src={thumbnail.preview} />
            )}
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="gender"
        rules={{ required: 'Please enter the gender' }}
        render={({ field }) => (
          <FormItem className="flex flex-col col-span-2">
            <FormLabel>Gender</FormLabel>
            <FormControl>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Choose an option" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="any">Any</SelectItem>
                  <SelectItem value="male">Male</SelectItem>
                  <SelectItem value="female">Female</SelectItem>
                </SelectContent>
              </Select>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {type == 'freebies' && (
        <FormField
          control={form.control}
          name="category"
          rules={{ required: 'Please enter the category' }}
          render={({ field }) => (
            <FormItem className="flex flex-col col-span-2">
              <FormLabel>Category</FormLabel>
              <FormControl>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose an option" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {categories?.map((value) => {
                      return (
                        <SelectItem key={value.title} value={value.title}>
                          {value.title}
                        </SelectItem>
                      )
                    })}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      {userAgreements.map((item, i) => {
        return (
          <React.Fragment key={item.id}>
            <FormField
              control={form.control}
              name={`metadata.user_agreements.${i}`}
              rules={{ required: 'This is required' }}
              render={({ field }) => {
                return (
                  <FormItem className="flex flex-col col-span-2">
                    <FormLabel className="flex gap-2">
                      <span>User Agreement {i + 1}</span>
                      {userAgreements.length > 1 && (
                        <Trash2Icon size={16} color="red" onClick={() => removeAgreement(i)} />
                      )}
                    </FormLabel>
                    <EditorComponent content={field.value} onChange={field.onChange} />
                    <FormMessage />
                  </FormItem>
                )
              }}
            />
          </React.Fragment>
        )
      })}
      <FormItem className="flex flex-col col-span-2">
        <Button type="button" onClick={() => appendAgreement('')}>
          Add User Agreement
        </Button>
      </FormItem>
    </div>
  )
}

export default CampaignGeneralInformation
