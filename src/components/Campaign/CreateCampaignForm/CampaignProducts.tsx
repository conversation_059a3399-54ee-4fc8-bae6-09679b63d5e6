import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { useFormContext } from 'react-hook-form'
import { CreateCampaignInput } from '@/types/CreateCampaign'
import { DataTable, FilterColumn } from '@/components/Table/DataTable'
import { IDataResponse } from '@/network/request'
import { ColumnDef, Row } from '@tanstack/react-table'
import IndeterminateCheckbox from '@/components/Table/IndeterminateCheckbox'
import { Image } from '@unpic/react'
import CampaignService from '@/network/services/campaign'
import { Dispatch, SetStateAction, useEffect } from 'react'
import { Product } from '@/types/Product'

type CampaignProductsProps = {
  productType: 'event_products' | 'shopify_products'
  setProductType: Dispatch<SetStateAction<'event_products' | 'shopify_products'>>
}

const CampaignProducts = ({ productType }: CampaignProductsProps) => {
  const form = useFormContext<CreateCampaignInput>()
  const eventId = form.watch('event_id')

  useEffect(() => {
    if (!eventId) {
      form.unregister('products')
      form.unregister('shopify_products')
    }
  }, [eventId])

  return (
    <div>
      {/* <FormField
        control={form.control}
        name="event_id"
        render={() => (
          <FormItem className="flex flex-col col-span-2">
            <FormLabel>Event</FormLabel>
            <FormDescription>The event can only be assigned on campaign create.</FormDescription>
            <FormControl>
              <EventOptionList />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      /> */}

      {eventId && (
        <FormItem>
          <FormLabel>Products</FormLabel>
          {/* <div className="flex flex-row space-x-4">
          <span
            className="flex flex-row space-x-1"
            onClick={() => {
              setProductType('event_products')
            }}
          >
            <IndeterminateCheckbox readOnly checked={productType == 'event_products'} />
            <FormLabel>Event Products</FormLabel>
          </span>
          <span
            className="flex flex-row space-x-1"
            onClick={() => {
              setProductType('shopify_products')
            }}
          >
            <IndeterminateCheckbox readOnly checked={productType == 'shopify_products'} />
            <FormLabel>Shopify Products</FormLabel>
          </span>
        </div> */}

          {eventId && productType == 'event_products' && (
            <FormField
              control={form.control}
              name={'products'}
              render={() => (
                <FormItem className="flex flex-col col-span-2">
                  <FormControl>
                    <EventProductOptionList />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          {eventId && productType == 'shopify_products' && (
            <FormField
              control={form.control}
              name={'shopify_products'}
              render={() => (
                <FormItem className="flex flex-col col-span-2">
                  <FormControl>
                    <ShopifyProductOptionList />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
        </FormItem>
      )}
    </div>
  )
}

// const EventOptionList = () => {
//   const form = useFormContext<CreateCampaignInput>()

//   const columns: ColumnDef<Event>[] = [
//     {
//       id: 'select',
//       cell: ({ row, table }) => (
//         <div className="px-1">
//           <IndeterminateCheckbox
//             {...{
//               checked: row.getIsSelected(),
//               disabled: !row.getCanSelect(),
//               indeterminate: row.getIsSomeSelected(),
//               onChange: () => {
//                 // Reset all selection 1st to allow 1 selected only
//                 table.resetRowSelection()
//                 row.toggleSelected()
//               }
//             }}
//           />
//         </div>
//       )
//     },
//     {
//       accessorKey: 'title',
//       header: 'Title'
//     },
//     {
//       accessorKey: 'location',
//       header: 'Location'
//     },
//     {
//       accessorKey: 'from',
//       header: 'From',
//       cell: (props) => <span>{props.getValue<DateTime>().toFormat('yyyy-MM-dd')}</span>
//     },
//     {
//       accessorKey: 'to',
//       header: 'To',
//       cell: (props) => <span>{props.getValue<DateTime>().toFormat('yyyy-MM-dd')}</span>
//     }
//     // TODO: dialog to edit row
//     // columnHelper.display({
//     //   id: "actions",
//     //   cell: (props) => <RowActions row={props.row} />,
//     // }),
//   ]

//   const filterColumns: FilterColumn[] = [
//     { columnKey: 'title', header: 'Title', dataType: 'string' }
//   ]

//   return (
//     <div className="grid gap-4">
//       {/* Selectable table for all options */}
//       <DataTable<Event, unknown, IDataResponse<Event>>
//         columns={columns}
//         filterColumns={filterColumns}
//         swrService={EventService.getEvents}
//         toRow={EventService.toRow as any}
//         toPaginate={EventService.toPaginate}
//         setSelectedRows={(selectedRows) => {
//           form.setValue('event_id', selectedRows[0])
//         }}
//         onRowClick={(row: Row<Event>, table: Table<Event>) => {
//           // Reset all selection 1st to allow 1 selected only
//           table.resetRowSelection()
//           row.toggleSelected()
//         }}
//       />
//     </div>
//   )
// }

const EventProductOptionList = () => {
  const form = useFormContext<CreateCampaignInput>()
  const selectedEventId = form.watch('event_id')

  const columns: ColumnDef<Product>[] = [
    {
      id: 'select',
      header: ({ table }) => (
        <IndeterminateCheckbox
          {...{
            checked: table.getIsAllRowsSelected(),
            indeterminate: table.getIsSomeRowsSelected(),
            onChange: table.getToggleAllPageRowsSelectedHandler()
          }}
        />
      ),
      cell: ({ row }) => (
        <div className="px-1">
          <IndeterminateCheckbox
            {...{
              checked: row.getIsSelected(),
              disabled: !row.getCanSelect(),
              indeterminate: row.getIsSomeSelected(),
              onChange: () => {
                row.toggleSelected()
              }
            }}
          />
        </div>
      )
    },
    {
      accessorKey: 'title',
      header: 'Product',
      cell: (props) => {
        // add product title to cache
        props.row._valuesCache['title'] = props.row.original.title
        props.row._valuesCache['thumbnail'] = props.row.original.thumbnail
        return (
          <span className="flex flex-row items-center gap-4">
            {(props.row.getValue('thumbnail') as string) && (
              <Image
                layout="constrained"
                width={50}
                height={50}
                src={props.row.getValue('thumbnail')}
              />
            )}
            {props.row.getValue('title')}
          </span>
        )
      }
    },
    {
      accessorKey: 'store.name',
      header: 'Store'
    },
    {
      accessorKey: 'product_collections.title',
      header: 'Brands'
    }
    // TODO: dialog to edit row
    // columnHelper.display({
    //   id: "actions",
    //   cell: (props) => <RowActions row={props.row} />,
    // }),
  ]

  const filterColumns: FilterColumn[] = [
    { columnKey: 'title', header: 'Product', dataType: 'string' },
    { columnKey: 'store_name', header: 'Store', dataType: 'string' },
    { columnKey: 'product_collections_title', header: 'Brands', dataType: 'string' }
  ]

  return (
    <div className="grid gap-4">
      {/* Selectable table for all options */}
      {selectedEventId ? (
        <DataTable<Product, unknown, IDataResponse<Product>>
          columns={columns}
          filterColumns={filterColumns}
          swrService={CampaignService.getCampaignEventProductOptions(selectedEventId)}
          toRow={CampaignService.toRow as any}
          toPaginate={CampaignService.toPaginate as any}
          setSelectedRows={(selectedRows) => {
            form.setValue(
              'products',
              selectedRows.length > 0 ? (selectedRows as number[]) : undefined
            )
          }}
          onRowClick={(row: Row<Product>) => {
            row.toggleSelected()
          }}
        />
      ) : (
        <div className="space-y-4">
          <div className="rounded-md border">
            <span>Choose an event</span>
          </div>
        </div>
      )}
    </div>
  )
}

const ShopifyProductOptionList = () => {
  const form = useFormContext<CreateCampaignInput>()
  const selectedEventId = form.watch('event_id')
  // const initialSelected = form.getValues('shopify_products')

  // const columns: ColumnDef<EventProduct>[] = [
  //   {
  //     id: 'select',
  //     header: ({ table }) => (
  //       <IndeterminateCheckbox
  //         {...{
  //           checked: table.getIsAllRowsSelected(),
  //           indeterminate: table.getIsSomeRowsSelected(),
  //           onChange: table.getToggleAllPageRowsSelectedHandler()
  //         }}
  //       />
  //     ),
  //     cell: ({ row }) => (
  //       <div className="px-1">
  //         <IndeterminateCheckbox
  //           {...{
  //             checked: row.getIsSelected(),
  //             disabled: !row.getCanSelect(),
  //             indeterminate: row.getIsSomeSelected(),
  //             onChange: () => {
  //               row.toggleSelected()
  //             }
  //           }}
  //         />
  //       </div>
  //     )
  //   },
  //   {
  //     accessorKey: 'product.title',
  //     header: 'Product',
  //     cell: (props) => {
  //       // add product title to cache
  //       props.row._valuesCache['product_title'] = props.row.original.product?.title
  //       props.row._valuesCache['product_thumbnail'] = props.row.original.product?.thumbnail
  //       return (
  //         <span className="flex flex-row items-center gap-4">
  //           <Image
  //             layout="constrained"
  //             width={50}
  //             height={50}
  //             src={props.row.getValue('product_thumbnail')}
  //           />
  //           {props.row.getValue('product_title')}
  //         </span>
  //       )
  //     }
  //   },
  //   {
  //     accessorKey: 'store.name',
  //     header: 'Store'
  //   },
  //   {
  //     accessorKey: 'store.brands',
  //     header: 'Brands'
  //   }
  //   // TODO: dialog to edit row
  //   // columnHelper.display({
  //   //   id: "actions",
  //   //   cell: (props) => <RowActions row={props.row} />,
  //   // }),
  // ]

  // const filterColumns: FilterColumn[] = [
  //   { columnKey: 'product_title', header: 'Product', dataType: 'string' },
  //   { columnKey: 'store_name', header: 'Store', dataType: 'string' },
  //   { columnKey: 'store_brands', header: 'Brands', dataType: 'string' }
  // ]

  return (
    <div className="grid gap-4">
      {/* Selectable table for all options */}
      {selectedEventId ? (
        // <DataTable<EventProduct, unknown, IDataResponse<EventProduct>>
        //   columns={columns}
        //   initialSelected={initialSelected}
        //   filterColumns={filterColumns}
        //   // TODO: shopify products API
        //   swrService={CampaignService.getCampaignEventProductOptions(selectedEventId)}
        //   toRow={CampaignService.toRow as any}
        //   toPaginate={CampaignService.toPaginate as any}
        //   setSelectedRows={(selectedRows) => {
        //     form.setValue('shopify_products', selectedRows.length > 0 ? selectedRows : undefined)
        //   }}
        //   onRowClick={(row: Row<EventProduct>) => {
        //     row.toggleSelected()
        //   }}
        // />
        <>Not implemented</>
      ) : (
        <div className="space-y-4">
          <div className="rounded-md border">
            <span>Choose an event</span>
          </div>
        </div>
      )}
    </div>
  )
}

export default CampaignProducts
