import EditorComponent from '@/components/Editor/Editor'
import TextEditorComponent from '@/components/Editor/TextEditor'
import { Dropzone } from '@/components/Form/Dropzone'
import { Button } from '@/components/ui/button'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { CreateCampaignInput } from '@/types/CreateCampaign'
import { Image } from '@unpic/react'
import { PlusCircleIcon, TrashIcon } from 'lucide-react'
import React from 'react'
import { FileWithPath } from 'react-dropzone'
import { useFieldArray, useFormContext } from 'react-hook-form'

const CampaignTabs = () => {
  const form = useFormContext<CreateCampaignInput>()
  const { fields, append, remove } = useFieldArray({ control: form.control, name: 'redemption' })

  return (
    <div className="grid grid-cols-2 gap-8">
      <FormField
        control={form.control}
        name="rewards"
        // rules={{ required: 'Please enter the fields' }}
        render={({ field }) => (
          <FormItem className="flex flex-col col-span-2">
            <FormLabel>Rewards</FormLabel>
            <FormControl>
              <EditorComponent content={field.value} onChange={field.onChange} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="description"
        // rules={{ required: 'Please enter the fields' }}
        render={({ field }) => (
          <FormItem className="flex flex-col col-span-2">
            <FormLabel>Long Description</FormLabel>
            <FormControl>
              <EditorComponent content={field.value} onChange={field.onChange} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="application"
        // rules={{ required: 'Please enter the fields' }}
        render={({ field }) => (
          <FormItem className="flex flex-col col-span-2">
            <FormLabel>Application</FormLabel>
            <FormControl>
              <EditorComponent content={field.value} onChange={field.onChange} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="mission"
        // rules={{ required: 'Please enter the fields' }}
        render={({ field }) => (
          <FormItem className="flex flex-col col-span-2">
            <FormLabel>Mission</FormLabel>
            <FormControl>
              <EditorComponent content={field.value} onChange={field.onChange} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="redemption"
        // rules={{ required: 'Please enter the fields' }}
        render={() => (
          <FormItem className="flex flex-col col-span-2">
            <FormLabel>Redemption</FormLabel>
            {fields.map((item, i) => (
              <React.Fragment key={item.id}>
                {i > 0 && <br />}
                <FormLabel>
                  <span className="flex gap-x-2">
                    Step {i + 1} <TrashIcon size={16} color="red" onClick={() => remove(i)} />
                  </span>
                </FormLabel>
                <FormField
                  control={form.control}
                  name={`redemption.${i}.description`}
                  //   rules={{ required: 'Please enter the terms & conditions' }}
                  render={({ field }) => (
                    <FormItem className="flex flex-col col-span-2">
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <TextEditorComponent content={field.value} onChange={field.onChange} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name={`redemption.${i}.image`}
                  //   rules={{ required: 'Please enter the terms & conditions' }}
                  render={({ field }) => {
                    const thumbnail = form.watch(`redemption.${i}.image`) as FileWithPath & {
                      preview: string
                    }
                    return (
                      <FormItem className="flex flex-col col-span-2">
                        <FormLabel>Image</FormLabel>
                        <FormControl>
                          <Dropzone
                            multiple={false}
                            onDrop={(acceptedFiles) => {
                              const preview = Object.assign(acceptedFiles[0], {
                                preview: URL.createObjectURL(acceptedFiles[0])
                              })

                              form.setValue(`redemption.${i}.image`, preview as File, {
                                shouldValidate: true
                              })
                            }}
                            {...field}
                          />
                        </FormControl>
                        {thumbnail && (
                          <Image
                            layout="constrained"
                            width={320}
                            height={320}
                            src={thumbnail.preview}
                          />
                        )}
                        <FormMessage />
                      </FormItem>
                    )
                  }}
                />
              </React.Fragment>
            ))}
            <Button
              asChild
              variant="outline"
              type="button"
              onClick={() => append({ description: '', image: undefined })}
            >
              <div>
                <PlusCircleIcon size="16" className="mr-2" />
                Add a new step
              </div>
            </Button>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="terms_and_conditions"
        // rules={{ required: 'Please enter the terms & conditions' }}
        render={({ field }) => (
          <FormItem className="flex flex-col col-span-2">
            <FormLabel>Terms & Conditions</FormLabel>
            <FormControl>
              <EditorComponent content={field.value} onChange={field.onChange} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  )
}

export default CampaignTabs
