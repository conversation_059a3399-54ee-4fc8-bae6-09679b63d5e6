import { FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { useFormContext } from 'react-hook-form'
import { CreateCampaignInput } from '@/types/CreateCampaign'
import { Image } from '@unpic/react'
import { Dropzone } from '@/components/Form/Dropzone'
import { FileWithPath } from 'react-dropzone'
import { Label } from '@/components/ui/label'
import { bytesToSize } from '@/lib/utils'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import { MoreHorizontal, Trash2Icon } from 'lucide-react'
import { title } from 'radash'

type MediaProps = {}

const Media = ({}: MediaProps) => {
  const form = useFormContext<CreateCampaignInput>()
  const gallery = form.watch('gallery')
  const media = form.watch('media') as ((FileWithPath & { preview: string }) | undefined)[]

  return (
    <div>
      <FormField
        control={form.control}
        name="gallery"
        // rules={{ required: 'Please insert the image' }}
        render={({ field }) => (
          <>
            <FormItem>
              <FormControl>
                <Dropzone
                  accept={{ 'image/*': ['.jpeg', '.jpg', '.png'], 'video/*': ['.mp4'] }}
                  description="Drop your videos or images here, or click to browse"
                  multiple={true}
                  onDrop={(acceptedFiles) => {
                    console.log('ondrop')
                    const galleryTmp = gallery ? [...gallery] : []
                    const mediaTmp = media ? [...media] : []

                    // remove existing already uploaded file
                    const acceptedFiles2 = acceptedFiles.filter((acceptedFile) =>
                      galleryTmp?.every((file) => file.name != acceptedFile.name)
                    )
                    for (const index in acceptedFiles2) {
                      const file = acceptedFiles2[index]
                      const findIndex = media?.findIndex(
                        (f) => (f as FileWithPath)?.path == file.path
                      )

                      if ((findIndex ?? -1) == -1) {
                        const preview = Object.assign(file, {
                          preview: URL.createObjectURL(file)
                        })

                        mediaTmp[galleryTmp.length] = preview
                        galleryTmp.push({
                          name: preview.name,
                          type: preview.type.split('/')[0] as 'video' | 'image'
                        })
                      }
                    }

                    form.setValue('gallery', galleryTmp, {
                      shouldValidate: true
                    })
                    form.setValue('media', mediaTmp as File[])
                  }}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
            {(gallery ?? []).length > 0 && (
              <div className="space-y-4 mt-2">
                {gallery?.map((file, index) => {
                  return (
                    <div
                      key={index}
                      className="grid grid-cols-[150px_1fr_36px] space-x-2 items-center"
                    >
                      {file.type.startsWith('image') && (
                        <Image
                          key={index}
                          src={file.url ?? media[index]?.preview ?? ''}
                          height={150}
                          width={150}
                          objectFit="contain"
                          className="rounded-md"
                        />
                      )}
                      {file.type.startsWith('video') && (
                        <video
                          // controls
                          key={index}
                          src={file.url ?? media[index]?.preview ?? ''}
                          height={150}
                          width={150}
                          className="rounded-md"
                        />
                      )}
                      <div className="flex flex-col">
                        {file.url ? (
                          <Label className="text-xs font-normal">{title(file.type)} uploaded</Label>
                        ) : (
                          <>
                            <Label className="text-xs font-normal">{media[index]!.path}</Label>
                            <Label className="text-xs font-normal text-gray-500">
                              {bytesToSize(media[index]!.size)}
                            </Label>
                          </>
                        )}
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation()
                              const galleryTmp = [...gallery]
                              const mediaTmp = media ? [...media] : []

                              if (!file.url) {
                                mediaTmp.splice(index, 1)
                              }

                              galleryTmp?.splice(index, 1)
                              form.setValue('media', mediaTmp as File[])
                              form.setValue('gallery', galleryTmp, {
                                shouldValidate: true
                              })
                            }}
                            className="space-x-2"
                          >
                            <Trash2Icon size="16" />
                            <span>Delete</span>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  )
                })}
              </div>
            )}
          </>
        )}
      />
    </div>
  )
}

export default Media
