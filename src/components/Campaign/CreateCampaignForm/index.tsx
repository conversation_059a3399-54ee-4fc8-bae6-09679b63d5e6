import * as React from 'react'
import { useFormContext } from 'react-hook-form'

import { cn, convertValuesToFormData } from '@/lib/utils'
import { CreateCampaignInput } from '@/types/CreateCampaign'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from '@/components/ui/accordion'
import useSWR, { useSWRConfig } from 'swr'
import { toast } from '@/components/ui/use-toast'
import CampaignService from '@/network/services/campaign'
import CampaignGeneralInformation from './CampaignGeneralInformation'
import CampaignFields from './CampaignFields'
import CampaignProducts from './CampaignProducts'
import Media from './Media'
import CampaignTabs from './CampaignTabs'
import FileService from '@/network/services/file'
import { title } from 'radash'
import SettingService from '@/network/services/setting'
import Settings, { Freebies } from '@/types/Setting'

interface CampaignFormProps extends React.HTMLAttributes<HTMLDivElement> {
  type: 'freebies' | 'deal'
  setIsDialogOpen: (value: boolean) => void
}

function CreateCampaignForm({ className, setIsDialogOpen, type }: CampaignFormProps) {
  const { mutate } = useSWRConfig()
  const form = useFormContext<CreateCampaignInput>()
  const [productType, setProductType] = React.useState<'event_products' | 'shopify_products'>(
    'event_products'
  )

  const { data, error, isLoading } = useSWR(SettingService.getSetting('freebies'))

  if ((!data || error || isLoading) && type === 'freebies') {
    return <></>
  }

  const categories = ((data?.data as Settings)?.value as Freebies)?.categories ?? undefined

  const onSubmit = form.handleSubmit(async (values) => {
    if (productType == 'event_products') {
      delete values.shopify_products
    } else {
      delete values.products
    }

    // remove whichever thumbnail not using
    if (values.thumbnail_url) {
      delete values.thumbnail
    } else if (values.thumbnail) {
      delete values.thumbnail_url
    }

    if (values.redemption && values.redemption.length > 0) {
      for (const index in values.redemption) {
        const image = values.redemption[index].image!
        const formData = new FormData()
        formData.set('file', image)
        const { data } = await FileService.uploadImage(formData)
        values.redemption[index].image_url = data.data.url

        delete values.redemption[index].image
      }
    }

    console.log('event submit', values)
    const tempBody = values

    const formData = new FormData()
    convertValuesToFormData(formData, tempBody)

    try {
      // post api
      const { data: submitResponse } = await CampaignService.createCampaign(formData)
      console.log('response', submitResponse)

      if (submitResponse.success) {
        // refresh event list
        mutate((key) => typeof key === 'string' && key.startsWith(CampaignService.getCampaigns))

        // Close dialog & reset
        setIsDialogOpen(false)
        form.reset()
        toast({
          title: 'Campaign created',
          variant: 'success'
        })

        return
      }

      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    } catch (error) {
      console.error(error)

      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <form
      id="create-campaign-form"
      onSubmit={onSubmit}
      className={cn('flex flex-col space-y-8 max-w-4xl mx-auto', className)}
    >
      <Accordion type="multiple" className="w-full" defaultValue={['campaign-general-information']}>
        <AccordionItem value="campaign-general-information">
          <AccordionTrigger disabled>{title(type)} General Information*</AccordionTrigger>
          <AccordionContent>
            <CampaignGeneralInformation {...{ type, categories }} />
          </AccordionContent>
        </AccordionItem>

        {type == 'deal' && (
          <AccordionItem value="campaign-products">
            <AccordionTrigger>Deal Products</AccordionTrigger>
            <AccordionContent>
              <CampaignProducts productType={productType} setProductType={setProductType} />
            </AccordionContent>
          </AccordionItem>
        )}

        <AccordionItem value="campaign-form">
          <AccordionTrigger>
            <div className="flex">
              <p className="mr-2">{title(type)} Form</p>
            </div>
          </AccordionTrigger>
          <AccordionContent>
            <CampaignFields />
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="campaign-tabs">
          <AccordionTrigger>
            <div className="flex">
              <p className="mr-2">{title(type)} Tabs</p>
            </div>
          </AccordionTrigger>
          <AccordionContent>
            <CampaignTabs />
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="campaign-media">
          <AccordionTrigger>
            <div className="flex">
              <p className="mr-2">Media</p>
            </div>
          </AccordionTrigger>
          <AccordionContent>
            <Media />
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </form>
  )
}

export default CreateCampaignForm
