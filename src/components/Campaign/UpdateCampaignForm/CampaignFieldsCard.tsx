import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  CardFooter,
  CardContentLabel,
  CardContentTitle
} from '@/components/ui/card'
import { DialogHeader } from '@/components/ui/dialog'
import { toast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import CampaignService from '@/network/services/campaign'
import { CampaignFormField } from '@/types/Campaign'
import { UpdateCampaignFieldInput } from '@/types/CreateCampaign'
import { Dialog, DialogContent, DialogTitle, DialogClose } from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem
} from '@/components/ui/dropdown-menu'
import { EditIcon, MoreHorizontal, Trash2Icon } from 'lucide-react'
import { FC, useState } from 'react'
import { useForm } from 'react-hook-form'
import { mutate } from 'swr'
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import { capitalize } from 'radash'
import { Switch } from '@/components/ui/switch'
import { CheckIcon, Cross2Icon } from '@radix-ui/react-icons'
import { Separator } from '@/components/ui/separator'
import { CampaignOptionsInput } from './CampaignOptionsInput'
import { CampaignFileExtensionsInput } from './CampaignFileExtensionsInput'

const CampaignFieldProperties: FC<{ field: CampaignFormField }> = ({ field }) => {
  if (field.type == 'text') {
    return (
      <>
        <Separator className="col-span-2 my-2" />
        <div className="col-span-2 text-sm font-semibold">Properties</div>
        <CardContentTitle>TextArea</CardContentTitle>
        <CardContentLabel>
          {field.props.text_props?.textarea == true ? (
            <CheckIcon className="h-4 w-4 inline-block text-success" />
          ) : (
            <Cross2Icon className="h-4 w-4 inline-block text-destructive" />
          )}
        </CardContentLabel>
        {field.props.text_props?.textarea == true && (
          <>
            <CardContentTitle>Minimum length</CardContentTitle>
            <CardContentLabel>{field.props.text_props.min}</CardContentLabel>
          </>
        )}
        {field.props.text_props?.textarea == true && (
          <>
            <CardContentTitle>Maximumu length</CardContentTitle>
            <CardContentLabel>{field.props.text_props.max}</CardContentLabel>
          </>
        )}
      </>
    )
  }

  if (field.type == 'number') {
    return (
      <>
        <Separator className="col-span-2 my-2" />
        <div className="col-span-2 text-sm font-semibold">Properties</div>
        <CardContentTitle>Minimum</CardContentTitle>
        <CardContentLabel>{field.props.number_props?.min}</CardContentLabel>
        <CardContentTitle>Maximum</CardContentTitle>
        <CardContentLabel>{field.props.number_props?.max}</CardContentLabel>
      </>
    )
  }

  if (field.type == 'file') {
    return (
      <>
        <Separator className="col-span-2 my-2" />
        <div className="col-span-2 text-sm font-semibold">Properties</div>
        <CardContentTitle>Extensions</CardContentTitle>
        <CardContentLabel>{field.props.file_props?.extensions?.join(', ')}</CardContentLabel>
        <CardContentTitle>Size limit</CardContentTitle>
        <CardContentLabel>{field.props.file_props?.size_limit}</CardContentLabel>
      </>
    )
  }

  if (field.type == 'options') {
    return (
      <>
        <Separator className="col-span-2 my-2" />
        <div className="col-span-2 text-sm font-semibold">Properties</div>
        <CardContentTitle>Options</CardContentTitle>
        <CardContentLabel>{field.props.option_props?.options?.join(', ')}</CardContentLabel>
      </>
    )
  }

  return <></>
  // if (field.type == 'email') return <></>
  // if (field.type == 'datetime') return <></>
  // if (field.type == 'checkbox') return <></>
}

export const CampaignFieldsCard: FC<{ field: CampaignFormField }> = ({ field }) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const form = useForm<UpdateCampaignFieldInput>({
    shouldUseNativeValidation: false,
    defaultValues: {
      type: field.type,
      name: field.name,
      form_index: field.form_index,
      required: field.required,
      props: field.props
    }
  })

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      console.log(values, field)
      const { data: response } = await CampaignService.updateCampaignField(
        field.campaign_id,
        field.id,
        values
      )
      const updateField = response.data
      form.reset({
        type: updateField.type,
        name: updateField.name,
        form_index: updateField.form_index,
        required: updateField.required,
        props: updateField.props
      })

      setIsDialogOpen(false)
      mutate(
        (key) =>
          typeof key === 'string' && key.startsWith(CampaignService.getCampaign(field.campaign_id))
      )
      toast({
        title: 'Campaign field updated',
        variant: 'success'
      })
    } catch (error) {
      console.error(error)
      form.setError('root', {})
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <CardTitle>{field.name}</CardTitle>
          <div className="flex flex-row space-x-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" forceMount>
                <DropdownMenuGroup>
                  <DropdownMenuItem
                    onClick={async (event) => {
                      event.stopPropagation()
                      setIsDialogOpen(true)
                    }}
                    className="space-x-2"
                  >
                    <EditIcon size="16" />
                    <span>Edit</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={async (event) => {
                      event.stopPropagation()

                      try {
                        await CampaignService.deleteCampaignField(field.campaign_id, field.id)
                        mutate(
                          (key) =>
                            typeof key === 'string' && key.startsWith(CampaignService.getCampaigns)
                        )
                        toast({
                          description: 'Campaign field deleted',
                          variant: 'destructive'
                        })
                      } catch (error) {
                        console.log(error)
                      }
                    }}
                    className="space-x-2"
                  >
                    <Trash2Icon size="16" />
                    <span>Delete</span>
                  </DropdownMenuItem>
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-2">
            <CardContentTitle>Type</CardContentTitle>
            <CardContentLabel>{field.type}</CardContentLabel>
            <CardContentTitle>Order</CardContentTitle>
            <CardContentLabel>{field.form_index}</CardContentLabel>
            <CardContentTitle>Required</CardContentTitle>
            <CardContentLabel>{field.required ? 'Required' : 'Optional'}</CardContentLabel>
            <CampaignFieldProperties field={field} />
          </div>
        </CardContent>
        <CardFooter></CardFooter>
      </Card>

      <Dialog
        open={isDialogOpen}
        onOpenChange={(isOpen) => {
          setIsDialogOpen(isOpen)
        }}
      >
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle className="flex justify-between items-center">
              Edit Field
              <div className="flex space-x-2">
                <DialogClose>
                  <Button variant="outline">Cancel</Button>
                </DialogClose>
                <Button
                  type="submit"
                  form={`update-campaign-field-form-${field.id}`}
                  disabled={form.formState.isSubmitting}
                  onClick={() => {
                    form.clearErrors()
                  }}
                >
                  {form.formState.isSubmitting && (
                    <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  Save
                </Button>
              </div>
            </DialogTitle>
          </DialogHeader>
          <Form {...form}>
            <form
              id={`update-campaign-field-form-${field.id}`}
              onSubmit={onSubmit}
              className={cn('flex flex-col space-y-8')}
            >
              <FormField
                control={form.control}
                name="name"
                rules={{ required: 'Please enter the name' }}
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="type"
                rules={{ required: 'Please enter the type' }}
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Type</FormLabel>
                    <FormControl>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Choose an option" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {[
                            'email',
                            'text',
                            'number',
                            'datetime',
                            'file',
                            'options',
                            'checkbox'
                          ].map((value) => {
                            return (
                              <SelectItem key={value} value={value}>
                                {capitalize(value)}
                              </SelectItem>
                            )
                          })}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="required"
                // rules={{ required: 'Please select' }}
                render={({ field }) => (
                  <FormItem className="flex items-center space-y-0 space-x-2">
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        aria-readonly
                      />
                    </FormControl>
                    <FormLabel>{field.value ? 'Required' : 'Optional'}</FormLabel>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {form.watch('type') == 'text' && (
                <>
                  <Separator className="col-span-2 my-2" />
                  <div className="col-span-2 text-sm font-semibold">Properties</div>
                  <FormField
                    control={form.control}
                    name="props.text_props.textarea"
                    render={({ field }) => (
                      <FormItem className="flex items-center space-y-0 space-x-2">
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            aria-readonly
                          />
                        </FormControl>
                        <FormLabel>{field.value ? 'TextArea' : 'Text'}</FormLabel>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {form.watch('props.text_props.textarea') == true && (
                    <>
                      <FormField
                        control={form.control}
                        name="props.text_props.min"
                        render={({ field }) => (
                          <FormItem className="flex flex-col">
                            <FormLabel>Min</FormLabel>
                            <FormControl>
                              <Input type="number" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="props.text_props.max"
                        render={({ field }) => (
                          <FormItem className="flex flex-col">
                            <FormLabel>Max</FormLabel>
                            <FormControl>
                              <Input type="number" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </>
                  )}
                </>
              )}

              {form.watch('type') == 'number' && (
                <>
                  <Separator className="col-span-2 my-2" />
                  <div className="col-span-2 text-sm font-semibold">Properties</div>

                  <FormField
                    control={form.control}
                    name="props.number_props.min"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>Min</FormLabel>
                        <FormControl>
                          <Input type="number" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="props.number_props.max"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>Max</FormLabel>
                        <FormControl>
                          <Input type="number" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </>
              )}

              {form.watch('type') == 'file' && (
                <>
                  <Separator className="col-span-2 my-2" />
                  <div className="col-span-2 text-sm font-semibold">Properties</div>

                  <CampaignFileExtensionsInput />

                  <FormField
                    control={form.control}
                    name="props.file_props.size_limit"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>Size Limit</FormLabel>
                        <FormControl>
                          <Input placeholder="in MB" type="number" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </>
              )}

              {form.watch('type') == 'options' && (
                <>
                  <Separator className="col-span-2 my-2" />
                  <div className="col-span-2 text-sm font-semibold">Properties</div>

                  <CampaignOptionsInput />
                </>
              )}
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  )
}
