import { Button } from '@/components/ui/button'
import { PlusCircleIcon, Trash2Icon } from 'lucide-react'
import { useFieldArray, useFormContext } from 'react-hook-form'
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'

export const CampaignFileExtensionsInput = () => {
  const form = useFormContext()
  const { fields, append, remove } = useFieldArray({ name: 'props.file_props.extensions' })

  return (
    <div className="space-y-2">
      <FormLabel>Extensions</FormLabel>

      {fields.map((field, index) => {
        return (
          <FormField
            key={field.id}
            control={form.control}
            name={`props.file_props.extensions.${index}`}
            render={({ field }) => (
              <div className="flex items-center space-x-2">
                <FormItem className="flex flex-col flex-1">
                  <FormControl>
                    <Input placeholder="jpg" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  onClick={() => {
                    remove(index)
                  }}
                >
                  <Trash2Icon className="h-4 w-4" />
                </Button>
              </div>
            )}
          />
        )
      })}

      <Button
        variant="outline"
        type="button"
        className="w-full"
        onClick={() => {
          append('')
        }}
      >
        <PlusCircleIcon size="16" className="mr-2" />
        Add an extension
      </Button>
    </div>
  )
}
