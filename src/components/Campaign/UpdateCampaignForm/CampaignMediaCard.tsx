import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Campaign } from '@/types/Campaign'
import { Image } from '@unpic/react'
import { EditIcon, MoreHorizontal } from 'lucide-react'
import { FC, useState } from 'react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import UpdateCampaignMediaDialog from './UpdateCampaignMediaDialog'
import { Label } from '@/components/ui/label'

export const CampaignMediaCard: FC<{ campaign: Campaign }> = ({ campaign }) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <CardTitle>Media</CardTitle>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" forceMount>
              <DropdownMenuGroup>
                <DropdownMenuItem
                  onClick={async (event) => {
                    event.stopPropagation()
                    setIsDialogOpen(true)
                  }}
                  className="space-x-2"
                >
                  <EditIcon size="16" />
                  <span>Edit</span>
                </DropdownMenuItem>
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>
        </CardHeader>
        <CardContent className="space-y-4">
          {campaign.gallery?.map((galleryMedia, index) => {
            return (
              <div key={index}>
                {galleryMedia.type == 'image' && (
                  <span className="grid grid-cols-[150px_1fr] space-x-2 items-center">
                    <Image
                      key={index}
                      layout="constrained"
                      width={150}
                      height={150}
                      src={galleryMedia.url}
                    />
                    <Label className="text-xs font-normal text-gray-500 text-ellipsis">
                      {galleryMedia.name}
                    </Label>
                  </span>
                )}
                {galleryMedia.type == 'video' && (
                  <span className="grid grid-cols-[150px_1fr] space-x-2 items-center">
                    <video
                      // controls
                      key={index}
                      src={galleryMedia.url}
                      height={150}
                      width={150}
                      className="rounded-md"
                    />
                    <Label className="text-xs font-normal text-gray-500 truncate">
                      {galleryMedia.name}
                    </Label>
                  </span>
                )}
              </div>
            )
          })}
        </CardContent>
      </Card>

      <UpdateCampaignMediaDialog {...{ isDialogOpen, setIsDialogOpen, campaign }} />
    </>
  )
}
