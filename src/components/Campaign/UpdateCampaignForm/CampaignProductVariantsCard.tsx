import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { ProductVariant } from '@/types/Product'
import { FC, useState } from 'react'
import { Button } from '@/components/ui/button'
import { MoreHorizontal, EditIcon, DownloadIcon } from 'lucide-react'
import { useProduct } from '@/hooks/products/useProduct'
import { UpdateCampaignProductVariantPricesForm } from './UpdateCampaignProductVariantPricesDialog'
import { usePriceLists } from '@/hooks/price_list/usePriceLists'
import { useParams } from 'react-router-dom'
import { Campaign, CampaignProduct } from '@/types/Campaign'
import { DateTime } from 'luxon'
import { toast } from '@/components/ui/use-toast'
import SocialShareService from '@/network/services/social_share'
import CryptoJs from 'crypto-js'

export type SocialShareType = {
  event: {
    id: number
    title: string
    date: string
    venue: string
  }
  product: {
    id: number
    medusa_id: string
    title: string
    original_price: number
    calculated_price: number
    unit_per_day?: number
    start_time?: string
    thumbnail_url: string
  }
  free_gifts?: {
    thumbnail_url: string
    worth: number
  }
  collection: {
    medusa_id: string
    title: string
    thumbnail_url: string
  }
  campaign: {
    id: number
    title: string
  }
}

const SECRET_KEY = import.meta.env.VITE_SOCIAL_SECRET_KEY as string

const CampaignProductVariantsCard: FC<{
  campaign: Campaign
  campaignProduct: CampaignProduct
}> = ({ campaign, campaignProduct }) => {
  const { event_id, campaign_id } = useParams()
  const { product, isLoading, error } = useProduct(
    campaignProduct.product?.medusa_product_id ?? '',
    {
      expand: [
        'variants',
        'variants.options',
        'variants.prices',
        'variants.prices.price_list',
        'options',
        'options.values',
        'store',
        'collection',
        'categories',
        'images'
      ]
    },
    'comma'
  )
  const { priceLists } = usePriceLists(Number(event_id), Number(campaign_id))
  const [editVariant, setEditVariant] = useState<ProductVariant | null>(null)

  if (!product || isLoading || error) {
    return <></>
  }

  async function shareDataProcessing(originalPrice: number, calculatedPrice: number) {
    const fromDate = DateTime.fromISO(campaign.event?.from as string)
    const toDate = DateTime.fromISO(campaign.event?.to as string)
    // const startTime = DateTime.fromISO(campaign.event?.from as string).toFormat('HH:mm')
    // const endTime = DateTime.fromISO(campaign.event?.to as string).toFormat('HH:mm')

    const fromDateFormat =
      fromDate.year !== toDate.year
        ? 'dd LLL yyyy'
        : fromDate.month !== toDate.month
          ? 'dd LLL'
          : 'dd'

    const fromDateString = fromDate.toFormat(fromDateFormat)
    const toDateString = toDate.toFormat('dd LLL yyyy')

    const tempBody: SocialShareType = {
      event: {
        id: campaign.event_id as number,
        title: campaign.event?.title as string,
        date: `${fromDateString}-${toDateString}`,
        venue: campaign.event?.location as string
      },
      product: {
        id: campaignProduct?.product_id as number,
        medusa_id: product.id,
        title: product.title,
        original_price: originalPrice,
        calculated_price: calculatedPrice,
        thumbnail_url: product.thumbnail as string
      },
      collection: {
        medusa_id: product.collection_id as string,
        title: product.collection.title ?? '',
        thumbnail_url: product.collection.thumbnail_url ?? ''
      },
      campaign: {
        id: campaign.id,
        title: campaign.title
      }
    }

    // optional fields
    if (campaignProduct?.effective_dates && campaignProduct.effective_dates?.length > 0) {
      const effectiveDate =
        campaignProduct.effective_dates.find(
          (effectiveDate) => DateTime.fromISO(effectiveDate.start) > DateTime.now()
        ) ?? campaignProduct.effective_dates[0]
      tempBody.product.start_time = DateTime.fromISO(effectiveDate.start).toFormat('HH:mm')
    }
    if (campaignProduct?.unit_per_day) {
      tempBody.product.unit_per_day = campaignProduct.unit_per_day
    }
    if (campaignProduct?.free_gifts_worth && campaignProduct?.free_gift_thumbnail) {
      tempBody.free_gifts = {
        thumbnail_url: campaignProduct.free_gift_thumbnail,
        worth: campaignProduct.free_gifts_worth
      }
    }

    const hmac = CryptoJs.HmacSHA256(JSON.stringify(tempBody), SECRET_KEY)
    const calculatatedHMAC = hmac.toString(CryptoJs.enc.Hex)

    const data = {
      data: tempBody,
      hmac: calculatatedHMAC
    }

    console.log(data)
    return data
  }

  return (
    <>
      <div className="flex flex-row space-y-0 items-center justify-between">
        <div className="text-lg">{campaignProduct.product?.title} Variants Prices</div>
        {/* <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" forceMount>
              <DropdownMenuGroup>
                <DropdownMenuItem
                  onClick={async (event) => {
                    event.stopPropagation()
                  }}
                  className="space-x-2"
                >
                  <PlusIcon size="16" />
                  <span>Add Variant</span>
                </DropdownMenuItem>
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu> */}
      </div>
      <div className="space-y-6">
        <div className="flex flex-row flex-wrap space-x-6">
          {/* {product.options?.map((opt) => {
              return (
                <div key={opt.title} className="flex flex-col space-y-1">
                  <p className="font-medium">{opt.title}</p>
                  <div className="flex flex-row space-x-1">
                    {opt.values?.map((val, index) => {
                      return (
                        <Badge key={index} variant="secondary">
                          {val.value}
                        </Badge>
                      )
                    })}
                  </div>
                </div>
              )
            })} */}
        </div>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Title</TableHead>
              <TableHead>Original Price</TableHead>
              <TableHead>Sale Price</TableHead>
              <TableHead className="w-12"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {product.variants.map((variant) => {
              const price = variant.prices.find((price) => !price.price_list_id)?.amount
              const priceList = priceLists.find(
                (priceList) => priceList.event_id && priceList.campaign_id
              )

              const eventPrice = variant.prices.find(
                (price) => price.price_list_id === priceList?.id
              )

              const amount = eventPrice?.amount

              return (
                <TableRow key={variant.id}>
                  <TableCell>{variant.title}</TableCell>
                  <TableCell>{price ? `MYR ${(price / 100).toFixed(2)}` : '-'}</TableCell>
                  <TableCell>
                    {amount
                      ? `MYR ${(amount / 100).toFixed(2)}`
                      : price
                        ? `MYR ${(price / 100).toFixed(2)}`
                        : '-'}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" forceMount>
                        <DropdownMenuGroup>
                          <DropdownMenuItem
                            onClick={async (event) => {
                              event.stopPropagation()
                              setEditVariant(variant)
                            }}
                            className="space-x-2"
                          >
                            <EditIcon size="16" />
                            <span>Edit Variant Sale Price</span>
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={async (event) => {
                              event.stopPropagation()
                              const data = await shareDataProcessing(
                                price as number,
                                amount as number
                              )

                              try {
                                const { data: social } =
                                  await SocialShareService.generateShareImage(data)

                                console.log(social)
                              } catch (error) {
                                console.log(error)
                                toast({
                                  title: 'Action failed, please try again',
                                  variant: 'destructive'
                                })
                              }
                            }}
                            className="space-x-2"
                          >
                            <DownloadIcon size="16" />
                            <span>Download image</span>
                          </DropdownMenuItem>
                        </DropdownMenuGroup>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              )
            })}
          </TableBody>
        </Table>
      </div>

      {editVariant && (
        <UpdateCampaignProductVariantPricesForm
          {...{
            setEditVariant,
            variant: editVariant,
            priceLists: priceLists.filter(
              (priceList) => priceList.event_id && priceList.campaign_id
            )
          }}
        />
      )}
    </>
  )
}

export default CampaignProductVariantsCard
