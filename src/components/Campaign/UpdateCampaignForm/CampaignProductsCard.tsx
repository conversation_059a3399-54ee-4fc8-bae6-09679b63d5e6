import { But<PERSON> } from '@/components/ui/button'
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  Card<PERSON>ooter,
  CardContentLabel,
  CardContentTitle
} from '@/components/ui/card'
import { Campaign, CampaignProduct, ProductSocialMediaImageBulkRequest } from '@/types/Campaign'
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem
} from '@/components/ui/dropdown-menu'
import { DownloadIcon, EditIcon, MoreHorizontal, UploadIcon } from 'lucide-react'
import { Dispatch, FC, SetStateAction, useEffect, useMemo, useState } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'
import UpdateCampaignProductsDialog from './UpdateCampaignProductsDialog'
import UpdateCampaignProductFreeGiftsDialog from './UpdateCampaignProductFreeGiftsDialog'
import CampaignService from '@/network/services/campaign'
import UploadCampaignProductCSVDialog from '@/components/Campaign/UpdateCampaignForm/UploadCampaignCsvDialog'
import useSWR from 'swr'
import { DateTime } from 'luxon'
import { toast } from '@/components/ui/use-toast'
import jsZip from 'jszip'
import { saveAs } from 'file-saver'

const CampaignProductsCard: FC<{
  campaign: Campaign
  campaignProductView?: CampaignProduct
  setCampaignProductView: Dispatch<SetStateAction<CampaignProduct | undefined>>
}> = ({ campaign, campaignProductView, setCampaignProductView }) => {
  const [bulkRequestActive, setBulkRequestActive] = useState(true)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false)
  const { data: bulkRequest, mutate: mutateBulkRequest } =
    useSWR<ProductSocialMediaImageBulkRequest>(
      CampaignService.getProductSocialMediaImageBulkRequest(campaign.id)
    )

  const bulkText = useMemo(() => {
    console.log(bulkRequest)
    if (bulkRequest) {
      if (bulkRequest.completed_at) {
        return `(Last generated at ${DateTime.fromISO(bulkRequest.completed_at).toFormat(
          'MM/dd/yyyy t'
        )})`
      }
      const completion =
        ((bulkRequest.total_completed + bulkRequest.total_failed) / bulkRequest.total) * 100
      return `(${completion.toFixed(0)}% Completed)`
    }
    bulkRequest

    return ''
  }, [bulkRequest])

  useEffect(() => {
    let interval: NodeJS.Timeout

    if (!bulkRequest) {
      setBulkRequestActive(false)
      return
    }

    //if the request is generating, refresh every 5 secs
    if (!bulkRequest?.completed_at && bulkRequestActive) {
      interval = setInterval(() => {
        mutateBulkRequest()
      }, 5000)
    }

    return () => {
      if (interval) {
        clearInterval(interval)
      }
    }
  }, [bulkRequest])

  // const [products, shopifyProducts] = useMemo(() => {
  //   const products: Product[] = []
  //   // TODO: shopify products
  //   const shopifyProducts: any[] = []
  //   for (const campaignProduct of campaign.campaign_products ?? []) {
  //     if (campaignProduct.product) {
  //       products.push(campaignProduct.product)
  //     } else if (campaignProduct.shopify_product) {
  //       shopifyProducts.push(campaignProduct.shopify_product)
  //     }
  //   }

  //   return [products, shopifyProducts]
  // }, [campaign.campaign_products])

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <CardTitle>Campaign Products</CardTitle>
          <div className="flex flex-row space-x-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" forceMount>
                <DropdownMenuGroup>
                  <DropdownMenuItem
                    onClick={async (event) => {
                      event.stopPropagation()
                      setIsDialogOpen(true)
                    }}
                    className="space-x-2"
                  >
                    <EditIcon size="16" />
                    <span>Edit</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    disabled={(campaign.campaign_products?.length ?? 0) <= 0}
                    onClick={async (event) => {
                      event.stopPropagation()

                      try {
                        const { data: response } = await CampaignService.generateBooklet(
                          campaign.id
                        )

                        if (response.success) {
                          window.open(response.data.url)
                        }
                      } catch (error) {
                        console.log(error)
                      }
                    }}
                    className="space-x-2"
                  >
                    <DownloadIcon size="16" />
                    <span>Download booklet</span>
                  </DropdownMenuItem>

                  <DropdownMenuItem
                    disabled={(campaign.campaign_products?.length ?? 0) <= 0}
                    onClick={async (event) => {
                      event.stopPropagation()
                      try {
                        await CampaignService.generateProductSocialMediaImageBulk(campaign.id)
                        mutateBulkRequest()
                        setBulkRequestActive(true)

                        toast({
                          title: `Please wait patiently until the genration is completed`,
                          variant: 'success'
                        })
                      } catch (error) {
                        console.log(error)
                      }
                    }}
                    className="space-x-2"
                  >
                    <DownloadIcon size="16" />
                    <span>Generate Social Media Images In Bulk</span>
                  </DropdownMenuItem>

                  <DropdownMenuItem
                    disabled={
                      (campaign.campaign_products?.length ?? 0) <= 0 || !bulkRequest?.completed_at
                    }
                    onClick={async (event) => {
                      event.stopPropagation()

                      toast({
                        title: `Downloading`,
                        variant: 'success'
                      })

                      const zip = new jsZip()
                      const folder = zip.folder('')

                      if (bulkRequest?.download_links) {
                        for (const data of bulkRequest.download_links) {
                          try {
                            const response = await fetch(data.download_url)
                            const blob = await response.blob()
                            folder!.file(data.file_name, blob)
                          } catch (error) {
                            toast({
                              title: `Error download bulk`,
                              variant: 'destructive'
                            })
                          }
                        }

                        zip.generateAsync({ type: 'blob' }).then((zipBlob) => {
                          saveAs(zipBlob, `${campaign.title}-social-medias.zip`) // Use the filename specified by the user
                        })
                      }
                    }}
                    className="space-x-2"
                  >
                    <DownloadIcon size="16" />
                    <span>Download Social Media Images Bulk {bulkText}</span>
                  </DropdownMenuItem>

                  <DropdownMenuItem
                    disabled={(campaign.campaign_products?.length ?? 0) <= 0}
                    onClick={async () => {
                      const { data: result } = await CampaignService.generateCampaignProductCsv(
                        campaign.id
                      )
                      window.open(result.download_url, '_blank')
                    }}
                    className="space-x-2"
                  >
                    <DownloadIcon size="16" />
                    <span>Download Products CSV</span>
                  </DropdownMenuItem>

                  <DropdownMenuItem
                    disabled={(campaign.campaign_products?.length ?? 0) <= 0}
                    onClick={() => {
                      setIsUploadDialogOpen(true)
                    }}
                    className="space-x-2"
                  >
                    <UploadIcon size="16" />
                    <span>Update Products With CSV</span>
                  </DropdownMenuItem>
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>
        <CardContent className="flex flex-col space-y-4">
          <div className="grid grid-cols-[200px_1fr] gap-2">
            <CardContentTitle>Event</CardContentTitle>
            <CardContentLabel>{campaign.event?.title}</CardContentLabel>
          </div>
          {campaign.campaign_products && campaign.campaign_products.length > 0 && (
            <div>
              <CardContentTitle>Products</CardContentTitle>
              <CampaignProductsTable
                campaignProducts={campaign.campaign_products}
                campaignProductView={campaignProductView}
                setCampaignProductView={setCampaignProductView}
              />
            </div>
          )}
          {/* {shopifyProducts.length > 0 && (
            <div>
              <CardContentTitle>Shopify Products</CardContentTitle>
              <ShopifyProductsTable shopifyProducts={shopifyProducts} />
            </div>
          )} */}
        </CardContent>
        <CardFooter></CardFooter>
      </Card>

      <UpdateCampaignProductsDialog {...{ isDialogOpen, setIsDialogOpen, campaign }} />
      <UploadCampaignProductCSVDialog
        isDialogOpen={isUploadDialogOpen}
        setIsDialogOpen={setIsUploadDialogOpen}
        campaign={campaign}
      />
    </>
  )
}

const CampaignProductsTable = ({
  campaignProducts,
  campaignProductView,
  setCampaignProductView
}: {
  campaignProducts: CampaignProduct[]
  campaignProductView?: CampaignProduct
  setCampaignProductView: Dispatch<SetStateAction<CampaignProduct | undefined>>
}) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editCampaignProduct, setEditCampaignProduct] = useState<CampaignProduct | null>(null)

  const handleDownloadSocialMediaImage = async (campaignProduct: CampaignProduct) => {
    try {
      const { data: response } = await CampaignService.generateProductSocialMediaImage(
        campaignProduct.campaign_id,
        campaignProduct.id
      )

      if (response.success) {
        window.open(response.data.url)
      }
    } catch (error) {
      console.log(error)
    }
  }

  return (
    <>
      {campaignProducts && (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Store</TableHead>
              <TableHead>Product</TableHead>
              <TableHead>Brand</TableHead>
              <TableHead>Free Gifts</TableHead>
              <TableHead>Unit Per Day</TableHead>
              {/* <TableHead>Start Time</TableHead>
              <TableHead>End Time</TableHead> */}
              <TableHead></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {campaignProducts?.map((campaignProduct, index: number) => {
              return (
                <TableRow
                  key={index}
                  onClick={(e) => {
                    e.stopPropagation()
                    setCampaignProductView(campaignProduct)
                  }}
                  className={campaignProduct.id == campaignProductView?.id ? 'bg-slate-100' : ''}
                >
                  <TableCell>{campaignProduct.product?.store?.name}</TableCell>
                  <TableCell align="center">
                    <span className="flex flex-row items-center gap-4">
                      {campaignProduct.product?.thumbnail && (
                        <img
                          className="h-[64px]"
                          src={campaignProduct.product?.thumbnail ?? ''}
                          alt={'campaign-product-' + index}
                        />
                      )}
                      {campaignProduct.product?.title}
                    </span>
                  </TableCell>
                  <TableCell align="center">
                    <span className="flex flex-row items-center gap-4">
                      {campaignProduct.product?.product_collection?.thumbnail_url && (
                        <img
                          className="h-[64px]"
                          src={campaignProduct.product?.product_collection.thumbnail_url ?? ''}
                          alt={'campaign-product-collection' + index}
                        />
                      )}
                      {campaignProduct.product?.product_collection?.title ?? '-'}
                    </span>
                  </TableCell>
                  <TableCell>
                    <span className="flex flex-row items-center gap-4">
                      {campaignProduct.free_gift_thumbnail && (
                        <img
                          className="h-[64px]"
                          src={campaignProduct.free_gift_thumbnail ?? ''}
                          alt={'campaign-product-free-gifts' + index}
                        />
                      )}
                      {campaignProduct.free_gifts ?? '-'}{' '}
                      {campaignProduct.free_gifts_worth &&
                        `(Worth RM${campaignProduct.free_gifts_worth ?? '-'})`}
                    </span>
                  </TableCell>
                  <TableCell>{campaignProduct.unit_per_day ?? '-'}</TableCell>
                  {/* <TableCell>{campaignProduct.start_time ?? '-'}</TableCell>
                  <TableCell>{campaignProduct.end_time ?? '-'}</TableCell> */}
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" forceMount>
                        <DropdownMenuGroup>
                          <DropdownMenuItem
                            onClick={async (event) => {
                              event.stopPropagation()
                              setEditCampaignProduct(campaignProduct)
                              setIsDialogOpen(true)
                            }}
                            className="space-x-2"
                          >
                            <EditIcon size="16" />
                            <span>Edit Free Gifts</span>
                          </DropdownMenuItem>

                          <DropdownMenuItem
                            onClick={async (event) => {
                              event.stopPropagation()
                              await handleDownloadSocialMediaImage(campaignProduct)
                            }}
                            className="space-x-2"
                          >
                            <DownloadIcon size="16" />
                            <span>Download Social Media Image</span>
                          </DropdownMenuItem>
                        </DropdownMenuGroup>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              )
            })}
          </TableBody>
        </Table>
      )}
      {editCampaignProduct && (
        <UpdateCampaignProductFreeGiftsDialog
          campaignProduct={editCampaignProduct}
          isDialogOpen={isDialogOpen}
          setIsDialogOpen={setIsDialogOpen}
        />
      )}
    </>
  )
}

// const ShopifyProductsTable = ({ shopifyProducts }: { shopifyProducts: any[] }) => {
//   return (
//     <>
//       {shopifyProducts && (
//         <Table>
//           <TableHeader>
//             <TableRow>
//               <TableHead>Product</TableHead>
//             </TableRow>
//           </TableHeader>
//           <TableBody>
//             {shopifyProducts?.map((shopifyProduct, index: number) => {
//               return (
//                 <TableRow key={index}>
//                   <TableCell align="center">
//                     {shopifyProduct}
//                     <img
//                       className="h-[64px]"
//                       src={shopifyProduct.event_product?.product.thumbnail ?? ''}
//                       alt={'campaign-product-option-' + index}
//                     />
//                   </TableCell>
//                 </TableRow>
//               )
//             })}
//           </TableBody>
//         </Table>
//       )}
//     </>
//   )
// }

export default CampaignProductsCard
