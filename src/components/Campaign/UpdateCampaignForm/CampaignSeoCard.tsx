import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { FC, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { mutate } from 'swr'
import { Icons } from '@/components/icons'
import { useToast } from '@/components/ui/use-toast'
import { FileWithPath } from 'react-dropzone'
import FileService from '@/network/services/file'
import { Dropzone } from '@/components/Form/Dropzone'
import { Image } from '@unpic/react'
import TextEditorComponent from '@/components/Editor/TextEditor'
import { Input } from '@/components/ui/input'
import OrSeparator from '@/components/ui/orSeparator'
import { UpdateCampaignInput } from '@/types/CreateCampaign'
import { Campaign } from '@/types/Campaign'
import CampaignService from '@/network/services/campaign'

const CampaignSeoCard: FC<{ campaign: Campaign }> = ({ campaign }) => {
  const { toast } = useToast()
  const form = useForm<UpdateCampaignInput>({
    shouldUseNativeValidation: false,
    defaultValues: {
      metadata: campaign.metadata
    }
  })

  useEffect(() => {
    form.reset({
      ...campaign,
      title: campaign.title ?? '',
      description: campaign.description ?? '',
      metadata: campaign.metadata
    })
  }, [campaign])

  const seoThumbnailUrl = form.watch('metadata.seo.thumbnail')
  const seoMedia = form.watch('tmp_seo_media') as (FileWithPath & { preview: string }) | undefined

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      if (values.tmp_seo_media) {
        // only upload if no url provided
        if (!values.metadata.seo.thumbnail) {
          const { data: mediaResponse } = await FileService.upload([values.tmp_seo_media] as File[])
          values.metadata.seo.thumbnail = mediaResponse.uploads[0].url
        }
        delete values.tmp_seo_media
      }

      if (values.metadata.seo.thumbnail) {
        delete values.tmp_seo_media
      }

      console.log('submit values: ', values)

      const { data: response } = await CampaignService.updateCampaign(campaign.id, {
        ...values
      })

      if (response.success) {
        toast({
          title: 'Update successfully',
          variant: 'success'
        })

        mutate(
          (key) =>
            typeof key === 'string' && key.startsWith(CampaignService.getCampaign(campaign.id))
        )
      }
    } catch (error) {
      console.log(error)
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0">
        <CardTitle>SEO</CardTitle>
        <Button
          type="submit"
          form="campaign-seo-form"
          disabled={form.formState.isSubmitting}
          onClick={() => {
            form.clearErrors()
          }}
          className="gap-1"
        >
          Save
          {form.formState.isSubmitting && <Icons.spinner className="h-4 w-4 animate-spin" />}
        </Button>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form id="campaign-seo-form" onSubmit={onSubmit} className="space-y-4">
            <FormField
              control={form.control}
              name="metadata.seo.title"
              // rules={{ required: 'Please enter the SEO title' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>SEO Title</FormLabel>
                  <FormControl>
                    <TextEditorComponent content={field.value} onChange={field.onChange} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="metadata.seo.description"
              // rules={{ required: 'Please enter the SEO description' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>SEO Description</FormLabel>
                  <FormControl>
                    <TextEditorComponent content={field.value} onChange={field.onChange} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="metadata.seo.thumbnail"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>SEO Thumbnail</FormLabel>
                  <FormLabel>Image URL</FormLabel>
                  <FormControl>
                    <Input placeholder="Image Url" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <OrSeparator />

            <FormField
              control={form.control}
              name="tmp_seo_media"
              render={({ field }) => (
                <>
                  <FormItem>
                    <FormLabel>Upload Image</FormLabel>
                    <FormControl>
                      <Dropzone
                        accept={{ 'image/*': ['.jpeg', '.jpg', '.png'] }}
                        description="Drop your image here, or click to browse"
                        multiple={false}
                        onDrop={(acceptedFiles) => {
                          const file = acceptedFiles[0]
                          const exist = seoMedia?.path === file.path

                          if (!exist) {
                            Object.assign(file, {
                              preview: URL.createObjectURL(file)
                            })
                          }

                          form.setValue('tmp_seo_media', file)
                        }}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>

                  {(seoThumbnailUrl || seoMedia?.preview) && (
                    <div className="mt-2 flex flex-col space-y-4">
                      <Image
                        src={seoMedia?.preview ?? seoThumbnailUrl ?? ''}
                        layout="constrained"
                        className="rounded-md"
                        width={320}
                        height={320}
                      />
                    </div>
                  )}
                </>
              )}
            />
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}

export default CampaignSeoCard
