import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import { DialogHeader } from '@/components/ui/dialog'
import { toast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import CampaignService from '@/network/services/campaign'
import { Dialog, DialogContent, DialogTitle, DialogClose } from '@/components/ui/dialog'
import { FC } from 'react'
import { useForm } from 'react-hook-form'
import { mutate } from 'swr'
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import { capitalize } from 'radash'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'
import { Campaign } from '@/types/Campaign'
import { CreateCampaignFieldInput } from '@/types/CreateCampaign'
import { CampaignFileExtensionsInput } from './CampaignFileExtensionsInput'
import { CampaignOptionsInput } from './CampaignOptionsInput'

export const CreateCampaignFieldDialog: FC<{
  isDialogOpen: boolean
  setIsDialogOpen: (value: boolean) => void
  campaign: Campaign
}> = ({ isDialogOpen, setIsDialogOpen, campaign }) => {
  const form = useForm<CreateCampaignFieldInput>({
    shouldUseNativeValidation: false,
    defaultValues: {
      props: {
        text_props: {
          textarea: false
        }
      }
    }
  })

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      await CampaignService.createCampaignField(campaign.id, values)
      setIsDialogOpen(false)
      mutate(
        (key) => typeof key === 'string' && key.startsWith(CampaignService.getCampaign(campaign.id))
      )
      form.reset()
      toast({
        title: 'Campaign field created',
        variant: 'success'
      })
    } catch (error) {
      console.error(error)
      form.setError('root', {})
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })
  return (
    <Dialog
      open={isDialogOpen}
      onOpenChange={(isOpen) => {
        setIsDialogOpen(isOpen)
      }}
    >
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle className="flex justify-between items-center">
            Create Campaign Field
            <div className="flex space-x-2">
              <DialogClose>
                <Button variant="outline">Cancel</Button>
              </DialogClose>
              <Button
                type="submit"
                form="create-campaign-field-form"
                disabled={form.formState.isSubmitting}
                onClick={() => {
                  form.clearErrors()
                }}
              >
                {form.formState.isSubmitting && (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                )}
                Save
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form
            id="create-campaign-field-form"
            onSubmit={onSubmit}
            className={cn('flex flex-col space-y-8')}
          >
            <FormField
              control={form.control}
              name="name"
              rules={{ required: 'Please enter the name' }}
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="type"
              rules={{ required: 'Please enter the type' }}
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Type</FormLabel>
                  <FormControl>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose an option" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {['email', 'text', 'number', 'datetime', 'file', 'options', 'checkbox'].map(
                          (value) => {
                            return (
                              <SelectItem key={value} value={value}>
                                {capitalize(value)}
                              </SelectItem>
                            )
                          }
                        )}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="required"
              // rules={{ required: 'Please select' }}
              render={({ field }) => (
                <FormItem className="flex items-center space-y-0 space-x-2">
                  <FormControl>
                    <Switch checked={field.value} onCheckedChange={field.onChange} aria-readonly />
                  </FormControl>
                  <FormLabel>{field.value ? 'Required' : 'Optional'}</FormLabel>
                  <FormMessage />
                </FormItem>
              )}
            />

            {form.watch('type') == 'text' && (
              <>
                <Separator className="col-span-2 my-2" />
                <div className="col-span-2 text-sm font-semibold">Properties</div>
                <FormField
                  control={form.control}
                  name="props.text_props.textarea"
                  render={({ field }) => (
                    <FormItem className="flex items-center space-y-0 space-x-2">
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          aria-readonly
                        />
                      </FormControl>
                      <FormLabel>{field.value ? 'TextArea' : 'Text'}</FormLabel>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {form.watch('props.text_props.textarea') == true && (
                  <>
                    <FormField
                      control={form.control}
                      name="props.text_props.min"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Min</FormLabel>
                          <FormControl>
                            <Input type="number" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="props.text_props.max"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Max</FormLabel>
                          <FormControl>
                            <Input type="number" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                )}
              </>
            )}

            {form.watch('type') == 'number' && (
              <>
                <Separator className="col-span-2 my-2" />
                <div className="col-span-2 text-sm font-semibold">Properties</div>

                <FormField
                  control={form.control}
                  name="props.number_props.min"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Min</FormLabel>
                      <FormControl>
                        <Input type="number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="props.number_props.max"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Max</FormLabel>
                      <FormControl>
                        <Input type="number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </>
            )}

            {form.watch('type') == 'file' && (
              <>
                <Separator className="col-span-2 my-2" />
                <div className="col-span-2 text-sm font-semibold">Properties</div>

                <CampaignFileExtensionsInput />

                <FormField
                  control={form.control}
                  name="props.file_props.size_limit"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Size Limit</FormLabel>
                      <FormControl>
                        <Input placeholder="in MB" type="number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </>
            )}

            {form.watch('type') == 'options' && (
              <>
                <Separator className="col-span-2 my-2" />
                <div className="col-span-2 text-sm font-semibold">Properties</div>

                <CampaignOptionsInput />
              </>
            )}
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
