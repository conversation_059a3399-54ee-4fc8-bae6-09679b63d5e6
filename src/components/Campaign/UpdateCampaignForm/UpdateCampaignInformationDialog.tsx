import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import { DialogHeader } from '@/components/ui/dialog'
import { toast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import CampaignService from '@/network/services/campaign'
import { Campaign } from '@/types/Campaign'
import { UpdateCampaignInput } from '@/types/CreateCampaign'
import { Dialog, DialogContent, DialogTitle, DialogClose } from '@/components/ui/dialog'
import React, { FC, useEffect } from 'react'
import { useFieldArray, useForm } from 'react-hook-form'
import { mutate } from 'swr'
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { PlusCircleIcon, Trash2Icon, TrashIcon } from 'lucide-react'
import { FileWithPath } from 'react-dropzone'
import { Dropzone } from '@/components/Form/Dropzone'
import { Image } from '@unpic/react'
import FileService from '@/network/services/file'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import EditorComponent from '@/components/Editor/Editor'
import TextEditorComponent from '@/components/Editor/TextEditor'

export const UpdateCampaignInformationDialog: FC<{
  isDialogOpen: boolean
  setIsDialogOpen: (value: boolean) => void
  campaign: Campaign
  type: 'freebies' | 'deal'
  categories: any[]
}> = ({ isDialogOpen, setIsDialogOpen, campaign, type, categories }) => {
  const form = useForm<UpdateCampaignInput>({
    shouldUseNativeValidation: false,
    defaultValues: {
      title: campaign.title,
      slug: campaign.slug,
      gender: campaign.gender,
      abstract: campaign.abstract,
      rewards: campaign.rewards,
      application: campaign.application,
      mission: campaign.mission,
      redemption: campaign.redemption,
      description: campaign.description,
      terms_and_conditions: campaign.terms_and_conditions,
      rank: campaign.rank,
      published: campaign.published,
      metadata: campaign.metadata ?? { user_agreements: [''], deal_wishlist_description: '' }
    }
  })

  const { fields, append, remove } = useFieldArray({ control: form.control, name: 'redemption' })
  const {
    fields: userAgreements,
    append: appendAgreement,
    remove: removeAgreement
  } = useFieldArray({ name: 'metadata.user_agreements', control: form.control })

  useEffect(() => {
    if (isDialogOpen || campaign) {
      form.reset({
        title: campaign.title,
        slug: campaign.slug,
        abstract: campaign.abstract,
        rewards: campaign.rewards,
        application: campaign.application,
        mission: campaign.mission,
        redemption: campaign.redemption,
        description: campaign.description,
        terms_and_conditions: campaign.terms_and_conditions,
        rank: campaign.rank,
        published: campaign.published,
        category: campaign.category,
        gender: campaign.gender,
        metadata: campaign.metadata ?? { user_agreements: [''], deal_wishlist_description: '' }
      })
    }
  }, [isDialogOpen, campaign])

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      if (values.redemption && values.redemption.length > 0) {
        for (const index in values.redemption) {
          const image = values.redemption[index].image
          if (image) {
            const { data } = await FileService.upload([image])
            values.redemption[index].image_url = data.uploads[0].url

            delete values.redemption[index].image
          }
        }
      }

      const { data: response } = await CampaignService.updateCampaign(campaign.id, values)
      if (response.success) {
        setIsDialogOpen(false)
        mutate(
          (key) =>
            typeof key === 'string' && key.startsWith(CampaignService.getCampaign(campaign.id))
        )
        toast({
          title: 'Campaign updated',
          variant: 'success'
        })
      }
    } catch (error) {
      console.error(error)
      form.setError('root', {})
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <Dialog
      open={isDialogOpen}
      onOpenChange={(isOpen) => {
        setIsDialogOpen(isOpen)
      }}
    >
      <DialogContent className="max-w-2xl max-h-screen overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex justify-between items-center">
            Edit Campaign Information
            <div className="flex space-x-2">
              <DialogClose>
                <Button variant="outline">Cancel</Button>
              </DialogClose>
              <Button
                type="submit"
                form="update-campaign-form"
                disabled={form.formState.isSubmitting}
                onClick={() => {
                  form.clearErrors()
                }}
              >
                {form.formState.isSubmitting && (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                )}
                Save
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form
            id="update-campaign-form"
            onSubmit={onSubmit}
            className={cn('flex flex-col space-y-8')}
          >
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Title</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="slug"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Slug</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="abstract"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <EditorComponent content={field.value} onChange={field.onChange} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="gender"
              // rules={{ required: 'Please enter the terms & conditions' }}
              render={({ field }) => (
                <FormItem className="flex flex-col col-span-2">
                  <FormLabel>Gender</FormLabel>
                  <FormControl>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose an option" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="any">Any</SelectItem>
                        <SelectItem value="male">Male</SelectItem>
                        <SelectItem value="female">Female</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {type == 'freebies' && (
              <FormField
                control={form.control}
                name="category"
                // rules={{ required: 'Please enter the terms & conditions' }}
                render={({ field }) => (
                  <FormItem className="flex flex-col col-span-2">
                    <FormLabel>Category</FormLabel>
                    <FormControl>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Choose an option" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {categories?.map((value) => {
                            return (
                              <SelectItem key={value.title} value={value.title}>
                                {value.title}
                              </SelectItem>
                            )
                          })}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <FormField
              control={form.control}
              name="rewards"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Rewards</FormLabel>
                  <FormControl>
                    <EditorComponent content={field.value} onChange={field.onChange} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Long Description</FormLabel>
                  <FormControl>
                    <EditorComponent content={field.value} onChange={field.onChange} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="application"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Application</FormLabel>
                  <FormControl>
                    <EditorComponent content={field.value} onChange={field.onChange} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="mission"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Mission</FormLabel>
                  <FormControl>
                    <EditorComponent content={field.value} onChange={field.onChange} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="redemption"
              // rules={{ required: 'Please enter the fields' }}
              render={() => (
                <FormItem className="flex flex-col col-span-2">
                  <FormLabel>Redemption</FormLabel>
                  {fields.map((item, i) => (
                    <React.Fragment key={item.id}>
                      {i > 0 && <br />}
                      <FormLabel>
                        <span className="flex gap-x-2">
                          Step {i + 1} <TrashIcon size={16} color="red" onClick={() => remove(i)} />
                        </span>
                      </FormLabel>
                      <FormField
                        control={form.control}
                        name={`redemption.${i}.description`}
                        // rules={{ required: 'Please enter the terms & conditions' }}
                        render={({ field }) => (
                          <FormItem className="flex flex-col col-span-2">
                            <FormLabel>Description</FormLabel>
                            <FormControl>
                              <TextEditorComponent
                                content={field.value}
                                onChange={field.onChange}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`redemption.${i}.image`}
                        // rules={{ required: 'Please enter the terms & conditions' }}
                        render={({ field }) => {
                          const thumbnail = form.watch(`redemption.${i}.image`) as FileWithPath & {
                            preview: string
                          }
                          return (
                            <FormItem className="flex flex-col col-span-2">
                              <FormLabel>Image</FormLabel>
                              <FormControl>
                                <Dropzone
                                  multiple={false}
                                  onDrop={(acceptedFiles) => {
                                    const preview = Object.assign(acceptedFiles[0], {
                                      preview: URL.createObjectURL(acceptedFiles[0])
                                    })

                                    form.setValue(`redemption.${i}.image`, preview as File, {
                                      shouldValidate: true
                                    })
                                  }}
                                  {...field}
                                />
                              </FormControl>
                              {!thumbnail && item.image_url && (
                                <Image
                                  layout="constrained"
                                  width={180}
                                  height={180}
                                  src={item.image_url}
                                />
                              )}
                              {thumbnail && (
                                <Image
                                  layout="constrained"
                                  width={180}
                                  height={180}
                                  src={thumbnail.preview}
                                />
                              )}
                              <FormMessage />
                            </FormItem>
                          )
                        }}
                      />
                    </React.Fragment>
                  ))}
                  <Button
                    asChild
                    variant="outline"
                    type="button"
                    onClick={() => append({ description: '', image: undefined })}
                  >
                    <div>
                      <PlusCircleIcon size="16" className="mr-2" />
                      Add a new step
                    </div>
                  </Button>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="terms_and_conditions"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Terms & Conditions</FormLabel>
                  <FormControl>
                    <EditorComponent content={field.value} onChange={field.onChange} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {userAgreements.map((item, i) => {
              return (
                <React.Fragment key={item.id}>
                  <FormField
                    control={form.control}
                    name={`metadata.user_agreements.${i}`}
                    rules={{ required: 'This is required' }}
                    render={({ field }) => {
                      return (
                        <FormItem className="flex flex-col col-span-2">
                          <FormLabel className="flex gap-2">
                            <span>User Agreement {i + 1}</span>
                            {userAgreements.length > 1 && (
                              <Trash2Icon
                                size={16}
                                color="red"
                                onClick={() => removeAgreement(i)}
                              />
                            )}
                          </FormLabel>
                          <EditorComponent content={field.value} onChange={field.onChange} />
                          <FormMessage />
                        </FormItem>
                      )
                    }}
                  />
                </React.Fragment>
              )
            })}
            <FormItem className="flex flex-col col-span-2">
              <Button type="button" onClick={() => appendAgreement('')}>
                Add User Agreement
              </Button>
            </FormItem>

            <FormField
              control={form.control}
              name="metadata.deal_wishlist_description"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Deal Wishlist Description</FormLabel>
                  <FormControl>
                    <TextEditorComponent content={field.value} onChange={field.onChange} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
