import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import { DialogHeader } from '@/components/ui/dialog'
import { toast } from '@/components/ui/use-toast'
import { bytesToSize, cn, convertValuesToFormData } from '@/lib/utils'
import CampaignService from '@/network/services/campaign'
import { Campaign } from '@/types/Campaign'
import { UpdateCampaignInput } from '@/types/CreateCampaign'
import { Dialog, DialogContent, DialogTitle, DialogClose } from '@/components/ui/dialog'
import { FC, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { mutate } from 'swr'
import { Form, FormField, FormItem, FormControl, FormMessage } from '@/components/ui/form'
import { Dropzone } from '@/components/Form/Dropzone'
import { Image } from '@unpic/react'
import { Label } from '@/components/ui/label'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { MoreHorizontal, Trash2Icon } from 'lucide-react'
import { title } from 'radash'

const UpdateCampaignMediaDialog: FC<{
  isDialogOpen: boolean
  setIsDialogOpen: (value: boolean) => void
  campaign: Campaign
}> = ({ isDialogOpen, setIsDialogOpen, campaign }) => {
  const form = useForm<UpdateCampaignInput>({
    shouldUseNativeValidation: false,
    defaultValues: {
      gallery: campaign.gallery
    }
  })
  const gallery = form.watch('gallery')

  useEffect(() => {
    if (isDialogOpen || campaign) {
      form.reset({
        gallery: campaign.gallery
      })
    }
  }, [campaign, isDialogOpen])

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      if (values.gallery && (values.gallery?.length ?? 0) > 0) {
        for (const i in values.gallery) {
          const item = values.gallery[i]
          if (item.media_file) {
            if (!values.media) values.media = []
            values.media[i] = item.media_file
            delete values.gallery[i].media_file
          }
        }
      }
      console.log(values)
      const formData = new FormData()
      convertValuesToFormData(formData, values)

      let totalRequestSize = 0
      const requestSize = Array.from(formData.entries(), ([key, prop]) => {
        totalRequestSize += typeof prop === 'string' ? prop.length : prop.size
        return {
          [key]: {
            ContentLength: typeof prop === 'string' ? prop.length : prop.size,
            value: prop
          }
        }
      })
      totalRequestSize /= 1024 * 1024

      console.debug(requestSize)
      // server config size limit 128mb
      if (totalRequestSize > 128) {
        toast({
          title: `Request size too large (${totalRequestSize.toFixed(2)}mb > 128mb)`,
          variant: 'destructive'
        })
        return
      }
      const { data: response } = await CampaignService.updateCampaign(
        campaign.id,
        requestSize.length > 0 ? formData : { gallery: [], media: [] }
      )

      if (response.success) {
        setIsDialogOpen(false)
        mutate(
          (key) =>
            typeof key === 'string' && key.startsWith(CampaignService.getCampaign(campaign.id))
        )
        toast({
          title: 'Campaign updated',
          variant: 'success'
        })
      }
    } catch (error) {
      console.error(error)
      form.setError('root', {})
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <Dialog
      open={isDialogOpen}
      onOpenChange={(isOpen) => {
        setIsDialogOpen(isOpen)
      }}
    >
      <DialogContent className="max-w-2xl max-h-screen overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex justify-between items-center">
            Edit Media
            <div className="flex space-x-2">
              <DialogClose asChild>
                <Button variant="outline">Cancel</Button>
              </DialogClose>
              <Button
                type="submit"
                form="update-thumbnail-form"
                disabled={form.formState.isSubmitting}
                onClick={() => {
                  form.clearErrors()
                }}
              >
                {form.formState.isSubmitting && (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                )}
                Save
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form
            encType="multipart/form-data"
            id="update-thumbnail-form"
            onSubmit={onSubmit}
            className={cn('flex flex-col space-y-8')}
          >
            <FormField
              control={form.control}
              name="gallery"
              // rules={{ required: 'Please insert the image' }}
              render={({ field }) => (
                <>
                  <FormItem>
                    {/* <FormDescription>Replace the thumbnail</FormDescription> */}
                    <FormControl>
                      <Dropzone
                        accept={{ 'image/*': ['.jpeg', '.jpg', '.png'], 'video/*': ['.mp4'] }}
                        description="Drop your videos or images here, or click to browse"
                        multiple={true}
                        onDrop={(acceptedFiles) => {
                          console.log('ondrop')
                          const galleryTmp = gallery ? [...gallery] : []

                          // remove existing already uploaded file
                          const acceptedFiles2 = acceptedFiles.filter((acceptedFile) =>
                            galleryTmp?.every((file) => file.name != acceptedFile.name)
                          )
                          for (const index in acceptedFiles2) {
                            const file = acceptedFiles2[index]
                            const findIndex = gallery?.findIndex(
                              (f) => f.media_file?.path == file.path
                            )

                            if ((findIndex ?? -1) == -1) {
                              const preview = Object.assign(file, {
                                preview: URL.createObjectURL(file)
                              })

                              galleryTmp.push({
                                name: preview.name,
                                type: preview.type.split('/')[0] as 'video' | 'image',
                                media_file: preview
                              })
                            }
                          }

                          form.setValue('gallery', galleryTmp, {
                            shouldValidate: true
                          })
                        }}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>

                  {(gallery ?? []).length > 0 && (
                    <div className="flex flex-col space-y-4 mt-2">
                      {gallery?.map((file, index) => {
                        return (
                          <div
                            key={index}
                            className="grid grid-cols-[150px_1fr_36px] space-x-2 items-center"
                          >
                            {file.type.startsWith('image') && (
                              <Image
                                key={index}
                                src={file.url ?? file.media_file?.preview ?? ''}
                                height={150}
                                width={150}
                                objectFit="contain"
                                className="rounded-md"
                              />
                            )}
                            {file.type.startsWith('video') && (
                              <video
                                // controls
                                key={index}
                                src={file.url ?? file.media_file?.preview ?? ''}
                                height={150}
                                width={150}
                                className="rounded-md"
                              />
                            )}
                            <div className="flex flex-col">
                              {file.url ? (
                                <Label className="text-xs font-normal">
                                  {title(file.type)} uploaded
                                </Label>
                              ) : (
                                <>
                                  <Label className="text-xs font-normal">
                                    {file.media_file?.name}
                                  </Label>
                                  <Label className="text-xs font-normal text-gray-500">
                                    {bytesToSize(file.media_file?.size ?? 0)}
                                  </Label>
                                </>
                              )}
                            </div>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <span className="sr-only">Open menu</span>
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    const galleryTmp = [...gallery]

                                    galleryTmp?.splice(index, 1)
                                    form.setValue('gallery', galleryTmp, {
                                      shouldValidate: true
                                    })
                                  }}
                                  className="space-x-2"
                                >
                                  <Trash2Icon size="16" />
                                  <span>Delete</span>
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        )
                      })}
                    </div>
                  )}
                </>
              )}
            />
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}

export default UpdateCampaignMediaDialog
