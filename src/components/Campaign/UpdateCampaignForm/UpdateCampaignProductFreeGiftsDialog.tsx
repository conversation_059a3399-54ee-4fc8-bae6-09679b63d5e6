import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import { DialogHeader } from '@/components/ui/dialog'
import { toast } from '@/components/ui/use-toast'
import { bytesToSize, cn } from '@/lib/utils'
import CampaignService from '@/network/services/campaign'
import { CampaignProduct } from '@/types/Campaign'
import { UpdateCampaignProductFreeGiftsInput } from '@/types/CreateCampaign'
import { Dialog, DialogContent, DialogTitle, DialogClose } from '@/components/ui/dialog'
import { FC, useEffect } from 'react'
import { useFieldArray, useForm } from 'react-hook-form'
import { mutate } from 'swr'
import {
  Form,
  FormField,
  FormItem,
  FormControl,
  FormMessage,
  FormLabel
} from '@/components/ui/form'
import { Dropzone } from '@/components/Form/Dropzone'
import { FileWithPath } from 'react-dropzone'
import { Image } from '@unpic/react'
import { Label } from '@/components/ui/label'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { MoreHorizontal, Trash2Icon } from 'lucide-react'
import FileService from '@/network/services/file'
import { Input } from '@/components/ui/input'
import { CalendarDatePicker } from '@/components/DatePicker/CalendarDatePicker'
import { DateTime } from 'luxon'
import TextEditorComponent from '@/components/Editor/TextEditor'

const UpdateCampaignProductFreeGiftsDialog: FC<{
  isDialogOpen: boolean
  setIsDialogOpen: (value: boolean) => void
  campaignProduct: CampaignProduct
}> = ({ isDialogOpen, setIsDialogOpen, campaignProduct }) => {
  const form = useForm<UpdateCampaignProductFreeGiftsInput>({
    shouldUseNativeValidation: false,
    defaultValues: {
      free_gifts: campaignProduct.free_gifts,
      free_gift_thumbnail: campaignProduct.free_gift_thumbnail,
      free_gifts_worth: campaignProduct.free_gifts_worth,
      thumbnail: undefined,
      unit_per_day: campaignProduct.unit_per_day,
      effective_dates: campaignProduct.effective_dates ?? [],
      tags: campaignProduct.tags ?? [],
      labels: campaignProduct.labels ?? []
    }
  })
  const uploadedImage = form.watch('free_gift_thumbnail')
  const imagesFile = form.watch('thumbnail')
  const imageFileWithPath = imagesFile
    ? (imagesFile as unknown as FileWithPath & { preview: string })
    : undefined

  const tags = form.watch('tags')

  const {
    fields: effectiveDates,
    append: appendDate,
    remove: removeDate
  } = useFieldArray({ name: 'effective_dates', control: form.control })

  useEffect(() => {
    if (isDialogOpen || campaignProduct) {
      form.reset({
        free_gifts: campaignProduct.free_gifts,
        free_gift_thumbnail: campaignProduct.free_gift_thumbnail,
        free_gifts_worth: campaignProduct.free_gifts_worth,
        thumbnail: undefined,
        unit_per_day: campaignProduct.unit_per_day,
        effective_dates: campaignProduct.effective_dates ?? [],
        tags: campaignProduct.tags ?? [],
        labels: campaignProduct.labels ?? []
      })
    }
  }, [campaignProduct, isDialogOpen])

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      console.log(values)
      if (values.thumbnail) {
        const { data } = await FileService.upload([values.thumbnail])

        values.free_gift_thumbnail = data.uploads[0].url
      }
      delete values.thumbnail

      const { data: response } = await CampaignService.updateCampaignProduct(
        campaignProduct.id,
        values
      )

      if (response.success) {
        setIsDialogOpen(false)
        mutate(
          (key) =>
            typeof key === 'string' &&
            key.startsWith(CampaignService.getCampaign(campaignProduct.campaign_id))
        )
        toast({
          title: 'Campaign updated',
          variant: 'success'
        })
      }
    } catch (error) {
      console.error(error)
      form.setError('root', {})
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <Dialog
      open={isDialogOpen}
      onOpenChange={(isOpen) => {
        setIsDialogOpen(isOpen)
      }}
    >
      <DialogContent className="max-w-2xl max-h-screen overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex justify-between items-center">
            Edit {campaignProduct.product?.title} Free Gifts
            <div className="flex space-x-2">
              <DialogClose asChild>
                <Button variant="outline">Cancel</Button>
              </DialogClose>
              <Button
                type="submit"
                form="update-campaign-product-free-gifts-form"
                disabled={form.formState.isSubmitting}
                onClick={() => {
                  form.clearErrors()
                }}
              >
                {form.formState.isSubmitting && (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                )}
                Save
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form
            id="update-campaign-product-free-gifts-form"
            onSubmit={onSubmit}
            className={cn('flex flex-col space-y-8')}
          >
            <FormField
              control={form.control}
              name="free_gifts"
              // rules={{ required: 'Please insert the image' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Free Gifts</FormLabel>
                  {/* <FormDescription>Replace the thumbnail</FormDescription> */}
                  <FormControl>
                    <TextEditorComponent content={field.value} onChange={field.onChange} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="free_gifts_worth"
              // rules={{ required: 'Please insert the image' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Free Gifts Worth</FormLabel>
                  {/* <FormDescription>Replace the thumbnail</FormDescription> */}
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="thumbnail"
              // rules={{ required: 'Please insert the image' }}
              render={({ field }) => (
                <>
                  <FormItem>
                    <FormLabel>Free Gift Thumbnail</FormLabel>
                    {/* <FormDescription>Replace the thumbnail</FormDescription> */}
                    <FormControl>
                      <Dropzone
                        multiple={false}
                        accept={{ 'image/*': ['.jpeg', '.jpg', '.png'] }}
                        onDrop={(acceptedFiles) => {
                          if (imageFileWithPath?.path != acceptedFiles[0].path) {
                            const preview = Object.assign(acceptedFiles[0], {
                              preview: URL.createObjectURL(acceptedFiles[0])
                            })

                            form.setValue('thumbnail', preview as File, {
                              shouldValidate: true
                            })
                          }
                        }}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                  <div className="flex flex-col space-y-4 mt-2">
                    <div className="grid grid-cols-[150px_1fr_36px] space-x-2 items-center">
                      {imageFileWithPath?.type.startsWith('image') && (
                        <Image
                          key={imageFileWithPath.path}
                          src={imageFileWithPath.preview ?? ''}
                          height={150}
                          width={150}
                          objectFit="contain"
                          className="rounded-md"
                        />
                      )}
                      {!imageFileWithPath && uploadedImage && (
                        <Image
                          key={uploadedImage}
                          src={uploadedImage ?? ''}
                          height={150}
                          width={150}
                          objectFit="contain"
                          className="rounded-md"
                        />
                      )}
                      {imageFileWithPath && (
                        <div className="flex flex-col">
                          <Label className="text-xs font-normal">{imageFileWithPath.path}</Label>
                          <Label className="text-xs font-normal text-gray-500">
                            {bytesToSize(imageFileWithPath.size)}
                          </Label>
                        </div>
                      )}
                      {!imageFileWithPath && uploadedImage && (
                        <div className="flex flex-col">
                          <Label className="text-xs font-normal">Uploaded</Label>
                        </div>
                      )}
                      {(imageFileWithPath || uploadedImage) && (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={(e) => {
                                e.stopPropagation()

                                if (field.value) {
                                  form.setValue('thumbnail', undefined, {
                                    shouldValidate: true
                                  })
                                } else {
                                  console.log('delete')
                                  form.setValue('free_gift_thumbnail', undefined, {
                                    shouldValidate: true
                                  })
                                }
                              }}
                              className="space-x-2"
                            >
                              <Trash2Icon size="16" />
                              <span>Delete</span>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      )}
                    </div>
                  </div>
                </>
              )}
            />

            <FormField
              control={form.control}
              name="unit_per_day"
              // rules={{ required: 'Please insert the image' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Unit per day</FormLabel>
                  {/* <FormDescription>Replace the thumbnail</FormDescription> */}
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {effectiveDates.map((_effectiveDate, i) => {
              return (
                <div key={i} className="grid grid-cols-2 gap-2">
                  <FormLabel className="flex flex-row gap-2 col-span-2">
                    <span>Effective Date {i + 1}</span>
                    <Trash2Icon
                      size={16}
                      color="red"
                      onClick={(e) => {
                        e.stopPropagation()
                        removeDate(i)
                      }}
                    />
                  </FormLabel>

                  <FormField
                    control={form.control}
                    name={`effective_dates.${i}.start`}
                    key={`effective_dates.${i}.start`}
                    rules={{ required: 'Please insert the start date' }}
                    render={({ field }) => (
                      <FormItem className="flex flex-col col-span-1">
                        <FormControl>
                          {/* <Input type="time" {...field} /> */}
                          <CalendarDatePicker
                            mode="single"
                            includeTime={true}
                            placeholder="Start"
                            buttonLabel={
                              field.value
                                ? DateTime.fromISO(field.value as string).toFormat(
                                    'yyyy-MM-dd HH:mm:ss'
                                  )
                                : undefined
                            }
                            // selected={new Date((field.value as DateTime)?.toISO() ?? new Date())}
                            // onSelect={(e) => {
                            //   field.onChange(DateTime.fromISO(e?.toISOString() ?? '').toISO())
                            // }}
                            value={field.value as string}
                            onChange={(v) => {
                              field.onChange(DateTime.fromISO(v?.toISOString() ?? '').toISO())
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name={`effective_dates.${i}.end`}
                    key={`effective_dates.${i}.end`}
                    rules={{ required: 'Please insert the end date' }}
                    render={({ field }) => (
                      <FormItem className="flex flex-col col-span-1">
                        <FormControl>
                          {/* <Input type="time" {...field} /> */}
                          <CalendarDatePicker
                            mode="single"
                            includeTime={true}
                            placeholder="End"
                            buttonLabel={
                              field.value
                                ? DateTime.fromISO(field.value as string).toFormat(
                                    'yyyy-MM-dd HH:mm:ss'
                                  )
                                : undefined
                            }
                            // selected={new Date((field.value as DateTime)?.toISO() ?? new Date())}
                            // onSelect={(e) => {
                            //   field.onChange(DateTime.fromISO(e?.toISOString() ?? '').toISO())
                            // }}
                            value={field.value as string}
                            onChange={(v) => {
                              field.onChange(DateTime.fromISO(v?.toISOString() ?? '').toISO())
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              )
            })}

            <FormItem className="flex flex-col col-span-2">
              <Button type="button" onClick={() => appendDate({ start: '', end: '' })}>
                Add Effective Date
              </Button>
            </FormItem>

            {tags?.map((_tag, i) => {
              return (
                <div key={i} className="grid grid-cols-2 gap-2">
                  <FormLabel className="flex flex-row gap-2 col-span-2">
                    <span>Tag {i + 1}</span>
                    <Trash2Icon
                      size={16}
                      color="red"
                      onClick={(e) => {
                        e.stopPropagation()
                        // remove tag
                        const tmpTags = form.getValues('tags') ?? []
                        tmpTags.splice(i, 1)
                        form.setValue('tags', tmpTags)
                      }}
                    />
                  </FormLabel>

                  <FormField
                    control={form.control}
                    name={`tags.${i}`}
                    key={`tags.${i}`}
                    rules={{ required: 'Please insert the tag' }}
                    render={({ field }) => (
                      <FormItem className="flex flex-col col-span-2">
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              )
            })}

            <FormItem className="flex flex-col col-span-2">
              <Button
                type="button"
                onClick={(e) => {
                  e.stopPropagation()
                  // append tag
                  const tmpTags = form.getValues('tags') ?? []
                  tmpTags.push('')
                  form.setValue('tags', tmpTags)
                }}
              >
                Add Tag
              </Button>
            </FormItem>

            {/* <FormItem className="flex flex-col col-span-2">
              <Button type="button" onClick={() => appendLabel('')}>
                Add Label
              </Button>
            </FormItem> */}
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}

export default UpdateCampaignProductFreeGiftsDialog
