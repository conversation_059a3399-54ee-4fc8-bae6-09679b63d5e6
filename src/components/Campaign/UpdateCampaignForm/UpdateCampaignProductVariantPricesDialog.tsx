import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from '@/components/ui/accordion'
import { useForm } from 'react-hook-form'
import { cn } from '@/lib/utils'
import { UpdateProductVariantSalePrices } from '@/types/CreateProduct'
import { Form } from '@/components/ui/form'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { PriceList, ProductVariant } from '@/types/Product'
import ProductService from '@/network/services/product'
import { mutate } from 'swr'
import { toast } from '@/components/ui/use-toast'
import CampaignProductVariantSalePricing from './CampaignProductVariantSalePricing'

interface UpdateProductUpdateVariantFormProps extends React.HTMLAttributes<HTMLDivElement> {
  variant: ProductVariant
  priceLists: PriceList[]
  setEditVariant: (value: ProductVariant | null) => void
}

export function UpdateCampaignProductVariantPricesForm({
  className,
  variant,
  priceLists,
  setEditVariant
}: UpdateProductUpdateVariantFormProps) {
  const form = useForm<UpdateProductVariantSalePrices>({
    defaultValues: {
      price_lists: priceLists.map((priceList) => {
        const price = variant.prices.find((price) => price.price_list_id == priceList.id) ?? null
        return {
          id: priceList.id,
          prices: [
            {
              variant_id: variant.id,
              currency_code: 'myr',
              amount: price?.amount ?? 0,
              start_time: price?.start_time,
              end_time: price?.end_time,
              id: price?.id ?? undefined
            }
          ]
        }
      })
    }
  })

  const onSubmit = form.handleSubmit(async (values) => {
    console.log(values)

    try {
      for (const priceList of values.price_lists) {
        for (const index in priceList.prices) {
          if (typeof priceList.prices[index].amount == 'string') {
            priceList.prices[index].amount = Number(priceList.prices[index].amount)
          }
        }
        await ProductService.updatePriceList(priceList.id, { prices: priceList.prices })
      }

      setEditVariant(null)
      // form.reset()
      mutate(
        (key) =>
          typeof key === 'string' && key.startsWith(ProductService.getProduct(variant.product_id))
      )

      toast({
        title: 'Product variant updated',
        variant: 'success'
      })
    } catch (error) {
      console.log(error)
      form.setError('root', {})
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <Dialog
      open={variant ? true : false}
      onOpenChange={(isOpen) => {
        if (!isOpen) {
          setEditVariant(null)
        }
      }}
    >
      <DialogContent className="max-w-3xl" onInteractOutside={(e) => e.preventDefault()}>
        <DialogHeader>
          <DialogTitle className="flex justify-between items-center">
            Update Variant Sale Prices
            <div className="flex space-x-2">
              <DialogClose asChild>
                <Button variant="outline">Cancel</Button>
              </DialogClose>
              <Button
                type="submit"
                form="update-product-create-variant-form"
                // disabled={form.formState.isSubmitting}
                onClick={() => {
                  form.clearErrors()
                }}
              >
                Save
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form
            id="update-product-create-variant-form"
            onSubmit={onSubmit}
            className={cn('flex flex-col space-y-8', className)}
          >
            <Accordion type="multiple" className="w-full" defaultValue={['pricing']}>
              <AccordionItem disabled value="pricing">
                <AccordionTrigger>Pricing</AccordionTrigger>
                <AccordionContent forceMount>
                  <CampaignProductVariantSalePricing variant={variant} priceLists={priceLists} />
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
