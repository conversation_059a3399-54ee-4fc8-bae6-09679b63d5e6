import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import { DialogHeader } from '@/components/ui/dialog'
import { toast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import CampaignService from '@/network/services/campaign'
import { Campaign } from '@/types/Campaign'
import { UpdateCampaignInput } from '@/types/CreateCampaign'
import { Dialog, DialogContent, DialogTitle, DialogClose } from '@/components/ui/dialog'
import { FC, useEffect, useState } from 'react'
import { useForm, useFormContext } from 'react-hook-form'
import { mutate } from 'swr'
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage
} from '@/components/ui/form'
import { ColumnDef, Row } from '@tanstack/react-table'
import IndeterminateCheckbox from '@/components/Table/IndeterminateCheckbox'
import { DataTable, FilterColumn } from '@/components/Table/DataTable'
import { IDataResponse, serialize } from '@/network/request'
import { Image } from '@unpic/react'
import { Product } from '@/types/Product'

const UpdateCampaignProductsDialog: FC<{
  isDialogOpen: boolean
  setIsDialogOpen: (value: boolean) => void
  campaign: Campaign
}> = ({ isDialogOpen, setIsDialogOpen, campaign }) => {
  const form = useForm<UpdateCampaignInput>({
    shouldUseNativeValidation: false,
    defaultValues: {
      event_id: campaign.event_id,
      products: campaign.campaign_products?.every(
        (campaignProduct) =>
          campaignProduct.product_id != null && campaignProduct.product_id != undefined
      )
        ? campaign.campaign_products.map((campaignProduct) => campaignProduct.product_id)
        : undefined,
      shopify_products: campaign.campaign_products?.every(
        (campaignProduct) =>
          campaignProduct.shopify_product_id != null &&
          campaignProduct.shopify_product_id != undefined
      )
        ? campaign.campaign_products.map((campaignProduct) => campaignProduct.shopify_product_id)
        : undefined
    }
  })
  const eventId = form.watch('event_id')
  const [productType] = useState<'event_products' | 'shopify_products'>('event_products')

  useEffect(() => {
    if (isDialogOpen || campaign) {
      form.reset({
        event_id: campaign.event_id,
        products: campaign.campaign_products?.every(
          (campaignProduct) =>
            campaignProduct.product_id != null && campaignProduct.product_id != undefined
        )
          ? campaign.campaign_products.map((campaignProduct) => campaignProduct.product_id)
          : undefined,
        shopify_products: campaign.campaign_products?.every(
          (campaignProduct) =>
            campaignProduct.shopify_product_id != null &&
            campaignProduct.shopify_product_id != undefined
        )
          ? campaign.campaign_products.map((campaignProduct) => campaignProduct.shopify_product_id)
          : undefined
      })
    }
  }, [isDialogOpen, campaign])

  useEffect(() => {
    if (!eventId) {
      form.unregister('products')
      form.unregister('shopify_products')
    }
  }, [eventId])

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      if (productType == 'event_products') {
        delete values.shopify_products
      } else {
        delete values.products
      }
      console.log(values)
      const { data: response } = await CampaignService.addOrRemoveCampaignProducts(
        campaign.id,
        values
      )
      if (response.success) {
        setIsDialogOpen(false)
        mutate(
          (key) =>
            typeof key === 'string' && key.startsWith(CampaignService.getCampaign(campaign.id))
        )
        toast({
          title: 'Campaign updated',
          variant: 'success'
        })
      }

      form.reset()
    } catch (error) {
      console.error(error)
      form.setError('root', {})
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })
  return (
    <Dialog
      open={isDialogOpen}
      onOpenChange={(isOpen) => {
        setIsDialogOpen(isOpen)
      }}
    >
      <DialogContent className="max-w-5xl max-h-screen overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex justify-between items-center">
            Edit Campaign Event Products
            <div className="flex space-x-2">
              <DialogClose asChild>
                <Button variant="outline">Cancel</Button>
              </DialogClose>
              <Button
                type="submit"
                form="update-campaign-products-form"
                disabled={form.formState.isSubmitting}
                onClick={() => {
                  form.clearErrors()
                }}
              >
                {form.formState.isSubmitting && (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                )}
                Save
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form
            id="update-campaign-products-form"
            onSubmit={onSubmit}
            className={cn('flex flex-col space-y-8')}
          >
            {/* <FormField
              control={form.control}
              name="event_id"
              render={() => (
                <FormItem className="flex flex-col col-span-2">
                  <FormLabel>Event</FormLabel>
                  <FormControl>
                    <EventOptionList />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            /> */}

            <FormItem>
              <FormLabel>Products</FormLabel>
              {/* <div className="flex flex-row space-x-4">
                <span
                  className="flex flex-row space-x-1"
                  onClick={() => {
                    setProductType('event_products')
                  }}
                >
                  <IndeterminateCheckbox readOnly checked={productType == 'event_products'} />
                  <FormLabel>Event Products</FormLabel>
                </span>
                <span
                  className="flex flex-row space-x-1"
                  onClick={() => {
                    setProductType('shopify_products')
                  }}
                >
                  <IndeterminateCheckbox readOnly checked={productType == 'shopify_products'} />
                  <FormLabel>Shopify Products</FormLabel>
                </span>
              </div> */}

              {eventId && productType == 'event_products' && (
                <FormField
                  control={form.control}
                  name={'products'}
                  render={() => (
                    <FormItem className="flex flex-col col-span-2">
                      <FormControl>
                        <EventProductOptionList />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
              {eventId && productType == 'shopify_products' && (
                <FormField
                  control={form.control}
                  name={'shopify_products'}
                  render={() => (
                    <FormItem className="flex flex-col col-span-2">
                      <FormControl>
                        <ShopifyProductOptionList />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
            </FormItem>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}

// const EventOptionList = () => {
//   const form = useFormContext<UpdateCampaignInput>()
//   const initialEventId = form.getValues('event_id')

//   const columns: ColumnDef<Event>[] = [
//     {
//       id: 'select',
//       cell: ({ row, table }) => (
//         <div className="px-1">
//           <IndeterminateCheckbox
//             {...{
//               checked: row.getIsSelected(),
//               disabled: !row.getCanSelect(),
//               indeterminate: row.getIsSomeSelected(),
//               onChange: () => {
//                 // Reset all selection 1st to allow 1 selected only
//                 table.resetRowSelection()
//                 row.toggleSelected()
//               }
//             }}
//           />
//         </div>
//       )
//     },
//     {
//       accessorKey: 'title',
//       header: 'Title'
//     },
//     {
//       accessorKey: 'location',
//       header: 'Location'
//     },
//     {
//       accessorKey: 'from',
//       header: 'From',
//       cell: (props) => <span>{props.getValue<DateTime>().toFormat('yyyy-MM-dd')}</span>
//     },
//     {
//       accessorKey: 'to',
//       header: 'To',
//       cell: (props) => <span>{props.getValue<DateTime>().toFormat('yyyy-MM-dd')}</span>
//     }
//     // TODO: dialog to edit row
//     // columnHelper.display({
//     //   id: "actions",
//     //   cell: (props) => <RowActions row={props.row} />,
//     // }),
//   ]

//   const filterColumns: FilterColumn[] = [
//     { columnKey: 'title', header: 'Event', dataType: 'string' }
//   ]

//   return (
//     <div className="grid gap-4">
//       {/* Selectable table for all options */}
//       <DataTable<Event, unknown, IDataResponse<Event>>
//         columns={columns}
//         filterColumns={filterColumns}
//         initialSelected={initialEventId ? [initialEventId] : []}
//         swrService={EventService.getEvents}
//         toRow={EventService.toRow as any}
//         toPaginate={EventService.toPaginate}
//         setSelectedRows={(selectedRows) => form.setValue('event_id', selectedRows[0])}
//         onRowClick={(row: Row<Event>, table: Table<Event>) => {
//           // Reset all selection 1st to allow 1 selected only
//           table.resetRowSelection()
//           row.toggleSelected()
//         }}
//       />
//     </div>
//   )
// }

const EventProductOptionList = () => {
  const form = useFormContext<UpdateCampaignInput>()
  const selectedEventId = form.watch('event_id')
  const initialSelected = form.getValues('products')

  const columns: ColumnDef<Product>[] = [
    {
      id: 'select',
      header: ({ table }) => (
        <IndeterminateCheckbox
          {...{
            checked: table.getIsAllRowsSelected(),
            indeterminate: table.getIsSomeRowsSelected(),
            onChange: table.getToggleAllPageRowsSelectedHandler()
          }}
        />
      ),
      cell: ({ row }) => (
        <div className="px-1">
          <IndeterminateCheckbox
            {...{
              checked: row.getIsSelected(),
              disabled: !row.getCanSelect(),
              indeterminate: row.getIsSomeSelected(),
              onChange: () => {
                row.toggleSelected()
              }
            }}
          />
        </div>
      )
    },
    {
      accessorKey: 'title',
      header: 'Product',
      cell: (props) => {
        // add product title to cache
        props.row._valuesCache['title'] = props.row.original.title
        props.row._valuesCache['thumbnail'] = props.row.original.thumbnail
        return (
          <span className="flex flex-row items-center gap-4">
            {(props.row.getValue('thumbnail') as string) && (
              <Image
                layout="constrained"
                width={50}
                height={50}
                src={props.row.getValue('thumbnail')}
              />
            )}
            {props.row.getValue('title')}
          </span>
        )
      }
    },
    {
      accessorKey: 'store.name',
      header: 'Store'
    },
    {
      accessorKey: 'product_collection.title',
      header: 'Brands'
    }
    // TODO: dialog to edit row
    // columnHelper.display({
    //   id: "actions",
    //   cell: (props) => <RowActions row={props.row} />,
    // }),
  ]

  const filterColumns: FilterColumn[] = [
    { columnKey: 'title', header: 'Product', dataType: 'string' },
    { columnKey: 'store_name', header: 'Store', dataType: 'string' },
    { columnKey: 'product_collection_title', header: 'Brands', dataType: 'string' }
  ]

  return (
    <div className="grid gap-4">
      {/* Selectable table for all options */}
      {selectedEventId ? (
        <DataTable<Product, unknown, IDataResponse<Product>>
          columns={columns}
          initialSelected={initialSelected}
          filterColumns={filterColumns}
          swrService={serialize(CampaignService.getCampaignEventProductOptions(selectedEventId), {
            'status[0]': 'published'
          })}
          toRow={CampaignService.toRow as any}
          toPaginate={CampaignService.toPaginate as any}
          setSelectedRows={(selectedRows) => {
            form.setValue(
              'products',
              selectedRows.length > 0 ? (selectedRows as number[]) : undefined
            )
          }}
          onRowClick={(row: Row<Product>) => {
            row.toggleSelected()
          }}
        />
      ) : (
        <div className="space-y-4">
          <div className="rounded-md border">
            <span>Choose an event</span>
          </div>
        </div>
      )}
    </div>
  )
}

const ShopifyProductOptionList = () => {
  const form = useFormContext<UpdateCampaignInput>()
  const selectedEventId = form.watch('event_id')
  // const initialSelected = form.getValues('shopify_products')

  // const columns: ColumnDef<EventProduct>[] = [
  //   {
  //     id: 'select',
  //     header: ({ table }) => (
  //       <IndeterminateCheckbox
  //         {...{
  //           checked: table.getIsAllRowsSelected(),
  //           indeterminate: table.getIsSomeRowsSelected(),
  //           onChange: table.getToggleAllPageRowsSelectedHandler()
  //         }}
  //       />
  //     ),
  //     cell: ({ row }) => (
  //       <div className="px-1">
  //         <IndeterminateCheckbox
  //           {...{
  //             checked: row.getIsSelected(),
  //             disabled: !row.getCanSelect(),
  //             indeterminate: row.getIsSomeSelected(),
  //             onChange: () => {
  //               row.toggleSelected()
  //             }
  //           }}
  //         />
  //       </div>
  //     )
  //   },
  //   {
  //     accessorKey: 'product.title',
  //     header: 'Product',
  //     cell: (props) => {
  //       // add product title to cache
  //       props.row._valuesCache['product_title'] = props.row.original.product?.title
  //       props.row._valuesCache['product_thumbnail'] = props.row.original.product?.thumbnail
  //       return (
  //         <span className="flex flex-row items-center gap-4">
  //           <Image
  //             layout="constrained"
  //             width={50}
  //             height={50}
  //             src={props.row.getValue('product_thumbnail')}
  //           />
  //           {props.row.getValue('product_title')}
  //         </span>
  //       )
  //     }
  //   },
  //   {
  //     accessorKey: 'store.name',
  //     header: 'Store'
  //   },
  //   {
  //     accessorKey: 'store.brands',
  //     header: 'Brands'
  //   }
  //   // TODO: dialog to edit row
  //   // columnHelper.display({
  //   //   id: "actions",
  //   //   cell: (props) => <RowActions row={props.row} />,
  //   // }),
  // ]

  // const filterColumns: FilterColumn[] = [
  //   { columnKey: 'product_title', header: 'Product', dataType: 'string' },
  //   { columnKey: 'store_name', header: 'Store', dataType: 'string' },
  //   { columnKey: 'store_brands', header: 'Brands', dataType: 'string' }
  // ]

  return (
    <div className="grid gap-4">
      {/* Selectable table for all options */}
      {selectedEventId ? (
        // <DataTable<EventProduct, unknown, IDataResponse<EventProduct>>
        //   columns={columns}
        //   initialSelected={initialSelected}
        //   filterColumns={filterColumns}
        //   // TODO: shopify products API
        //   swrService={CampaignService.getCampaignEventProductOptions(selectedEventId)}
        //   toRow={CampaignService.toRow as any}
        //   toPaginate={CampaignService.toPaginate as any}
        //   setSelectedRows={(selectedRows) => {
        //     form.setValue('shopify_products', selectedRows.length > 0 ? selectedRows : undefined)
        //   }}
        //   onRowClick={(row: Row<EventProduct>) => {
        //     row.toggleSelected()
        //   }}
        // />
        <>Not implemented</>
      ) : (
        <div className="space-y-4">
          <div className="rounded-md border">
            <span>Choose an event</span>
          </div>
        </div>
      )}
    </div>
  )
}

export default UpdateCampaignProductsDialog
