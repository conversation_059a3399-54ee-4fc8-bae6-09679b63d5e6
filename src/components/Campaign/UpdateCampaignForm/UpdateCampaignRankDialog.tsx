import { Icons } from '@/components/icons'
import { But<PERSON> } from '@/components/ui/button'
import { DialogHeader } from '@/components/ui/dialog'
import { toast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import { Dialog, DialogContent, DialogTitle, DialogClose } from '@/components/ui/dialog'
import { FC, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { mutate } from 'swr'
import {
  Form,
  FormField,
  FormItem,
  FormControl,
  FormMessage,
  FormLabel
} from '@/components/ui/form'
import { Campaign } from '@/types/Campaign'
import { UpdateCampaignInput } from '@/types/CreateCampaign'
import CampaignService from '@/network/services/campaign'
import { Input } from '@/components/ui/input'

const UpdateCampaignRankDialog: FC<{
  isDialogOpen: boolean
  setIsDialogOpen: (value: boolean) => void
  campaign: Campaign
}> = ({ isDialogOpen, setIsDialogOpen, campaign }) => {
  const form = useForm<UpdateCampaignInput>({
    shouldUseNativeValidation: false,
    defaultValues: {
      rank: campaign.rank
    }
  })

  useEffect(() => {
    if (isDialogOpen || campaign) {
      form.reset({
        rank: campaign.rank
      })
    }
  }, [campaign, isDialogOpen])

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      console.log(values)

      await CampaignService.sortDealCampaign(campaign.id, { ...values })
      setIsDialogOpen(false)
      mutate((key) => typeof key === 'string' && key.startsWith(CampaignService.getCampaigns))
      toast({
        title: 'Campaign rank updated',
        variant: 'success'
      })
    } catch (error) {
      console.error(error)
      form.setError('root', {})
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <Dialog
      open={isDialogOpen}
      onOpenChange={(isOpen) => {
        setIsDialogOpen(isOpen)
      }}
    >
      <DialogContent
        className="max-w-2xl max-h-screen overflow-y-auto"
        onClick={(e) => e.stopPropagation()}
      >
        <DialogHeader>
          <DialogTitle className="flex justify-between items-center">
            Edit Rank
            <div className="flex space-x-2">
              <DialogClose asChild>
                <Button variant="outline">Cancel</Button>
              </DialogClose>
              <Button
                type="submit"
                form="update-rank-form"
                disabled={form.formState.isSubmitting}
                onClick={() => {
                  form.clearErrors()
                }}
              >
                {form.formState.isSubmitting && (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                )}
                Save
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form id="update-rank-form" onSubmit={onSubmit} className={cn('flex flex-col space-y-8')}>
            <FormField
              control={form.control}
              name="rank"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Rank</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}

export default UpdateCampaignRankDialog
