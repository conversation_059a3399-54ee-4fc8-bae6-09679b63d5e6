import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import { DialogHeader } from '@/components/ui/dialog'
import { toast } from '@/components/ui/use-toast'
import { bytesToSize, cn, convertValuesToFormData } from '@/lib/utils'
import CampaignService from '@/network/services/campaign'
import { Campaign } from '@/types/Campaign'
import { UpdateCampaignInput } from '@/types/CreateCampaign'
import { Dialog, DialogContent, DialogTitle, DialogClose } from '@/components/ui/dialog'
import { FC, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { mutate } from 'swr'
import {
  Form,
  FormField,
  FormItem,
  FormControl,
  FormMessage,
  FormLabel
} from '@/components/ui/form'
import { Dropzone } from '@/components/Form/Dropzone'
import { FileWithPath } from 'react-dropzone'
import { Image } from '@unpic/react'
import { Label } from '@/components/ui/label'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { MoreHorizontal, Trash2Icon } from 'lucide-react'
import { Input } from '@/components/ui/input'
import OrSeparator from '@/components/ui/orSeparator'

const UpdateCampaignThumbnailDialog: FC<{
  isDialogOpen: boolean
  setIsDialogOpen: (value: boolean) => void
  campaign: Campaign
}> = ({ isDialogOpen, setIsDialogOpen, campaign }) => {
  const form = useForm<UpdateCampaignInput>({
    shouldUseNativeValidation: false,
    defaultValues: {
      thumbnail: undefined
    }
  })
  const imagesFile = form.watch('thumbnail')
  const imageFileWithPath = imagesFile
    ? (imagesFile as unknown as FileWithPath & { preview: string })
    : undefined

  useEffect(() => {
    if (isDialogOpen || campaign) {
      form.reset({
        thumbnail: undefined
      })
    }
  }, [campaign, isDialogOpen])

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      console.log(values)
      // remove whichever thumbnail not using
      if (values.thumbnail_url) {
        delete values.thumbnail
      } else if (values.thumbnail) {
        delete values.thumbnail_url
      }

      const formData = new FormData()
      convertValuesToFormData(formData, values)
      const { data: response } = await CampaignService.updateCampaign(campaign.id, formData)

      if (response.success) {
        setIsDialogOpen(false)
        mutate(
          (key) =>
            typeof key === 'string' && key.startsWith(CampaignService.getCampaign(campaign.id))
        )
        toast({
          title: 'Campaign updated',
          variant: 'success'
        })
      }
    } catch (error) {
      console.error(error)
      form.setError('root', {})
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <Dialog
      open={isDialogOpen}
      onOpenChange={(isOpen) => {
        setIsDialogOpen(isOpen)
      }}
    >
      <DialogContent className="max-w-2xl max-h-screen overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex justify-between items-center">
            Edit Thumbnail
            <div className="flex space-x-2">
              <DialogClose asChild>
                <Button variant="outline">Cancel</Button>
              </DialogClose>
              <Button
                type="submit"
                form="update-thumbnail-form"
                disabled={form.formState.isSubmitting}
                onClick={() => {
                  form.clearErrors()
                }}
              >
                {form.formState.isSubmitting && (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                )}
                Save
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form
            id="update-thumbnail-form"
            onSubmit={onSubmit}
            className={cn('flex flex-col space-y-8')}
          >
            <FormField
              control={form.control}
              name="thumbnail_url"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Image URL</FormLabel>
                  <FormControl>
                    <Input placeholder="Image Url" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <OrSeparator />

            <FormField
              control={form.control}
              name="thumbnail"
              render={({ field }) => (
                <>
                  <FormItem>
                    <FormLabel>Upload Image</FormLabel>
                    <FormControl>
                      <Dropzone
                        multiple={false}
                        onDrop={(acceptedFiles) => {
                          if (imageFileWithPath?.path != acceptedFiles[0].path) {
                            const preview = Object.assign(acceptedFiles[0], {
                              preview: URL.createObjectURL(acceptedFiles[0])
                            })

                            form.setValue('thumbnail', preview as File, {
                              shouldValidate: true
                            })
                          }
                        }}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                  {imageFileWithPath && (
                    <div className="flex flex-col space-y-4 mt-2">
                      <div className="grid grid-cols-[150px_1fr_36px] space-x-2 items-center">
                        {imageFileWithPath.type.startsWith('image') && (
                          <Image
                            key={imageFileWithPath.path}
                            src={imageFileWithPath.preview ?? ''}
                            height={150}
                            width={150}
                            objectFit="contain"
                            className="rounded-md"
                          />
                        )}
                        {imageFileWithPath.type.startsWith('video') && (
                          <video
                            // controls
                            key={imageFileWithPath.path}
                            src={imageFileWithPath.preview ?? ''}
                            height={150}
                            width={150}
                            className="rounded-md"
                          />
                        )}
                        <div className="flex flex-col">
                          <Label className="text-xs font-normal">{imageFileWithPath.path}</Label>
                          <Label className="text-xs font-normal text-gray-500">
                            {bytesToSize(imageFileWithPath.size)}
                          </Label>
                        </div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={(e) => {
                                e.stopPropagation()

                                form.setValue('thumbnail', undefined, {
                                  shouldValidate: true
                                })
                              }}
                              className="space-x-2"
                            >
                              <Trash2Icon size="16" />
                              <span>Delete</span>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  )}
                </>
              )}
            />
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}

export default UpdateCampaignThumbnailDialog
