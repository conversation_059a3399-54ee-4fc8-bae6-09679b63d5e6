import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import { DialogHeader } from '@/components/ui/dialog'
import { toast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import CampaignService from '@/network/services/campaign'
import { BulkUpdateCampaignProductInput, Campaign } from '@/types/Campaign'
import { Dialog, DialogContent, DialogTitle, DialogClose } from '@/components/ui/dialog'
import { FC } from 'react'
import { useForm } from 'react-hook-form'
import { mutate } from 'swr'
import {
  Form,
  FormField,
  FormItem,
  FormControl,
  FormMessage,
  FormLabel
} from '@/components/ui/form'
import { Dropzone } from '@/components/Form/Dropzone'
import { FileWithPath } from 'react-dropzone'
import { Card } from '@/components/ui/card'

const UploadCampaignProductCSVDialog: FC<{
  isDialogOpen: boolean
  setIsDialogOpen: (value: boolean) => void
  campaign: Campaign
}> = ({ isDialogOpen, setIsDialogOpen, campaign }) => {
  const form = useForm<BulkUpdateCampaignProductInput>({
    shouldUseNativeValidation: false
  })
  const csvFile = form.watch('csv_file')
  const csvFileWithPath = csvFile
    ? (csvFile as unknown as FileWithPath & { preview: string })
    : undefined

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      console.log(values)
      const formdata = new FormData()
      formdata.append('csv_file', values.csv_file)

      const { data: response } = await CampaignService.bulkImportCampaignProduct(
        campaign.id,
        formdata
      )

      if (response.success) {
        setIsDialogOpen(false)
        mutate(
          (key) =>
            typeof key === 'string' && key.startsWith(CampaignService.getCampaign(campaign.id))
        )
        toast({
          title: 'Campaign Products updated',
          variant: 'success'
        })
      }
    } catch (error) {
      console.error(error)
      form.setError('root', {})
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <Dialog
      open={isDialogOpen}
      onOpenChange={(isOpen) => {
        setIsDialogOpen(isOpen)
      }}
    >
      <DialogContent className="max-w-2xl max-h-screen overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex justify-between items-center">
            Bulk Update Campaign Products with CSV
            <div className="flex space-x-2">
              <DialogClose asChild>
                <Button variant="outline">Cancel</Button>
              </DialogClose>
              <Button
                type="submit"
                form="update-campaign-product-free-gifts-form"
                disabled={form.formState.isSubmitting}
                onClick={() => {
                  form.clearErrors()
                }}
              >
                {form.formState.isSubmitting && (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                )}
                Save
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form
            id="update-campaign-product-free-gifts-form"
            onSubmit={onSubmit}
            className={cn('flex flex-col space-y-8')}
          >
            <FormField
              control={form.control}
              name="csv_file"
              // rules={{ required: 'Please insert the image' }}
              render={({ field }) => (
                <>
                  <FormItem>
                    <FormLabel>CSV File</FormLabel>
                    {/* <FormDescription>Replace the thumbnail</FormDescription> */}
                    <FormControl>
                      <Dropzone
                        description="Drop your file here, or click to browse"
                        multiple={false}
                        accept={{ 'text/*': ['.csv'] }}
                        onDrop={(acceptedFiles) => {
                          if (csvFileWithPath?.path != acceptedFiles[0].path) {
                            const preview = Object.assign(acceptedFiles[0], {
                              preview: URL.createObjectURL(acceptedFiles[0])
                            })

                            form.setValue('csv_file', preview as File, {
                              shouldValidate: true
                            })
                          }
                        }}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                  <div className="flex flex-col space-y-4 mt-2">
                    <div className="grid grid-cols-[150px_1fr_36px] space-x-2 items-center">
                      {csvFileWithPath && (
                        <Card className="rounded-md w-full p-8">{csvFileWithPath.path}</Card>
                      )}
                    </div>
                  </div>
                </>
              )}
            />
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}

export default UploadCampaignProductCSVDialog
