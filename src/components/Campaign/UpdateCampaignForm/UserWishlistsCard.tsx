import { <PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ontent, CardFooter } from '@/components/ui/card'
import { Campaign } from '@/types/Campaign'
import { FC, useState } from 'react'
import UpdateCampaignProductsDialog from './UpdateCampaignProductsDialog'
import { DataTable, FilterColumn } from '@/components/Table/DataTable'
import { ColumnDef, Row, createColumnHelper } from '@tanstack/react-table'
import { PricedWishlist, WishlistResponse } from '@/types/Wishlist'
import WishlistService from '@/network/services/wishlist'
import { useToast } from '@/components/ui/use-toast'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import { DownloadIcon, MoreHorizontal } from 'lucide-react'
import { DateTime } from 'luxon'

const UserWishlistsCard: FC<{
  campaign: Campaign
}> = ({ campaign }) => {
  const [isDialogO<PERSON>, setIsDialogOpen] = useState(false)

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <CardTitle className="h-8 flex items-center">User Wishlists</CardTitle>
          {/* <div className="flex flex-row space-x-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" forceMount>
                <DropdownMenuGroup>
                  <DropdownMenuItem
                    onClick={async (event) => {
                      event.stopPropagation()
                      setIsDialogOpen(true)
                    }}
                    className="space-x-2"
                  >
                    <EditIcon size="16" />
                    <span>Edit</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    disabled={(campaign.campaign_products?.length ?? 0) <= 0}
                    onClick={async (event) => {
                      event.stopPropagation()

                      try {
                        const { data: response } = await CampaignService.generateBooklet(
                          campaign.id
                        )

                        if (response.success) {
                          window.open(response.data.url)
                        }
                      } catch (error) {
                        console.log(error)
                      }
                    }}
                    className="space-x-2"
                  >
                    <DownloadIcon size="16" />
                    <span>Download booklet</span>
                  </DropdownMenuItem>
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          </div> */}
        </CardHeader>
        <CardContent className="flex flex-col space-y-4">
          {campaign.campaign_products && campaign.campaign_products.length > 0 && (
            <div>
              <UserWishlistsTable campaign={campaign} />
            </div>
          )}
        </CardContent>
        <CardFooter></CardFooter>
      </Card>

      <UpdateCampaignProductsDialog {...{ isDialogOpen, setIsDialogOpen, campaign }} />
    </>
  )
}

const UserWishlistsTable = ({ campaign }: { campaign: Campaign }) => {
  // column declaration for DataTable
  const columnHelper = createColumnHelper<PricedWishlist>()

  const columnFilter: FilterColumn[] = [
    {
      columnKey: 'email',
      header: 'Search',
      dataType: 'string'
    }
  ]

  function wishlistDataProcessing(wishlistResponse: PricedWishlist) {
    const wishlists: any[] = []
    const wishlist: any = {
      title: wishlistResponse.title,
      description: campaign.metadata?.deal_wishlist_description ?? '',
      booths: {}
    }

    for (let key in wishlistResponse.hall_and_booth) {
      // by hall and booth, might have one or two brands inside
      const w_hallBooth_variants = wishlistResponse.hall_and_booth[key]

      const brands: any = {}
      w_hallBooth_variants.forEach((variant) => {
        const collection = variant.product.collection.title
        const product = variant.product.title
        const campaignProduct = campaign.campaign_products?.find(
          (campaignProduct) => campaignProduct.product?.medusa_product_id === variant.product.id
        )

        if (!brands[collection]) {
          brands[collection] = {}
        }

        if (!brands[collection][product]) {
          brands[collection][product] = {
            thumbnail: variant.product.thumbnail,
            variants: [] as any[],
            tags: campaignProduct?.tags ?? [],
            original_price: 0,
            calculated_price: 0
          }
        }

        brands[collection][product].original_price += variant.original_price * variant.quantity
        brands[collection][product].calculated_price += variant.calculated_price * variant.quantity
        brands[collection][product].variants.push({
          title: variant.title,
          quantity: variant.quantity,
          original_price: variant.original_price,
          calculated_price: variant.calculated_price
        })
      })

      if (!wishlist.booths[key]) {
        wishlist.booths[key] = brands
      }
    }

    wishlists.push(wishlist)

    const fromDate = DateTime.fromISO(campaign.event?.from as string)
    const toDate = DateTime.fromISO(campaign.event?.to as string)
    const startTime = DateTime.fromISO(campaign.event?.from as string).toFormat('HH:mm')
    const endTime = DateTime.fromISO(campaign.event?.to as string).toFormat('HH:mm')

    const fromDateFormat =
      fromDate.year !== toDate.year
        ? 'dd/MM/yyyy'
        : fromDate.month !== toDate.month
        ? 'dd/MM'
        : 'dd'

    const fromDateString = fromDate.toFormat(fromDateFormat)
    const toDateString = toDate.toFormat('dd/MM/yyyy')

    const data: any = {
      event: {
        title: campaign.event?.title,
        datetime: `${fromDateString}-${toDateString}, ${startTime}-${endTime}`,
        location: campaign.event?.location
      },
      customer: wishlistResponse.customer.first_name + ' ' + wishlistResponse.customer.last_name,
      wishlists
    }

    return data
  }

  const RowActions: FC<{ row: Row<PricedWishlist> }> = ({ row }) => {
    const { toast } = useToast()

    return (
      <div className="w-full flex justify-end">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem
              className="space-x-2"
              onClick={async (e) => {
                e.stopPropagation()

                const data = wishlistDataProcessing(row.original)

                try {
                  const { data: wishlist } = await WishlistService.generateWishlist(data)

                  if (wishlist.data.success) {
                    window.open(wishlist.data.data.url)
                    toast({
                      title: 'Download successfully',
                      variant: 'success'
                    })
                  } else {
                    toast({
                      title: 'Action failed, please try again',
                      variant: 'destructive'
                    })
                  }
                } catch (error) {
                  console.log(error)
                  toast({
                    title: 'Action failed, please try again',
                    variant: 'destructive'
                  })
                }
              }}
            >
              <DownloadIcon size="16" />
              <span>Download wishlist</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    )
  }

  const columns: ColumnDef<PricedWishlist>[] = [
    {
      id: 'email',
      accessorKey: 'customer.email',
      header: 'Email',
      enableSorting: true
    },
    columnHelper.display({
      id: 'actions',
      cell: (props) => <RowActions row={props.row} />
    })
  ]

  return (
    <>
      <DataTable<PricedWishlist, unknown, WishlistResponse>
        columns={columns}
        filterColumns={columnFilter}
        filterParam="q"
        swrService={WishlistService.getWishlists(campaign.id)}
        pageParam="page"
        limitParam="limit"
        sortParam="sort"
        sortColumns={['email']}
        toRow={WishlistService.toRow}
        toPaginate={WishlistService.toPaginate}
        idDataType="string"
      />
    </>
  )
}

export default UserWishlistsCard
