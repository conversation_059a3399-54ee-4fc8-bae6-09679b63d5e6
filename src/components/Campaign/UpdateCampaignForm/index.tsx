import React, { FC, useState } from 'react'

import { Campaign, CampaignProduct } from '@/types/Campaign'
import { CampaignCard } from './CampaignCard'
import { CampaignFieldsCard } from './CampaignFieldsCard'
import { CampaignThumbnailCard } from './CampaignThumbnailCard'
import { CampaignMediaCard } from './CampaignMediaCard'
import CampaignProductsCard from './CampaignProductsCard'
import CampaignProductVariantsCard from './CampaignProductVariantsCard'
import UserWishlistsCard from './UserWishlistsCard'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Sheet, SheetContent, SheetHeader } from '@/components/ui/sheet'
import CampaignSeoCard from './CampaignSeoCard'

interface CampaignFormProps extends React.HTMLAttributes<HTMLDivElement> {
  initialValue: Campaign
}

export const UpdateCampaignForm: FC<CampaignFormProps> = ({ initialValue }) => {
  const [campaignProductView, setCampaignProductView] = useState<CampaignProduct>()

  return (
    <div className="flex flex-col space-y-4">
      <Tabs defaultValue="deal" className="space-y-4">
        <TabsList>
          <TabsTrigger value="deal">{initialValue.event_id ? 'Deal' : 'Campaign'}</TabsTrigger>
          <TabsTrigger value="seo">SEO</TabsTrigger>
          {(initialValue.campaign_products?.length ?? 0) > 0 && (
            <TabsTrigger value="wishlist">Wishlist</TabsTrigger>
          )}
          {initialValue.event_id && <TabsTrigger value="products">Products</TabsTrigger>}
        </TabsList>

        <TabsContent value="deal" className="space-y-4">
          <div className="grid grid-cols-[4fr_2fr] gap-4">
            <div className="flex flex-col space-y-4">
              <CampaignCard campaign={initialValue} />
            </div>

            <div className="flex flex-col space-y-4">
              <CampaignThumbnailCard campaign={initialValue} />
              <CampaignMediaCard campaign={initialValue} />
            </div>
          </div>

          <div className="grid grid-cols-[2fr_2fr] gap-4">
            {initialValue.fields?.map((field) => {
              return <CampaignFieldsCard field={field} />
            })}
          </div>
        </TabsContent>
        <TabsContent value="seo">
          <div className="space-y-4">
            <CampaignSeoCard campaign={initialValue} />
          </div>
        </TabsContent>
        {initialValue.campaign_products && (
          <TabsContent value="wishlist">
            <div className="space-y-4">
              <UserWishlistsCard campaign={initialValue} />
            </div>
          </TabsContent>
        )}
        {initialValue.event_id && (
          <TabsContent value="products">
            <div className="space-y-4">
              <CampaignProductsCard
                campaign={initialValue}
                campaignProductView={campaignProductView}
                setCampaignProductView={setCampaignProductView}
              />

              <Sheet
                open={campaignProductView != undefined}
                onOpenChange={(isOpen) => {
                  if (!isOpen) {
                    setCampaignProductView(undefined)
                  }
                }}
              >
                <SheetContent className="sm:max-w-[1000px]">
                  <SheetHeader>
                    {campaignProductView && (
                      <CampaignProductVariantsCard
                        campaign={initialValue}
                        campaignProduct={campaignProductView}
                      />
                    )}
                  </SheetHeader>
                </SheetContent>
              </Sheet>
            </div>
          </TabsContent>
        )}
      </Tabs>
    </div>
  )
}
