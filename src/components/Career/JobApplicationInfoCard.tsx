import React, { FC, useMemo } from 'react'
import { Card, CardContent, CardHeader, CardTitle, Content, Title } from '../ui/card'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu'
import { Button } from '../ui/button'
import { cn, statusToColor } from '@/lib/utils'
import { toast } from '../ui/use-toast'
import { mutate } from 'swr'
import { ApplicationStatus, JobApplication } from '@/types/Career'
import CareerService from '@/network/services/career'
import { title } from 'radash'

const JobApplicationInfoCard: FC<{
  application: JobApplication
}> = ({ application }) => {
  return (
    <>
      <Card>
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <CardTitle>Application Form</CardTitle>

          <div className="flex flex-row space-x-2">
            <ApplicationStatusBadge {...{ application }} />
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-2">
            {application.answer.map((item) => {
              switch (item.q) {
                case 'cv':
                case 'cover_letter':
                case 'portfolio':
                  return (
                    <React.Fragment key={item.q}>
                      <Title>{item.q}</Title>
                      <Content className="break-all">
                        {item.a ? (
                          <a href={item.a} target="_blank">
                            View
                          </a>
                        ) : (
                          '-'
                        )}
                      </Content>
                    </React.Fragment>
                  )
                case 'expected_salary':
                  return (
                    <React.Fragment key={item.q}>
                      <Title>{item.q}</Title>
                      <Content className="break-all">RM{item.a}</Content>
                    </React.Fragment>
                  )
                default:
                  return (
                    <React.Fragment key={item.q}>
                      <Title>{item.q}</Title>
                      <Content className="break-all">{item.a}</Content>
                    </React.Fragment>
                  )
              }
            })}
          </div>
        </CardContent>
      </Card>
    </>
  )
}

const ApplicationStatusBadge: FC<{ application: JobApplication }> = ({ application }) => {
  const color = useMemo(() => {
    return statusToColor(application.status)
  }, [application.status])

  const updateStatus = async (application: JobApplication, data: { status: ApplicationStatus }) => {
    try {
      await CareerService.updateApplication(application.id, data)
      mutate(
        (key) =>
          typeof key === 'string' && key.startsWith(CareerService.getJobApplication(application.id))
      )

      toast({
        title: `Status updated`,
        variant: 'success'
      })
    } catch (error) {
      console.log(error)
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="h-8">
          <div className="flex items-center space-x-2">
            <div className={cn('h-1.5 w-1.5 self-center rounded-full', color)} />
            <span className="capitalize text-xs">{title(application.status)}</span>
          </div>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" forceMount>
        <DropdownMenuGroup>
          {Object.values(ApplicationStatus).map((status) => (
            <DropdownMenuItem
              onClick={async (event) => {
                event.stopPropagation()
                await updateStatus(application, { status: status })
              }}
              className="space-x-2"
            >
              <div
                className={cn('h-1.5 w-1.5 self-center rounded-full', statusToColor(status))}
              />
              <span className="capitalize text-xs">{title(status)}</span>
            </DropdownMenuItem>
          ))}
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

export default JobApplicationInfoCard
