import { ColumnDef, Row } from '@tanstack/react-table'
import { DataTable, FilterColumn } from '@/components/Table/DataTable'
// import {
//   DropdownMenu,
//   DropdownMenuTrigger,
//   DropdownMenuContent,
//   DropdownMenuItem,
//   DropdownMenuGroup
// } from '@/components/ui/dropdown-menu'
// import { Button } from '@/components/ui/button'
// import { toast } from '@/components/ui/use-toast'
import { IDataResponse, serialize } from '@/network/request'
import { useNavigate } from 'react-router-dom'
// import { useSWRConfig } from 'swr'
// import { MonitorXIcon, MoreHorizontal } from 'lucide-react'
// import { FC } from 'react'
import { JobApplication } from '@/types/Career'
import CareerService from '@/network/services/career'
import { title } from 'radash'

// column declaration for DataTable
// const columnHelper = createColumnHelper<JobApplication>()
const columns: ColumnDef<JobApplication>[] = [
  {
    accessorKey: 'job.title',
    header: 'Job'
  },
  {
    accessorKey: 'job.nature',
    header: 'Nature',
    cell: (props) => <span>{title(props.getValue<string>())}</span>
  },
  {
    accessorKey: 'job',
    header: 'Location',
    cell: (props) => (
      <span>
        {props.row.original.job.state}, {props.row.original.job.country}
      </span>
    )
  },
  {
    accessorKey: 'job.status',
    header: 'Job Status'
  },
  {
    accessorKey: 'answer',
    header: 'Email',
    cell: (props) => <span>{props.row.original.answer.find((q: any) => q.q === 'email')?.a}</span>
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: (props) => <span>{title(props.row.original.status)}</span>
  }
  //   columnHelper.display({
  //     id: 'actions',
  //     cell: (props) => <RowActions row={props.row} />
  //   })
]

// const RowActions: FC<{ row: Row<JobApplication> }> = ({ row }) => {
//   const { mutate } = useSWRConfig()

//   return (
//     <DropdownMenu>
//       <DropdownMenuTrigger asChild>
//         <Button variant="ghost" className="h-8 w-8 p-0" onClick={(e) => e.stopPropagation()}>
//           <span className="sr-only">Open menu</span>
//           <MoreHorizontal className="h-4 w-4" />
//         </Button>
//       </DropdownMenuTrigger>
//       <DropdownMenuContent className="w-56" align="end">
//         <DropdownMenuGroup>
//           {Object.values(ApplicationStatus).map((status) => (
//             <DropdownMenuItem
//               onClick={async (e) => {
//                 e.stopPropagation()

//                 try {
//                   // update application status
//                   const { data: response } = await CareerService.updateApplication(
//                     row.original.id,
//                     { status }
//                   )

//                   if (response.success == true) {
//                     mutate(
//                       (key) =>
//                         typeof key === 'string' && key.startsWith(CareerService.getJobApplications)
//                     )
//                     toast({
//                       title: 'Status updated',
//                       variant: 'success'
//                     })
//                   }

//                   console.log(response)
//                 } catch (e) {
//                   console.log(e)

//                   toast({
//                     title: 'Action failed, please try again',
//                     variant: 'destructive'
//                   })
//                 }
//               }}
//               className="space-x-2"
//               disabled={row.original.status === status}
//             >
//               <MonitorXIcon size="16" />
//               <span>{title(status)}</span>
//             </DropdownMenuItem>
//           ))}
//         </DropdownMenuGroup>
//       </DropdownMenuContent>
//     </DropdownMenu>
//   )
// }

// Declaration of which column can be filtered
const columnFilter: FilterColumn[] = [
  { columnKey: 'job_title', header: 'Job', dataType: 'string' },
  {
    columnKey: 'job_nature',
    header: 'Nature',
    dataType: 'faceted',
    options: [
      { value: 'part_time', label: 'PART TIME', icon: undefined },
      { value: 'full_time', label: 'FULL TIME', icon: undefined },
      { value: 'internship', label: 'INTERNSHIP', icon: undefined }
    ]
  },
  {
    columnKey: 'job_status',
    header: 'Job Status',
    dataType: 'faceted',
    options: [
      { value: 'close', label: 'CLOSE', icon: undefined },
      { value: 'open', label: 'OPEN', icon: undefined }
    ]
  },
  {
    columnKey: 'status',
    header: 'Status',
    dataType: 'faceted',
    options: [
      { value: 'pending', label: 'PENDING', icon: undefined },
      { value: 'passed', label: 'PASSED', icon: undefined },
      { value: 'interview_1', label: 'INTERVIEW 1', icon: undefined },
      { value: 'interview_2', label: 'INTERVIEW 2', icon: undefined },
      { value: 'rejected', label: 'REJECTED', icon: undefined }
    ]
  }
]

const JobApplicationTable = () => {
  const nav = useNavigate()

  return (
    <DataTable<JobApplication, unknown, IDataResponse<JobApplication>>
      columns={columns}
      filterColumns={columnFilter}
      swrService={serialize(CareerService.getJobApplications, { paginate: 1 })}
      pageParam="page"
      limitParam="limit"
      sortParam="sort"
      sortColumns={['status']}
      toRow={CareerService.toRow}
      toPaginate={CareerService.toPaginate}
      onRowClick={(row: Row<JobApplication>) => {
        nav(`/job-applications/${row.original.id}`)
      }}
    />
  )
}

export default JobApplicationTable
