import { Button } from '@/components/ui/button'
import { Form } from '@/components/ui/form'
import {
  Sheet,
  <PERSON>et<PERSON>lose,
  <PERSON>et<PERSON>ontent,
  <PERSON>etHeader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>rigger
} from '@/components/ui/sheet'
import { CreateProductCategoryInput } from '@/types/ProductCategory'
import { PlusCircleIcon } from 'lucide-react'
import { FC, useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { CreateProductCategoryForm } from './CreateProductCategoryForm'
import useAuth from '@/hooks/useAuth'

export const AddProductCategoryButton: FC<{ categoryId?: string }> = ({ categoryId }) => {
  const { role } = useAuth()
  const [isSheetOpen, setIsSheetOpen] = useState(false)
  const form = useForm<CreateProductCategoryInput>({
    defaultValues: {
      parent_category_id: categoryId ?? null
    },
    shouldUseNativeValidation: false
  })

  useEffect(() => {
    if (form.formState.isSubmitSuccessful) {
      form.reset()
      setIsSheetOpen(false)
    }
  }, [form.formState.isSubmitSuccessful])

  if (role == 'vendor') {
    return <></>
  }

  return (
    <div className="flex justify-end">
      <Sheet
        open={isSheetOpen}
        onOpenChange={(isOpen) => {
          setIsSheetOpen(isOpen)
        }}
      >
        <SheetTrigger asChild>
          <Button>
            <PlusCircleIcon size="16" className="mr-2" />
            Create Product Category
          </Button>
        </SheetTrigger>
        <SheetContent side="bottom" className="w-full h-full">
          <SheetHeader className="max-w-4xl mx-auto pt-10 pb-6">
            <SheetTitle className="flex justify-between items-center">
              New Product Category
              <div className="flex space-x-2">
                <SheetClose asChild>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      form.reset()
                    }}
                  >
                    Cancel
                  </Button>
                </SheetClose>
                <Button
                  type="submit"
                  form="create-product-category-form"
                  disabled={form.formState.isSubmitting}
                  onClick={() => {
                    form.clearErrors()
                  }}
                >
                  Submit
                </Button>
              </div>
            </SheetTitle>
          </SheetHeader>
          <Form {...form}>
            <CreateProductCategoryForm categoryId={categoryId} />
          </Form>
        </SheetContent>
      </Sheet>
    </div>
  )
}
