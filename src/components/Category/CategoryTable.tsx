import ProductCategoryService, {
  ProductCategoryResponse
} from '@/network/services/product_category'
import { ColumnDef, Row, createColumnHelper } from '@tanstack/react-table'
import { DataTable, FilterColumn } from '@/components/Table/DataTable'
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem
} from '@/components/ui/dropdown-menu'
import { useToast } from '@/components/ui/use-toast'
import { Button } from '@/components/ui/button'
import { MoreHorizontal, Trash2Icon } from 'lucide-react'
import { FC } from 'react'
import { useNavigate } from 'react-router-dom'
import { ProductCategory } from '@/types/ProductCategory'
import { mutate } from 'swr'
import { cn } from '@/lib/utils'
import { serialize } from '@/network/request'

const columnHelper = createColumnHelper<ProductCategory>()
const columns: ColumnDef<ProductCategory>[] = [
  {
    accessorKey: 'name',
    header: 'Name'
  },
  {
    accessorKey: 'is_active',
    header: 'Status',
    cell: (props) => {
      const value = props.getValue<boolean>()
      const color = value ? 'bg-success' : 'bg-destructive'

      return (
        <div className="flex items-center space-x-2">
          <div className={cn('h-1.5 w-1.5 self-center rounded-full', color)} />
          <span className="capitalize">{value ? 'Active' : 'Inactive'}</span>
        </div>
      )
    }
  },
  columnHelper.display({
    id: 'actions',
    cell: (props) => <RowActions row={props.row} />
  })
]

const columnFilter: FilterColumn[] = [{ columnKey: 'name', header: 'Search', dataType: 'string' }]

const RowActions: FC<{ row: Row<ProductCategory> }> = ({ row }) => {
  const productCategoryId = row.original.id
  const { toast } = useToast()

  return (
    <div className="w-full flex justify-end">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0" onClick={(e) => e.stopPropagation()}>
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem
            onClick={async (event) => {
              event.stopPropagation()

              try {
                await ProductCategoryService.deleteProductCategory(productCategoryId)
                mutate(
                  (key) =>
                    typeof key === 'string' &&
                    key.startsWith(ProductCategoryService.getProductCategories)
                )
                toast({
                  description: 'Product category deleted',
                  variant: 'destructive'
                })
              } catch (error) {
                console.log(error)
              }
            }}
            className="space-x-2"
          >
            <Trash2Icon size="16" />
            <span>Delete</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}

const CategoryTable: FC<{ parentCategoryId?: string; subCategoryId?: string }> = ({
  parentCategoryId,
  subCategoryId
}) => {
  const nav = useNavigate()

  return (
    <DataTable<ProductCategory, unknown, ProductCategoryResponse>
      columns={columns}
      filterColumns={columnFilter}
      swrService={serialize(ProductCategoryService.getProductCategories, {
        parent_category_id: subCategoryId ?? parentCategoryId ?? 'null'
      })}
      pageParam="offset"
      limitParam="limit"
      filterParam="q"
      toRow={ProductCategoryService.toRow}
      toPaginate={ProductCategoryService.toPaginate}
      onRowClick={(row: Row<ProductCategory>) => {
        nav(
          `/categories/${
            parentCategoryId && subCategoryId
              ? `${parentCategoryId}/sub-category/${subCategoryId}/sub-category2/${row.original.id}`
              : parentCategoryId
              ? `${parentCategoryId}/sub-category/${row.original.id}`
              : row.original.id
          }`
        )
      }}
    />
  )
}

export default CategoryTable
