/* eslint-disable @typescript-eslint/ban-ts-comment */
import * as React from 'react'
import { useFormContext } from 'react-hook-form'
import { mutate } from 'swr'

import { cn } from '@/lib/utils'
import {
  FormControl,
  // FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { CreateProductCategoryInput } from '@/types/ProductCategory'
import ProductCategoryService from '@/network/services/product_category'
import { toast } from '@/components/ui/use-toast'
import { isAxiosError } from 'axios'
import { Input } from '@/components/ui/input'
import TextEditorComponent from '../Editor/TextEditor'

interface ProductCategoryFormProps extends React.HTMLAttributes<HTMLDivElement> {
  categoryId?: string
}

export function CreateProductCategoryForm({ className, categoryId }: ProductCategoryFormProps) {
  const form = useFormContext<CreateProductCategoryInput>()

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      if (categoryId) {
        values.parent_category_id = categoryId
      }
      await ProductCategoryService.createProductCategory(values)
      mutate(
        (key) =>
          typeof key === 'string' && key.startsWith(ProductCategoryService.getProductCategories)
      )
      toast({
        title: 'Product category created',
        variant: 'success'
      })
    } catch (error) {
      console.error(error)

      if (isAxiosError(error) && error.response?.data.type == 'duplicate_error') {
        // likely is handle duplicated
        if (error.response.data.message.startsWith('Product_category with handle')) {
          form.setError('handle', {
            message: error.response.data.message.replace('_', ' ')
          })
        }
      } else {
        form.setError('root', {})
      }

      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <form
      id="create-product-category-form"
      onSubmit={onSubmit}
      className={cn('flex flex-col space-y-8 max-w-4xl mx-auto', className)}
    >
      <div className="grid grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="name"
          rules={{ required: 'Please enter the name' }}
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="handle"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Handle</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          // rules={{ required: "Please enter the description" }}
          render={({ field }) => (
            <FormItem className="flex flex-col col-span-2">
              <FormLabel>Description</FormLabel>
              <FormControl>
                <TextEditorComponent content={field.value} onChange={field.onChange} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </form>
  )
}
