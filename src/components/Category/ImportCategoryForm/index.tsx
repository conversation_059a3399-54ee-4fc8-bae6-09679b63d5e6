/* eslint-disable @typescript-eslint/ban-ts-comment */
import * as React from 'react'

import { cn } from '@/lib/utils'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Dropzone } from '@/components/Form/Dropzone'
import { useFormContext } from 'react-hook-form'
import FileService from '@/network/services/file'
import { CreateBatchJobInput } from '@/types/BatchJob'
import BatchJobService from '@/network/services/batch-job'
import { toast } from '@/components/ui/use-toast'
import { isAxiosError } from 'axios'
import { FileWithPath } from 'react-dropzone'
import { Separator } from '@/components/ui/separator'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from '@/components/ui/accordion'

interface ImportCategoryForm extends React.HTMLAttributes<HTMLDivElement> {}

export function ImportCategoryForm({ className }: ImportCategoryForm) {
  const form = useFormContext<CreateBatchJobInput & { file?: File }>()
  const file = form.watch('file') as FileWithPath

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      if (!values.file) {
        return
      }

      const { data } = await FileService.uploadPrivate([values.file])
      const fileKey = data.uploads[0].key
      values.context.fileKey = fileKey
      values.type = 'product-category-import'

      delete values.file

      await BatchJobService.createBatchJob(values)

      toast({
        title: 'File uploaded',
        variant: 'success'
      })
    } catch (error) {
      console.error(error)
      if (isAxiosError(error) && error.response?.data.type == 'duplicate_error') {
        // TODO: need to handle errors here
      } else {
        form.setError('root', {})
      }

      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <>
      <form
        id="import-product-category-form"
        onSubmit={onSubmit}
        className={cn('mx-auto flex max-w-4xl flex-col space-y-8', className)}
      >
        <FormField
          control={form.control}
          name="file"
          rules={{ required: 'Please upload the csv file' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Upload CSV file</FormLabel>
              <FormControl>
                <Dropzone
                  multiple={false}
                  onDrop={(acceptedFiles) => {
                    const file = Object.assign(acceptedFiles[0])

                    form.setValue('file', file as File, {
                      shouldValidate: true
                    })
                  }}
                  description="Drop your csv file here, or click to browse"
                  accept=".csv"
                  files={file ? [file] : []}
                  {...field}
                />
              </FormControl>
              <FormDescription>Please follow the format exactly</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
      </form>
      <div className="pt-5 pb-5">
        <Separator className={cn('mx-auto flex max-w-4xl flex-col space-y-8', className)} />
      </div>
      <div className={cn('mx-auto flex max-w-4xl flex-col space-y-2', className)}>
        <h1 className="font-semibold">Guidelines</h1>

        <Accordion type="multiple" className="w-full">
          <AccordionItem value="create-product-category">
            <AccordionTrigger>A. Create New Product Category</AccordionTrigger>
            <AccordionContent>
              <div className="space-y-5">
                'Product Category Handle', 'Product Category Name', 'Product Category Description'
                are <span className="font-semibold text-red-900">compulsory</span> fields. The
                system will automatically differentiate different product category based on 'Product
                Category Handle' alone, if a 'Product Category Handle' is not exist, it will be
                treated as creating new category, else it will be treated as update an existing
                catgory, hence 'Product Category Id' is{' '}
                <span className="font-semibold text-red-900">not needed</span> for both
                create/update. However, an existing product category id will be needed for{' '}
                <span className="font-semibold text-red-900">parent category assigning</span>, the
                id will be generated automatically by the system.
                <div className="bg-gray-300 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md">
                  1. 'Product Category Is Active' are default to 'TRUE' when leave blank.
                </div>
                <div className="bg-gray-300 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md">
                  2. Please ensure the 'Product Category Handle' are{' '}
                  <span className="font-semibold text-red-900">unique</span>.
                </div>
                <div className="bg-gray-300 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md">
                  3. If a product category has a parent, it can be assigned with an existing product
                  category id, please ensure the 'Product Category Parent Id' is an existing product
                  category id.
                  <div className="bg-gray-400 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md m-3">
                    Eg.
                    <br />A product category named 'Milk Powder' with handle 'milk-powder', and
                    description of 'Milk powder for kids' is already being created and exist in the
                    system, hence it should have a 'Product Category Id' generated by system such as{' '}
                    <span className="font-semibold text-blue-900">
                      pcat_01J0NRQHX4YK3PE036VRVRGDJC
                    </span>
                    .
                    <br />
                    <br />
                    Another product category named 'Milk Powder New Born' which is expected to be a{' '}
                    <span className="font-semibold text-blue-900">child category</span> of product
                    category <span className="font-semibold text-blue-900">Milk Powder</span>.
                    Hence, in the spreadsheet column 'Product Category Parent Id' should be assigned
                    with value{' '}
                    <span className="font-semibold text-blue-900">
                      pcat_01J0NRQHX4YK3PE036VRVRGDJC
                    </span>{' '}
                    which is the product category id of the product category 'Milk Powder',
                  </div>
                </div>
                <div className="bg-gray-300 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md">
                  4. There is a <span className="font-semibold text-blue-900">child rank</span> for
                  the children of a product category which will be automatically assigned by the
                  system based on the order in the spreadsheet row.
                  <div className="bg-gray-400 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md m-3">
                    Eg.
                    <br />
                    Product category 'Milk Powder' has child categories of 'Milk Powder New Born'
                    and 'Milk Powder Toddler'.
                    <br />
                    <br />
                    Depends on the order in the spreadsheet such as: <br />
                    Row 1: Product Category Handle: milk-powder-newborn, Product Category Name: Milk
                    Powder New Born, Parent Category Id:{' '}
                    <span className="font-semibold text-blue-900">
                      pcat_01J0NRQHX4YK3PE036VRVRGDJC
                    </span>
                    ... <br />
                    Row 2: Product Category Handle: milk-powder-toddler, Product Category Nane: Milk
                    Powder Toddler, Parent Category Id:{' '}
                    <span className="font-semibold text-blue-900">
                      pcat_01J0NRQHX4YK3PE036VRVRGDJC
                    </span>
                    ...
                    <br />
                    Be aware both categories above has the{' '}
                    <span className="font-semibold text-blue-900">same</span> parent category id.
                    <br />
                    <br />
                    Product Category{' '}
                    <span className="font-semibold text-blue-900">'Milk Powder New Born'</span> will
                    be assigned with rank{' '}
                    <span className="font-semibold text-blue-900">0 (ranking starts from 0)</span>{' '}
                    since it comes first in the spreadsheet row. Therefore, rank{' '}
                    <span className="font-semibold text-blue-900">0</span> cannot belongs to any
                    other product category with the same parent category.
                  </div>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="update-product-category">
            <AccordionTrigger>B. Update Product Category</AccordionTrigger>
            <AccordionContent>
              <div className="space-y-5">
                'Product Category Handle', 'Product Category Name', 'Product Category Description'
                are <span className="font-semibold text-red-900">compulsory</span> fields. Only
                'Product Category Name', 'Product Category Description', 'Product Category Parent
                Id', 'Product Category Is Active' can be updated.
                <div className="bg-gray-300 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md">
                  1. Please ensure the 'Product Parent Category Id' is not a product category's own
                  id and is an existing product category's id.
                </div>
                <div className="bg-gray-300 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md">
                  2. 'Product Category Is Active' are default to 'TRUE' when leave blank.
                </div>
                <div className="bg-gray-300 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md">
                  3. System will automatically adjust the ranking if any of the child category are
                  being deleted.
                  <br />
                  <div className="bg-gray-400 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md m-3">
                    Eg.
                    <br />
                    Assume row 1 product category is being{' '}
                    <span className="font-semibold text-red-900">deleted</span>. The product
                    category <span className="font-semibold text-blue-900">row 2</span>, and{' '}
                    <span className="font-semibold text-blue-900">row 3</span>{' '}
                    <span className="font-semibold text-red-900">
                      which has the same parent category
                    </span>{' '}
                    will have it's ranking automatically adjusted to{' '}
                    <span className="font-semibold text-blue-900">0</span> and{' '}
                    <span className="font-semibold text-blue-900">1</span> respectively.
                    <br />
                    <br />
                    <span className="line-through">
                      Row 1: Product Category Handle: milk-powder-newborn, Product Category Name:
                      Milk Powder New Born, Parent Category Id:{' '}
                      <span className="font-semibold text-blue-900">
                        pcat_01J0NRQHX4YK3PE036VRVRGDJC
                      </span>
                      , Rank: <span className="font-semibold text-blue-900">0</span>
                    </span>{' '}
                    <span className="font-semibold text-red-900">Deleted</span>
                    <br />
                    <br />
                    Row 2: Product Category Handle: milk-powder-toddler-1-year, Product Category
                    Name: Milk Powder Toddler 1 year, Parent Category Id:{' '}
                    <span className="font-semibold text-blue-900">
                      pcat_01J0NRQHX4YK3PE036VRVRGDJC
                    </span>
                    , <br />
                    Rank: <span className="font-semibold text-red-900 line-through">1</span>{' '}
                    {'after any same parent child deleted, automatically adjust to'}{' '}
                    <span className="font-semibold text-blue-900">0</span>
                    <br />
                    <br />
                    Row 3: Product Category Handle: milk-powder-toddler-quick-dissolve, Product
                    Category Name: Milk Powder Toddler quick dissolve, Parent Category Id:
                    <span className="font-semibold text-blue-900">
                      pcat_01J0NRQHX4YK3PE036VRVRGDJC
                    </span>
                    , <br />
                    Rank: <span className="font-semibold text-red-900 line-through">2</span>{' '}
                    {'after any same parent child deleted, automatically adjust to'}{' '}
                    <span className="font-semibold text-blue-900">1</span>
                  </div>
                </div>
                <div className="bg-gray-300 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md">
                  4. System will automatically adjust the parent category id and rank if any of the
                  product category's parent category are being deleted.
                  <div className="bg-gray-400 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md m-3">
                    Eg.
                    <br />
                    <span className="line-through">
                      Row 1: Product Category Handle: milk-powder-newborn,
                      <br />
                      Product Category Name: Milk Powder New Born,
                      <br />
                      <span className="font-semibold text-red-900">Product Category Id</span>:{' '}
                      <span className="font-semibold text-blue-900">
                        {' '}
                        pcat_01J0NRQHX4YK3PE036VRVRGDJC
                      </span>
                      ,
                      <br />
                      Product Parent Category Id: null,
                      <br />
                      Rank: <span className="font-semibold text-blue-900">0</span>
                    </span>
                    <span className="font-semibold text-red-900"> Deleted</span>
                    <br />
                    <span className="font-semibold text-red-900">
                      (Assume this is the parent category of both category row 2 and row 3, and is
                      being deleted, please be aware that it's product category id is
                      'pcat_01J0NRQHX4YK3PE036VRVRGDJC', both row 2 and row 3 have this value as
                      their 'Product Parent Category Id')
                    </span>
                    <br />
                    <br />
                    Row 2: Product Category Handle: milk-powder-toddler-1-year,
                    <br />
                    Product Category Name: Milk Powder Toddler 1 year,
                    <br />
                    Parent Category Id:{' '}
                    <span className="font-semibold text-red-900 line-through">
                      pcat_01J0NRQHX4YK3PE036VRVRGDJC
                    </span>
                    {' after parent deleted, automatically set to > '}
                    <span className="font-semibold text-blue-900">null</span>
                    ,
                    <br />
                    Rank: <span className="font-semibold text-red-900 line-through">0</span>
                    {' after parent deleted, automatically set to > '}
                    <span className="font-semibold text-blue-900">0</span>
                    <br />
                    <br />
                    Row 3: Product Category Handle: milk-powder-toddler-quick-dissolve,
                    <br />
                    Product Category Name: Milk Powder Toddler quick dissolve,
                    <br />
                    Parent Category Id:{' '}
                    <span className="font-semibold text-red-900 line-through">
                      pcat_01J0NRQHX4YK3PE036VRVRGDJC
                    </span>
                    {' after parent deleted, automatically set to > '}
                    <span className="font-semibold text-blue-900">null</span>
                    ,
                    <br />
                    Rank: <span className="font-semibold text-red-900 line-through">1</span>
                    {' after parent deleted, automatically set to > '}
                    <span className="font-semibold text-blue-900">0</span>
                    <br />
                  </div>
                </div>
                <div className="bg-gray-300 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md">
                  5. During update, system will not adjust the existing product category's rank base
                  on the order of row in spreadsheet, only the rank of a product category that will
                  be assigned with a new parent will have their rank determined by the order of the
                  row.
                  <div className="bg-gray-400 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md m-3">
                    Eg.
                    <br />
                    Row 1: Product Category Handle: milk-powder-newborn,
                    <br />
                    Product Category Name: Milk Powder New Born,
                    <br />
                    Parent Category Id:{' '}
                    <span className="font-semibold text-blue-900">
                      pcat_01J0NRQHX4YK3PE036VRVRGDJC
                    </span>
                    ,
                    <br />
                    Rank: <span className="font-semibold text-blue-900">0</span>{' '}
                    <span className="font-semibold text-red-900">
                      (Existing Category with Parent Category: pcat_01J0NRQHX4YK3PE036VRVRGDJC)
                    </span>
                    <br />
                    <br />
                    Row 2: Product Category Handle: milk-powder-toddler-1-year,
                    <br />
                    Product Category Name: Milk Powder Toddler 1 year,
                    <br />
                    Parent Category Id:{' '}
                    <span className="font-semibold text-blue-900">
                      pcat_01J0NRQHX4YK3PE036VRVRGDJC
                    </span>
                    ,
                    <br />
                    Rank: <span className="font-semibold text-blue-900">2</span>{' '}
                    <span className="font-semibold text-red-900">
                      (A category that is newly assigned with Parent Category:
                      pcat_01J0NRQHX4YK3PE036VRVRGDJC during update, notice the rank is assigned
                      with 2 despite it is row 2 while row 3 is still remain rank 1)
                    </span>
                    <br />
                    <br />
                    Row 3: Product Category Handle: milk-powder-toddler-quick-dissolve,
                    <br />
                    Product Category Name: Milk Powder Toddler quick dissolve,
                    <br />
                    Parent Category Id:{' '}
                    <span className="font-semibold text-blue-900">
                      pcat_01J0NRQHX4YK3PE036VRVRGDJC
                    </span>
                    ,
                    <br />
                    Rank: <span className="font-semibold text-blue-900">1</span>{' '}
                    <span className="font-semibold text-red-900">
                      (Existing Category with Parent Category: pcat_01J0NRQHX4YK3PE036VRVRGDJC)
                    </span>
                    <br />
                    <br />
                    Row 4: Product Category Handle: milk-powder-dha-extra,
                    <br />
                    Product Category Name: Milk Powder Extra DHA,
                    <br />
                    Parent Category Id:{' '}
                    <span className="font-semibold text-blue-900">
                      pcat_01J0NRQHX4YK3PE036VRVRGDJC
                    </span>
                    ,
                    <br />
                    Rank: <span className="font-semibold text-blue-900">3</span>{' '}
                    <span className="font-semibold text-red-900">
                      (A category that is newly assigned with Parent Category:
                      pcat_01J0NRQHX4YK3PE036VRVRGDJC during update, notice the rank is assigned
                      with 3 because it is at row 4 which is later than row 2, hence it is assigned
                      with rank 3)
                    </span>
                  </div>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
    </>
  )
}
