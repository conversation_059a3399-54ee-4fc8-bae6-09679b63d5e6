/* eslint-disable @typescript-eslint/ban-ts-comment */
import { cn } from '@/lib/utils'
import ProductCategoryService from '@/network/services/product_category'
import { ProductCategory, ProductCategoryBatchProduct } from '@/types/ProductCategory'
import { isAxiosError } from 'axios'
import { useForm } from 'react-hook-form'
import { mutate } from 'swr'
import { Form, FormField } from '@/components/ui/form'
import {
  DialogHeader,
  Dialog,
  DialogContent,
  DialogTitle,
  DialogClose
} from '@/components/ui/dialog'
import { toast } from '@/components/ui/use-toast'
import { Button } from '@/components/ui/button'
import { ProductComboBox } from '@/components/Product/ProductComboBox'
import { Icons } from '@/components/icons'
import { Product } from '@/types/Product'
import { serialize } from '@/network/request'
import ProductService from '@/network/services/product'
import { diff, isEmpty } from 'radash'

interface ProductCategoryBatchProductDialogProps extends React.HTMLAttributes<HTMLDivElement> {
  isDialogOpen: boolean
  setIsDialogOpen: (value: boolean) => void
  category: ProductCategory
  products: Product[]
}

export function ProductCategoryBatchProductDialog({
  isDialogOpen,
  setIsDialogOpen,
  category,
  products,
  className
}: ProductCategoryBatchProductDialogProps) {
  const form = useForm<ProductCategoryBatchProduct>({
    defaultValues: {
      product_ids: products.map((product) => ({
        id: product.id
      }))
    }
  })

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      const initialValues = products.map((prod) => {
        return { id: prod.id }
      })
      const toSubmit = values.product_ids.map((prod) => {
        return { id: prod.id }
      })

      // find difference from initial and add or delete
      const toAdd = diff(toSubmit, initialValues, (item) => item.id)
      const toRemove = diff(initialValues, toSubmit, (item) => item.id)

      if (!isEmpty(toAdd)) {
        await ProductCategoryService.addProductsToProductCategory(category.id, {
          product_ids: toAdd
        })
      }

      if (!isEmpty(toRemove)) {
        await ProductCategoryService.deleteProductsFromProductCategory(category.id, {
          product_ids: toRemove
        })
      }

      mutate(ProductCategoryService.getProductCategory(category.id))
      mutate(serialize(ProductService.getProducts, { category_id: [category.id] }))
      toast({
        title: 'Product(s) updated successfully',
        variant: 'success'
      })
      setIsDialogOpen(false)
    } catch (error) {
      console.error(error)

      if (isAxiosError(error) && error.response?.data.type == 'duplicate_error') {
        // likely is handle duplicated
      } else {
        form.setError('root', {})
      }

      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <Dialog
      open={isDialogOpen}
      onOpenChange={(isOpen) => {
        setIsDialogOpen(isOpen)
      }}
    >
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle className="flex justify-between items-center">
            Add products to category
            <div className="flex space-x-2">
              <DialogClose>
                <Button variant="outline">Cancel</Button>
              </DialogClose>
              <Button
                type="submit"
                form="product-category-batch-product-form"
                disabled={form.formState.isSubmitting}
                onClick={() => {
                  form.clearErrors()
                }}
              >
                {form.formState.isSubmitting && (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                )}
                Save
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form
            id="product-category-batch-product-form"
            onSubmit={onSubmit}
            className={cn('flex flex-col space-y-8', className)}
          >
            <FormField
              control={form.control}
              name="product_ids"
              render={({ field }) => {
                // @ts-ignore
                return <ProductComboBox products={field.value ?? []} name={field.name} />
              }}
            />
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
