import { Button } from '@/components/ui/button'
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  CardFooter,
  CardContentLabel,
  CardContentTitle
} from '@/components/ui/card'
import { ProductCategory } from '@/types/ProductCategory'
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem
} from '@/components/ui/dropdown-menu'
import { EditIcon, MoreHorizontal } from 'lucide-react'
import { FC, useMemo, useState } from 'react'
import { UpdateProductCategoryInformationDialog } from './UpdateProductCategoryDialog'
import useAuth from '@/hooks/useAuth'
import { cn } from '@/lib/utils'
import ProductCategoryService from '@/network/services/product_category'
import { mutate } from 'swr'
import { toast } from '@/components/ui/use-toast'

const STATUS_LIST = [true, false]

const ProductCategoryStatusBadge: FC<{ category: ProductCategory }> = ({ category }) => {
  const { role } = useAuth()
  const color = useMemo(() => {
    if (category.is_active) {
      return 'bg-success'
    }

    return 'bg-destructive'
  }, [category.is_active])

  const statuses = useMemo(() => {
    return STATUS_LIST.filter((f) => f != category.is_active)
  }, [category.is_active])

  const updateStatus = async (category: ProductCategory, isActive: boolean) => {
    try {
      await ProductCategoryService.updateProductCategory(category.id, { is_active: isActive })
      mutate(
        (key) =>
          typeof key === 'string' &&
          key.startsWith(ProductCategoryService.getProductCategory(category.id))
      )
      toast({
        title: `Product category ${isActive ? 'activated' : 'deactivated'}`,
        variant: 'success'
      })
    } catch (error) {
      console.log(error)
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="h-8" asChild>
          <div className="flex items-center space-x-2">
            <div className={cn('h-1.5 w-1.5 self-center rounded-full', color)} />
            <span className="capitalize text-xs">{category.is_active ? 'Active' : 'Inactive'}</span>
          </div>
        </Button>
      </DropdownMenuTrigger>
      {role != 'vendor' && (
        <DropdownMenuContent align="end" forceMount>
          <DropdownMenuGroup>
            {statuses.map((status, i) => {
              return (
                <DropdownMenuItem
                  key={i}
                  onClick={async (event) => {
                    event.stopPropagation()
                    updateStatus(category, status)
                  }}
                  className="space-x-2"
                >
                  <div
                    className={cn(
                      'h-1.5 w-1.5 self-center rounded-full',
                      status ? 'bg-success' : 'bg-destructive'
                    )}
                  />
                  <span className="capitalize text-xs">{status ? 'Activate' : 'Deactivate'}</span>
                </DropdownMenuItem>
              )
            })}
          </DropdownMenuGroup>
        </DropdownMenuContent>
      )}
    </DropdownMenu>
  )
}

export const ProductCategoryCard: FC<{ category: ProductCategory }> = ({ category }) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const { role } = useAuth()

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <CardTitle>{category.name}</CardTitle>
          <div className="flex flex-row space-x-2">
            <ProductCategoryStatusBadge {...{ category }} />
            {role != 'vendor' && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-8 w-8 p-0">
                    <span className="sr-only">Open menu</span>
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" forceMount>
                  <DropdownMenuGroup>
                    <DropdownMenuItem
                      onClick={async (event) => {
                        event.stopPropagation()
                        setIsDialogOpen(true)
                      }}
                      className="space-x-2"
                    >
                      <EditIcon size="16" />
                      <span>Edit product category</span>
                    </DropdownMenuItem>
                  </DropdownMenuGroup>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        </CardHeader>
        <CardContent className="flex flex-col space-y-4">
          <div className="grid grid-cols-[200px_1fr] gap-2">
            <CardContentTitle>Handle</CardContentTitle>
            <CardContentLabel>{category.handle}</CardContentLabel>
            <CardContentTitle>Description</CardContentTitle>
            <CardContentLabel>{category.description}</CardContentLabel>
            <CardContentTitle>Parent Category</CardContentTitle>
            <CardContentLabel>{category.parent_category?.name ?? '-'}</CardContentLabel>
          </div>
        </CardContent>
        <CardFooter></CardFooter>
      </Card>

      <UpdateProductCategoryInformationDialog {...{ isDialogOpen, setIsDialogOpen, category }} />
    </>
  )
}
