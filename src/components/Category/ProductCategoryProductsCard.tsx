import { ProductCategory } from '@/types/ProductCategory'
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  CardFooter,
  CardDescription,
  CardContentTitle,
  CardContentLabel
} from '@/components/ui/card'
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import { EditIcon, MoreHorizontal } from 'lucide-react'
import { FC, useState } from 'react'
import { ProductCategoryBatchProductDialog } from './ProductCategoryBatchProductDialog'
import { Product } from '@/types/Product'

const ProductCategoryProductsCard: FC<{ category: ProductCategory; products: Product[] }> = ({
  category,
  products
}) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <div className="flex flex-col space-y-2">
            <CardTitle>Products</CardTitle>
            <CardDescription>Products under this category</CardDescription>
          </div>
          <div className="flex flex-row space-x-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" forceMount>
                <DropdownMenuGroup>
                  <DropdownMenuItem
                    onClick={async (event) => {
                      event.stopPropagation()
                      setIsDialogOpen(true)
                    }}
                    className="space-x-2"
                  >
                    <EditIcon size="16" />
                    <span>Update products</span>
                  </DropdownMenuItem>
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>
        <CardContent className="flex flex-col space-y-4">
          <div className="grid grid-cols-2 gap-1">
            {products?.map((field) => {
              return (
                <>
                  <CardContentTitle>{field.title}</CardContentTitle>
                  <CardContentLabel>{field.status}</CardContentLabel>
                </>
              )
            })}
          </div>
        </CardContent>
        <CardFooter></CardFooter>
      </Card>

      <ProductCategoryBatchProductDialog
        {...{
          isDialogOpen,
          setIsDialogOpen,
          category,
          products
        }}
      />
    </>
  )
}

export default ProductCategoryProductsCard
