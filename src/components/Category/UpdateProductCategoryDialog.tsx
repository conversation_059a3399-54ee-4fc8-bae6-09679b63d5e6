import { cn } from '@/lib/utils'
import ProductCategoryService from '@/network/services/product_category'
import { ProductCategory, UpdateProductCategoryInput } from '@/types/ProductCategory'
import { isAxiosError } from 'axios'
import { useForm } from 'react-hook-form'
import { mutate } from 'swr'
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage
} from '@/components/ui/form'
import {
  DialogHeader,
  Dialog,
  DialogContent,
  DialogTitle,
  DialogClose
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { toast } from '@/components/ui/use-toast'
import { Button } from '@/components/ui/button'
import { Icons } from '@/components/icons'
import TextEditorComponent from '../Editor/TextEditor'

interface UpdateProductCategoryInformationDialogProps extends React.HTMLAttributes<HTMLDivElement> {
  isDialogOpen: boolean
  setIsDialogOpen: (value: boolean) => void
  category: ProductCategory
}

export function UpdateProductCategoryInformationDialog({
  isDialogOpen,
  setIsDialogOpen,
  category,
  className
}: UpdateProductCategoryInformationDialogProps) {
  const form = useForm<UpdateProductCategoryInput>({
    defaultValues: {
      name: category.name,
      handle: category.handle,
      description: category.description
    }
  })

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      await ProductCategoryService.updateProductCategory(category.id, values)
      mutate(ProductCategoryService.getProductCategory(category.id))
      toast({
        title: 'Product category updated',
        variant: 'success'
      })
      setIsDialogOpen(false)
    } catch (error) {
      console.error(error)

      if (isAxiosError(error) && error.response?.data.type == 'duplicate_error') {
        // likely is handle duplicated
        if (error.response.data.message.startsWith('Product_category with handle')) {
          form.setError('handle', {
            message: error.response.data.message.replace('_', ' ')
          })
        }
      } else {
        form.setError('root', {})
      }

      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <Dialog
      open={isDialogOpen}
      onOpenChange={(isOpen) => {
        setIsDialogOpen(isOpen)
      }}
    >
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle className="flex justify-between items-center">
            Update Product Category
            <div className="flex space-x-2">
              <DialogClose>
                <Button variant="outline">Cancel</Button>
              </DialogClose>
              <Button
                type="submit"
                form="update-product-category-form"
                disabled={form.formState.isSubmitting}
                onClick={() => {
                  form.clearErrors()
                }}
              >
                {form.formState.isSubmitting && (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                )}
                Save
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form
            id="update-product-category-form"
            onSubmit={onSubmit}
            className={cn('flex flex-col space-y-8', className)}
          >
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="name"
                rules={{ required: 'Please enter the name' }}
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="handle"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Handle</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                // rules={{ required: "Please enter the description" }}
                render={({ field }) => (
                  <FormItem className="flex flex-col col-span-2">
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <TextEditorComponent content={field.value} onChange={field.onChange} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
