/* eslint-disable @typescript-eslint/ban-ts-comment */
import * as React from 'react'

import { ProductCategory } from '@/types/ProductCategory'
import { ProductCategoryCard } from './ProductCategoryCard'
import ProductCategoryProductsCard from './ProductCategoryProductsCard'
import { Product } from '@/types/Product'

interface ProductCategoryFormProps extends React.HTMLAttributes<HTMLDivElement> {
  initialValue: ProductCategory
  products: Product[]
}

export function UpdateProductCategoryForm({ initialValue, products }: ProductCategoryFormProps) {
  return (
    <div className="flex flex-col space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <ProductCategoryCard category={initialValue} />
      </div>

      <div className="grid grid-cols-1 gap-4">
        <ProductCategoryProductsCard category={initialValue} products={products} />
      </div>
    </div>
  )
}
