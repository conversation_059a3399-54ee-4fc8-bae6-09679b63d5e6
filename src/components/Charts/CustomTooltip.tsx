import { Card, CardContent, CardHeader, Title } from '@/components/ui/card'
import { TooltipProps } from 'recharts'

const CustomTooltip = ({ active, payload, total }: { total?: number } & TooltipProps<any, any>) => {
  if (active && payload && payload.length) {
    return (
      <Card className="bg-opacity-70">
        <CardHeader>
          <Title className="text-start">{payload[0].name ?? 'NULL'}</Title>
        </CardHeader>
        <CardContent>
          <div>
            <div>Total: {payload[0].value}</div>
            {total && (
              <div>({((parseInt(`${payload[0].value}`) / (total ?? 1)) * 100).toFixed(1)}%)</div>
            )}
          </div>
        </CardContent>
      </Card>
    )
  }

  return null
}

export default CustomTooltip
