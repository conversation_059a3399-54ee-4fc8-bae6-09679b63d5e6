import { Exhibitor } from '@/types/Exhibitor'
import { FC } from 'react'
import { Card, CardHeader, CardTitle, CardContent, Title, Content } from '../ui/card'

const ContactCard: FC<{ exhibitor: Exhibitor; variant?: 'finance' | 'marketing' }> = ({
  exhibitor,
  variant
}) => {
  const fullName = variant
    ? variant == 'finance'
      ? exhibitor?.finance_full_name
      : exhibitor?.marketing_full_name
    : exhibitor?.contact_full_name

  const email = variant
    ? variant == 'finance'
      ? exhibitor?.finance_email
      : exhibitor?.marketing_email
    : exhibitor?.contact_email

  const contactNumber = variant
    ? variant == 'finance'
      ? exhibitor?.finance_mobile_number
      : exhibitor?.marketing_mobile_number
    : exhibitor?.contact_mobile_number

  return (
    <Card className="h-fit">
      <CardHeader className="flex flex-row space-y-0 items-center justify-between">
        <CardTitle>
          {variant ? (variant == 'finance' ? 'Finance Contact' : 'Marketing Contact') : 'Contact'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-2">
          <Title>Full Name</Title>
          <Content>{fullName ?? '-'}</Content>
          <Title>Email</Title>
          <Content>{email ?? '-'}</Content>
          <Title>Contact Number</Title>
          <Content>{contactNumber ?? '-'}</Content>
        </div>
      </CardContent>
    </Card>
  )
}

export default ContactCard
