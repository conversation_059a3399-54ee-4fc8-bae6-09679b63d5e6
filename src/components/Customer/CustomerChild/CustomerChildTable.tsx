import {
  ColumnDef
  // Row
} from '@tanstack/react-table'
import { DataTable, FilterColumn } from '@/components/Table/DataTable'
import { serialize } from '@/network/request'
import { Child, ChildResponse } from '@/types/CustomerChild'
import CustomerChildService from '@/network/services/customer_child'

const columns: ColumnDef<Child>[] = [
  // {
  //   accessorKey: 'id',
  //   header: 'ID'
  // },
  {
    accessorKey: 'full_name',
    header: 'Full Name'
  },
  {
    accessorKey: 'gender',
    header: 'Gender'
  },
  {
    accessorKey: 'dob',
    header: 'DOB'
  }
]

const columnFilter: FilterColumn[] = []

const CustomerChildTable = (data: any) => {
  // const nav = useNavigate()

  return (
    <DataTable<Child, unknown, ChildResponse>
      columns={columns}
      filterColumns={columnFilter}
      swrService={serialize(CustomerChildService.getChildren(data.customer.id), {})}
      pageParam="offset"
      limitParam="limit"
      toRow={CustomerChildService.toRow}
      toPaginate={CustomerChildService.toPaginate}
    />
  )
}

export default CustomerChildTable
