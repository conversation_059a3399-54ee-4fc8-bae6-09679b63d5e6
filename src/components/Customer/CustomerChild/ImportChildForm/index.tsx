/* eslint-disable @typescript-eslint/ban-ts-comment */
import * as React from 'react'

import { cn } from '@/lib/utils'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Dropzone } from '@/components/Form/Dropzone'
import { useFormContext } from 'react-hook-form'
import FileService from '@/network/services/file'
import { CreateBatchJobInput } from '@/types/BatchJob'
import BatchJobService from '@/network/services/batch-job'
import { toast } from '@/components/ui/use-toast'
import { isAxiosError } from 'axios'
import { FileWithPath } from 'react-dropzone'

interface ImportChildForm extends React.HTMLAttributes<HTMLDivElement> {}

export function ImportChildForm({ className }: ImportChildForm) {
  const form = useFormContext<CreateBatchJobInput & { file?: File }>()
  const file = form.watch('file') as FileWithPath

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      if (!values.file) {
        return
      }

      const { data } = await FileService.uploadPrivate([values.file])
      const fileKey = data.uploads[0].key
      values.context.fileKey = fileKey
      values.type = 'child-import'

      delete values.file

      await BatchJobService.createBatchJob(values)

      toast({
        title: 'File uploaded',
        variant: 'success'
      })
    } catch (error) {
      console.error(error)
      if (isAxiosError(error) && error.response?.data.type == 'duplicate_error') {
        // TODO: need to handle errors here
      } else {
        form.setError('root', {})
      }

      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <form
      id="import-child-form"
      onSubmit={onSubmit}
      className={cn('mx-auto flex max-w-4xl flex-col space-y-8', className)}
    >
      <FormField
        control={form.control}
        name="file"
        rules={{ required: 'Please upload the csv file' }}
        render={({ field }) => (
          <FormItem>
            <FormLabel>Upload CSV file</FormLabel>
            <FormControl>
              <Dropzone
                multiple={false}
                onDrop={(acceptedFiles) => {
                  const file = Object.assign(acceptedFiles[0])

                  form.setValue('file', file as File, {
                    shouldValidate: true
                  })
                }}
                description="Drop your csv file here, or click to browse"
                accept=".csv"
                files={file ? [file] : []}
                {...field}
              />
            </FormControl>
            <FormDescription>Please follow the format exactly</FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />
    </form>
  )
}
