import { Button } from '@/components/ui/button'
import { Form } from '@/components/ui/form'
import {
  Sheet,
  <PERSON>et<PERSON>lose,
  <PERSON>et<PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger
} from '@/components/ui/sheet'
import useAuth from '@/hooks/useAuth'
import { ImportIcon } from 'lucide-react'
import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { ImportChildForm } from '../ImportChildForm'
import { CreateBatchJobInput } from '@/types/BatchJob'

export const ImportChildButton = () => {
  const { role } = useAuth()
  const [isSheetOpen, setIsSheetOpen] = useState(false)
  const form = useForm<CreateBatchJobInput & { file?: File }>({
    shouldUseNativeValidation: false,
    defaultValues: {
      type: 'child-import',
      context: {}
    }
  })

  const downloadFile = async () => {
    try {
      const response = await fetch('/customer-child-import-sample.csv')
      const content = await response.text()
      const blob = new Blob([content], { type: 'text/csv' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = 'customer-child-import-sample.csv'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    } catch (error) {
      console.error('Error downloading file:', error)
    }
  }

  useEffect(() => {
    if (form.formState.isSubmitSuccessful) {
      form.reset()
      setIsSheetOpen(false)
    }
  }, [form.formState.isSubmitSuccessful])

  if (role != 'super_admin') {
    return <></>
  }

  return (
    <Sheet
      open={isSheetOpen}
      onOpenChange={(isOpen) => {
        setIsSheetOpen(isOpen)
      }}
    >
      <SheetTrigger asChild>
        <Button variant="outline">
          <ImportIcon size="16" className="mr-2" />
          Import
        </Button>
      </SheetTrigger>
      <SheetContent side="bottom" className="h-full w-full">
        <SheetHeader className="mx-auto max-w-4xl pb-6 pt-10">
          <SheetTitle className="flex items-center justify-between">
            Import Children
            <div className="flex space-x-2">
              <SheetClose asChild>
                <Button
                  variant="outline"
                  onClick={() => {
                    form.reset()
                  }}
                >
                  Cancel
                </Button>
              </SheetClose>
              <Button type="button" disabled={form.formState.isSubmitting} onClick={downloadFile}>
                Download Sample Import CSV
              </Button>
              <Button
                type="submit"
                form="import-child-form"
                disabled={form.formState.isSubmitting}
                onClick={() => {
                  form.clearErrors()
                }}
              >
                Upload
              </Button>
            </div>
          </SheetTitle>
        </SheetHeader>
        <Form {...form}>
          <ImportChildForm />
        </Form>
      </SheetContent>
    </Sheet>
  )
}
