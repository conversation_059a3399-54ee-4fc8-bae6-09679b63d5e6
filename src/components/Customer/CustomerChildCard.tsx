import { FC } from 'react'
import { Card, CardContent, CardHeader, CardTitle, Content, Title } from '../ui/card'
import { Customer } from '@/types/Customer'
import { Separator } from '../ui/separator'
import CustomerChildTable from './CustomerChild/CustomerChildTable'

const CustomerChildCard: FC<{
  customer: Customer
}> = ({ customer }) => {
  // const { role } = useAuth()

  return (
    <>
      <Card className="h-fit">
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <CardTitle>Child Information</CardTitle>
          {/* {role === 'super_admin' && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" forceMount>
                <DropdownMenuGroup>
                  <DropdownMenuItem
                    onClick={async (event) => {
                      event.stopPropagation()
                      setIsDialogOpen(true)
                    }}
                    className="space-x-2"
                  >
                    <SettingsIcon size="16" />
                    <span>Edit</span>
                  </DropdownMenuItem>
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          )} */}
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-2">
            <Title>Number of Child</Title>
            <Content>{customer.children.length ?? '-'}</Content>
          </div>
          <Separator className="my-2" />
          <CustomerChildTable customer={customer} />
        </CardContent>
      </Card>

      {/* Update customer child */}
      {/* <Dialog
        open={isDialogOpen}
        onOpenChange={(isOpen) => {
          setIsDialogOpen(isOpen)
        }}
      >
        <UpdateCustomerChildForm initialValues={customer} setIsDialogOpen={setIsDialogOpen} />
      </Dialog> */}
    </>
  )
}

export default CustomerChildCard
