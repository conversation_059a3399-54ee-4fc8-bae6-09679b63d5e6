import { FC, useMemo, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle, Content, Title } from '../ui/card'
import { Customer, LicenseCustomer } from '@/types/Customer'
import { Dialog } from '../ui/dialog'
import UpdateCustomerGeneralForm from './UpdateCustomerForm/UpdateCustomerGeneralForm'
import useAuth from '@/hooks/useAuth'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu'
import { MoreHorizontal, SettingsIcon } from 'lucide-react'
import { Button } from '../ui/button'
import { cn, statusToColor } from '@/lib/utils'
import CustomerService from '@/network/services/customer'
import { mutate } from 'swr'
import { toast } from '../ui/use-toast'
import AuthService from '@/network/services/auth'

const CustomerGeneralInfoCard: FC<{
  customer: Customer
  licenseCustomer: LicenseCustomer
}> = ({ customer, licenseCustomer }) => {
  const { role } = useAuth()
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <CardTitle>General Information</CardTitle>
          <div className="flex flex-row space-x-2">
            <CustomerBlockedBadge licenseCustomer={licenseCustomer} />
            {role === 'super_admin' && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-8 w-8 p-0">
                    <span className="sr-only">Open menu</span>
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" forceMount>
                  <DropdownMenuGroup>
                    <DropdownMenuItem
                      onClick={async (event) => {
                        event.stopPropagation()
                        setIsDialogOpen(true)
                      }}
                      className="space-x-2"
                    >
                      <SettingsIcon size="16" />
                      <span>Edit</span>
                    </DropdownMenuItem>
                  </DropdownMenuGroup>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-2">
            <Title>First Name</Title>
            <Content>{customer?.first_name ?? '-'}</Content>
            <Title>Last Name</Title>
            <Content>{customer?.last_name ?? '-'}</Content>
            <Title>Current Stage</Title>
            <Content>{customer?.current_stage ?? '-'}</Content>
            <Title>Gender</Title>
            <Content>{customer?.gender ?? '-'}</Content>
            <Title>Email</Title>
            <Content>{customer?.email ?? '-'}</Content>
            <Title>Contact Number</Title>
            <Content>{customer?.mobile_number ?? '-'}</Content>
            <Title>DOB</Title>
            <Content>{customer?.dob ?? '-'}</Content>
            <Title>Expected Delivery Date</Title>
            <Content>{customer?.expected_delivery_date ?? '-'}</Content>
            <Title>Hospital of Delivery</Title>
            <Content>{customer?.hospital_of_delivery ?? '-'}</Content>
            <Title>Household Income Range</Title>
            <Content>{customer?.household_income_range ?? '-'}</Content>
            <Title>Friend Code</Title>
            <Content>{customer?.friend_code ?? '-'}</Content>
          </div>
        </CardContent>
      </Card>

      {/* Update customer general */}
      <Dialog
        open={isDialogOpen}
        onOpenChange={(isOpen) => {
          setIsDialogOpen(isOpen)
        }}
      >
        <UpdateCustomerGeneralForm initialValues={customer} setIsDialogOpen={setIsDialogOpen} />
      </Dialog>
    </>
  )
}

const CustomerBlockedBadge: FC<{ licenseCustomer: LicenseCustomer }> = ({ licenseCustomer }) => {
  const { role } = useAuth()
  const color = useMemo(() => {
    return statusToColor(!licenseCustomer?.blocked)
  }, [licenseCustomer?.blocked])

  const updateStatus = async (licenseCustomer: LicenseCustomer, blocked: boolean) => {
    try {
      await AuthService.updateLicenseUser(licenseCustomer.id, { blocked })
      mutate(
        (key) =>
          typeof key === 'string' &&
          key.startsWith(CustomerService.getLicenseCustomer(licenseCustomer.id))
      )
      !blocked
        ? toast({
            title: `Account blocked`,
            variant: 'success'
          })
        : toast({
            title: `Account unblocked`,
            variant: 'destructive'
          })
    } catch (error) {
      console.log(error)
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="h-8">
          <div className="flex items-center space-x-2">
            <div className={cn('h-1.5 w-1.5 self-center rounded-full', color)} />
            <span className="capitalize text-xs">
              {licenseCustomer?.blocked ? 'Blocked' : 'Active'}
            </span>
          </div>
        </Button>
      </DropdownMenuTrigger>
      {role != 'vendor' && (
        <DropdownMenuContent align="end" forceMount>
          <DropdownMenuGroup>
            <DropdownMenuItem
              onClick={async (event) => {
                event.stopPropagation()
                await updateStatus(licenseCustomer, !licenseCustomer?.blocked)
              }}
              className="space-x-2"
            >
              <div
                className={cn(
                  'h-1.5 w-1.5 self-center rounded-full',
                  statusToColor(licenseCustomer?.blocked)
                )}
              />
              <span className="capitalize text-xs">
                {licenseCustomer?.blocked ? 'Unblocked' : 'Blocked'}
              </span>
            </DropdownMenuItem>
            {/* <DropdownMenuItem
              onClick={async (event) => {
                event.stopPropagation()
                await updateStatus(licenseCustomer, false)
              }}
              className="space-x-2"
            >
              <div
                className={cn('h-1.5 w-1.5 self-center rounded-full', statusToColor(false))}
              />
              <span className="capitalize text-xs">Reject</span>
            </DropdownMenuItem> */}
          </DropdownMenuGroup>
        </DropdownMenuContent>
      )}
    </DropdownMenu>
  )
}

export default CustomerGeneralInfoCard
