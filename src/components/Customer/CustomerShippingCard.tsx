import React, { <PERSON> } from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, Content, Title } from '../ui/card'
import { Customer } from '@/types/Customer'
import { Separator } from '../ui/separator'

const CustomerShippingCard: FC<{
  customer: Customer
}> = ({ customer }) => {
  return (
    <Card className="h-fit">
      <CardHeader className="flex flex-row space-y-0 items-center justify-between">
        <CardTitle>Shipping Information</CardTitle>
      </CardHeader>
      <CardContent>
        {customer.shipping_addresses.map((address, i) => (
          <React.Fragment key={i}>
            {i > 0 && <Separator className="my-4" />}
            <div className="grid grid-cols-2 gap-2">
              {address.default && <Content className="col-span-2 text-start text-md">Default</Content>}
              <Title>Name</Title>
              <Content>{address.first_name}</Content>
              <Title>Address 1</Title>
              <Content>{address.address_1}</Content>
              <Title>Address 2</Title>
              <Content>{address.address_2}</Content>
              <Title>State</Title>
              <Content>{address.province}</Content>
              <Title>Country</Title>
              <Content>{address.country?.display_name}</Content>
            </div>
          </React.Fragment>
        ))}
      </CardContent>
    </Card>
  )
}

export default CustomerShippingCard
