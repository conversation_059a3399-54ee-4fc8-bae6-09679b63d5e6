import { ColumnDef, Row, createColumnHelper } from '@tanstack/react-table'
import { DataTable, FilterColumn } from '@/components/Table/DataTable'
import { useNavigate } from 'react-router-dom'
import { Customer, CustomerResponse } from '@/types/Customer'
import CustomerService from '@/network/services/customer'
import IndeterminateCheckbox from '../Table/IndeterminateCheckbox'
import { Button } from '../ui/button'
import { FC, useState } from 'react'
import { MonitorXIcon, MoreHorizontal, Trash2Icon } from 'lucide-react'
import { useToast } from '../ui/use-toast'
import AuthService from '@/network/services/auth'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger
} from '../ui/alert-dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu'
import { mutate } from 'swr'
import { DateTime } from 'luxon'

// column declaration for DataTable
const columnHelper = createColumnHelper<Customer>()

// Declaration of which column can be filtered
const columnFilter: FilterColumn[] = [
  {
    columnKey: 'email',
    header: 'Search',
    dataType: 'string'
  }
]

const RowActions: FC<{ row: Row<Customer> }> = ({ row }) => {
  const customerId = row.original.id
  const { toast } = useToast()

  return (
    <div className="w-full flex justify-end">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0" onClick={(e) => e.stopPropagation()}>
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem className="space-x-2 text-red-500">
            <AlertDialog>
              <AlertDialogTrigger onClick={(e) => e.stopPropagation()} className="text-red-500">
                <div className="flex flex-row items-center gap-x-2">
                  <Trash2Icon size="16" />
                  <span>Delete</span>
                </div>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Delete {row.original.email}</AlertDialogTitle>
                  <AlertDialogDescription className="text-red-500">
                    *NOTE: this action cannot be undone
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={async (e) => {
                      e.stopPropagation()

                      try {
                        const { data: response } = await AuthService.softDelete(customerId)

                        if (response.success) {
                          toast({
                            title: 'Delete successfully',
                            variant: 'success'
                          })

                          mutate(
                            (key) =>
                              typeof key === 'string' &&
                              key.startsWith(CustomerService.getCustomers)
                          )
                        }
                      } catch (e) {
                        console.log(e)
                        toast({
                          title: 'Action failed, please try again',
                          variant: 'destructive'
                        })
                      }
                    }}
                  >
                    Confirm
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}

const CustomerTable = () => {
  const nav = useNavigate()
  const [editBlockUsers, setEditBlockUsers] = useState(false)
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])
  const { toast } = useToast()

  const columns: ColumnDef<Customer>[] = [
    {
      id: 'email',
      accessorKey: 'email',
      header: 'Email',
      enableSorting: true
    },
    {
      accessorKey: 'first_name',
      header: 'Name',
      cell: (props) => {
        const value = props.row.original
        return value.first_name || value.last_name
          ? value.first_name + ' ' + value.last_name
          : undefined
      }
    },
    {
      id: 'current_stage',
      accessorKey: 'current_stage',
      header: 'Stage'
    },
    {
      accessorKey: 'mobile_number',
      header: 'Mobile Number'
    },
    {
      accessorKey: 'created_at',
      header: 'Date',
      cell: (props) => DateTime.fromISO(props.getValue<string>()).toFormat('yyyy-MM-dd HH:mm:ss')
    },
    columnHelper.display({
      id: 'actions',
      cell: (props) => !editBlockUsers && <RowActions row={props.row} />
    })
  ]

  const TriggerBlockUsersButton = (
    <div className="flex justify-end">
      <Button
        type="button"
        variant={editBlockUsers ? 'outline' : 'destructive'}
        onClick={() => {
          // if (editBlockUsers) {
          //   setSelectedUsers([])
          // }
          setEditBlockUsers((prev) => !prev)
        }}
      >
        {editBlockUsers ? (
          <div className="flex flex-row items-center gap-x-2">
            <MonitorXIcon size="16" />
            Cancel
          </div>
        ) : (
          <div className="flex flex-row items-center gap-x-2">
            <MonitorXIcon size="16" />
            Block Users
          </div>
        )}
      </Button>
    </div>
  )

  const ConfirmBlockUsersButton = (
    <div className="flex justify-end">
      <AlertDialog>
        <AlertDialogTrigger asChild onClick={(e) => e.stopPropagation()}>
          <Button type="button" variant="destructive" disabled={selectedUsers.length <= 0}>
            <div className="flex flex-row items-center gap-x-2">
              <MonitorXIcon size="16" />
              Block ({selectedUsers.length})
            </div>
          </Button>
        </AlertDialogTrigger>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Block {selectedUsers.length} Frineds</AlertDialogTitle>
            <AlertDialogDescription>Are you sure to block?</AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={async (e) => {
                e.stopPropagation()
                try {
                  const { data } = await AuthService.bulkBlockLicenseUsers({ ids: selectedUsers })

                  if (data.success) {
                    setEditBlockUsers(false)
                    toast({
                      title: 'Block users successfully',
                      variant: 'success'
                    })
                  }
                } catch (error) {
                  console.log(error)
                  toast({
                    title: 'Action failed, please try again',
                    variant: 'destructive'
                  })
                }
              }}
            >
              Confirm
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )

  return (
    <>
      <DataTable<Customer, unknown, CustomerResponse>
        columns={
          editBlockUsers
            ? [
                {
                  id: 'select',
                  header: ({ table }) => (
                    <IndeterminateCheckbox
                      {...{
                        checked: table.getIsAllRowsSelected(),
                        indeterminate: table.getIsSomeRowsSelected(),
                        onChange: table.getToggleAllPageRowsSelectedHandler()
                      }}
                    />
                  ),
                  cell: ({ row }) => (
                    <div className="px-1">
                      <IndeterminateCheckbox
                        {...{
                          checked: row.getIsSelected(),
                          disabled: !row.getCanSelect(),
                          indeterminate: row.getIsSomeSelected(),
                          onChange: row.getToggleSelectedHandler()
                        }}
                      />
                    </div>
                  )
                },
                ...columns
              ]
            : columns
        }
        filterColumns={columnFilter}
        filterParam="q"
        swrService={CustomerService.getCustomers}
        pageParam="offset"
        limitParam="limit"
        sortParam="sort"
        sortColumns={['email', 'created_at']}
        defaultSort={[
          {
            id: 'created_at',
            desc: true
          }
        ]}
        toRow={CustomerService.toRow}
        toPaginate={CustomerService.toPaginate}
        setSelectedRows={(selectedRows) => {
          setSelectedUsers(selectedRows as string[])
        }}
        initialSelected={selectedUsers}
        idDataType="string"
        onRowClick={(row: Row<Customer>) => {
          if (editBlockUsers) {
            row.toggleSelected()
          } else {
            nav(`/friends/${row.original.id}`)
          }
        }}
        triggerButton={TriggerBlockUsersButton}
        triggerState={editBlockUsers}
        actionButton={ConfirmBlockUsersButton}
      />
    </>
  )
}

export default CustomerTable
