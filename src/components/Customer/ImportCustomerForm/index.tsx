/* eslint-disable @typescript-eslint/ban-ts-comment */
import * as React from 'react'

import { cn } from '@/lib/utils'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Dropzone } from '@/components/Form/Dropzone'
import { useFormContext } from 'react-hook-form'
import FileService from '@/network/services/file'
import { CreateBatchJobInput } from '@/types/BatchJob'
import BatchJobService from '@/network/services/batch-job'
import { toast } from '@/components/ui/use-toast'
import { isAxiosError } from 'axios'
import { FileWithPath } from 'react-dropzone'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from '@/components/ui/accordion'
import { Separator } from '@/components/ui/separator'

interface ImportCustomerForm extends React.HTMLAttributes<HTMLDivElement> {}

export function ImportCustomerForm({ className }: ImportCustomerForm) {
  const form = useFormContext<CreateBatchJobInput & { file?: File }>()
  const file = form.watch('file') as FileWithPath

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      if (!values.file) {
        return
      }

      const { data } = await FileService.uploadPrivate([values.file])
      const fileKey = data.uploads[0].key
      values.context.fileKey = fileKey
      values.type = 'customer-import'

      delete values.file

      await BatchJobService.createBatchJob(values)

      toast({
        title: 'File uploaded',
        variant: 'success'
      })
    } catch (error) {
      console.error(error)
      if (isAxiosError(error) && error.response?.data.type == 'duplicate_error') {
        // TODO: need to handle errors here
      } else {
        form.setError('root', {})
      }

      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <>
      <form
        id="import-customer-form"
        onSubmit={onSubmit}
        className={cn('mx-auto flex max-w-4xl flex-col space-y-8', className)}
      >
        <FormField
          control={form.control}
          name="file"
          rules={{ required: 'Please upload the csv file' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Upload CSV file</FormLabel>
              <FormControl>
                <Dropzone
                  multiple={false}
                  onDrop={(acceptedFiles) => {
                    const file = Object.assign(acceptedFiles[0])

                    form.setValue('file', file as File, {
                      shouldValidate: true
                    })
                  }}
                  description="Drop your csv file here, or click to browse"
                  accept=".csv"
                  files={file ? [file] : []}
                  {...field}
                />
              </FormControl>
              <FormDescription>Please follow the format exactly</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
      </form>
      <div className="pt-5 pb-5">
        <Separator className={cn('mx-auto flex max-w-4xl flex-col space-y-8', className)} />
      </div>
      <div className={cn('mx-auto flex max-w-4xl flex-col space-y-2', className)}>
        <h1 className="font-semibold">Guidelines</h1>

        <Accordion type="multiple" className="w-full">
          <AccordionItem value="create-customer">
            <AccordionTrigger>A. Create New Customer</AccordionTrigger>
            <AccordionContent>
              <div className="space-y-5">
                'Customer Current Stage', 'Customer First Name', 'Customer Last Name', 'Customer
                Email', 'Customer Mobile Number', ' Customer DOB'
                <span className="font-semibold text-red-900">compulsory</span> fields. 'Customer
                Email' will be used to uniquely identify each row, create/update process will be
                carried out depending on if an email already exist.
                <br />
                <div className="bg-gray-300 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md">
                  1. 'Customer Current Stage' column value can only be 'I am pregnant / My partner
                  is pregnant' or 'I am parent'.
                </div>
                <div className="bg-gray-300 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md">
                  2. When 'Customer Password' is not provided, it will be default to 'abcd1234'.
                </div>
                <div className="bg-gray-300 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md">
                  3. When 'Customer Country Code' is not provided, it will be default to 'my' which
                  represent 'Malaysia'.
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="update-customer">
            <AccordionTrigger>A. Update Customer</AccordionTrigger>
            <AccordionContent>
              <div className="space-y-5">
                'Customer Current Stage', 'Customer First Name', 'Customer Last Name', 'Customer
                Email', 'Customer Mobile Number', ' Customer DOB'
                <span className="font-semibold text-red-900">compulsory</span> fields. 'Customer
                Email' will be used to uniquely identify each row, create/update process will be
                carried out depending on if an email already exist.
                <br />
                <div className="bg-gray-300 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md">
                  1. 'Customer Current Stage' column value can only be 'I am pregnant / My partner
                  is pregnant' or 'I am parent'.
                </div>
                <div className="bg-gray-300 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md">
                  2. When 'Customer Password' is blank, the password will not be updated.
                </div>
                <div className="bg-gray-300 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md">
                  3. When 'Customer Country Code' is not provided, it will be default to 'my' which
                  represent 'Malaysia'.
                </div>
                <div className="bg-gray-300 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md">
                  4. Please ensure all the old values (except password, country code as mentioned in
                  2 and 3) is present when updating a customer row. Or it will be treated as{' '}
                  <span className="font-semibold text-red-900">update to empty value</span>.
                  <div className="bg-gray-400 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md m-3">
                    Eg.
                    <br />
                    1. Before Update Customer A:
                    <br />
                    ...other column values...
                    <br />
                    Customer Last Name: Kai Yuan
                    <br />
                    ...other column values...
                    <br />
                    <br />
                    <br />
                    2. Submitted spreadsheet to update Customer A:
                    <br />
                    ...other column values...
                    <br />
                    Customer Last Name:{' '}
                    <span className="font-semibold text-red-900">
                      (value not provided, assume this is blank)
                    </span>
                    <br />
                    ...other column values...
                    <br />
                    <br />
                    <br />
                    3. Customer A after update:
                    <br />
                    ...other column values...
                    <br />
                    Customer Last Name:{' '}
                    <span className="font-semibold text-red-900 line-through">Kai Yuan</span>{' '}
                    <span className="font-semibold text-red-900">
                      (value are now updated blank, assume this is blank)
                    </span>
                    <br />
                    ...other column values...
                    <br />
                    <br />
                  </div>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
    </>
  )
}
