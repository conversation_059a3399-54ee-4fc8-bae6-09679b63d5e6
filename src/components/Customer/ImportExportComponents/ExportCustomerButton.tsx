import { Button } from '@/components/ui/button'
import useAuth from '@/hooks/useAuth'
import BatchJobService from '@/network/services/batch-job'
import { FileIcon } from 'lucide-react'
import { mutate } from 'swr'
import { useToast } from '@/components/ui/use-toast'
import { useState } from 'react'
import { Icons } from '@/components/icons'
import { serialize } from '@/network/request'

export const ExportCustomerButton = () => {
  const { role } = useAuth()
  const { toast } = useToast()
  const [isBusy, setIsBusy] = useState(false)

  const exportCustomer = async () => {
    setIsBusy(true)

    try {
      await BatchJobService.createBatchJob({
        type: 'customer-export',
        context: { filterable_fields: { export: true } }
      })
      mutate(
        (key) =>
          typeof key === 'string' &&
          key.startsWith(serialize(BatchJobService.getBatchJobs, { type: ['customer-export'] }))
      )
      toast({
        title: `Export process initiated`,
        variant: 'success'
      })
    } catch (error) {
      // TODO: handle error
    }

    setIsBusy(false)
  }

  if (role != 'super_admin') {
    return <></>
  }

  return (
    <Button variant="outline" onClick={exportCustomer} disabled={isBusy}>
      {isBusy ? (
        <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
      ) : (
        <FileIcon size="16" className="mr-2" />
      )}
      Export
    </Button>
  )
}
