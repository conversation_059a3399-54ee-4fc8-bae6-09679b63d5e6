import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import { DialogClose, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Form } from '@/components/ui/form'
import { Customer } from '@/types/Customer'
import { FC } from 'react'
import { useForm } from 'react-hook-form'

const UpdateCustomerChildForm: FC<{
  initialValues?: Customer
  setIsDialogOpen: (value: boolean) => void
}> = ({ setIsDialogOpen }) => {
  const form = useForm<Customer>({
    shouldUseNativeValidation: false
    // defaultValues: {
    //   first_name: initialValues?.first_name,
    //   last_name: initialValues?.last_name,
    //   current_stage: initialValues?.current_stage,
    //   gender: initialValues?.gender,
    //   race: initialValues?.race,
    //   email: initialValues?.email,
    //   mobile_number: initialValues?.mobile_number,
    //   dob: initialValues?.dob
    // }
  })

  const onSubmit = form.handleSubmit(async (values) => {
    console.log('submit values', values)
    setIsDialogOpen(false)

    // // block if no changes
    // if (isEqual(values.event.explore_description, initialValues.explore_description)) {
    //   console.log('same')
    //   toast({
    //     title: 'No changes applied',
    //     variant: 'destructive'
    //   })

    //   return
    // }

    // try {
    //   const { data } = await EventService.updateEvent(initialValues.id, values)
    //   console.log('response', data)

    //   if (data.success) {
    //     setIsDialogOpen(false)
    //     mutate((key) => typeof key === 'string' && key.includes(EventService.getEvents))
    //     toast({
    //       title: 'Explore description updated',
    //       variant: 'success'
    //     })
    //   } else {
    //     toast({
    //       title: 'Action failed, please try again',
    //       variant: 'destructive'
    //     })
    //   }
    // } catch (e) {
    //   console.log(e)
    //   toast({
    //     title: 'Action failed, please try again',
    //     variant: 'destructive'
    //   })
    // }
  })

  return (
    <DialogContent className="max-h-[80vh] max-w-3xl overflow-y-auto">
      <DialogHeader>
        <DialogTitle className="flex items-center justify-between">
          Edit Customer Child
          <div className="flex space-x-2">
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button
              type="submit"
              form="update-exhibitor-brand-form"
              disabled={form.formState.isSubmitting}
              onClick={() => {
                form.clearErrors()
              }}
            >
              {form.formState.isSubmitting && (
                <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
              )}
              Save
            </Button>
          </div>
        </DialogTitle>
      </DialogHeader>

      <Form {...form}>
        <form id="update-exhibitor-brand-form" className="grid gap-4" onSubmit={onSubmit}></form>
      </Form>
    </DialogContent>
  )
}

export default UpdateCustomerChildForm
