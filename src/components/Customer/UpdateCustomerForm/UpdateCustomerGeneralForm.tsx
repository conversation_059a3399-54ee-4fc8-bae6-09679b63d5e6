import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import { DialogClose, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import { toast } from '@/components/ui/use-toast'
import CustomerService from '@/network/services/customer'
import { Customer, UpdateCustomerInput } from '@/types/Customer'
import isEqual from 'lodash.isequal'
import { FC } from 'react'
import { useForm } from 'react-hook-form'
import { mutate } from 'swr'

const UpdateCustomerGeneralForm: FC<{
  initialValues: Customer
  setIsDialogOpen: (value: boolean) => void
}> = ({ initialValues, setIsDialogOpen }) => {
  const form = useForm<UpdateCustomerInput>({
    shouldUseNativeValidation: false,
    defaultValues: {
      first_name: initialValues?.first_name,
      last_name: initialValues?.last_name,
      current_stage: initialValues?.current_stage,
      gender: initialValues?.gender,
      race: initialValues?.race,
      email: initialValues?.email,
      mobile_number: initialValues?.mobile_number,
      dob: initialValues?.dob
    }
  })

  const onSubmit = form.handleSubmit(async (values) => {
    console.log('submit values', values)
    setIsDialogOpen(false)

    // block if no changes
    if (isEqual(values, initialValues)) {
      console.log('same')
      toast({
        title: 'No changes applied',
        variant: 'destructive'
      })

      return
    }

    try {
      const { data } = await CustomerService.updateCustomer(initialValues.id, values)
      console.log('response', data)

      if (data.success) {
        setIsDialogOpen(false)
        mutate(
          (key) =>
            typeof key === 'string' && key.includes(CustomerService.getCustomer(initialValues.id))
        )
        toast({
          title: 'Customer information updated',
          variant: 'success'
        })
      } else {
        toast({
          title: 'Action failed, please try again',
          variant: 'destructive'
        })
      }
    } catch (e) {
      console.log(e)
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle className="flex justify-between items-center">
          Edit Customer Information
          <div className="flex space-x-2">
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button
              type="submit"
              form="update-customer-general-form"
              disabled={form.formState.isSubmitting}
              onClick={() => {
                form.clearErrors()
              }}
            >
              {form.formState.isSubmitting && (
                <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
              )}
              Save
            </Button>
          </div>
        </DialogTitle>
      </DialogHeader>

      <Form {...form}>
        <form id="update-customer-general-form" className="grid gap-4" onSubmit={onSubmit}>
          <FormField
            control={form.control}
            name="email"
            rules={{ required: 'This is required' }}
            render={({ field }) => {
              return (
                <FormItem className="flex flex-col col-span-2">
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input disabled {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )
            }}
          />

          <FormField
            control={form.control}
            name="first_name"
            // rules={{ required: 'This is required' }}
            render={({ field }) => {
              return (
                <FormItem className="flex flex-col col-span-2">
                  <FormLabel>First Name</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )
            }}
          />

          <FormField
            control={form.control}
            name="last_name"
            // rules={{ required: 'This is required' }}
            render={({ field }) => {
              return (
                <FormItem className="flex flex-col col-span-2">
                  <FormLabel>Last Name</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )
            }}
          />

          <FormField
            control={form.control}
            name="current_stage"
            // rules={{ required: 'This is required' }}
            render={({ field }) => {
              return (
                <FormItem className="flex flex-col col-span-2">
                  <FormLabel>Current Stage</FormLabel>
                  <FormControl>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose an option" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {['I am a pregnant / My partner is pregnant', 'I am a parent'].map(
                          (value) => {
                            return (
                              <SelectItem key={value} value={value}>
                                {value}
                              </SelectItem>
                            )
                          }
                        )}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )
            }}
          />

          <FormField
            control={form.control}
            name="gender"
            // rules={{ required: 'This is required' }}
            render={({ field }) => {
              return (
                <FormItem className="flex flex-col col-span-2">
                  <FormLabel>Gender</FormLabel>
                  <FormControl>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose an option" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {['Boy', 'Girl'].map((value) => {
                          return (
                            <SelectItem key={value} value={value}>
                              {value}
                            </SelectItem>
                          )
                        })}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )
            }}
          />

          {/* <FormField
            control={form.control}
            name="race"
            // rules={{ required: 'This is required' }}
            render={({ field }) => {
              return (
                <FormItem className="flex flex-col col-span-2">
                  <FormLabel>Race</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )
            }}
          /> */}

          <FormField
            control={form.control}
            name="mobile_number"
            rules={{ required: 'This is required' }}
            render={({ field }) => {
              return (
                <FormItem className="flex flex-col col-span-2">
                  <FormLabel>Mobile Number</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )
            }}
          />

          <FormField
            control={form.control}
            name="dob"
            // rules={{ required: 'This is required' }}
            render={({ field }) => {
              return (
                <FormItem className="flex flex-col col-span-2">
                  <FormLabel>Date of Birth</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )
            }}
          />
        </form>
      </Form>
    </DialogContent>
  )
}

export default UpdateCustomerGeneralForm
