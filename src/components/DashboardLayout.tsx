/* eslint-disable @typescript-eslint/ban-ts-comment */
// import { Menu } from "@/components/Nav/Menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from '@/components/ui/alert-dialog'
import { Navigate, Outlet, useMatches } from 'react-router-dom'
import useAuth, { sessionState } from '@/hooks/useAuth'
import { useSnapshot } from 'valtio'
import { SidebarInset, SidebarProvider } from './ui/sidebar'
import { AppSidebar } from './Sidebar/app-sidebar'
import { SiteHeader } from './Sidebar/site-header'
import { useDocumentTitle } from 'usehooks-ts'
import { useEffect } from 'react'

const DashboardLayout = () => {
  const { user } = useAuth()
  const matches = useMatches()
  useDocumentTitle('Dashboard')

  const { isTimeout } = useSnapshot(sessionState)

  const crumbs = matches
    // @ts-ignore
    .filter((match) => Boolean(match.handle?.crumb))
    // @ts-ignore
    .map((match) => {
      // @ts-ignore
      return match.handle.crumb(match.params)
    })

  // Helper function to extract text from React elements
  const extractTextFromReactElement = (element: any): string => {
    // If it's a string, return it directly
    if (typeof element === 'string') return element

    // If it's null or undefined, return empty string
    if (!element) return ''

    // If it's a React element with props.children
    if (element.props) {
      if (element.props.children) {
        // If children is a string, return it
        if (typeof element.props.children === 'string') {
          return element.props.children
        }

        // If children is an array, extract text from each child and join
        if (Array.isArray(element.props.children)) {
          return element.props.children
            .map((child: any) => extractTextFromReactElement(child))
            .join('')
        }

        // Recursively extract text from children
        return extractTextFromReactElement(element.props.children)
      }

      if (typeof element.props.label === 'string') {
        return element.props.label
      }
    }

    // Default fallback
    return ''
  }

  // Update document title based on breadcrumbs
  useEffect(() => {
    // Helper function to determine the page title
    const determinePageTitle = () => {
      // Check if we're on a detail page (has more than 2 breadcrumbs)
      if (crumbs.length > 2) {
        // For detail pages, use the parent section name (second breadcrumb)
        // This prevents using dynamic IDs as page titles
        const parentCrumb = crumbs[1] // The parent section breadcrumb
        if (parentCrumb) {
          const parentText = extractTextFromReactElement(parentCrumb)
          if (parentText) {
            return `${parentText} Detail`
          }
        }
      } else if (crumbs.length > 0) {
        // For section pages or dashboard, use the last breadcrumb
        const lastCrumb = crumbs[crumbs.length - 1]
        if (lastCrumb) {
          const extractedText = extractTextFromReactElement(lastCrumb)
          if (extractedText) {
            return extractedText
          }
        }
      }
      return 'Dashboard'
    }

    // Get the title and update document.title directly
    const pageTitle = determinePageTitle()
    const appName = 'awegoodbaby'
    document.title = `${pageTitle} | ${appName}`
  }, [crumbs]) // Only re-run when crumbs change

  if (!user) {
    return <Navigate to="/" replace />
  }

  return (
    <>
      <SidebarProvider>
        <AppSidebar variant="inset" />
        <SidebarInset>
          {/* <Navbar /> */}
          <SiteHeader crumbs={crumbs} />
          <Outlet />
          <AlertDialog open={isTimeout}>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Session Timeout</AlertDialogTitle>
                <AlertDialogDescription>Please login again</AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <a href="/">
                  <AlertDialogAction>Okay</AlertDialogAction>
                </a>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </SidebarInset>
      </SidebarProvider>
    </>
  )
}

export default DashboardLayout
