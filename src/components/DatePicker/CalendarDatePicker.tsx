'use client'

import * as React from 'react'
import { CalendarIcon } from '@radix-ui/react-icons'

import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Calendar, CalendarProps } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'

type CalendarDatePickerProps = {
  className?: React.HTMLAttributes<HTMLDivElement>
  buttonLabel?: string
  placeholder?: string
} & CalendarProps

export function CalendarDatePicker({
  className,
  buttonLabel,
  placeholder = 'Pick a date',
  onChange,
  value,
  includeTime = false,
  ...calendarProps
}: CalendarDatePickerProps & {
  onChange?: (date: Date) => void
  value?: string
  includeTime?: boolean
}) {
  return (
    <div className={cn('grid gap-20', className)}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant={'outline'}
            className={cn(
              'justify-start text-left font-normal',
              !buttonLabel && 'text-muted-foreground'
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            <span>{buttonLabel ? buttonLabel : placeholder}</span>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="end">
          <Calendar
            mode="default"
            onChange={onChange}
            value={value}
            includeTime={includeTime}
            {...calendarProps}
          />
        </PopoverContent>
      </Popover>
    </div>
  )
}
