import { <PERSON><PERSON><PERSON><PERSON>, J<PERSON>NContent, useEditor } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import React from 'react'

interface EditorProps {
  content?: string
  placeholder?: string
  onChange?: (value: JSONContent | string) => void
}

const ReadOnlyEditorComponent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & EditorProps
>(({ className = 'prose max-w-none w-full bg-transparent text-sm', content = '' }) => {
  const editor = useEditor(
    {
      editable: false,
      extensions: [StarterKit],
      content: content,
      editorProps: {
        attributes: {
          class: className
        }
      }
    },
    [content]
  )

  return (
    <div className="editor w-full text-left">
      <EditorContent editor={editor} />
    </div>
  )
})

export default ReadOnlyEditorComponent
