import { <PERSON><PERSON><PERSON><PERSON>, J<PERSON><PERSON>ontent, useCurrentEditor } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import CharacterCount from '@tiptap/extension-character-count'
import { Document } from '@tiptap/extension-document'

interface EditorProps {
  content?: string
  placeholder?: string
  limit?: number
  onChange?: (value: JSONContent | string) => void
}

const Count = () => {
  const { editor } = useCurrentEditor()

  if (!editor) {
    return
  }

  return (
    <div className="text-sm text-muted-foreground rounded-b-md border-b px-3 pb-2 bg-white">
      {editor.storage.characterCount.characters()} characters
    </div>
  )
}

const TitleDocument = Document.extend({
  content: 'block'
})

const TextEditorComponent = ({ content = '', limit = 180, onChange }: EditorProps) => {
  return (
    <div className="editor w-full rounded-md border">
      {/*
       // @ts-expect-error: missing children bug */}
      <EditorProvider
        slotAfter={<Count />}
        extensions={[StarterKit, TitleDocument, CharacterCount.configure({ limit: limit })]}
        content={content}
        editorProps={{
          attributes: {
            class:
              'prose focus-visible:outline-none max-w-none placeholder:text-muted-foreground w-full bg-transparent px-3 py-2 text-sm disabled:cursor-not-allowed disabled:opacity-50'
          }
        }}
        onUpdate={({ editor }) => {
          onChange && onChange(editor.getText())
        }}
      />
    </div>
  )
}

export default TextEditorComponent
