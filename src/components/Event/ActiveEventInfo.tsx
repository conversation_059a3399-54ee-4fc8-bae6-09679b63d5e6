import { FC } from 'react'
import { Card, CardTitle } from '../ui/card'
import EventService from '@/network/services/event'
import { DateTime } from 'luxon'
import Join<PERSON>vent<PERSON>utton from './JoinEventButton'
import { useNavigate } from 'react-router-dom'
import { Button } from '../ui/button'
import { useActiveEvent } from '@/hooks/event/useActiveEvent'

const ActiveEventInfo: FC<{}> = () => {
  return (
    <div className="grid gap-4">
      <CardTitle>Ongoing Event</CardTitle>
      <Card className="p-4">
        <ActiveEventCard />
      </Card>
    </div>
  )
}

const ActiveEventCard: FC = () => {
  const nav = useNavigate()
  const { event, error, isLoading } = useActiveEvent()
  console.log('active event', event)

  if (error) {
    console.log('active event error', error)
    return <p className="text-sm font-semibold">No ongoing event.</p>
  }
  if (isLoading) return <></>

  return (
    <div className="flex justify-between items-center">
      <div>
        <p className="text-sm font-bold">{event?.title ?? '-'}</p>
        <p className="text-sm font-semibold">{EventService.eventDurationString(event)}</p>
        <p className="text-sm font-semibold">
          Joinable Until: {DateTime.fromISO(event?.to as string).toFormat('dd LLL yyyy')}
        </p>
      </div>
      <div className="grid grid-cols-2 gap-4">
        {/* Manage product button */}
        <Button onClick={() => nav(`/events/${event?.id}`)}>
          {event?.joined ? 'Manage Products' : 'Event Details'}
        </Button>
        {event && <JoinEventButton event={event} />}
      </div>
    </div>
  )
}

export default ActiveEventInfo
