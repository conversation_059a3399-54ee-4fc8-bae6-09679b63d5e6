import { CreateEventForm } from '@/components/Event/CreateEventForm'
import { Icons } from '@/components/icons'
import { But<PERSON> } from '@/components/ui/button'
import {
  Sheet,
  SheetClose,
  Sheet<PERSON>ontent,
  Sheet<PERSON>eader,
  SheetTitle,
  SheetTrigger
} from '@/components/ui/sheet'
import { Form } from '@/components/ui/form'
import { CreateEventInput } from '@/types/CreateEvent'
import { PlusCircleIcon } from 'lucide-react'
import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'

const AddEventButton = () => {
  const [isSheetOpen, setIsSheetOpen] = useState(false)
  const form = useForm<CreateEventInput>({
    shouldUseNativeValidation: false
  })

  useEffect(() => {
    if (form.formState.isSubmitSuccessful) {
      form.reset()
    }
  }, [form.formState.isSubmitSuccessful])

  return (
    <div className="flex justify-end">
      <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
        <SheetTrigger asChild>
          <Button>
            <PlusCircleIcon size="16" className="mr-2" />
            Create Event
          </Button>
        </SheetTrigger>
        <SheetContent side="bottom" className="w-full h-full">
          <SheetHeader className="max-w-4xl mx-auto">
            <SheetTitle className="flex justify-between items-center">
              New Event
              <div className="flex space-x-2 ">
                <SheetClose asChild>
                  <Button
                    variant="outline"
                    onClick={() => {
                      form.clearErrors()
                    }}
                  >
                    Cancel
                  </Button>
                </SheetClose>
                <Button type="submit" form="create-event-form">
                  {form.formState.isSubmitting && (
                    <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  Create
                </Button>
              </div>
            </SheetTitle>
          </SheetHeader>
          <Form {...form}>
            <CreateEventForm setIsDialogOpen={setIsSheetOpen} />
          </Form>
        </SheetContent>
      </Sheet>
    </div>
  )
}

export default AddEventButton
