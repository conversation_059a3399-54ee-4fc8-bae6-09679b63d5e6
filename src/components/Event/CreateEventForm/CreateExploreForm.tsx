import { Input } from '@/components/ui/input'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { useForm } from 'react-hook-form'
import { useEffect, useState } from 'react'
import { DialogClose, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { cn, convertValuesToFormData } from '@/lib/utils'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import { capitalize } from 'radash'
import { useSWRConfig } from 'swr'
import { toast } from '@/components/ui/use-toast'
import EventService from '@/network/services/event'
import { CreateExploreInput } from '@/types/ExploreCard'
import { Label } from '@/components/ui/label'
import { Image } from '@unpic/react'
import { useParams } from 'react-router-dom'
import TextEditorComponent from '@/components/Editor/TextEditor'

interface CreateExploreFormProps extends React.HTMLAttributes<HTMLDivElement> {
  initialValues?: CreateExploreInput & { id?: number; image_url?: string }
  setIsDialogOpen: (value: boolean) => void
  isEdit?: boolean
}

const CreateExploreForm: React.FC<CreateExploreFormProps> = ({
  className,
  initialValues,
  setIsDialogOpen,
  isEdit = false
}) => {
  const { event_id } = useParams()
  const { mutate } = useSWRConfig()
  const form = useForm<CreateExploreInput>({
    shouldUseNativeValidation: false,
    defaultValues: {
      ...initialValues
    }
  })

  useEffect(() => {
    if (initialValues) {
      form.reset(initialValues)
    }
  }, [initialValues])

  const [imagePreview, setImagePreview] = useState<string>('')

  async function createEventExplore(values: CreateExploreInput) {
    console.log('value', values)

    const formData = new FormData()
    convertValuesToFormData(formData, values)
    // // logging formData
    // for (var pair of formData.entries()) {
    //   console.log('boo', pair[0] + ', ' + pair[1])
    // }

    try {
      const { data } = await EventService.createExplore(formData)
      console.log('response', data)

      if (data.success) {
        // refresh explore list
        mutate(
          (key) =>
            typeof key === 'string' &&
            (key.includes(EventService.getEventExplores) ||
              key.includes(EventService.getEvent(event_id as string)))
        )

        // Close dialog & reset
        setIsDialogOpen(false)
        setImagePreview('')
        form.reset()

        toast({
          title: 'Explore option created',
          variant: 'success'
        })
      }
    } catch (e) {
      console.log(e)

      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  }

  // TODO: edit event explore api
  async function editEventExplore(values: CreateExploreInput) {
    console.log('edit value', values)

    const formData = new FormData()
    convertValuesToFormData(formData, values)
    // // logging formData
    // for (var pair of formData.entries()) {
    //   console.log('boo', pair[0] + ', ' + pair[1])
    // }

    try {
      const { data } = await EventService.updateExplore(initialValues!.id!, formData)
      console.log('response', data)

      if (data.success) {
        // refresh explore list
        mutate(
          (key) =>
            typeof key === 'string' &&
            (key.includes(EventService.getEventExplores) ||
              key.includes(EventService.getEvent(event_id as string)))
        )

        // Close dialog & reset
        setIsDialogOpen(false)
        setImagePreview('')
        form.reset()

        toast({
          title: 'Explore option updated',
          variant: 'success'
        })
      }
    } catch (e) {
      console.log(e)

      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  }

  const onSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    // this part is for stopping parent forms to trigger their submit
    if (event) {
      // sometimes not true, e.g. React Native
      if (typeof event.preventDefault === 'function') {
        event.preventDefault()
      }
      if (typeof event.stopPropagation === 'function') {
        // prevent any outer forms from receiving the event too
        event.stopPropagation()
      }
    }

    // TODO Create new explore api
    form.handleSubmit(async (values) => {
      isEdit ? await editEventExplore(values) : await createEventExplore(values)
    })(event)
  }

  return (
    <DialogContent className="max-w-3xl" onInteractOutside={(e) => e.preventDefault()}>
      <DialogHeader>
        <DialogTitle className="flex justify-between items-center">
          {isEdit ? 'Edit' : 'New'} Explore Option
          <div className="flex space-x-2">
            <DialogClose asChild>
              <Button
                variant="outline"
                onClick={() => {
                  form.reset()
                  form.clearErrors()
                }}
              >
                Cancel
              </Button>
            </DialogClose>
            <Button
              type="submit"
              form="create-explore-form"
              onClick={() => {
                form.clearErrors()
              }}
            >
              Save
            </Button>
          </div>
        </DialogTitle>
      </DialogHeader>

      <Form {...form}>
        <form
          id="create-explore-form"
          onSubmit={onSubmit}
          className={cn('flex flex-col space-y-8', className)}
        >
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="type"
              rules={{ required: 'Please provide the event image' }}
              render={({ field }) => {
                return (
                  <FormItem className="flex flex-col col-span-1">
                    <FormLabel>Type</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose an option" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {['shop', 'eat', 'stay', 'sight'].map((value) => {
                          return (
                            <SelectItem key={value} value={value}>
                              {capitalize(value)}
                            </SelectItem>
                          )
                        })}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )
              }}
            />

            <FormField
              control={form.control}
              name="title"
              rules={{ required: 'This is required' }}
              render={({ field }) => {
                return (
                  <FormItem className="flex flex-col col-span-2">
                    <FormLabel>Title</FormLabel>
                    <FormControl>
                      <TextEditorComponent content={field.value} onChange={field.onChange} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )
              }}
            />

            <FormField
              control={form.control}
              name="description"
              rules={{ required: 'This is required' }}
              render={({ field }) => {
                return (
                  <FormItem className="flex flex-col col-span-2">
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <TextEditorComponent content={field.value} onChange={field.onChange} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )
              }}
            />

            <FormField
              control={form.control}
              name="image"
              rules={isEdit ? undefined : { required: 'This is required' }}
              render={({ field }) => (
                <FormItem className="flex flex-col col-span-2">
                  <FormLabel>Image</FormLabel>
                  <div className="flex w-full space-x-2 ">
                    <div className="flex-1">
                      {imagePreview && (
                        <img className="w-[180px] py-4" src={imagePreview} alt="event-image" />
                      )}
                      {isEdit && !imagePreview && initialValues && initialValues.image_url && (
                        <div className="flex flex-col space-y-4 mt-2">
                          <div className="grid grid-cols-[150px_1fr_36px] space-x-2 items-center">
                            <Image
                              key={initialValues.image_url}
                              src={initialValues.image_url ?? ''}
                              height={150}
                              width={150}
                              objectFit="contain"
                              className="rounded-md"
                            />
                            <div className="flex flex-col">
                              <Label className="text-xs font-normal">Uploaded</Label>
                            </div>
                          </div>
                        </div>
                      )}
                      <FormControl>
                        <Input
                          type="file"
                          onChange={(e) => {
                            if (e.target.files) {
                              field.onChange(e.target.files[0])
                              setImagePreview(URL.createObjectURL(e.target.files[0]))
                            }
                          }}
                        />
                      </FormControl>
                    </div>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </form>
      </Form>
    </DialogContent>
  )
}

export default CreateExploreForm
