import { Input } from '@/components/ui/input'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { useForm } from 'react-hook-form'
import { useSWRConfig } from 'swr'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from '@/components/ui/accordion'
import { bytesToSize, cn } from '@/lib/utils'
import { DialogClose, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import GettingHere from './GettingHere'
import ParkingRate from './ParkingRate'
import { toast } from '@/components/ui/use-toast'
import EventService from '@/network/services/event'
import { CreateVisitUsInput } from '@/types/VisitUs'
import { FileWithPath } from 'react-dropzone'
import { Dropzone } from '@/components/Form/Dropzone'
import { Image } from '@unpic/react'
import { Label } from '@/components/ui/label'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { MoreHorizontal, Trash2Icon } from 'lucide-react'
import FileService from '@/network/services/file'
import { useEffect } from 'react'
import { useParams } from 'react-router-dom'
import TextEditorComponent from '@/components/Editor/TextEditor'

interface CreateVisitUsFormProps extends React.HTMLAttributes<HTMLDivElement> {
  initialValues?: CreateVisitUsInput & { id?: number }
  setIsDialogOpen: (value: boolean) => void
  isEdit?: boolean
}

const CreateVisitUsForm: React.FC<CreateVisitUsFormProps> = ({
  className,
  initialValues,
  setIsDialogOpen,
  isEdit = false
}) => {
  const { event_id } = useParams()
  const { mutate } = useSWRConfig()
  const form = useForm<CreateVisitUsInput>({
    shouldUseNativeValidation: false,
    defaultValues: {
      ...initialValues
    }
  })

  useEffect(() => {
    if (initialValues) {
      console.log('reset')
      form.reset({ ...initialValues })
    }
  }, [initialValues])

  const imageFile = form.watch(`shuttle_van_service.image`)
  const imageFileWithPath = imageFile
    ? (imageFile as unknown as FileWithPath & { preview: string })
    : undefined

  async function createVisitUs(values: CreateVisitUsInput) {
    // upload shuttle image if not null
    if (values.shuttle_van_service?.image) {
      const { data } = await FileService.upload([values.shuttle_van_service.image])
      values.shuttle_van_service.image_url = data.uploads[0].url
      delete values.shuttle_van_service.image
    }

    if (
      values.shuttle_van_service?.image == undefined &&
      values.shuttle_van_service?.description == ''
    ) {
      values.shuttle_van_service = undefined
    }

    console.log('value', values)

    try {
      const { data } = await EventService.createVisitUs(values)
      console.log('response', data)

      if (data.success) {
        // refresh explore list
        mutate(
          (key) =>
            typeof key === 'string' &&
            (key.includes(EventService.getEventVisitUs) ||
              key.includes(EventService.getEvent(event_id as string)))
        )

        // Close dialog & reset
        setIsDialogOpen(false)
        form.reset()

        toast({
          title: 'Visit us created',
          variant: 'success'
        })
      }
    } catch (e) {
      console.log(e)

      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  }

  // TODO edit visit us api call
  async function editVisitUs(values: CreateVisitUsInput) {
    // upload shuttle image if not null
    if (values.shuttle_van_service?.image) {
      const { data } = await FileService.upload([values.shuttle_van_service.image])
      values.shuttle_van_service.image_url = data.uploads[0].url
      delete values.shuttle_van_service.image
    }

    if (
      values.shuttle_van_service?.image == undefined &&
      values.shuttle_van_service?.description == ''
    ) {
      values.shuttle_van_service = undefined
    }

    console.log('edit values', values)

    try {
      const { data } = await EventService.updateVisitUs(initialValues!.id!, values)
      console.log('response', data)

      if (data.success) {
        // refresh explore list
        mutate(
          (key) =>
            typeof key === 'string' &&
            (key.includes(EventService.getEventVisitUs) ||
              key.includes(EventService.getEvent(event_id as string)))
        )

        // Close dialog & reset
        setIsDialogOpen(false)
        form.reset()

        toast({
          title: 'Visit us updated',
          variant: 'success'
        })
      }
    } catch (e) {
      console.log(e)

      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  }

  const onSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    // this part is for stopping parent forms to trigger their submit
    if (event) {
      // sometimes not true, e.g. React Native
      if (typeof event.preventDefault === 'function') {
        event.preventDefault()
      }
      if (typeof event.stopPropagation === 'function') {
        // prevent any outer forms from receiving the event too
        event.stopPropagation()
      }
    }

    // TODO Create new explore api
    form.handleSubmit(async (values) => {
      isEdit ? await editVisitUs(values) : await createVisitUs(values)
    })(event)
  }

  return (
    <DialogContent
      className="max-w-4xl max-h-[80vh] overflow-y-auto"
      onInteractOutside={(e) => e.preventDefault()}
    >
      <DialogHeader>
        <DialogTitle className="flex justify-between items-center">
          {isEdit ? 'Edit' : 'New'} Visit Us
          <div className="flex space-x-2">
            <DialogClose asChild>
              <Button
                variant="outline"
                onClick={() => {
                  form.reset()
                  form.clearErrors()
                }}
              >
                Cancel
              </Button>
            </DialogClose>
            <Button
              type="submit"
              form="create-visit-us-form"
              onClick={() => {
                form.clearErrors()
              }}
            >
              Save
            </Button>
          </div>
        </DialogTitle>
      </DialogHeader>

      <Form {...form}>
        <form
          id="create-visit-us-form"
          onSubmit={onSubmit}
          className={cn('flex flex-col space-y-8 ', className)}
        >
          <Accordion type="multiple" className="w-full" defaultValue={['venue-info']}>
            <AccordionItem value="venue-info">
              <AccordionTrigger disabled>Venue Information</AccordionTrigger>
              <AccordionContent>
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="venue_name"
                    rules={{ required: 'Please provide the venue name' }}
                    render={({ field }) => (
                      <FormItem className="flex flex-col col-span-2">
                        <FormLabel>Venue</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="venue_address"
                    rules={{ required: 'Please provide the venue address' }}
                    render={({ field }) => (
                      <FormItem className="flex flex-col col-span-2">
                        <FormLabel>Address</FormLabel>
                        <FormControl>
                          <TextEditorComponent content={field.value} onChange={field.onChange} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="location_url"
                    rules={{ required: 'Please attach location link from Google Map' }}
                    render={({ field }) => (
                      <FormItem className="flex flex-col col-span-2">
                        <FormLabel>Google Map URL</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="contact_mobile_number"
                    rules={{ required: "Please provide contact number of venue's PIC" }}
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>Contact Number</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="contact_email"
                    rules={{ required: "Please provide email of venue's PIC" }}
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>Email Address</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="website_url"
                    render={({ field }) => (
                      <FormItem className="flex flex-col col-span-2">
                        <FormLabel>Website (if any)</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="instagram_url"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>Instagram</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="facebook_url"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>Facebook</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="getting-here">
              <AccordionTrigger>Getting Here</AccordionTrigger>
              <AccordionContent>
                <FormField
                  control={form.control}
                  name="getting_here"
                  render={() => (
                    <FormItem className="flex flex-col col-span-2">
                      <FormControl>
                        <GettingHere />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="parking">
              <AccordionTrigger>Parking & Rate</AccordionTrigger>
              <AccordionContent>
                <ParkingRate />
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="shuttle">
              <AccordionTrigger>Shuttle Van Service</AccordionTrigger>
              <AccordionContent>
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="shuttle_van_service.description"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <TextEditorComponent content={field.value} onChange={field.onChange} />
                        </FormControl>
                        <FormDescription>
                          Information of shuttle services available for this event
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name={`shuttle_van_service.image`}
                    // rules={{ required: 'This is required' }}
                    render={({ field }) => (
                      <FormItem className="flex flex-col col-span-2">
                        <FormLabel>Image</FormLabel>
                        <FormControl>
                          <Dropzone
                            multiple={false}
                            onDrop={(acceptedFiles) => {
                              if (imageFileWithPath?.path != acceptedFiles[0].path) {
                                const preview = Object.assign(acceptedFiles[0], {
                                  preview: URL.createObjectURL(acceptedFiles[0])
                                })

                                form.setValue(`shuttle_van_service.image`, preview as File, {
                                  shouldValidate: true
                                })
                              }
                            }}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                        {isEdit &&
                          !imageFileWithPath &&
                          initialValues &&
                          initialValues.shuttle_van_service?.image_url && (
                            <div className="flex flex-col space-y-4 mt-2">
                              <div className="grid grid-cols-[150px_1fr_36px] space-x-2 items-center">
                                <Image
                                  key={initialValues.shuttle_van_service.image_url}
                                  src={initialValues.shuttle_van_service.image_url ?? ''}
                                  height={150}
                                  width={150}
                                  objectFit="contain"
                                  className="rounded-md"
                                />
                                <div className="flex flex-col">
                                  <Label className="text-xs font-normal">Uploaded</Label>
                                </div>
                              </div>
                            </div>
                          )}
                        {imageFileWithPath && (
                          <div className="flex flex-col space-y-4 mt-2">
                            <div className="grid grid-cols-[150px_1fr_36px] space-x-2 items-center">
                              {imageFileWithPath.type.startsWith('image') && (
                                <Image
                                  key={imageFileWithPath.path}
                                  src={imageFileWithPath.preview ?? ''}
                                  height={150}
                                  width={150}
                                  objectFit="contain"
                                  className="rounded-md"
                                />
                              )}
                              {imageFileWithPath.type.startsWith('video') && (
                                <video
                                  // controls
                                  key={imageFileWithPath.path}
                                  src={imageFileWithPath.preview ?? ''}
                                  height={150}
                                  width={150}
                                  className="rounded-md"
                                />
                              )}
                              <div className="flex flex-col">
                                <Label className="text-xs font-normal">
                                  {imageFileWithPath.path}
                                </Label>
                                <Label className="text-xs font-normal text-gray-500">
                                  {bytesToSize(imageFileWithPath.size)}
                                </Label>
                              </div>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" className="h-8 w-8 p-0">
                                    <span className="sr-only">Open menu</span>
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem
                                    onClick={(e) => {
                                      e.stopPropagation()

                                      form.setValue(`shuttle_van_service.image`, undefined)
                                    }}
                                    className="space-x-2"
                                  >
                                    <Trash2Icon size="16" />
                                    <span>Delete</span>
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </div>
                        )}
                      </FormItem>
                    )}
                  />
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </form>
      </Form>
    </DialogContent>
  )
}

export default CreateVisitUsForm
