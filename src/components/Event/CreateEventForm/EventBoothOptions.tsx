import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { useFormContext } from 'react-hook-form'
import { ColumnDef, Row, Table } from '@tanstack/react-table'
import IndeterminateCheckbox from '@/components/Table/IndeterminateCheckbox'
import { DataTable } from '@/components/Table/DataTable'
import { IDataResponse } from '@/network/request'
import { CreateEventBoothInput } from '@/types/Booth'
import BoothService from '@/network/services/booth'
import { useParams } from 'react-router-dom'
import { CoreExhibitor } from '@/types/Exhibitor'
import { Input } from '@/components/ui/input'
import { Dispatch, FC, SetStateAction, useEffect, useState } from 'react'
import { CoreProductCollection } from '@/types/ProductCollection'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import { useValueKey } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Cross1Icon } from '@radix-ui/react-icons'

const EventBoothOptions = () => {
  const form = useFormContext<CreateEventBoothInput>()
  const [collections, setCollections] = useState<CoreProductCollection[]>([])
  const storeId = form.watch('store_id')
  const c1 = form.watch('collection_id_1')
  const c2 = form.watch('collection_id_2')
  const collection1Key = useValueKey(c1?.toString())
  const collection2Key = useValueKey(c2?.toString())

  useEffect(() => {
    form.resetField('collection_id_1')
    form.resetField('collection_id_2')
  }, [storeId])

  return (
    <div className="max-w-4xl mx-auto grid grid-cols-2 gap-4 space-y-2">
      <FormField
        control={form.control}
        name="store_id"
        render={() => (
          <FormItem className="flex flex-col col-span-2">
            <FormLabel>Vendors Elligible for Booth</FormLabel>
            <FormControl>
              <BoothOptionsTable setCollections={setCollections} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {storeId && (
        <>
          <FormField
            control={form.control}
            name="hall_no"
            render={({ field }) => (
              <FormItem className="flex flex-col col-span-1">
                <FormLabel>Hall No.</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="booth_no"
            render={({ field }) => (
              <FormItem className="flex flex-col col-span-1">
                <FormLabel>Booth No.</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="booth_size"
            render={({ field }) => (
              <FormItem className="flex flex-col col-span-1">
                <FormLabel>Booth Size</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div></div>

          <FormField
            control={form.control}
            name="collection_id_1"
            render={({ field }) => {
              return (
                <FormItem className="flex flex-col col-span-1">
                  <FormLabel>Brand 1*</FormLabel>
                  <div className="flex flex-row items-center space-x-2">
                    <Select
                      key={collection1Key}
                      onValueChange={field.onChange}
                      defaultValue={field.value?.toString()}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose an option" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {collections.length > 0 ? (
                          collections.map((collection) => (
                            <SelectItem key={collection.id} value={collection.id.toString()}>
                              {collection.title}
                            </SelectItem>
                          ))
                        ) : (
                          <SelectItem value="no collection" disabled>
                            No collection
                          </SelectItem>
                        )}
                      </SelectContent>
                    </Select>

                    {field.value != null && (
                      <Button
                        type="button"
                        size="icon"
                        variant="outline"
                        onClick={() => {
                          form.resetField(field.name)
                        }}
                      >
                        <Cross1Icon />
                      </Button>
                    )}
                  </div>

                  <FormMessage />
                </FormItem>
              )
            }}
          />

          <FormField
            control={form.control}
            name="collection_id_2"
            render={({ field }) => (
              <FormItem className="col-span-1 flex flex-col">
                <FormLabel>Brand 2</FormLabel>
                <div className="flex flex-row items-center space-x-2">
                  <Select
                    key={collection2Key}
                    onValueChange={field.onChange}
                    defaultValue={field.value?.toString()}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose an option" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {collections.length > 0 ? (
                        collections.map((collection) => (
                          <SelectItem key={collection.id} value={collection.id.toString()}>
                            {collection.title}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="no collection" disabled>
                          No collection
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>

                  {field.value != null && (
                    <Button
                      type="button"
                      size="icon"
                      variant="outline"
                      onClick={() => {
                        form.resetField(field.name)
                      }}
                    >
                      <Cross1Icon />
                    </Button>
                  )}
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
        </>
      )}
    </div>
  )
}

const BoothOptionsTable: FC<{
  setCollections: Dispatch<SetStateAction<CoreProductCollection[]>>
}> = ({ setCollections }) => {
  const { event_id } = useParams()
  const form = useFormContext<CreateEventBoothInput>()

  const columns: ColumnDef<Partial<CoreExhibitor>>[] = [
    {
      id: 'select',
      // header: ({ table }) => (
      //   <IndeterminateCheckbox
      //     {...{
      //       checked: table.getIsAllRowsSelected(),
      //       indeterminate: table.getIsSomeRowsSelected(),
      //       onChange: table.getToggleAllPageRowsSelectedHandler()
      //     }}
      //   />
      // ),
      cell: ({ row }) => (
        <div className="px-1">
          <IndeterminateCheckbox
            {...{
              checked: row.getIsSelected(),
              disabled: !row.getCanSelect(),
              indeterminate: row.getIsSomeSelected(),
              onChange: row.getToggleSelectedHandler()
            }}
          />
        </div>
      )
    },
    {
      accessorKey: 'name',
      header: 'Name'
    },
    {
      accessorKey: 'brands',
      header: 'Brands'
    },
    {
      accessorKey: 'contact_mobile_number',
      header: 'Contact No.'
    },
    {
      accessorKey: 'contact_email',
      header: 'Email'
    }
  ]

  const toRow = (
    data: IDataResponse<Partial<CoreExhibitor>> | undefined
  ): Partial<CoreExhibitor>[] => {
    if (data?.data && data?.data?.length > 0) {
      return data?.data?.map((element) => {
        return {
          ...element,
          key: element.id
        }
      })
    }

    return []
  }

  const toPaginate = (data: IDataResponse<Partial<CoreExhibitor>> | undefined) => {
    return {
      total: data?.meta?.total ?? 0,
      lastPage: data?.meta?.last_page ?? 0
    }
  }

  if (!event_id) return <></>

  return (
    <DataTable<Partial<CoreExhibitor>, unknown, IDataResponse<Partial<CoreExhibitor>>>
      columns={columns}
      swrService={BoothService.getBoothOptions}
      toRow={toRow}
      toPaginate={toPaginate}
      setSelectedRows={(selectedRows) => form.setValue('store_id', selectedRows[0] as number)}
      onRowClick={(row: Row<Partial<CoreExhibitor>>, table: Table<Partial<CoreExhibitor>>) => {
        if (!row.getIsSelected()) {
          table.resetRowSelection()
          row.toggleSelected()
          setCollections(
            (row.original.product_collections as unknown as CoreProductCollection[]) ?? []
          )
        }
      }}
    />
  )
}

export default EventBoothOptions
