import { Input } from '@/components/ui/input'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { useFormContext } from 'react-hook-form'
import { CreateEventInput } from '@/types/CreateEvent'
import { Textarea } from '@/components/ui/textarea'
import { useState } from 'react'

const EventDetailForm = () => {
  const form = useFormContext<CreateEventInput>()
  const [imagePreview, setImagePreview] = useState<string>('')

  return (
    <>
      <FormField
        control={form.control}
        name="event_info.banner_text"
        rules={{ required: 'Please provide an event banner text' }}
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Event Banner Text</FormLabel>
            <FormControl>
              <Input {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="event_info.description"
        rules={{ required: 'Please an event description' }}
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Event Description</FormLabel>
            <FormControl>
              <Textarea {...field} rows={4} className="resize-none" />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="event_info.image_file"
        rules={{ required: 'Please provide the event image' }}
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Event Image</FormLabel>
            {imagePreview != '' && (
              <img className="w-[320px] py-4" src={imagePreview} alt="event-image" />
            )}
            <FormControl>
              <Input
                type="file"
                onChange={(e) => {
                  if (e.target.files) {
                    field.onChange(e.target.files[0])
                    setImagePreview(URL.createObjectURL(e.target.files[0]))
                  }
                }}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* TODO: social medias sharing */}
      {/* <FormField
        control={form.control}
        name="media.event_info.share_image"
        render={({ field }) => (
          <FormItem className="flex flex-col col-span-2">
            <FormLabel>Share Image</FormLabel>
            {imagePreview != '' && (
              <img className="w-[320px] py-4" src={imagePreview} alt="event-image" />
            )}
            <FormControl>
              <Input
                type="file"
                onChange={(e) => {
                  if (e.target.files) {
                    field.onChange(e.target.files[0])
                    setImagePreview(URL.createObjectURL(e.target.files[0]))
                  }
                }}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      /> */}
    </>
  )
}

export default EventDetailForm
