import { Input } from '@/components/ui/input'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { useFormContext } from 'react-hook-form'
import { CalendarDatePicker } from '@/components/DatePicker/CalendarDatePicker'
import { DateTime } from 'luxon'
import { CreateEventInput } from '@/types/CreateEvent'
// import EventDetailForm from './EventDetailForm'

const EventGeneralInformation = () => {
  const form = useFormContext<CreateEventInput>()
  const fromDate = form.watch('event.from')
  const toDate = form.watch('event.to')

  return (
    <div className="grid grid-cols-2 gap-4">
      <FormField
        control={form.control}
        name="event.title"
        rules={{ required: 'Please enter the title' }}
        render={({ field }) => (
          <FormItem className="col-span-2 flex flex-col">
            <FormLabel>Title</FormLabel>
            <FormControl>
              <Input {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="event.from"
        rules={{ required: 'Please enter the start date' }}
        render={({ field, fieldState }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Starting Date</FormLabel>
            <CalendarDatePicker
              mode="single"
              includeTime={true}
              buttonLabel={
                field.value
                  ? DateTime.fromISO(field.value as string).toFormat('yyyy-MM-dd HH:mm:ss')
                  : undefined
              }
              // selected={new Date((field.value as DateTime)?.toISO() ?? new Date())}
              // onSelect={(e) => {
              //   console.log(e)
              //   field.onChange(DateTime.fromISO(e?.toISOString() ?? '').toISO())
              // }}
              value={field.value as string}
              onChange={(v) => {
                field.onChange(DateTime.fromISO(v?.toISOString() ?? '').toISO())
              }}
              disabled={(date) => date < new Date() || date < new Date('1900-01-01')}
            />
            {fieldState.error || fieldState.invalid ? (
              <FormMessage />
            ) : (
              <FormDescription>The start date for the event</FormDescription>
            )}
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="event.to"
        rules={{ required: 'Please enter the end date' }}
        render={({ field, fieldState }) => (
          <FormItem className="flex flex-col">
            <FormLabel>End Date</FormLabel>
            <CalendarDatePicker
              mode="single"
              includeTime={true}
              buttonLabel={
                field.value
                  ? DateTime.fromISO(field.value as string).toFormat('yyyy-MM-dd HH:mm:ss')
                  : undefined
              }
              // selected={new Date((field.value as DateTime)?.toISO() ?? new Date())}
              // onSelect={(e) => {
              //   field.onChange(DateTime.fromISO(e?.toISOString() ?? '').toISO())
              // }}
              value={field.value as string}
              onChange={(v) => {
                field.onChange(DateTime.fromISO(v?.toISOString() ?? '').toISO())
              }}
              disabled={(date) => date < DateTime.fromISO(fromDate as string).toJSDate()}
            />
            {fieldState.error || fieldState.invalid ? (
              <FormMessage />
            ) : (
              <FormDescription>The end date for the event</FormDescription>
            )}
          </FormItem>
        )}
      />

      {/* need to be between from & to */}
      <FormField
        control={form.control}
        name="event.cut_off"
        rules={{ required: 'Please enter the cut off date' }}
        render={({ field, fieldState }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Final Registration Date</FormLabel>
            <CalendarDatePicker
              mode="single"
              includeTime={true}
              buttonLabel={
                field.value
                  ? DateTime.fromISO(field.value as string).toFormat('yyyy-MM-dd HH:mm:ss')
                  : undefined
              }
              // selected={new Date((field.value as DateTime)?.toISO() ?? new Date())}
              // onSelect={(e) => {
              //   field.onChange(DateTime.fromISO(e?.toISOString() ?? '').toISO())
              // }}
              value={field.value as string}
              onChange={(v) => {
                field.onChange(DateTime.fromISO(v?.toISOString() ?? '').toISO())
              }}
              disabled={(date) =>
                fromDate == undefined ||
                toDate == undefined ||
                date >= DateTime.fromISO(fromDate as string).toJSDate()
              }
            />
            {fieldState.error || fieldState.invalid ? (
              <FormMessage />
            ) : (
              <FormDescription>The final date allowed to register for the event</FormDescription>
            )}
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="event.location"
        rules={{ required: 'Please enter event location' }}
        render={({ field, fieldState }) => (
          <FormItem className="col-span-2 flex flex-col">
            <FormLabel>Venue</FormLabel>
            <FormControl>
              <Input {...field} />
            </FormControl>
            {fieldState.error || fieldState.invalid ? (
              <FormMessage />
            ) : (
              <FormDescription>Location of the event</FormDescription>
            )}
          </FormItem>
        )}
      />

      {/* <EventDetailForm /> */}
    </div>
  )
}

export default EventGeneralInformation
