import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { useFormContext } from 'react-hook-form'
import { CreateEventInput } from '@/types/CreateEvent'
import { Button } from '@/components/ui/button'
import { PlusCircleIcon } from 'lucide-react'
import { useState } from 'react'
import { Dialog, DialogTrigger } from '@/components/ui/dialog'
import CreateExploreForm from './CreateExploreForm'
import { ColumnDef, Row } from '@tanstack/react-table'
import IndeterminateCheckbox from '@/components/Table/IndeterminateCheckbox'
import { DataTable } from '@/components/Table/DataTable'
import EventService from '@/network/services/event'
import { IDataResponse } from '@/network/request'
import { ExploreCard } from '@/types/ExploreCard'

const ExploreOption = () => {
  const form = useFormContext<CreateEventInput>()

  return (
    <div className="grid grid-cols-2 gap-4">
      <FormField
        control={form.control}
        name="explore.cards"
        // rules={{ required: 'Please select at least one' }}
        render={() => (
          <FormItem className="flex flex-col col-span-2">
            <FormLabel>Explore options</FormLabel>
            <FormControl>
              <ExploreOptionList />
            </FormControl>
            <FormDescription>Areas near the fair that can be explored</FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  )
}

const ExploreOptionList = () => {
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  return (
    <div className="grid gap-4">
      {/* dialog to create new explore options */}
      <Dialog
        open={isDialogOpen}
        onOpenChange={(isOpen) => {
          setIsDialogOpen(isOpen)
        }}
      >
        <DialogTrigger asChild>
          <Button variant="outline" type="button">
            <PlusCircleIcon size="16" className="mr-2" />
            Add a new option
          </Button>
        </DialogTrigger>

        {/* Explore option form */}
        <CreateExploreForm
          setIsDialogOpen={setIsDialogOpen}
          initialValues={{
            type: undefined,
            title: '',
            description: '',
            image: undefined
          }}
        />
      </Dialog>

      {/* Selectable table for all options */}
      <ExploreOptionTable />
    </div>
  )
}

const ExploreOptionTable = () => {
  const form = useFormContext<CreateEventInput>()

  const columns: ColumnDef<ExploreCard>[] = [
    {
      id: 'select',
      header: ({ table }) => (
        <IndeterminateCheckbox
          {...{
            checked: table.getIsAllRowsSelected(),
            indeterminate: table.getIsSomeRowsSelected(),
            onChange: table.getToggleAllPageRowsSelectedHandler()
          }}
        />
      ),
      cell: ({ row }) => (
        <div className="px-1">
          <IndeterminateCheckbox
            {...{
              checked: row.getIsSelected(),
              disabled: !row.getCanSelect(),
              indeterminate: row.getIsSomeSelected(),
              onChange: row.getToggleSelectedHandler()
            }}
          />
        </div>
      )
    },
    {
      accessorKey: 'type',
      header: 'Type'
    },
    {
      accessorKey: 'title',
      header: 'Title'
    },
    {
      accessorKey: 'image_url',
      header: 'Image',
      cell: (props) => {
        return <img className="w-[80px]" src={props.getValue<string>()} />
      }
    }
    // TODO: dialog to edit explore option
    // columnHelper.display({
    //   id: "actions",
    //   cell: (props) => <RowActions row={props.row} />,
    // }),
  ]

  const toRow = (data: IDataResponse<ExploreCard> | undefined): ExploreCard[] => {
    if (data?.data && data?.data?.length > 0) {
      return data?.data?.map((element) => {
        return {
          ...element,
          key: element.id
        }
      })
    }

    return []
  }

  const toPaginate = (data: IDataResponse<ExploreCard> | undefined) => {
    return {
      total: data?.meta?.total ?? 0,
      lastPage: data?.meta?.last_page ?? 0
    }
  }

  return (
    <DataTable<ExploreCard, unknown, IDataResponse<ExploreCard>>
      columns={columns}
      swrService={EventService.getEventExplores}
      toRow={toRow}
      toPaginate={toPaginate}
      setSelectedRows={(selectedRows) => form.setValue('explore.cards', selectedRows as number[])}
      onRowClick={(row: Row<ExploreCard>) => {
        row.toggleSelected()
      }}
    />
  )
}

export default ExploreOption
