import TextEditorComponent from '@/components/Editor/TextEditor'
import { Button } from '@/components/ui/button'
import { FormField, FormItem, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { CreateVisitUsInput } from '@/types/VisitUs'
import { PlusCircleIcon, Trash2Icon } from 'lucide-react'
import { FC } from 'react'
import { UseFieldArrayRemove, useFieldArray, useFormContext } from 'react-hook-form'

const GettingHereOptionFields: FC<{
  fields: Record<'id', string>[]
  remove: UseFieldArrayRemove
}> = ({ fields, remove }) => {
  const form = useFormContext<CreateVisitUsInput>()

  return (
    <div className="flex flex-col space-y-2">
      <div className="grid grid-cols-5 gap-4">
        <div className="col-span-2">
          <p className="text-sm">Transportation Mode</p>
        </div>
        <div className="col-span-3">
          <p className="text-sm">Description</p>
        </div>
      </div>
      {fields.map((field, index) => {
        return (
          <div className="grid grid-cols-5 gap-4" key={field.id}>
            <FormField
              key={field.id + 'method'}
              control={form.control}
              name={`getting_here.${index}.method`}
              render={({ field }) => {
                return (
                  <FormItem className="flex flex-col col-span-2">
                    <Input {...field} />
                    <FormMessage />
                  </FormItem>
                )
              }}
            />

            <FormField
              key={field.id + 'description'}
              control={form.control}
              name={`getting_here.${index}.description`}
              render={({ field }) => {
                return (
                  <FormItem className="flex flex-col col-span-3">
                    <div className="flex w-full space-x-2 ">
                      <div className="flex-1">
                        <TextEditorComponent content={field.value} onChange={field.onChange} />
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        size="icon"
                        onClick={() => {
                          remove(index)
                        }}
                      >
                        <Trash2Icon className="h-4 w-4" />
                      </Button>
                    </div>
                    <FormMessage />
                  </FormItem>
                )
              }}
            />
          </div>
        )
      })}
    </div>
  )
}

const GettingHere = () => {
  const form = useFormContext<CreateVisitUsInput>()
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'getting_here'
  })

  return (
    <div className="grid gap-4">
      <GettingHereOptionFields {...{ fields, remove }} />

      <Button
        variant="outline"
        type="button"
        onClick={() => {
          append({ method: '', description: '' })
        }}
      >
        <PlusCircleIcon size="16" className="mr-2" />
        Add a getting here option
      </Button>
    </div>
  )
}

export default GettingHere
