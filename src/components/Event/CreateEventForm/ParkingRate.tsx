import { Button } from '@/components/ui/button'
import { FormField, FormItem, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { CreateVisitUsInput } from '@/types/VisitUs'
import { PlusCircleIcon, Trash2Icon } from 'lucide-react'
import { FC } from 'react'
import { UseFieldArrayRemove, useFieldArray, useFormContext } from 'react-hook-form'

const ParkingRateOptionFields: FC<{
  fields: Record<'id', string>[]
  remove: UseFieldArrayRemove
}> = ({ fields, remove }) => {
  const form = useFormContext<CreateVisitUsInput>()

  return (
    <div className="flex flex-col space-y-2">
      <div className="grid grid-cols-6 gap-4">
        <div>
          <p className="text-sm">Location</p>
        </div>
        <div>
          <p className="text-sm">First hour</p>
        </div>
        <div>
          <p className="text-sm">Subsequent hour</p>
        </div>
        <div>
          <p className="text-sm">Maximum charge</p>
        </div>
        <div className="col-span-2">
          <p className="text-sm">Waze URL</p>
        </div>
      </div>
      {fields.map((field, index) => {
        return (
          <div className="grid grid-cols-6 gap-4" key={field.id}>
            <FormField
              key={field.id + 'label'}
              control={form.control}
              name={`parking_and_rate.${index}.label`}
              render={({ field }) => {
                return (
                  <FormItem className="flex flex-col">
                    <Input {...field} />
                    <FormMessage />
                  </FormItem>
                )
              }}
            />
            <FormField
              key={field.id + 'first_hour'}
              control={form.control}
              name={`parking_and_rate.${index}.first_hour`}
              render={({ field }) => {
                return (
                  <FormItem className="flex flex-col">
                    <Input type="number" {...field} />
                    <FormMessage />
                  </FormItem>
                )
              }}
            />
            <FormField
              key={field.id + 'every_subsequent_hour'}
              control={form.control}
              name={`parking_and_rate.${index}.every_subsequent_hour`}
              render={({ field }) => {
                return (
                  <FormItem className="flex flex-col">
                    <Input type="number" {...field} />
                    <FormMessage />
                  </FormItem>
                )
              }}
            />
            <FormField
              key={field.id + 'maximum_parking_fee_per_day'}
              control={form.control}
              name={`parking_and_rate.${index}.maximum_parking_fee_per_day`}
              render={({ field }) => {
                return (
                  <FormItem className="flex flex-col">
                    <Input type="number" {...field} />
                    <FormMessage />
                  </FormItem>
                )
              }}
            />

            <FormField
              key={field.id + 'waze_url'}
              control={form.control}
              name={`parking_and_rate.${index}.waze_url`}
              render={({ field }) => {
                return (
                  <FormItem className="flex flex-col col-span-2">
                    <div className="flex w-full space-x-2">
                      <div className="flex-1">
                        <Input {...field} />
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        size="icon"
                        onClick={() => {
                          remove(index)
                        }}
                      >
                        <Trash2Icon className="h-4 w-4" />
                      </Button>
                    </div>
                    <FormMessage />
                  </FormItem>
                )
              }}
            />
          </div>
        )
      })}
    </div>
  )
}

const ParkingRate = () => {
  const form = useFormContext<CreateVisitUsInput>()
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'parking_and_rate'
  })

  return (
    <div className="grid gap-4">
      <ParkingRateOptionFields {...{ fields, remove }} />

      <Button
        variant="outline"
        type="button"
        onClick={() => {
          append({
            label: '',
            waze_url: '',
            first_hour: 0,
            every_subsequent_hour: 0,
            maximum_parking_fee_per_day: 0
          })
        }}
      >
        <PlusCircleIcon size="16" className="mr-2" />
        Add a getting here option
      </Button>
    </div>
  )
}

export default ParkingRate
