import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { useFormContext } from 'react-hook-form'
import { CreateEventInput } from '@/types/CreateEvent'
import { Dropzone } from '@/components/Form/Dropzone'
import { FileWithPath } from 'react-dropzone'
import { Image } from '@unpic/react'
import { Label } from '@/components/ui/label'
import { bytesToSize } from '@/lib/utils'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import { MoreHorizontal, Trash2Icon } from 'lucide-react'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import TextEditorComponent from '@/components/Editor/TextEditor'

const PopUpReminder = () => {
  const form = useFormContext<CreateEventInput>()

  const mediaFile = form.watch('pop_up.media')
  const mediaFileWithPath = mediaFile
    ? (mediaFile as unknown as FileWithPath & { preview: string })
    : undefined

  return (
    <div className="grid grid-cols-1 gap-4">
      <FormField
        control={form.control}
        name="pop_up.description"
        // rules={{ required: 'This is required' }}
        render={({ field }) => (
          <FormItem className="col-span-2 flex flex-col">
            <FormLabel>Description</FormLabel>
            <FormControl>
              <TextEditorComponent content={field.value} onChange={field.onChange} />
            </FormControl>
            <FormDescription>Description in pop-up</FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="pop_up.media"
        // rules={{ required: 'This is required' }}
        render={({ field }) => (
          <>
            <FormItem className="col-span-2 flex flex-col">
              <FormLabel>Media</FormLabel>
              <FormControl>
                <FormControl>
                  <Dropzone
                    multiple={false}
                    onDrop={(acceptedFiles) => {
                      if (mediaFileWithPath?.path != acceptedFiles[0].path) {
                        const preview = Object.assign(acceptedFiles[0], {
                          preview: URL.createObjectURL(acceptedFiles[0])
                        })

                        form.setValue('pop_up.media', preview as File, {
                          shouldValidate: true
                        })
                      }
                    }}
                    {...field}
                  />
                </FormControl>
              </FormControl>
              <FormDescription>Image show in pop-up</FormDescription>
              <FormMessage />
            </FormItem>
            <div className="flex flex-col space-y-4 mt-2">
              {mediaFileWithPath && (
                <div className="grid grid-cols-[150px_1fr_36px] space-x-2 items-center">
                  {mediaFileWithPath.type.startsWith('image') && (
                    <Image
                      key={mediaFileWithPath.path}
                      src={mediaFileWithPath.preview ?? ''}
                      height={150}
                      width={150}
                      objectFit="contain"
                      className="rounded-md"
                    />
                  )}
                  {mediaFileWithPath.type.startsWith('video') && (
                    <video
                      // controls
                      key={mediaFileWithPath.path}
                      src={mediaFileWithPath.preview ?? ''}
                      height={150}
                      width={150}
                      className="rounded-md"
                    />
                  )}
                  <div className="flex flex-col">
                    <Label className="text-xs font-normal">{mediaFileWithPath.path}</Label>
                    <Label className="text-xs font-normal text-gray-500">
                      {bytesToSize(mediaFileWithPath.size)}
                    </Label>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onClick={(e) => {
                          e.stopPropagation()

                          form.setValue('pop_up.media', undefined, {
                            shouldValidate: true
                          })
                        }}
                        className="space-x-2"
                      >
                        <Trash2Icon size="16" />
                        <span>Delete</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              )}
            </div>
          </>
        )}
      />
      <FormField
        control={form.control}
        name="pop_up.show"
        // rules={{ required: 'This is required' }}
        render={({ field }) => (
          <FormItem className="col-span-2 flex flex-col">
            <FormLabel>Show</FormLabel>
            <FormControl>
              <Select onValueChange={field.onChange} defaultValue={field.value ? 'true' : 'false'}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Choose an option" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {['true', 'false'].map((value) => {
                    return (
                      <SelectItem key={value} value={value}>
                        {value === 'true' ? 'Yes' : 'No'}
                      </SelectItem>
                    )
                  })}
                </SelectContent>
              </Select>
            </FormControl>
            <FormDescription>To show pop-up</FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  )
}

export default PopUpReminder
