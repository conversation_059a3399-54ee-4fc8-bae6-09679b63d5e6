import { Input } from '@/components/ui/input'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { useFormContext } from 'react-hook-form'
import { CreateEventInput } from '@/types/CreateEvent'

const TabDescriptions = () => {
  const form = useFormContext<CreateEventInput>()

  return (
    <div className="grid grid-cols-1 gap-4">
      <FormField
        control={form.control}
        name="event.deal_description"
        rules={{ required: 'This is required' }}
        render={({ field }) => (
          <FormItem className="flex flex-col ">
            <FormLabel>Deals Tab Description</FormLabel>
            <FormControl>
              <Input {...field} />
            </FormControl>
            <FormDescription>Description for deals tab</FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="event.layout_description"
        rules={{ required: 'This is required' }}
        render={({ field }) => (
          <FormItem className="flex flex-col ">
            <FormLabel>Layout Map Tab Description</FormLabel>
            <FormControl>
              <Input {...field} />
            </FormControl>
            <FormDescription>Description for layout map tab</FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="event.visit_description"
        rules={{ required: 'This is required' }}
        render={({ field }) => (
          <FormItem className="flex flex-col ">
            <FormLabel>Visit Us Tab Description</FormLabel>
            <FormControl>
              <Input {...field} />
            </FormControl>
            <FormDescription>Description for Visit Us tab</FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="event.explore_description"
        rules={{ required: 'This is required' }}
        render={({ field }) => (
          <FormItem className="flex flex-col ">
            <FormLabel>Explore Tab Description</FormLabel>
            <FormControl>
              <Input {...field} />
            </FormControl>
            <FormDescription>Description for explore tab</FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  )
}

export default TabDescriptions
