import { FormControl, FormDescription, FormField, FormItem, FormLabel } from '@/components/ui/form'
import { useFormContext } from 'react-hook-form'
import { CreateEventInput } from '@/types/CreateEvent'
import { Button } from '@/components/ui/button'
import { PlusCircleIcon } from 'lucide-react'
import { useState } from 'react'
import { Dialog, DialogTrigger } from '@/components/ui/dialog'
import { ColumnDef, Row, Table } from '@tanstack/react-table'
import IndeterminateCheckbox from '@/components/Table/IndeterminateCheckbox'
import { DataTable } from '@/components/Table/DataTable'
import EventService from '@/network/services/event'
import { IDataResponse } from '@/network/request'
import CreateVisitUsForm from './CreateVisitUsForm'
import { VisitUs } from '@/types/VisitUs'

const VisitUsCard = () => {
  const form = useFormContext<CreateEventInput>()

  return (
    <div className="grid grid-cols-2 gap-4">
      <FormField
        control={form.control}
        name="visit_us"
        // rules={{ required: 'Please select a venue' }}
        render={() => (
          <FormItem className="flex flex-col col-span-2">
            <FormLabel>Venue options</FormLabel>
            <FormControl>
              <VisitUsList />
            </FormControl>
            <FormDescription>Venue of the event</FormDescription>
          </FormItem>
        )}
      />
    </div>
  )
}

const VisitUsList = () => {
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  return (
    <div className="grid gap-4">
      {/* dialog to create new visit us options */}
      <Dialog
        open={isDialogOpen}
        onOpenChange={(isOpen) => {
          setIsDialogOpen(isOpen)
        }}
      >
        <DialogTrigger asChild>
          <Button variant="outline" type="button">
            <PlusCircleIcon size="16" className="mr-2" />
            Add new venue
          </Button>
        </DialogTrigger>

        {/* Explore option form */}
        <CreateVisitUsForm
          setIsDialogOpen={setIsDialogOpen}
          initialValues={{
            location_url: '',
            venue_name: '',
            venue_address: '',
            contact_mobile_number: '',
            contact_email: '',
            website_url: '',
            facebook_url: '',
            instagram_url: '',
            getting_here: [],
            parking_and_rate: [],
            shuttle_van_service: { image: undefined, image_url: '', description: '' }
          }}
        />
      </Dialog>

      {/* Selectable table for all options */}
      <VisitUsTable />
    </div>
  )
}

const VisitUsTable = () => {
  const form = useFormContext<CreateEventInput>()

  const columns: ColumnDef<VisitUs>[] = [
    {
      id: 'select',
      cell: ({ row, table }) => (
        <div className="px-1">
          <IndeterminateCheckbox
            {...{
              checked: row.getIsSelected(),
              disabled: !row.getCanSelect(),
              indeterminate: row.getIsSomeSelected(),
              onChange: () => {
                // Reset all selection 1st to allow 1 selected only
                table.resetRowSelection()
                row.toggleSelected()
              }
            }}
          />
        </div>
      )
    },
    {
      accessorKey: 'venue_name',
      header: 'Venue'
    },
    {
      accessorKey: 'venue_address',
      header: 'Address'
    }
    // TODO: dialog to edit row
    // columnHelper.display({
    //   id: "actions",
    //   cell: (props) => <RowActions row={props.row} />,
    // }),
  ]

  const toRow = (data: IDataResponse<VisitUs> | undefined): VisitUs[] => {
    if (data?.data && data?.data?.length > 0) {
      return data?.data?.map((element) => {
        return {
          ...element,
          key: element.id
        }
      })
    }

    return []
  }

  const toPaginate = (data: IDataResponse<VisitUs> | undefined) => {
    return {
      total: data?.meta?.total ?? 0,
      lastPage: data?.meta?.last_page ?? 0
    }
  }

  return (
    <DataTable<VisitUs, unknown, IDataResponse<VisitUs>>
      columns={columns}
      swrService={EventService.getEventVisitUs}
      toRow={toRow}
      toPaginate={toPaginate}
      setSelectedRows={(selectedRows) => form.setValue('visit_us', selectedRows[0] as number)}
      onRowClick={(row: Row<VisitUs>, table: Table<VisitUs>) => {
        table.resetRowSelection()
        row.toggleSelected()
      }}
    />
  )
}

export default VisitUsCard
