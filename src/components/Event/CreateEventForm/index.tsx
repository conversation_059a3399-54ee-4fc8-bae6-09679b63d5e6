import * as React from 'react'
import { useFormContext } from 'react-hook-form'

import { cn } from '@/lib/utils'
import { CreateEventInput } from '@/types/CreateEvent'
// import {
//   Accordion,
//   AccordionContent,
//   AccordionItem,
//   AccordionTrigger
// } from '@/components/ui/accordion'
// import ExploreOption from './ExploreOption'
// import VisitUs from './VisitUs'
// import { ErrorMessage } from '@hookform/error-message'
// import TabDescriptions from './TabDescriptions'
// import PopUpReminder from './PopUpReminder'
// import FileService from '@/network/services/file'
import EventGeneralInformation from './EventGeneralInformation'
import { useSWRConfig } from 'swr'
import { toast } from '@/components/ui/use-toast'
import EventService from '@/network/services/event'

interface EventFormProps extends React.HTMLAttributes<HTMLDivElement> {
  setIsDialogOpen: (value: boolean) => void
}

export function CreateEventForm({ className, setIsDialogOpen }: EventFormProps) {
  const { mutate } = useSWRConfig()
  const form = useFormContext<CreateEventInput>()

  const onSubmit = form.handleSubmit(async (values) => {
    console.log('event submit', values)

    // const uploads: File[] = []
    // if (values.event_info.image_file) {
    //   uploads.push(values.event_info.image_file)
    //   delete values.event_info.image_file
    // }

    // if (values.pop_up?.media) {
    //   uploads.push(values.pop_up.media)
    //   delete values.pop_up.media
    // }

    // // block empty visit us & explore
    // if (!form.watch('visit_us')) {
    //   form.setError('visit_us', {
    //     type: 'manual',
    //     message: 'Please select a venue'
    //   })
    //   return
    // }

    // const formData = new FormData()
    // convertValuesToFormData(formData, { file: values.event_info.image_file })

    // // logging formData
    // for (const pair of formData.entries()) {
    //   console.log('boo', pair[0] + ', ' + pair[1])
    // }

    try {
      // Upload image
      // const { data: uploadResponse } = await FileService.upload(uploads)

      // if (uploadResponse.uploads) {
      //   console.log('upload url', uploadResponse.uploads)
      //   // Set url into form body
      //   values.event_info.image_url = uploadResponse.uploads[0].url

      //   if (uploadResponse.uploads.length > 1) {
      //     values.pop_up.media_url = uploadResponse.uploads[1].url
      //   }
      //   console.log('upload > set url', values)

      // post api
      const { data: submitResponse } = await EventService.createEvent(values)
      console.log('response', submitResponse)

      if (submitResponse.success) {
        // refresh event list
        mutate((key) => typeof key === 'string' && key.startsWith(EventService.getEvents))

        // Close dialog & reset
        setIsDialogOpen(false)
        form.reset()
        toast({
          title: 'Event created',
          variant: 'success'
        })

        return
      }
      // }

      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    } catch (error) {
      console.error(error)

      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <form
      id="create-event-form"
      onSubmit={onSubmit}
      className={cn('flex flex-col space-y-8 max-w-4xl mx-auto', className)}
    >
      <div className="pt-4">
        <EventGeneralInformation />
      </div>

      {/* <Accordion type="multiple" className="w-full" defaultValue={['event-general-information']}>
        <AccordionItem value="event-general-information">
          <AccordionTrigger disabled>Event General Information*</AccordionTrigger>
          <AccordionContent>
            <EventGeneralInformation />
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="visit-us">
          <AccordionTrigger>
            <div className="flex">
              <p className="mr-2">Visit Us</p>
              <ErrorMessage
                errors={form.formState.errors}
                name="visit_us"
                render={({ message }) => {
                  return <p className="text-[0.8rem] text-destructive">({message})</p>
                }}
              />
            </div>
          </AccordionTrigger>
          <AccordionContent>
            <VisitUs />
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="explore-options">
          <AccordionTrigger>
            <div className="flex">
              <p className="mr-2">Explore Options</p>
            </div>
          </AccordionTrigger>
          <AccordionContent>
            <ExploreOption />
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="tab-descriptions">
          <AccordionTrigger>
            <div className="flex">
              <p className="mr-2">Page Descriptions</p>
            </div>
          </AccordionTrigger>
          <AccordionContent>
            <TabDescriptions />
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="pop-up-reminder">
          <AccordionTrigger>
            <div className="flex">
              <p className="mr-2">Pop-up Reminder</p>
            </div>
          </AccordionTrigger>
          <AccordionContent>
            <PopUpReminder />
          </AccordionContent>
        </AccordionItem>
      </Accordion> */}
    </form>
  )
}
