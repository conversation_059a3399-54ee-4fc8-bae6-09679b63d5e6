import EventService from '@/network/services/event'
import { ColumnDef, Row, createColumnHelper } from '@tanstack/react-table'
import { DataTable, FilterColumn } from '@/components/Table/DataTable'
import { DateTime } from 'luxon'
import { CheckIcon, Cross2Icon } from '@radix-ui/react-icons'
import { useNavigate } from 'react-router-dom'
import { IDataResponse } from '@/network/request'
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem
} from '@/components/ui/dropdown-menu'
import { MonitorCheckIcon, MoreHorizontal, XCircle } from 'lucide-react'
import { FC } from 'react'
import { Button } from '../ui/button'
import { toast } from '../ui/use-toast'
import { useSWRConfig } from 'swr'
import { GeneralEventInfo } from '@/types/Event'

const RowActions: FC<{ row: Row<GeneralEventInfo> }> = ({ row }) => {
  const { mutate } = useSWRConfig()
  const event = row.original

  const updateStatus = async (event: GeneralEventInfo) => {
    try {
      const { data } = await EventService.updateEventStatus(event.id)

      mutate((key) => typeof key === 'string' && key.startsWith(EventService.getEvents))

      if (data.success) {
        toast({
          title: `Product ${event.selected ? 'Deactivated' : 'Activated'}`,
          variant: 'success'
        })
        return
      }

      toast({
        title: `Action failed, please try again`,
        variant: 'destructive'
      })
    } catch (error) {
      toast({
        title: `Action failed, please try again`,
        variant: 'destructive'
      })
      console.log(error)
    }
  }

  return (
    <div className="w-full flex justify-end">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0" onClick={(e) => e.stopPropagation()}>
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem
            disabled={row.original.selected == true}
            onClick={async (e) => {
              e.stopPropagation()
              updateStatus(event)
            }}
            className="space-x-2"
          >
            <MonitorCheckIcon size="16" />
            <span>Activate</span>
          </DropdownMenuItem>
          <DropdownMenuItem
            disabled={!row.original.selected == true}
            onClick={async (e) => {
              e.stopPropagation()
              updateStatus(event)
            }}
            className="space-x-2"
          >
            <XCircle size="16" />
            <span>Deactivate</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}

// column declaration for DataTable
const columnHelper = createColumnHelper<GeneralEventInfo>()
const columns: ColumnDef<GeneralEventInfo>[] = [
  {
    accessorKey: 'title',
    header: 'Title'
  },
  {
    accessorKey: 'from',
    header: 'From',
    cell: (props) => <span>{props.getValue<DateTime>().toFormat('yyyy-MM-dd')}</span>
  },
  {
    accessorKey: 'to',
    header: 'To',
    cell: (props) => <span>{props.getValue<DateTime>().toFormat('yyyy-MM-dd')}</span>
  },
  {
    accessorKey: 'location',
    header: 'Venue'
  },
  {
    accessorKey: 'selected',
    header: 'Selected',
    cell: (props) => {
      return props.getValue<boolean>() ? (
        <CheckIcon className="h-4 w-4 text-success" />
      ) : (
        <Cross2Icon className="h-4 w-4 text-destructive" />
      )
    }
  },
  columnHelper.display({
    id: 'actions',
    cell: (props) => <RowActions row={props.row} />
  })
]

// Declaration of which column can be filtered
const columnFilter: FilterColumn[] = [
  {
    columnKey: 'title',
    header: 'Title',
    dataType: 'string'
  },
  {
    columnKey: 'from',
    header: 'From',
    dataType: 'date'
  },
  {
    columnKey: 'to',
    header: 'To',
    dataType: 'date'
  }
]

// // Faceted options
// const statuses = [
//   {
//     value: "true",
//     label: "Selected",
//     icon: CheckCircledIcon,
//   },
//   {
//     value: "false",
//     label: "Not selected",
//     icon: CrossCircledIcon,
//   },
// ]
// const priorities = [
//   {
//     label: "Low",
//     value: "low",
//     icon: ArrowDownIcon,
//   },
//   {
//     label: "Medium",
//     value: "medium",
//     icon: ArrowRightIcon,
//   },
//   {
//     label: "High",
//     value: "high",
//     icon: ArrowUpIcon,
//   },
// ]

const EventTable = () => {
  const nav = useNavigate()

  return (
    <DataTable<GeneralEventInfo, unknown, IDataResponse<GeneralEventInfo>>
      columns={columns}
      filterColumns={columnFilter}
      swrService={EventService.getEvents}
      pageParam="page"
      limitParam="limit"
      toRow={EventService.toRow as () => GeneralEventInfo[]}
      toPaginate={EventService.toPaginate}
      onRowClick={(row: Row<GeneralEventInfo>) => {
        nav(`/events/${row.original.id}`)
      }}
    />
  )
}

export default EventTable
