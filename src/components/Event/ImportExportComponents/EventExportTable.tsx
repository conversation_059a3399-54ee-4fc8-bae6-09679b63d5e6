import { ColumnDef, Row, createColumnHelper } from '@tanstack/react-table'
import { DataTable, FilterColumn } from '@/components/Table/DataTable'
import { cn, statusToColor } from '@/lib/utils'
import { serialize } from '@/network/request'
import BatchJobService, { BatchJobResponse } from '@/network/services/batch-job'
import { BatchJob } from '@/types/BatchJob'
import { last } from 'radash'
import { FC } from 'react'
import { Button } from '@/components/ui/button'
import FileService from '@/network/services/file'
import { toast } from '@/components/ui/use-toast'

const columnHelper = createColumnHelper<BatchJob>()
const columns: ColumnDef<BatchJob>[] = [
  {
    accessorKey: 'id',
    header: 'ID'
  },
  {
    accessorKey: 'result.file_key',
    header: 'File',
    cell: (props) => {
      const value = last((props.getValue<string>() ?? '').split('/'))

      return (
        <div className="flex items-center space-x-2">
          <span className="capitalize">{value}</span>
        </div>
      )
    }
  },
  {
    accessorKey: 'created_at',
    header: 'Created At',
    cell: (props) => {
      const value = props.getValue<string>()
      return new Date(value).toDateString()
    }
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: (props) => {
      const value = props.getValue<string>()
      const color = statusToColor(value)

      return (
        <div className="flex items-center space-x-2">
          <div className={cn('h-1.5 w-1.5 self-center rounded-full', color)} />
          <span className="capitalize">{value}</span>
        </div>
      )
    }
  },
  columnHelper.display({
    id: 'actions',
    cell: (props) => <RowActions row={props.row} />
  })
]

const columnFilter: FilterColumn[] = []

const RowActions: FC<{ row: Row<BatchJob> }> = ({ row }) => {
  return (
    <div className="flex w-full justify-end">
      <Button
        size="sm"
        disabled={row.original.status != 'completed'}
        onClick={async (e) => {
          e.stopPropagation()

          try {
            const { data: response } = await FileService.getDownloadUrl(
              row.original.result.file_key
            )
            window.open(response.download_url)
          } catch (e) {
            console.log(e)
            toast({
              title: 'Action failed, please try again',
              variant: 'destructive'
            })
          }
        }}
      >
        Download
      </Button>
    </div>
  )
}

const EventExportTable = () => {
  return (
    <DataTable<BatchJob, unknown, BatchJobResponse>
      columns={columns}
      filterColumns={columnFilter}
      swrService={serialize(BatchJobService.getBatchJobs, { type: ['event-export'] })}
      pageParam="offset"
      limitParam="limit"
      toRow={BatchJobService.toRow}
      toPaginate={BatchJobService.toPaginate}
    />
  )
}

export default EventExportTable
