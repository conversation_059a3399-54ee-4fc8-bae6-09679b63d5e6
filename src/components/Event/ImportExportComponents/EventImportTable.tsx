import {
  ColumnDef
  // Row
} from '@tanstack/react-table'
import { DataTable, FilterColumn } from '@/components/Table/DataTable'
import { cn, statusToColor } from '@/lib/utils'
// import { useNavigate } from 'react-router-dom'
import { serialize } from '@/network/request'
import BatchJobService, { BatchJobResponse } from '@/network/services/batch-job'
import { BatchJob } from '@/types/BatchJob'

const columns: ColumnDef<BatchJob>[] = [
  {
    accessorKey: 'id',
    header: 'ID'
  },
  {
    accessorKey: 'context.fileKey',
    header: 'File'
  },
  {
    accessorKey: 'created_at',
    header: 'Created At',
    cell: (props) => {
      const value = props.getValue<string>()
      return new Date(value).toDateString()
    }
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: (props) => {
      const value = props.getValue<string>()
      const color = statusToColor(value)

      return (
        <div className="flex items-center space-x-2">
          <div className={cn('h-1.5 w-1.5 self-center rounded-full', color)} />
          <span className="capitalize">{value}</span>
        </div>
      )
    }
  }
]

const columnFilter: FilterColumn[] = []

const EventImportTable = () => {
  // const nav = useNavigate()

  return (
    <DataTable<BatchJob, unknown, BatchJobResponse>
      columns={columns}
      filterColumns={columnFilter}
      swrService={serialize(BatchJobService.getBatchJobs, { type: ['event-import'] })}
      pageParam="offset"
      limitParam="limit"
      toRow={BatchJobService.toRow}
      toPaginate={BatchJobService.toPaginate}
      // TODO: show import result
      // onRowClick={(row: Row<BatchJob>) => {
      //   nav(`/products/batch-jobs/${row.original.id}`)
      // }}
    />
  )
}

export default EventImportTable
