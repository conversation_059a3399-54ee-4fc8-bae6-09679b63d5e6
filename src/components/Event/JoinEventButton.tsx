import { Button } from '@/components/ui/button'
import { PlusCircleIcon } from 'lucide-react'
import { Popover, PopoverClose, PopoverContent, PopoverTrigger } from '../ui/popover'
import { toast } from '../ui/use-toast'
import EventService from '@/network/services/event'
import { useState } from 'react'
import { Event } from '@/types/Event'
import { mutate } from 'swr'

interface JoinEventProps {
  event: Event
}

const JoinEventButton = ({ event }: JoinEventProps) => {
  const [isPopoverOpen, setIsPopoverOpen] = useState(false)
  const joinEvent = async () => {
    try {
      const { data: submitResponse } = await EventService.joinEvent()
      if (submitResponse.success) {
        mutate(EventService.getActiveEvent)

        toast({
          title: 'Join event successfully',
          variant: 'success'
        })

        setIsPopoverOpen(false)
      } else {
        toast({
          title: 'Action failed, please try again',
          variant: 'destructive'
        })
      }
    } catch (e) {
      console.log(e)
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  }

  return (
    <>
      {!event.joined ? (
        <Popover open={isPopoverOpen} onOpenChange={(value) => setIsPopoverOpen(value)}>
          <PopoverTrigger asChild>
            <Button>
              <PlusCircleIcon size="16" className="mr-2" />
              Join Event
            </Button>
          </PopoverTrigger>
          <PopoverContent align="end">
            <p>Are you sure you want to join the event?</p>
            <div className="flex justify-end space-x-2 mt-2">
              <PopoverClose asChild>
                <Button size="sm" variant="outline">
                  No
                </Button>
              </PopoverClose>
              <Button size="sm" onClick={joinEvent}>
                Yes
              </Button>
            </div>
          </PopoverContent>
        </Popover>
      ) : (
        <Button disabled>Joined</Button>
      )}
    </>
  )
}

export default JoinEventButton
