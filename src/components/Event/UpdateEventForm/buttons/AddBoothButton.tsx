import { Button } from '@/components/ui/button'
import { Form } from '@/components/ui/form'
import {
  Sheet,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger
} from '@/components/ui/sheet'
import { PlusCircleIcon } from 'lucide-react'
import { FC, useState } from 'react'
import { useForm } from 'react-hook-form'
import { CreateEventBoothInput } from '@/types/Booth'
import EventBoothOptions from '../../CreateEventForm/EventBoothOptions'
import { toast } from '@/components/ui/use-toast'
import BoothService from '@/network/services/booth'
import { mutate } from 'swr'

export const AddBoothButton: FC<{
  eventId: number
  eventTitle?: string
}> = ({ eventId, eventTitle }) => {
  const [isSheetOpen, setIsSheetOpen] = useState(false)
  const form = useForm<CreateEventBoothInput>({
    shouldUseNativeValidation: false,
    defaultValues: {
      store_id: undefined,
      booth_no: undefined,
      hall_no: undefined,
      booth_size: undefined,
      collection_id_1: undefined,
      collection_id_2: undefined
    }
  })

  // useEffect(() => {
  //   if (form.formState.isSubmitSuccessful) {
  //     form.reset()
  //     setIsSheetOpen(false)
  //   }
  // }, [form.formState.isSubmitSuccessful])

  const onSubmit = form.handleSubmit(async (values) => {
    values.event_id = eventId

    console.log('submit values', values)

    // block if no changes
    // if (values.stores.length <= 0) {
    //   toast({
    //     title: 'No vendors selected',
    //     variant: 'destructive'
    //   })

    //   return
    // }

    try {
      const { data } = await BoothService.createBooths(values)
      console.log('response', data)

      if (data.success) {
        mutate((key) => typeof key === 'string' && key.includes(BoothService.getBooths))
        toast({
          title: 'Event Booth(s) created',
          variant: 'success'
        })
        setIsSheetOpen(false)
        form.reset()

        return
      } else {
        toast({
          title: data.message ?? 'Action failed, please try again',
          variant: 'destructive'
        })
      }
    } catch (e: any) {
      console.log(e?.response?.data?.message)
      toast({
        title: e?.response?.data?.message ?? 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <div className="flex justify-end">
      <Sheet
        open={isSheetOpen}
        onOpenChange={(isOpen) => {
          setIsSheetOpen(isOpen)
        }}
      >
        <SheetTrigger asChild>
          <Button>
            <PlusCircleIcon size="16" className="mr-2" />
            Create Booth
          </Button>
        </SheetTrigger>
        <SheetContent side="bottom" className="w-full h-full">
          <SheetHeader className="max-w-4xl mx-auto pt-10 pb-6">
            <SheetTitle className="flex justify-between items-center">
              Add booth to {eventTitle ?? '-'}
              <div className="flex space-x-2">
                <SheetClose asChild>
                  <Button
                    variant="outline"
                    onClick={() => {
                      form.reset()
                    }}
                  >
                    Cancel
                  </Button>
                </SheetClose>
                <Button
                  type="submit"
                  form="create-product-booth-form"
                  disabled={form.formState.isSubmitting}
                  onClick={() => {
                    form.clearErrors()
                  }}
                >
                  Submit
                </Button>
              </div>
            </SheetTitle>
          </SheetHeader>
          <Form {...form}>
            <form id="create-product-booth-form" onSubmit={onSubmit}>
              <EventBoothOptions />
            </form>
          </Form>
        </SheetContent>
      </Sheet>
    </div>
  )
}
