import { Button } from '@/components/ui/button'
import { Form } from '@/components/ui/form'
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  Sheet<PERSON>eader,
  SheetTitle,
  SheetTrigger
} from '@/components/ui/sheet'
import { PlusCircleIcon } from 'lucide-react'
import { FC, useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { CreateBoothContractInput } from '@/types/Contract'
import CreateContractForm from '../forms/CreateContractForm'

export const AddContractButton: FC<{
  eventId: number
  eventTitle?: string
}> = ({ eventId, eventTitle }) => {
  const [isSheetOpen, setIsSheetOpen] = useState(false)
  const form = useForm<CreateBoothContractInput>({
    defaultValues: {
      store_id: undefined,
      attention: {
        target: undefined,
        email: '',
        full_name: '',
        contact_number: '',
        designation: ''
      },
      company: {
        name: '',
        contact_number: '',
        address_1: '',
        address_2: '',
        fax: ''
      },
      event: {
        id: eventId,
        title: '',
        event_date: '',
        venue: ''
      },
      business_nature: ''
      // participation_fees: 0 // prevent initial value of 0
    },
    shouldUseNativeValidation: false
  })

  const storeId = form.watch('store_id')

  useEffect(() => {
    form.reset({ ...form.getValues() })
  }, [storeId])

  useEffect(() => {
    if (isSheetOpen) {
      form.reset({
        store_id: undefined,
        attention: {
          target: undefined,
          email: '',
          full_name: '',
          contact_number: '',
          designation: ''
        },
        company: {
          name: '',
          contact_number: '',
          address_1: '',
          address_2: '',
          fax: ''
        },
        event: {
          id: eventId,
          title: '',
          event_date: '',
          venue: ''
        },
        business_nature: ''
        // participation_fees: 0 // prevent initial value of 0
      })
    }
  }, [isSheetOpen])

  return (
    <div className="flex justify-end">
      <Sheet
        open={isSheetOpen}
        onOpenChange={(isOpen) => {
          setIsSheetOpen(isOpen)
        }}
      >
        <SheetTrigger asChild>
          <Button>
            <PlusCircleIcon size="16" className="mr-2" />
            Create Contract
          </Button>
        </SheetTrigger>
        <SheetContent side="bottom" className="w-full h-full">
          <SheetHeader className="max-w-4xl mx-auto pt-10 pb-6 flex flex-row space-x-2 items-center">
            <div className="flex-1">
              <SheetTitle className="flex justify-between items-center">
                New Contract for {eventTitle ?? '-'}
              </SheetTitle>
              <SheetDescription className="text-md text-red-600">
                *Only the latest contract will be saved for each company.
              </SheetDescription>
              <SheetDescription className="text-md text-red-600">
                *Please check if the desired contract exists before you create.
              </SheetDescription>
            </div>
            <div className="flex space-x-2">
              <SheetClose asChild>
                <Button
                  variant="outline"
                  onClick={() => {
                    form.reset()
                  }}
                >
                  Cancel
                </Button>
              </SheetClose>
              <Button
                type="submit"
                form="create-booth-contract-form"
                disabled={form.formState.isSubmitting}
                onClick={() => {
                  form.clearErrors()
                }}
              >
                Submit
              </Button>
            </div>
          </SheetHeader>
          <Form {...form}>
            <CreateContractForm setIsDialogOpen={setIsSheetOpen} />
          </Form>
        </SheetContent>
      </Sheet>
    </div>
  )
}
