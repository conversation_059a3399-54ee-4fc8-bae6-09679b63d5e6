import { DataTable, FilterColumn } from '@/components/Table/DataTable'
import { Button } from '@/components/ui/button'
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle, Title } from '@/components/ui/card'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { IDataResponse, serialize } from '@/network/request'
import { Event } from '@/types/Event'
import { ColumnDef, Row, createColumnHelper } from '@tanstack/react-table'
import {
  DownloadIcon,
  EditIcon,
  FileIcon,
  FileSignatureIcon,
  MoreHorizontal,
  FileQuestionIcon,
  FileXIcon,
  FileX2Icon,
  CoinsIcon
} from 'lucide-react'
import { FC, useState } from 'react'
import { useParams } from 'react-router-dom'
import { AddContractButton } from '../buttons/AddContractButton'
import { Contract, UpdateBoothContractInput } from '@/types/Contract'
import ContractService from '@/network/services/contract'
import { useToast } from '@/components/ui/use-toast'
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger
} from '@/components/ui/alert-dialog'
import GenerateNextInvoiceForm from '../forms/GenerateNextInvoiceForm'
import { Sheet, SheetClose, SheetContent, SheetHeader, SheetTitle } from '@/components/ui/sheet'
import UpdateContractForm from '../forms/UpdateContractForm'
import { useForm } from 'react-hook-form'
import { Form } from '@/components/ui/form'
import { mutate } from 'swr'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Label } from '@/components/ui/label'
import { PaymentFrequencyForm } from '../forms/PaymentFrequencyForm'

const EventContractCard: FC<{ event: Event }> = ({ event }) => {
  const { event_id } = useParams()

  if (!event_id) return <></>

  return (
    <div className="grid grid-cols-2 gap-4">
      <Card className="col-span-4 h-fit">
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <CardTitle>Contracts of {event?.title ?? '-'}</CardTitle>
          <div className="flex gap-x-2">
            <AddContractButton eventId={event.id} eventTitle={event?.title} />
          </div>
        </CardHeader>
        <CardContent>
          <EventContractTable />
        </CardContent>
        <CardFooter></CardFooter>
      </Card>
    </div>
  )
}

const EventContractTable = () => {
  const { event_id } = useParams()
  const [isSheetOpen, setIsSheetOpen] = useState(false)
  const [contractNo, setContractNo] = useState('')
  const form = useForm<UpdateBoothContractInput>({
    shouldUseNativeValidation: false
  })

  const columnHelper = createColumnHelper<Contract>()
  const columns: ColumnDef<Contract>[] = [
    {
      accessorKey: 'contract_no',
      header: 'Contract No.'
    },
    {
      accessorKey: 'event_name',
      header: 'Event'
    },
    {
      accessorKey: 'hall_no',
      header: 'Hall'
    },
    {
      accessorKey: 'booth_no',
      header: 'Booth'
    },
    {
      accessorKey: 'store_name',
      header: 'Exhibitor'
    },
    {
      accessorKey: 'payment_frequency',
      header: 'Payment Frequency',
      cell: (props) => {
        const contract = props.row.original

        return (
          <Popover>
            <PopoverTrigger asChild>
              <Button
                disabled={contract.payment_frequency == null}
                variant="outline"
                className={'justify-start text-left font-normal'}
              >
                <span>View</span>
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto">
              {contract.payment_frequency && (
                <div className="space-y-2">
                  <Title>Summary of payment frequency</Title>
                  <table className="border-separate border-spacing-2 border-spacing-x-4">
                    <thead className="border-b-2 border-black">
                      <tr>
                        <th>
                          <Title>Payment</Title>
                        </th>
                        <th>
                          <Title>Percentage (%)</Title>
                        </th>
                        <th>
                          <Title>Amount (RM)</Title>
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {contract.payment_frequency?.map((payment, index) => (
                        <tr key={index}>
                          <td align="center">
                            <Label>{index + 1}</Label>
                          </td>
                          <td align="center">
                            <Label>{payment.percentage}</Label>
                          </td>
                          <td align="right">
                            <Label>{payment.amount.toFixed(2)}</Label>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </PopoverContent>
          </Popover>
        )
      }
    },
    {
      accessorKey: 'status',
      header: 'Status'
    },
    columnHelper.display({
      id: 'actions',
      cell: (props) => <RowActions row={props.row} />
    })
  ]

  const columnFilter: FilterColumn[] = [
    { columnKey: 'contract_no', header: 'Contract No', dataType: 'string' },
    { columnKey: 'hall_no', header: 'Hall No', dataType: 'string' },
    { columnKey: 'booth_no', header: 'Booth No', dataType: 'string' },
    {
      columnKey: 'status',
      header: 'Status',
      dataType: 'faceted',
      options: [
        { value: 'pending', label: 'PENDING', icon: undefined },
        { value: 'accepted', label: 'ACCEPTED', icon: undefined },
        { value: 'rejected', label: 'REJECTED', icon: undefined }
      ]
    }
  ]

  const RowActions: FC<{ row: Row<Contract> }> = ({ row }) => {
    const downloadable = row.original.filename ? true : false
    // const invoices = row.original.invoices
    const paymentFrequency = row.original.payment_frequency
    const generateInvoice = row.original.generate_next_invoice ? true : false
    // const sign = row.original.status === 'pending'
    const { toast } = useToast()
    const [isInvoiceDialogOpen, setIsInvoiceDialogOpen] = useState(false)
    const [isPaymentFrequencyOpen, setIsPaymentFrequencyOpen] = useState(false)
    const defaultValues = row.original.metadata

    return (
      <div className="w-full flex justify-end">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuGroup>
              <DropdownMenuItem disabled={!downloadable} className="space-x-2">
                <AlertDialog key="download-contract-invoices">
                  <AlertDialogTrigger
                    onClick={(e) => e.stopPropagation()}
                    className="flex space-x-2 w-full"
                  >
                    <DownloadIcon size="16" />
                    <span>Download</span>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Generated Contract & Invoices</AlertDialogTitle>
                      <div key={row.original.contract_no} className="flex items-center space-x-2">
                        <span>{row.original.contract_no}</span>
                        <Button
                          type="button"
                          size="sm"
                          onClick={async (e) => {
                            e.stopPropagation()

                            try {
                              const { data: response } = await ContractService.download(
                                row.original.filename
                              )

                              window.open(response.data)
                            } catch (e) {
                              console.log(e)
                              toast({
                                title: 'Action failed, please try again',
                                variant: 'destructive'
                              })
                            }
                          }}
                        >
                          <DownloadIcon size="16" />
                        </Button>
                      </div>

                      {/* {row.original.invoices.length > 0 && (
                        <ul className="space-y-2">
                          {row.original.invoices.map((invoice) => (
                            <li key={invoice.invoice_no} className="flex items-center space-x-2">
                              <span>{invoice.invoice_no}</span>
                              <Button
                                type="button"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation()

                                  window.open(invoice.file_url)
                                }}
                              >
                                <DownloadIcon size="16" />
                              </Button>
                            </li>
                          ))}
                        </ul>
                      )} */}
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Close</AlertDialogCancel>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </DropdownMenuItem>
              <DropdownMenuItem
                disabled={row.original.status === 'accepted'}
                className="space-x-2"
                onClick={async (e) => {
                  e.stopPropagation()

                  try {
                    const { data: response } = await ContractService.updateStatus(row.original.id, {
                      status: 'accepted'
                    })

                    if (response.success) {
                      toast({
                        title: `Sign ${row.original.contract_no} successfully`,
                        variant: 'success'
                      })

                      mutate(
                        (key) =>
                          typeof key === 'string' && key.startsWith(ContractService.getContracts)
                      )
                    }
                  } catch (error) {
                    toast({
                      title: 'Action failed, please try again',
                      variant: 'destructive'
                    })
                  }
                }}
              >
                <FileSignatureIcon size="16" />
                <span>Accept</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                disabled={row.original.status === 'pending'}
                className="space-x-2"
                onClick={async (e) => {
                  e.stopPropagation()

                  try {
                    const { data: response } = await ContractService.updateStatus(row.original.id, {
                      status: 'pending'
                    })

                    if (response.success) {
                      toast({
                        title: `Revert ${row.original.contract_no} to pending successfully`,
                        variant: 'success'
                      })

                      mutate(
                        (key) =>
                          typeof key === 'string' && key.startsWith(ContractService.getContracts)
                      )
                    }
                  } catch (error) {
                    toast({
                      title: 'Action failed, please try again',
                      variant: 'destructive'
                    })
                  }
                }}
              >
                <FileQuestionIcon size="16" />
                <span>Pending</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                disabled={row.original.status === 'rejected'}
                className="space-x-2"
                onClick={async (e) => {
                  e.stopPropagation()

                  try {
                    const { data: response } = await ContractService.updateStatus(row.original.id, {
                      status: 'rejected'
                    })

                    if (response.success) {
                      toast({
                        title: `Reject ${row.original.contract_no} successfully`,
                        variant: 'success'
                      })

                      mutate(
                        (key) =>
                          typeof key === 'string' && key.startsWith(ContractService.getContracts)
                      )
                    }
                  } catch (error) {
                    toast({
                      title: 'Action failed, please try again',
                      variant: 'destructive'
                    })
                  }
                }}
              >
                <FileX2Icon size="16" />
                <span>Reject</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                disabled={row.original.status === 'void'}
                className="space-x-2"
                onClick={async (e) => {
                  e.stopPropagation()

                  try {
                    const { data: response } = await ContractService.updateStatus(row.original.id, {
                      status: 'void'
                    })

                    if (response.success) {
                      toast({
                        title: `Void ${row.original.contract_no} successfully`,
                        variant: 'success'
                      })

                      mutate(
                        (key) =>
                          typeof key === 'string' && key.startsWith(ContractService.getContracts)
                      )
                    }
                  } catch (error) {
                    toast({
                      title: 'Action failed, please try again',
                      variant: 'destructive'
                    })
                  }
                }}
              >
                <FileXIcon size="16" />
                <span>Void</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation()
                  setIsSheetOpen(true)
                  setContractNo(row.original.contract_no)
                  form.reset({ ...defaultValues, id: row.original.id })
                }}
                className="space-x-2"
              >
                <EditIcon size="16" />
                <span>Update</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => setIsPaymentFrequencyOpen(true)}
                className="space-x-2"
              >
                <CoinsIcon size="16" />
                <span>
                  {(paymentFrequency?.length ?? 0) > 0 ? 'Update ' : 'Add '}Payment Frequency
                </span>
              </DropdownMenuItem>
              {(paymentFrequency?.length ?? 0) > 0 && (
                <DropdownMenuItem
                  disabled={!generateInvoice}
                  onClick={() => setIsInvoiceDialogOpen(true)}
                  className="space-x-2"
                >
                  <FileIcon size="16" />
                  <span>Generate Next Invoice</span>
                </DropdownMenuItem>
              )}
              {/* <DropdownMenuItem disabled={(paymentFrequency?.length ?? 0) <= 0}>
                <AlertDialog key="void-invoices">
                  <AlertDialogTrigger
                    onClick={(e) => e.stopPropagation()}
                    className="flex space-x-2 w-full"
                  >
                    <TrashIcon size="16" />
                    <span>Void invoices</span>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>
                        Are you absolutely sure to void the {row.original.contract_no}'s invoices?
                      </AlertDialogTitle>
                      <AlertDialogDescription className="text-red-500">
                        The following invoices will be deleted and this action cannot be undone:
                      </AlertDialogDescription>
                      {row.original.invoices.length > 0 && (
                        <ul>
                          {row.original.invoices.map((invoice) => (
                            <li key={invoice.invoice_no} className="flex items-center space-x-2">
                              <span>{invoice.invoice_no}</span>
                              <Button
                                type="button"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation()

                                  window.open(invoice.file_url)
                                }}
                              >
                                <DownloadIcon size="16" />
                              </Button>
                            </li>
                          ))}
                        </ul>
                      )}
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={async () => {
                          try {
                            const { data: response } = await ContractService.voidInvoices(
                              row.original.id
                            )

                            if (response.success) {
                              toast({
                                title: `Void ${row.original.contract_no}'s invoices successfully`,
                                variant: 'success'
                              })

                              mutate(
                                (key) =>
                                  typeof key === 'string' &&
                                  key.startsWith(ContractService.getContracts)
                              )
                            }
                          } catch (error) {
                            toast({
                              title: 'Action failed, please try again',
                              variant: 'destructive'
                            })
                          }
                        }}
                      >
                        Confirm
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </DropdownMenuItem> */}
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>

        {generateInvoice && (
          <GenerateNextInvoiceForm
            contractId={row.original.id}
            invoice={row.original.payment_frequency[row.original.invoices.length]}
            isDialogOpen={isInvoiceDialogOpen}
            setIsDialogOpen={setIsInvoiceDialogOpen}
          />
        )}

        <PaymentFrequencyForm
          contract={row.original}
          isSheetOpen={isPaymentFrequencyOpen}
          setIsSheetOpen={setIsPaymentFrequencyOpen}
        />
      </div>
    )
  }

  return (
    <>
      <DataTable<Contract, unknown, IDataResponse<Contract>>
        columns={columns}
        filterColumns={columnFilter}
        swrService={serialize(ContractService.getContracts, { event_id: event_id })}
        toRow={ContractService.toRow}
        toPaginate={ContractService.toPaginate}
      />
      <div className="flex justify-end">
        <Sheet
          open={isSheetOpen}
          onOpenChange={(isOpen) => {
            setIsSheetOpen(isOpen)
          }}
        >
          <SheetContent side="bottom" className="w-full h-full">
            <SheetHeader className="max-w-4xl mx-auto pt-10 pb-6">
              <SheetTitle className="flex justify-between items-center">
                Update Contract {contractNo ?? ''}
                <div className="flex space-x-2">
                  <SheetClose asChild>
                    <Button
                      variant="outline"
                      onClick={() => {
                        form.reset()
                      }}
                    >
                      Cancel
                    </Button>
                  </SheetClose>
                  <Button
                    type="submit"
                    form="update-booth-contract-form"
                    disabled={form.formState.isSubmitting}
                    onClick={() => {
                      form.clearErrors()
                    }}
                  >
                    Submit
                  </Button>
                </div>
              </SheetTitle>
            </SheetHeader>
            <Form {...form}>
              <UpdateContractForm setIsDialogOpen={setIsSheetOpen} />
            </Form>
          </SheetContent>
        </Sheet>
      </div>
    </>
  )
}

export default EventContractCard
