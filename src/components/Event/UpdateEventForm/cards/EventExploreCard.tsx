import { Button } from '@/components/ui/button'
import { Card, Card<PERSON><PERSON>er, Card<PERSON><PERSON><PERSON>, CardContent, CardFooter } from '@/components/ui/card'
import { Dialog } from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Event } from '@/types/Event'
import { MoreHorizontal, SettingsIcon } from 'lucide-react'
import { FC, useState } from 'react'
import UpdateExploreCardForm from '../forms/UpdateExploreCardForm'
import useAuth from '@/hooks/useAuth'
import EventTabDescriptionCard from './EventTabDescriptionCard'
import EventExploreCardSort from './EventExploreCardSort'

const EventExploreCard: FC<{ event: Event }> = ({ event }) => {
  const { role } = useAuth()

  const [isCardO<PERSON>, setIsCardOpen] = useState(false)

  return (
    <>
      <div className="grid gap-4">
        <EventTabDescriptionCard event={event} type="explore" />

        <Card>
          <CardHeader className="flex flex-row space-y-0 items-center justify-between">
            <CardTitle>Explore Options</CardTitle>
            {role != 'vendor' && (
              <div className="flex flex-row space-x-2">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <span className="sr-only">Open menu</span>
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" forceMount>
                    <DropdownMenuGroup>
                      <DropdownMenuItem
                        onClick={async (event) => {
                          event.stopPropagation()
                          setIsCardOpen(true)
                        }}
                        className="space-x-2"
                      >
                        <SettingsIcon size="16" />
                        <span>Edit</span>
                      </DropdownMenuItem>
                    </DropdownMenuGroup>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            )}
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="eat" className="space-y-4">
              <TabsList>
                <TabsTrigger value="eat">Eat</TabsTrigger>
                <TabsTrigger value="shop">Shop</TabsTrigger>
                <TabsTrigger value="stay">Stay</TabsTrigger>
                <TabsTrigger value="sight">Sight</TabsTrigger>
              </TabsList>

              <TabsContent value="eat">
                <EventExploreCardSort
                  eventId={event.id}
                  eventExplores={event.event_explores?.filter(
                    (explore) => explore.explore_card.type == 'eat'
                  )}
                />
              </TabsContent>
              <TabsContent value="shop">
                <EventExploreCardSort
                  eventId={event.id}
                  eventExplores={event.event_explores?.filter(
                    (explore) => explore.explore_card.type == 'shop'
                  )}
                />
              </TabsContent>
              <TabsContent value="stay">
                <EventExploreCardSort
                  eventId={event.id}
                  eventExplores={event.event_explores?.filter(
                    (explore) => explore.explore_card.type == 'stay'
                  )}
                />
              </TabsContent>
              <TabsContent value="sight">
                <EventExploreCardSort
                  eventId={event.id}
                  eventExplores={event.event_explores?.filter(
                    (explore) => explore.explore_card.type == 'sight'
                  )}
                />
              </TabsContent>
            </Tabs>
          </CardContent>
          <CardFooter></CardFooter>
        </Card>
      </div>

      {/* Update explore cards */}
      <Dialog
        open={isCardOpen}
        onOpenChange={(isOpen) => {
          setIsCardOpen(isOpen)
        }}
      >
        <UpdateExploreCardForm event={event} setIsDialogOpen={setIsCardOpen} />
      </Dialog>
    </>
  )
}

export default EventExploreCard
