import { Button } from '@/components/ui/button'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'
import { SaveIcon } from 'lucide-react'
import { FC, useEffect, useState } from 'react'
import {
  DndContext,
  DragEndEvent,
  KeyboardSensor,
  PointerSensor,
  UniqueIdentifier,
  closestCenter,
  useSensor,
  useSensors
} from '@dnd-kit/core'
import {
  SortableContext,
  arrayMove,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy
} from '@dnd-kit/sortable'
import { SortableTableRow } from '@/components/ui/drag-and-drop'
import { mutate } from 'swr'
import { useToast } from '@/components/ui/use-toast'
import { Icons } from '@/components/icons'
import { Separator } from '@/components/ui/separator'
import EventService from '@/network/services/event'
import { EventExplore } from '@/types/Event'

const EventExploreCardSort: FC<{ eventId: number; eventExplores: EventExplore[] }> = ({
  eventId,
  eventExplores
}) => {
  // const { role } = useAuth()
  const { toast } = useToast()
  // const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  // const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false)
  // const [eventExploreSelected, setExploreCardSelected] = useState<ExploreCard | null>(null)
  const [isSorting, setIsSorting] = useState(false)

  // sorting sections
  const [sortableEventExplores, setSortableEventExplores] = useState(
    eventExplores.map((eventExplore) => ({
      ...eventExplore,
      id: eventExplore.id as UniqueIdentifier
    }))
  )
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates
    })
  )

  function handleDragEnd(event: DragEndEvent) {
    const { active, over } = event

    if (active.id !== over?.id) {
      setSortableEventExplores((items) => {
        const oldIndex = items.map((item) => item.id).indexOf(active.id)
        const newIndex = items.map((item) => item.id).indexOf(over!.id)

        return arrayMove(items, oldIndex, newIndex)
      })
    }
  }

  useEffect(() => {
    setSortableEventExplores(
      eventExplores.map((eventExplore) => ({
        ...eventExplore,
        id: eventExplore.id as UniqueIdentifier
      }))
    )
  }, [eventExplores])

  return (
    <>
      <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Title</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Description</TableHead>
              <TableHead className="text-center">Image</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody className="overflow-hidden">
            <SortableContext
              items={sortableEventExplores ?? []}
              strategy={verticalListSortingStrategy}
            >
              {sortableEventExplores.map((eventExplore) => (
                <SortableTableRow
                  key={eventExplore.id}
                  id={eventExplore.id as UniqueIdentifier}
                  className="overflow-hidden"
                >
                  <TableCell>{eventExplore.explore_card.title}</TableCell>
                  <TableCell>{eventExplore.explore_card.type}</TableCell>
                  <TableCell>{eventExplore.explore_card.description}</TableCell>
                  <TableCell align="center">
                    <img
                      className="h-[64px]"
                      src={eventExplore.explore_card.image_url}
                      alt={eventExplore.explore_card.title}
                    />
                  </TableCell>
                </SortableTableRow>
              ))}
            </SortableContext>
          </TableBody>
        </Table>
      </DndContext>

      {sortableEventExplores.length > 0 && (
        <>
          <Separator className="my-4" />
          <div className="flex justify-end">
            <Button
              type="button"
              variant="outline"
              disabled={isSorting}
              onClick={async () => {
                setIsSorting(true)

                try {
                  console.log(sortableEventExplores)
                  const ids = sortableEventExplores.map(
                    (eventExplore) => eventExplore.id
                  ) as number[]

                  const { data: response } = await EventService.sortEventExplore(eventId, { ids })
                  if (response.success) {
                    toast({
                      title: 'Sort successfully',
                      variant: 'success'
                    })
                    mutate(
                      (key) =>
                        typeof key === 'string' && key.startsWith(EventService.getEvent(eventId))
                    )
                  }
                } catch (error) {
                  toast({
                    title: 'Action failed, please try again',
                    variant: 'destructive'
                  })
                  setSortableEventExplores(
                    eventExplores.map((eventExplore) => ({
                      ...eventExplore,
                      id: eventExplore.id as UniqueIdentifier
                    }))
                  )
                }

                setIsSorting(false)
              }}
            >
              {isSorting ? (
                <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <SaveIcon size="16" className="mr-2" />
              )}
              Save Ordering
            </Button>
          </div>{' '}
        </>
      )}

      {/* Update eventExplores */}
      {/* <Dialog open={isFormOpen} onOpenChange={(isOpen) => setIsFormOpen(isOpen)}>
          <UpdateExploreCardHeadlinesForm
            initialValues={eventExplores}
            setIsDialogOpen={setIsFormOpen}
          />
        </Dialog> */}
    </>
  )
}

export default EventExploreCardSort
