import { Button } from '@/components/ui/button'
import {
  <PERSON>,
  CardHeader,
  Card<PERSON>itle,
  CardContent,
  CardFooter,
  Title,
  Content
} from '@/components/ui/card'
import { Dialog } from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { Event } from '@/types/Event'
import EventService from '@/network/services/event'
import { MoreHorizontal, SettingsIcon } from 'lucide-react'
import { FC, useMemo, useState } from 'react'
import UpdateEventInfoForm from '../forms/UpdateEventInfoForm'
import useAuth from '@/hooks/useAuth'
import { cn } from '@/lib/utils'
import { mutate } from 'swr'
import { toast } from '@/components/ui/use-toast'
import JoinEventButton from '../../JoinEventButton'
import { LicenseRoles } from '@/network/services/auth'
import { Image } from '@unpic/react'
import ReadOnlyEditorComponent from '@/components/Editor/ReadOnlyEditor'
import UpdateEventGeneralForm from '../forms/UpdateEventGeneralForm'
import { DateTime } from 'luxon'

const EventStatusBadge: FC<{ event: Event }> = ({ event }) => {
  const { role } = useAuth()

  const color = useMemo(() => {
    return event.selected ? 'bg-success' : event.selected == false ? 'bg-destructive' : 'bg-muted'
  }, [event.selected])
  const reverseColor = useMemo(() => {
    return event.selected == false
      ? 'bg-success'
      : event.selected == true
      ? 'bg-destructive'
      : 'bg-muted'
  }, [event.selected])

  const updateStatus = async (event: Event) => {
    try {
      const { data } = await EventService.updateEventStatus(event.id)

      mutate((key) => typeof key === 'string' && key.startsWith(EventService.getEvent(event.id)))

      if (data.success) {
        toast({
          title: `Product ${event.selected ? 'Deactivated' : 'Activated'}`,
          variant: 'success'
        })
        return
      }

      toast({
        title: `Action failed, please try again`,
        variant: 'destructive'
      })
    } catch (error) {
      toast({
        title: `Action failed, please try again`,
        variant: 'destructive'
      })
      console.log(error)
    }
  }

  if (role === 'vendor') {
    return <JoinEventButton event={event} />
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="h-8">
          <div className="flex items-center space-x-2">
            <div className={cn('h-1.5 w-1.5 self-center rounded-full', color)} />
            <span className="capitalize text-xs">{event.selected ? 'Active' : 'Deactivate'}</span>
          </div>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" forceMount>
        <DropdownMenuGroup>
          <DropdownMenuItem
            onClick={async (e) => {
              e.stopPropagation()
              updateStatus(event)
            }}
            className="space-x-2"
          >
            <div className={cn('h-1.5 w-1.5 self-center rounded-full', reverseColor)} />
            <span className="capitalize text-xs">
              {!event.selected ? 'Activate' : 'Deactivate'}
            </span>
          </DropdownMenuItem>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

const EventInfoCard: FC<{ event: Event }> = ({ event }) => {
  const { event_info: eventInfo } = event
  const { role } = useAuth()

  const [isEventGeneralDialogOpen, setIsEventGeneralDialogOpen] = useState(false)
  const [isEventInfoDialogOpen, setIsEventInfoDialogOpen] = useState(false)

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <CardTitle>Event General</CardTitle>

          {/* TODO: allow edit general event info? */}
          <div className="flex flex-row space-x-2">
            <EventStatusBadge event={event} />

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" forceMount>
                <DropdownMenuGroup>
                  <DropdownMenuItem
                    onClick={async (event) => {
                      event.stopPropagation()
                      setIsEventGeneralDialogOpen(true)
                    }}
                    className="space-x-2"
                  >
                    <SettingsIcon size="16" />
                    <span>Edit</span>
                  </DropdownMenuItem>
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            {/* Event general */}
            <Title>Title</Title>
            <Content>{event.title ?? '-'}</Content>
            <Title>Location</Title>
            <Content>{event.location ?? '-'}</Content>
            <Title>Cut-off date</Title>
            <Content>
              {DateTime.fromISO(event.cut_off as string).toFormat('yyyy-MM-dd HH:mm:ss') ?? '-'}
            </Content>
            <Title>Starting date</Title>
            <Content>
              {DateTime.fromISO(event.from as string).toFormat('yyyy-MM-dd HH:mm:ss') ?? '-'}
            </Content>
            <Title>Ending date</Title>
            <Content>
              {DateTime.fromISO(event.to as string).toFormat('yyyy-MM-dd HH:mm:ss') ?? '-'}
            </Content>
          </div>
        </CardContent>
        <CardFooter></CardFooter>
      </Card>

      {role != LicenseRoles.VENDOR && (
        <Card>
          <CardHeader className="flex flex-row space-y-0 items-center justify-between">
            <CardTitle>Event Information</CardTitle>
            <div className="flex flex-row space-x-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-8 w-8 p-0">
                    <span className="sr-only">Open menu</span>
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" forceMount>
                  <DropdownMenuGroup>
                    <DropdownMenuItem
                      onClick={async (event) => {
                        event.stopPropagation()
                        setIsEventInfoDialogOpen(true)
                      }}
                      className="space-x-2"
                    >
                      <SettingsIcon size="16" />
                      <span>Edit</span>
                    </DropdownMenuItem>
                  </DropdownMenuGroup>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <Title>Primary Banner</Title>
              <Content>
                <div className="flex flex-col justify-end space-y-4">
                  {/* <img className="w-[240px]" src={eventInfo?.image_url ?? '-'} alt="event-image" /> */}
                  {eventInfo?.gallery?.map((galleryMedia) => {
                    return (
                      <div key={galleryMedia.name} className="flex justify-end">
                        {galleryMedia.type == 'image' && (
                          <span>
                            <Image
                              layout="constrained"
                              width={150}
                              height={150}
                              src={galleryMedia.url}
                            />
                            {/* <Label className="text-xs font-normal text-gray-500 text-ellipsis">
                              {galleryMedia.name}
                            </Label> */}
                          </span>
                        )}
                        {galleryMedia.type == 'video' && (
                          <span>
                            <video
                              controls
                              src={galleryMedia.url}
                              height={150}
                              width={150}
                              className="rounded-md"
                            />
                            {/* <Label className="text-xs font-normal text-gray-500 truncate">
                              {galleryMedia.name}
                            </Label> */}
                          </span>
                        )}
                      </div>
                    )
                  })}
                </div>
              </Content>
              <Title>Description</Title>
              <Content>
                <ReadOnlyEditorComponent content={eventInfo?.description} />
              </Content>
              <Title>Secondary Banner Text</Title>
              <Content>
                <ReadOnlyEditorComponent content={eventInfo?.banner_text} />
              </Content>
            </div>
          </CardContent>
          <CardFooter></CardFooter>
        </Card>
      )}

      {/* {role != 'vendor' && <EventPopUpCard event={event} />} */}

      {/* Update event general */}
      <Dialog
        open={isEventGeneralDialogOpen}
        onOpenChange={(isOpen) => {
          setIsEventGeneralDialogOpen(isOpen)
        }}
      >
        <UpdateEventGeneralForm event={event} setIsDialogOpen={setIsEventGeneralDialogOpen} />
      </Dialog>

      {/* Update event information */}
      <Dialog
        open={isEventInfoDialogOpen}
        onOpenChange={(isOpen) => {
          setIsEventInfoDialogOpen(isOpen)
        }}
      >
        <UpdateEventInfoForm
          eventInfo={eventInfo}
          setIsDialogOpen={setIsEventInfoDialogOpen}
          eventId={event.id}
        />
      </Dialog>
    </>
  )
}

export default EventInfoCard
