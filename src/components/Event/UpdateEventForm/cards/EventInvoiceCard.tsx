import { DataTable, FilterColumn } from '@/components/Table/DataTable'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { IDataResponse, serialize } from '@/network/request'
import { Event } from '@/types/Event'
import { ColumnDef, Row, createColumnHelper } from '@tanstack/react-table'
import { CreditCardIcon, DownloadIcon, MoreHorizontal } from 'lucide-react'
import { FC } from 'react'
import { useParams } from 'react-router-dom'
import { Invoice } from '@/types/Contract'
import InvoiceService from '@/network/services/invoice'
import { useToast } from '@/components/ui/use-toast'
import { mutate } from 'swr'
import { DateTime } from 'luxon'

const EventInvoiceCard: FC<{ event: Event }> = ({ event }) => {
  const { event_id } = useParams()

  if (!event_id) return <></>

  return (
    <div className="grid grid-cols-2 gap-4">
      <Card className="col-span-4 h-fit">
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <CardTitle>Invoices of {event?.title ?? '-'}</CardTitle>
          {/* <AddInvoiceButton eventTitle={event?.title} /> */}
        </CardHeader>
        <CardContent>
          <EventInvoiceTable />
        </CardContent>
        <CardFooter></CardFooter>
      </Card>
    </div>
  )
}

const EventInvoiceTable: FC<{}> = ({}) => {
  const { event_id } = useParams()
  const columnHelper = createColumnHelper<Invoice>()
  const columns: ColumnDef<Invoice>[] = [
    {
      accessorKey: 'invoice_no',
      header: 'Invoice No.'
    },
    {
      accessorKey: 'contract_no',
      header: 'Contract No.'
    },
    {
      accessorKey: 'contract.store_name',
      header: 'Exhibitor'
    },
    {
      accessorKey: 'issued_date',
      header: 'Issued Date',
      cell: (props) => DateTime.fromISO(props.getValue<string>()).toFormat('dd-MM-yyyy')
    },
    {
      accessorKey: 'due_date',
      header: 'Due Date',
      cell: (props) => DateTime.fromISO(props.getValue<string>()).toFormat('dd-MM-yyyy')
    },
    {
      accessorKey: 'status',
      header: 'Status'
    },
    columnHelper.display({
      id: 'actions',
      cell: (props) => <RowActions row={props.row} />
    })
  ]

  const columnFilter: FilterColumn[] = [
    { columnKey: 'contract_no', header: 'Contract No', dataType: 'string' },
    { columnKey: 'invoice_no', header: 'Invoice No', dataType: 'string' },
    {
      columnKey: 'status',
      header: 'Status',
      dataType: 'faceted',
      options: [
        { value: 'pending', label: 'PENDING', icon: undefined },
        { value: 'paid', label: 'PAID', icon: undefined },
        { value: 'overdue', label: 'OVERDUE', icon: undefined }
      ]
    }
  ]

  const RowActions: FC<{ row: Row<Invoice> }> = ({ row }) => {
    const overdue =
      row.original.status === 'overdue' ||
      DateTime.now() > DateTime.fromISO(row.original.due_date as string)
    const { toast } = useToast()

    return (
      <div className="w-full flex justify-end">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuGroup>
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation()
                  window.open(row.original.file_url)
                }}
                className="space-x-2"
              >
                <DownloadIcon size="16" />
                <span>Download</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                disabled={overdue}
                onClick={async (e) => {
                  e.stopPropagation()

                  if (row.original.status === 'overdue') {
                    toast({
                      title: `Cannot pay ${row.original.invoice_no}`,
                      variant: 'destructive'
                    })
                    return
                  }

                  try {
                    const updateStatus = row.original.status === 'pending' ? 'paid' : 'pending'
                    const { data: response } = await InvoiceService.updateStatus(row.original.id, {
                      status: updateStatus
                    })

                    if (response.success) {
                      toast({
                        title: `${row.original.invoice_no} status had changed`,
                        variant: 'success'
                      })

                      mutate(
                        (key) =>
                          typeof key === 'string' && key.startsWith(InvoiceService.getInvoices)
                      )
                    } else {
                      toast({
                        title: `Action failed, please try again`,
                        variant: 'destructive'
                      })
                    }
                  } catch (error) {
                    toast({
                      title: 'Action failed, please try again',
                      variant: 'destructive'
                    })
                  }
                }}
                className="space-x-2"
              >
                <CreditCardIcon size="16" />
                <span>{row.original.status === 'pending' ? 'Pay' : 'Unpay'}</span>
              </DropdownMenuItem>
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    )
  }

  return (
    <DataTable<Invoice, unknown, IDataResponse<Invoice>>
      columns={columns}
      filterColumns={columnFilter}
      swrService={serialize(InvoiceService.getInvoices, { event_id: event_id })}
      toRow={InvoiceService.toRow}
      toPaginate={InvoiceService.toPaginate}
    />
  )
}

export default EventInvoiceCard
