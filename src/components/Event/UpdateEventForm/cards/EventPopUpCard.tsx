import { Button } from '@/components/ui/button'
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON><PERSON>,
  CardContent,
  CardFooter,
  Title,
  Content
} from '@/components/ui/card'
import { Dialog } from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { Event } from '@/types/Event'
import { MoreHorizontal, SettingsIcon } from 'lucide-react'
import { FC, useState } from 'react'
import UpdatePopUpReminderForm from '../forms/UpdatePopUpReminderForm'
import { Image } from '@unpic/react'

const EventPopUpCard: FC<{ event: Event }> = ({ event }) => {
  const [isFormOpen, setIsFormOpen] = useState(false)

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <CardTitle>Pop-up Reminder</CardTitle>
          <div className="flex flex-row space-x-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" forceMount>
                <DropdownMenuGroup>
                  <DropdownMenuItem
                    onClick={async (event) => {
                      event.stopPropagation()
                      setIsFormOpen(true)
                    }}
                    className="space-x-2"
                  >
                    <SettingsIcon size="16" />
                    <span>Edit</span>
                  </DropdownMenuItem>
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <Title>Description</Title>
            <Content>{event.pop_up_description ?? '-'}</Content>

            <Title>Media</Title>
            <Content className="flex justify-end">
              {event.pop_up_media && (
                <Image layout="constrained" width={150} height={150} src={event.pop_up_media} />
              )}
            </Content>

            <Title>Show</Title>
            <Content>{event.show_pop_up ? 'Yes' : 'No'}</Content>
          </div>
        </CardContent>
        <CardFooter></CardFooter>
      </Card>

      {/* Update explore description */}
      <Dialog open={isFormOpen} onOpenChange={(isOpen) => setIsFormOpen(isOpen)}>
        <UpdatePopUpReminderForm
          initialValues={event}
          isDialogOpen={isFormOpen}
          setIsDialogOpen={setIsFormOpen}
        />
      </Dialog>
    </>
  )
}

export default EventPopUpCard
