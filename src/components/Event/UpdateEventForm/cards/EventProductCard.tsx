import { Button } from '@/components/ui/button'
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from '@/components/ui/card'
import { Dialog } from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { Event, EventProduct } from '@/types/Event'
import { MoreHorizontal, SettingsIcon } from 'lucide-react'
import { FC, useState } from 'react'
import useAuth from '@/hooks/useAuth'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'
import useSWR from 'swr'
import EventService from '@/network/services/event'
import UpdateEventProductForm from '../forms/UpdateEventProductForm'
import { useNavigate } from 'react-router-dom'

const EventProductCard: FC<{ event: Event }> = ({ event }) => {
  const { role } = useAuth()
  const { data, error, isLoading } = useSWR<{ data: any }>(EventService.getVendorEventProducts)
  const eventProducts = data?.data ?? []
  console.log('event-prod', data?.data ?? [])

  const [isDialogOpen, setIsDialogOpen] = useState(false)

  if (error) {
    console.log('event-prod error', error)
    return <></>
  }

  if (isLoading) return <></>

  return (
    <>
      <div className="grid grid-cols-2 gap-4">
        <Card className="col-span-4 h-fit">
          <CardHeader className="flex flex-row space-y-0 items-center justify-between">
            <CardTitle>My Products for {event?.title ?? '-'}</CardTitle>
            {role === 'vendor' && (
              <div className="flex flex-row space-x-2">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <span className="sr-only">Open menu</span>
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" forceMount>
                    <DropdownMenuGroup>
                      <DropdownMenuItem
                        onClick={async (event) => {
                          event.stopPropagation()
                          setIsDialogOpen(true)
                        }}
                        className="space-x-2"
                      >
                        <SettingsIcon size="16" />
                        <span>Edit</span>
                      </DropdownMenuItem>
                    </DropdownMenuGroup>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            )}
          </CardHeader>
          <CardContent>
            <EventProductTable eventId={event.id} eventProducts={eventProducts ?? []} />
          </CardContent>
          <CardFooter></CardFooter>
        </Card>
      </div>

      {/* Update event product */}
      <Dialog
        open={isDialogOpen}
        onOpenChange={(isOpen) => {
          setIsDialogOpen(isOpen)
        }}
      >
        <UpdateEventProductForm
          initialValues={eventProducts ?? []}
          setIsDialogOpen={setIsDialogOpen}
        />
      </Dialog>
    </>
  )
}

const EventProductTable: FC<{
  eventId: number
  eventProducts: EventProduct[]
}> = ({ eventId, eventProducts }) => {
  const nav = useNavigate()

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Id</TableHead>
          <TableHead>Title</TableHead>
          <TableHead className="text-center">Image</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {eventProducts?.map((eventProduct, index) => {
          return (
            <TableRow key={index} onClick={() => nav(`/events/${eventId}/event_products/${eventProduct.id}`)}>
              <TableCell>{eventProduct.id}</TableCell>
              <TableCell>{eventProduct.product?.title}</TableCell>
              <TableCell align="center">
                <img className="h-[128px]" src={eventProduct.product?.thumbnail} alt={'event-product-' + index} />
              </TableCell>
            </TableRow>
          )
        })}
      </TableBody>
    </Table>
  )
}

export default EventProductCard
