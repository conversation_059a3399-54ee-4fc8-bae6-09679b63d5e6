import { ColumnDef, Row, createColumnHelper } from '@tanstack/react-table'
import { DataTable, FilterColumn } from '@/components/Table/DataTable'
import { IDataResponse, serialize } from '@/network/request'
import { EventPurchase, EventPurchaseStatus } from '@/types/EventPurchase'
import EventPurchaseService from '@/network/services/event_purchase'
import { useNavigate } from 'react-router-dom'
import { FC } from 'react'
import { mutate } from 'swr'
import { useToast } from '@/components/ui/use-toast'
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuGroup
} from '@/components/ui/dropdown-menu'
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from '@/components/ui/card'
import { MoreHorizontal } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { cn, statusToColor } from '@/lib/utils'
import { DateTime } from 'luxon'
import { Event } from '@/types/Event'
import EventUserAgreementsCard from './EventUserAgreementsCard'
import useAuth from '@/hooks/useAuth'

const columnHelper = createColumnHelper<EventPurchase>()

const columns: ColumnDef<EventPurchase>[] = [
  // {
  //   accessorKey: 'id',
  //   header: 'Id'
  // },
  {
    accessorKey: 'user.email',
    header: 'User'
  },
  // {
  //   accessorKey: 'event_id',
  //   header: 'Event Id'
  // },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: (props) => {
      const value = props.getValue<string>()
      const color = statusToColor(value)

      return (
        <div className="flex items-center space-x-2">
          <div className={cn('h-1.5 w-1.5 self-center rounded-full', color)} />
          <span className="capitalize">{value}</span>
        </div>
      )
    }
  },
  {
    accessorKey: 'total',
    header: 'Total'
  },
  {
    accessorKey: 'buy_date',
    header: 'Purchase Date',
    cell: (props) => {
      const value = props.getValue<string>()

      return DateTime.fromISO(value as string).toFormat('yyyy-MM-dd')
    }
  },
  {
    accessorKey: 'created_at',
    header: 'Created Date',
    cell: (props) => {
      const value = props.getValue<string>()

      return DateTime.fromISO(value as string).toFormat('yyyy-MM-dd HH:mm:ss')
    }
  },
  {
    accessorKey: 'transaction_id',
    header: 'Transaction Id'
  },
  columnHelper.display({
    id: 'actions',
    cell: (props) => <RowActions row={props.row} />
  })
]

interface EventPurchaseTableCardProps extends React.HTMLAttributes<HTMLDivElement> {
  event: Event
}

const RowActions: FC<{ row: Row<EventPurchase> }> = ({ row }) => {
  const { toast } = useToast()

  const updateEventPurchaseStatus = async (newStatus: EventPurchaseStatus) => {
    try {
      const { data: response } = await EventPurchaseService.updateEventPurchaseStatus(
        row.original.id,
        {
          status: newStatus
        }
      )

      if (response != null) {
        mutate(
          (key) =>
            typeof key === 'string' &&
            key.startsWith(EventPurchaseService.getEventPurchases(row.original.event_id))
        )
        toast({
          title: `Event Purchase ${newStatus}`,
          variant: 'success'
        })
      }

      console.log(response)
    } catch (e) {
      console.log(e)

      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  }

  return (
    <div className="w-full flex justify-end">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0" onClick={(e) => e.stopPropagation()}>
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuGroup>
            <DropdownMenuItem
              onClick={async (e) => {
                e.stopPropagation()
                updateEventPurchaseStatus(EventPurchaseStatus.APPROVED)
              }}
              className="space-x-2"
              disabled={row.original.status != EventPurchaseStatus.PENDING}
            >
              <div className={cn('h-1.5 w-1.5 self-center rounded-full', 'bg-success')} />
              <span className="capitalize text-xs">Approve</span>
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={async (e) => {
                e.stopPropagation()
                updateEventPurchaseStatus(EventPurchaseStatus.REJECTED)
              }}
              className="space-x-2"
              disabled={row.original.status != EventPurchaseStatus.PENDING}
            >
              <div className={cn('h-1.5 w-1.5 self-center rounded-full', 'bg-danger')} />
              <span className="capitalize text-xs">Reject</span>
            </DropdownMenuItem>
          </DropdownMenuGroup>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}

const columnFilter: FilterColumn[] = []

function EventPurchaseTableCard({ event }: EventPurchaseTableCardProps) {
  const { role } = useAuth()

  return (
    <div className="grid gap-4">
      <Card className="h-fit">
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <div>
            <CardTitle>Purchases of {event?.title ?? '-'}</CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <EventPurchaseTable eventId={event.id} />
        </CardContent>
        <CardFooter></CardFooter>
      </Card>

      {role != 'vendor' && <EventUserAgreementsCard event={event} />}
    </div>
  )
}

interface EventPurchaseTableProps extends React.HTMLAttributes<HTMLDivElement> {
  eventId: number
}

function EventPurchaseTable({ eventId }: EventPurchaseTableProps) {
  const nav = useNavigate()

  return (
    <DataTable<EventPurchase, unknown, IDataResponse<EventPurchase>>
      columns={columns}
      filterColumns={columnFilter}
      swrService={serialize(EventPurchaseService.getEventPurchases(eventId as number), {})}
      pageParam="page"
      limitParam="limit"
      sortParam="sort"
      sortColumns={[
        // 'user_email',
        'buy_date',
        'created_at'
      ]}
      toRow={EventPurchaseService.toRow}
      toPaginate={EventPurchaseService.toPaginate}
      onRowClick={(row: Row<EventPurchase>) => {
        nav(`event_purchases/${row.original.id}`)
      }}
    />
  )
}

export default EventPurchaseTableCard
