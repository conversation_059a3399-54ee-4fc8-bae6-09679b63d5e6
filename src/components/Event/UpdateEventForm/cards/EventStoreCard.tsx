import { Button } from '@/components/ui/button'
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  CardFooter,
  CardDescription
} from '@/components/ui/card'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { Event } from '@/types/Event'
import {
  DownloadIcon,
  MonitorCheckIcon,
  MonitorXIcon,
  MoreHorizontal,
  StoreIcon,
  Trash2Icon
} from 'lucide-react'
import { FC } from 'react'
import useSWR, { mutate } from 'swr'
import BoothService from '@/network/services/booth'
import { DataTable, FilterColumn } from '@/components/Table/DataTable'
import { Booth } from '@/types/Booth'
import { IDataResponse, serialize } from '@/network/request'
import { ColumnDef, Row, createColumnHelper } from '@tanstack/react-table'
import { NavLink, useParams } from 'react-router-dom'
import { toast } from '@/components/ui/use-toast'
import { AddBooth<PERSON>utton } from '../buttons/AddBoothButton'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger
} from '@/components/ui/alert-dialog'
import ContractService from '@/network/services/contract'
import { Invoice } from '@/types/Contract'

const EventStoreCard: FC<{ event: Event }> = ({ event }) => {
  const { event_id } = useParams()

  if (!event_id) return <></>

  return (
    <div className="grid grid-cols-2 gap-4">
      <Card className="col-span-4 h-fit">
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <div>
            <CardTitle>Booths of {event?.title ?? '-'}</CardTitle>
            <CardDescription>*The interaction in frontend depends on Booth No</CardDescription>
          </div>
          <AddBoothButton eventId={parseInt(event_id)} eventTitle={event?.title} />
        </CardHeader>
        <CardContent>
          <EventBoothTable eventId={event_id} />
        </CardContent>
        <CardFooter></CardFooter>
      </Card>
    </div>
  )
}

const EventBoothTable: FC<{ eventId: string }> = ({ eventId }) => {
  const columnHelper = createColumnHelper<Booth>()
  const columns: ColumnDef<Booth>[] = [
    {
      accessorKey: 'booth_no',
      header: 'Booth No.'
      // cell: (props) => {
      //   const booth = props.row.original

      //   return (
      //     <EditableSingleField
      //       initialValue={booth.booth_no}
      //       fieldKey="booth_no"
      //       updateTargetId={booth.id}
      //       updateApiService={BoothService.updateBooth}
      //       mutateUrl={BoothService.getBooths}
      //     />
      //   )
      // }
    },
    {
      accessorKey: 'booth_size',
      header: 'Booth Size'
      // cell: (props) => {
      //   const booth = props.row.original

      //   return (
      //     <EditableSingleField
      //       initialValue={booth.booth_size}
      //       fieldKey="booth_size"
      //       updateTargetId={booth.id}
      //       updateApiService={BoothService.updateBooth}
      //       mutateUrl={BoothService.getBooths}
      //     />
      //   )
      // }
    },
    {
      accessorKey: 'hall_no',
      header: 'Hall No'
      // cell: (props) => {
      //   const booth = props.row.original

      //   return (
      //     <EditableSingleField
      //       initialValue={booth.hall_no}
      //       fieldKey="hall_no"
      //       updateTargetId={booth.id}
      //       updateApiService={BoothService.updateBooth}
      //       mutateUrl={BoothService.getBooths}
      //     />
      //   )
      // }
    },
    // {
    //   accessorKey: 'contract_no',
    //   header: 'Contract No.'
    // },
    {
      accessorKey: 'store.name',
      header: 'Exhibitor'
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: (props) => <span className="capitalize">{props.row.original.status}</span>
    },
    columnHelper.display({
      id: 'actions',
      cell: (props) => <RowActions row={props.row} />
    })
  ]

  const columnFilter: FilterColumn[] = []

  const RowActions: FC<{ row: Row<Booth> }> = ({ row }) => {
    const boothDetail = row.original

    const { data, error } = useSWR(
      serialize(ContractService.getContractByFilters, {
        event_id: row.original.event_id,
        store_id: row.original.store_id,
        booth_no: row.original.booth_no,
        hall_no: row.original.hall_no
      })
    )

    if (!data || error) {
      return <></>
    }

    const updateBoothStatus = async (status: 'blocked' | 'active') => {
      try {
        const { data: response } = await BoothService.updateBooth(row.original.id, {
          status: status
        })

        if (response != null) {
          mutate((key) => typeof key === 'string' && key.startsWith(BoothService.getBooths))
          toast({
            title: `Booth ${status}`,
            variant: 'success'
          })
        }

        console.log(response)
      } catch (e) {
        console.log(e)

        toast({
          title: 'Action failed, please try again',
          variant: 'destructive'
        })
      }
    }

    return (
      <div className="w-full flex justify-end">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuGroup>
              <NavLink to={`/companies/${row.original.store.id}`}>
                <DropdownMenuItem className="space-x-2">
                  <StoreIcon size="16" />
                  <span>Go to Exhibitor</span>
                </DropdownMenuItem>
              </NavLink>
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation()
                  updateBoothStatus('active')
                }}
                className="space-x-2"
                disabled={boothDetail.status == 'active'}
              >
                <MonitorCheckIcon size="16" />
                <span>Activate</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation()
                  updateBoothStatus('blocked')
                }}
                className="space-x-2"
                disabled={boothDetail.status == 'blocked'}
              >
                <MonitorXIcon size="16" />
                <span>Block</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                // onClick={(e) => {
                //   e.stopPropagation()
                //   updateBoothStatus('blocked')
                // }}
                className="space-x-2"
              >
                <AlertDialog key="delete-event-store">
                  <AlertDialogTrigger
                    onClick={(e) => e.stopPropagation()}
                    className="flex space-x-2 w-full"
                  >
                    <Trash2Icon size="16" color="red" />
                    <span className="text-red-500">Delete</span>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>
                        Delete {row.original.store.name} - {row.original.hall_no},{' '}
                        {row.original.booth_no}({row.original.booth_size})
                      </AlertDialogTitle>
                      <AlertDialogDescription className="text-red-500">
                        *NOTE: The following contract and invoices will be deleted and this action
                        cannot be undone
                      </AlertDialogDescription>
                      {/* Contract */}
                      {data?.data?.file_url && (
                        <div key={data.data.contract_no} className="flex items-center space-x-2">
                          <span>{data.data.contract_no}</span>
                          <Button
                            type="button"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()

                              window.open(data.data.file_url)
                            }}
                          >
                            <DownloadIcon size="16" />
                          </Button>
                        </div>
                      )}
                      {/* Invoice */}
                      {data?.data?.invoices?.length > 0 && (
                        <ul className="space-y-2">
                          {data.data.invoices.map((invoice: Invoice) => (
                            <li key={invoice.invoice_no} className="flex items-center space-x-2">
                              <span>{invoice.invoice_no}</span>
                              <Button
                                type="button"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation()

                                  window.open(invoice.file_url)
                                }}
                              >
                                <DownloadIcon size="16" />
                              </Button>
                            </li>
                          ))}
                        </ul>
                      )}
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Close</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={async (e) => {
                          e.stopPropagation()

                          try {
                            const { data: response } = await BoothService.deleteBooth(
                              row.original.id
                            )

                            if (response.success) {
                              toast({
                                title: `Delete ${row.original.hall_no}, ${row.original.booth_no} successfully`,
                                variant: 'success'
                              })

                              mutate(
                                (key) =>
                                  typeof key === 'string' && key.startsWith(BoothService.getBooths)
                              )
                            }
                          } catch (error) {
                            toast({
                              title: 'Action failed, please try again',
                              variant: 'destructive'
                            })
                          }
                        }}
                      >
                        Confirm
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </DropdownMenuItem>
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    )
  }

  return (
    <DataTable<Booth, unknown, IDataResponse<Booth>>
      columns={columns}
      filterColumns={columnFilter}
      swrService={serialize(BoothService.getBooths, { event_id: eventId })}
      toRow={BoothService.toRow}
      toPaginate={BoothService.toPaginate}
    />
  )
}

export default EventStoreCard
