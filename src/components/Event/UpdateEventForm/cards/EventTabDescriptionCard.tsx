import { Button } from '@/components/ui/button'
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from '@/components/ui/card'
import { Dialog } from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { Event } from '@/types/Event'
import { MoreHorizontal, SettingsIcon } from 'lucide-react'
import { FC, useState } from 'react'
import useAuth from '@/hooks/useAuth'
import UpdateTabDescriptionForm from '../forms/UpdateTabDescriptionForm'
import ReadOnlyEditorComponent from '@/components/Editor/ReadOnlyEditor'

const EventTabDescriptionCard: FC<{
  event: Event
  type: 'deal' | 'layout' | 'visit' | 'explore'
}> = ({ event, type }) => {
  const { role } = useAuth()

  const [isFormOpen, setIsFormOpen] = useState(false)

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <CardTitle>Description</CardTitle>
          {role != 'vendor' && (
            <div className="flex flex-row space-x-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-8 w-8 p-0">
                    <span className="sr-only">Open menu</span>
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" forceMount>
                  <DropdownMenuGroup>
                    <DropdownMenuItem
                      onClick={async (event) => {
                        event.stopPropagation()
                        setIsFormOpen(true)
                      }}
                      className="space-x-2"
                    >
                      <SettingsIcon size="16" />
                      <span>Edit</span>
                    </DropdownMenuItem>
                  </DropdownMenuGroup>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          )}
        </CardHeader>
        <CardContent>
          <ReadOnlyEditorComponent content={event[`${type}_description`]} />
        </CardContent>
        <CardFooter></CardFooter>
      </Card>

      {/* Update explore description */}
      <Dialog open={isFormOpen} onOpenChange={(isOpen) => setIsFormOpen(isOpen)}>
        <UpdateTabDescriptionForm
          initialValues={event}
          setIsDialogOpen={setIsFormOpen}
          type={type}
        />
      </Dialog>
    </>
  )
}

export default EventTabDescriptionCard
