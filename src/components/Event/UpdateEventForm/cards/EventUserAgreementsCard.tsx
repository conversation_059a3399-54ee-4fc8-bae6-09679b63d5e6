import { Button } from '@/components/ui/button'
import { <PERSON>, Card<PERSON><PERSON>er, Card<PERSON><PERSON>le, Card<PERSON>ontent, CardFooter, Title } from '@/components/ui/card'
import { Dialog } from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { Event } from '@/types/Event'
import { MoreHorizontal, SettingsIcon } from 'lucide-react'
import { FC, useState } from 'react'
import UpdateUserAgreementsForm from '../forms/UpdateUserAgreementsForm'
import ReadOnlyEditorComponent from '@/components/Editor/ReadOnlyEditor'

const EventUserAgreementsCard: FC<{ event: Event }> = ({ event }) => {
  const [isFormOpen, setIsFormOpen] = useState(false)

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <CardTitle>User Agreements</CardTitle>
          <div className="flex flex-row space-x-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" forceMount>
                <DropdownMenuGroup>
                  <DropdownMenuItem
                    onClick={async (event) => {
                      event.stopPropagation()
                      setIsFormOpen(true)
                    }}
                    className="space-x-2"
                  >
                    <SettingsIcon size="16" />
                    <span>Edit</span>
                  </DropdownMenuItem>
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            {event.metadata?.user_agreements?.map((user_agreement: string, i: number) => (
              <>
                <Title>User Agreement {i + 1}</Title>
                <ReadOnlyEditorComponent className="text-right" content={user_agreement} />
              </>
            ))}
          </div>
        </CardContent>
        <CardFooter></CardFooter>
      </Card>

      {/* Update explore description */}
      <Dialog open={isFormOpen} onOpenChange={(isOpen) => setIsFormOpen(isOpen)}>
        <UpdateUserAgreementsForm
          initialValues={event}
          isDialogOpen={isFormOpen}
          setIsDialogOpen={setIsFormOpen}
        />
      </Dialog>
    </>
  )
}

export default EventUserAgreementsCard
