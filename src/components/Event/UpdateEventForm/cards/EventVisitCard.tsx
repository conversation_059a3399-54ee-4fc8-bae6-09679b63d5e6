import { Button } from '@/components/ui/button'
import {
  <PERSON>,
  CardHeader,
  CardTitle,
  CardContent,
  CardFooter,
  Title,
  Content
} from '@/components/ui/card'
import { Dialog } from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'
import { Event } from '@/types/Event'
import { MoreHorizontal, SettingsIcon } from 'lucide-react'
import React, { FC, useState } from 'react'
import UpdateVisitForm from '../forms/UpdateVisitForm'
import useAuth from '@/hooks/useAuth'
import EventTabDescriptionCard from './EventTabDescriptionCard'

const EventVisitCard: FC<{ event: Event }> = ({ event }) => {
  const { role } = useAuth()

  const [isVisitOpen, setIsVisitO<PERSON>] = useState(false)

  const visit_us = event.event_visit?.visit

  return (
    <>
      <EventTabDescriptionCard event={event} type="visit" />

      <Card>
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <CardTitle>Venue Information</CardTitle>
          {role != 'vendor' && (
            <div className="flex flex-row space-x-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-8 w-8 p-0">
                    <span className="sr-only">Open menu</span>
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" forceMount>
                  <DropdownMenuGroup>
                    <DropdownMenuItem
                      onClick={async (event) => {
                        event.stopPropagation()
                        setIsVisitOpen(true)
                      }}
                      className="space-x-2"
                    >
                      <SettingsIcon size="16" />
                      <span>Edit</span>
                    </DropdownMenuItem>
                  </DropdownMenuGroup>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          )}
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <Title>Venue</Title>
            <Content>{visit_us?.venue_name ?? '-'}</Content>
            <Title>Address</Title>
            <Content>{visit_us?.venue_address ?? '-'}</Content>
            <Title>Google Map URL</Title>
            <Content className="break-all">{visit_us?.location_url ?? '-'}</Content>
            <Title>Contact Number</Title>
            <Content>{visit_us?.contact_mobile_number ?? '-'}</Content>
            <Title>Email Address</Title>
            <Content>{visit_us?.contact_email ?? '-'}</Content>
            <Title>Website</Title>
            <Content>{visit_us?.website_url ?? '-'}</Content>
            <Title>Instagram</Title>
            <Content>{visit_us?.instagram_url ?? '-'}</Content>
            <Title>Facebook</Title>
            <Content>{visit_us?.facebook_url ?? '-'}</Content>
          </div>
        </CardContent>
        <CardFooter></CardFooter>
      </Card>

      <Card>
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <CardTitle>Parking & Rate</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-2/12">Location</TableHead>
                <TableHead>Waze URL</TableHead>
                <TableHead className="w-1/12">First Hour</TableHead>
                <TableHead className="w-1/12">Subsequent Hour</TableHead>
                <TableHead className="w-1/12">Maximum Charge</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {visit_us?.parking_and_rate?.map((parkInfo, index) => {
                return (
                  <TableRow key={index}>
                    <TableCell>{parkInfo.label}</TableCell>
                    <TableCell style={{ lineBreak: 'anywhere' }}>{parkInfo.waze_url}</TableCell>
                    <TableCell>{parkInfo.first_hour}</TableCell>
                    <TableCell>{parkInfo.every_subsequent_hour}</TableCell>
                    <TableCell>{parkInfo.maximum_parking_fee_per_day}</TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
          </Table>
        </CardContent>
        <CardFooter></CardFooter>
      </Card>

      <Card>
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <CardTitle>Getting Here</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Transportation Mode</TableHead>
                <TableHead>Description</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {visit_us?.getting_here?.map((gettingHere, index) => {
                return (
                  <TableRow key={index}>
                    <TableCell>{gettingHere.method}</TableCell>
                    <TableCell>{gettingHere.description}</TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
          </Table>
        </CardContent>
        <CardFooter></CardFooter>
      </Card>

      <Card>
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <CardTitle>Shuttle Van Service</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <Title>Title</Title>
            <Content className="break-words">
              {visit_us?.shuttle_van_service ? (
                <>
                  {visit_us.shuttle_van_service.description?.split('\n').map((text, i) => {
                    return (
                      <React.Fragment key={i}>
                        {i > 0 && <br />}
                        <p>{text}</p>
                      </React.Fragment>
                    )
                  }) ?? '-'}
                </>
              ) : (
                '-'
              )}
            </Content>
            <Title>Media</Title>
            <Content className="flex justify-end">
              {visit_us?.shuttle_van_service ? (
                <img
                  className="max-w-[180px]"
                  src={visit_us.shuttle_van_service.image_url ?? '-'}
                  alt="brand-thumbnail"
                />
              ) : (
                '-'
              )}
            </Content>
          </div>
        </CardContent>
        <CardFooter></CardFooter>
      </Card>

      {/* Update venue information */}
      <Dialog
        open={isVisitOpen}
        onOpenChange={(isOpen) => {
          setIsVisitOpen(isOpen)
        }}
      >
        <UpdateVisitForm event={event} setIsDialogOpen={setIsVisitOpen} />
      </Dialog>
    </>
  )
}

export default EventVisitCard
