import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from '@/components/ui/accordion'
import { toast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import { CreateBoothContractInput } from '@/types/Contract'
import { useFormContext } from 'react-hook-form'
import useSWR, { useSWRConfig } from 'swr'
import ContractGeneralInformation from './contract/ContractGeneralInformation'
import ContractAttentions from './contract/ContractAttentions'
import ContractCompanyDetails from './contract/ContractCompanyDetails'
import ContractEventDetails from './contract/ContractEventDetails'
import ContractBoothDetails from './contract/ContractBoothDetails'
import ContractFeesDetails from './contract/ContractFeesDetails'
import { IPostResponse } from '@/network/request'
import { ContractFormData } from '@/types/Contract'
import { useEffect } from 'react'
import ContractService from '@/network/services/contract'
import { isAxiosError } from 'axios'

interface ContractFormProps extends React.HTMLAttributes<HTMLDivElement> {
  setIsDialogOpen: (value: boolean) => void
}

function CreateContractForm({ className, setIsDialogOpen }: ContractFormProps) {
  const { mutate } = useSWRConfig()
  const form = useFormContext<CreateBoothContractInput>()

  const eventId = form.watch('event.id')
  const storeId = form.watch('store_id')

  const { data: contractFormData } = useSWR<IPostResponse<ContractFormData>>(
    eventId && storeId ? ContractService.getContractFormData(eventId, storeId) : null
  )

  useEffect(() => {
    if (contractFormData) {
      const brands: {
        event_space_type: {
          line_1: string
          line_2: string
          line_3: string
        }
        title: string
      }[] = []

      for (let booth of contractFormData.data?.booths) {
        brands.push({
          event_space_type: {
            line_1: '',
            line_2: booth.size,
            line_3: `Hall ${booth.hall_no}, Booth No. ${booth.no}`
          },
          title: `${booth.brand1}${booth.brand2 ? `, ${booth.brand2}` : ''}`
        })
      }
      form.setValue('brands', brands)
    }
  }, [contractFormData])

  const onSubmit = form.handleSubmit(async (values) => {
    values.participation_fees = parseFloat(values.participation_fees.toString())
    console.log('submit values', values)
    // block if no changes
    // if (values.stores.length <= 0) {
    //   toast({
    //     title: 'No vendors selected',
    //     variant: 'destructive'
    //   })

    //   return
    // }

    try {
      const { data } = await ContractService.createContract(values)
      console.log('response', data)

      if (data.success) {
        mutate((key) => typeof key === 'string' && key.includes(ContractService.getContracts))
        toast({
          title: 'Contract created',
          variant: 'success'
        })

        setIsDialogOpen(false)

        return
      } else {
        toast({
          title: 'Action failed, please try again',
          variant: 'destructive'
        })
      }
    } catch (e) {
      console.log(e)

      if (isAxiosError(e)) {
        let message = 'Action failed, please try again'
        if (e.response?.data.message) {
          message = e.response.data.message
        }

        toast({
          title: message,
          variant: 'destructive'
        })
      } else {
        toast({
          title: 'Action failed, please try again',
          variant: 'destructive'
        })
      }
    }
  })

  return (
    <form
      id="create-booth-contract-form"
      className={cn('flex flex-col space-y-8 max-w-4xl mx-auto', className)}
      onSubmit={onSubmit}
    >
      <Accordion
        type="multiple"
        className="w-full"
        defaultValue={[
          'contract-general-information',
          'contract-attentions',
          'contract-company-details',
          'contract-event-details',
          'contract-booth-details',
          'contract-fees-details'
        ]}
      >
        <AccordionItem value="contract-general-information">
          <AccordionTrigger disabled>General Information*</AccordionTrigger>
          <AccordionContent>
            <ContractGeneralInformation />
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="contract-attentions">
          <AccordionTrigger disabled>Attentions</AccordionTrigger>
          <AccordionContent>
            <ContractAttentions attention={contractFormData?.data.attention} />
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="contract-company-details">
          <AccordionTrigger disabled>Company Details</AccordionTrigger>
          <AccordionContent>
            <ContractCompanyDetails company={contractFormData?.data.company} />
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="contract-event-details">
          <AccordionTrigger disabled>Event Details</AccordionTrigger>
          <AccordionContent>
            <ContractEventDetails event={contractFormData?.data.event} />
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="contract-booth-details">
          <AccordionTrigger disabled>Booth Details</AccordionTrigger>
          <AccordionContent>
            <ContractBoothDetails booths={contractFormData?.data.booths} />
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="contract-fees-details">
          <AccordionTrigger disabled>Fees Details</AccordionTrigger>
          <AccordionContent>
            <ContractFeesDetails />
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </form>
  )
}

export default CreateContractForm
