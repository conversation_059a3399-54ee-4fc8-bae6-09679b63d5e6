import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from '@/components/ui/accordion'
import { toast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import { Contract, CreateBoothInvoiceInput } from '@/types/Contract'
import { useFormContext } from 'react-hook-form'
import { useSWRConfig } from 'swr'
import ContractService from '@/network/services/contract'
import InvoiceGeneralInformation from './invoice/InvoiceGeneralInformation'
import { DateTime } from 'luxon'

interface ContractFormProps extends React.HTMLAttributes<HTMLDivElement> {
  contract: Contract
  setIsDialogOpen: (value: boolean) => void
}

function CreateInvoiceForm({ className, contract, setIsDialogOpen }: ContractFormProps) {
  const { mutate } = useSWRConfig()
  const form = useFormContext<CreateBoothInvoiceInput>()

  const onSubmit = form.handleSubmit(async (values) => {
    console.log('submit values', values)

    // block if no changes
    // if (values.stores.length <= 0) {
    //   toast({
    //     title: 'No vendors selected',
    //     variant: 'destructive'
    //   })

    //   return
    // }

    //validate date

    var dateValid = true
    values.payments.forEach((item, index) => {
      if (index > 0) {
        const currDate = DateTime.fromISO(item.due_date as string)
        const prevDate = DateTime.fromISO(values.payments[index - 1].due_date as string)

        if (prevDate.toSeconds() > currDate.toSeconds()) {
          form.setError(`payments.${index}.due_date`, {
            message: 'Upcoming payment due date must be later than previous payment due date'
          })
          dateValid = false
        }
      }
    })

    if (!dateValid) {
      return
    }

    try {
      const { data } = await ContractService.updatePaymentFrequency(values.contract_id, values)
      console.log('response', data)

      if (data.success) {
        mutate((key) => typeof key === 'string' && key.includes(ContractService.getContracts))
        toast({
          title: `Payment frequency updated.`,
          variant: 'success'
        })

        setIsDialogOpen(false)

        return
      } else {
        toast({
          title: 'Action failed, please try again',
          variant: 'destructive'
        })
      }
    } catch (e) {
      console.log(e)
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <form
      id="create-booth-invoice-form"
      className={cn('flex flex-col space-y-8 max-w-4xl mx-auto', className)}
      onSubmit={onSubmit}
    >
      <Accordion
        type="multiple"
        className="w-full"
        defaultValue={[
          'invoice-general-information'
          // 'invoice-attentions',
          // 'invoice-company-details',
          // 'invoice-event-details',
          // 'invoice-business-details',
          // 'invoice-brands-details'
        ]}
      >
        <AccordionItem value="invoice-general-information">
          <AccordionTrigger disabled>General Information*</AccordionTrigger>
          <AccordionContent>
            <InvoiceGeneralInformation contract={contract} />
          </AccordionContent>
        </AccordionItem>
        {/* <AccordionItem value="contract-attentions">
            <AccordionTrigger disabled>Attentions</AccordionTrigger>
            <AccordionContent>
              <ContractAttentions attention={contractFormData?.data.attention} />
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="contract-company-details">
            <AccordionTrigger disabled>Company Details</AccordionTrigger>
            <AccordionContent>
              <ContractCompanyDetails company={contractFormData?.data.company} />
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="contract-event-details">
            <AccordionTrigger disabled>Event Details</AccordionTrigger>
            <AccordionContent>
              <ContractEventDetails event={contractFormData?.data.event} />
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="contract-business-details">
            <AccordionTrigger disabled>Business Details</AccordionTrigger>
            <AccordionContent>
              <ContractBusinessDetails booth={contractFormData?.data.booth} />
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="contract-brands-details">
            <AccordionTrigger disabled>Brands Details</AccordionTrigger>
            <AccordionContent>
              <ContractBrandsDetails brands={contractFormData?.data.brands} />
            </AccordionContent>
          </AccordionItem> */}
      </Accordion>
    </form>
  )
}

export default CreateInvoiceForm
