import { CalendarDatePicker } from '@/components/DatePicker/CalendarDatePicker'
import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { toast } from '@/components/ui/use-toast'
import ContractService from '@/network/services/contract'
import InvoiceService from '@/network/services/invoice'
import { GenerateInvoice, PaymentFrequency } from '@/types/Contract'
import { DateTime } from 'luxon'
import { FC, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { mutate } from 'swr'

const GenerateNextInvoiceForm: FC<{
  contractId: number | string
  invoice: PaymentFrequency
  isDialogOpen: boolean
  setIsDialogOpen: (value: boolean) => void
}> = ({ contractId, invoice, isDialogOpen, setIsDialogOpen }) => {
  const form = useForm<GenerateInvoice>({
    shouldUseNativeValidation: false,
    defaultValues: {}
  })

  useEffect(() => {
    if (isDialogOpen) {
      form.reset()
    }
  }, [isDialogOpen])

  const onSubmit = form.handleSubmit(async (values) => {
    console.log(values)

    try {
      const { data: response } = await InvoiceService.generateNextInvoice(contractId, values)

      if (response.success) {
        setIsDialogOpen(false)
        mutate((key) => typeof key === 'string' && key.startsWith(ContractService.getContracts))

        toast({
          title: 'Invoice generated successfully',
          variant: 'success'
        })

        setIsDialogOpen(false)
      } else {
        toast({
          title: 'Action failed, please try again',
          variant: 'destructive'
        })
      }
    } catch (e) {
      console.log(e)
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle className="flex justify-between items-center">
            Generate Invoice
            <div className="flex space-x-2">
              <DialogClose asChild>
                <Button variant="outline">Cancel</Button>
              </DialogClose>
              <Button
                type="submit"
                form="generate-invoice-form"
                disabled={form.formState.isSubmitting}
                onClick={() => {
                  form.clearErrors()
                }}
              >
                {form.formState.isSubmitting && (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                )}
                Submit
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form id="generate-invoice-form" onSubmit={onSubmit} className="grid grid-cols-2 gap-4">
            <FormItem className="flex flex-col col-span-2">
              <FormLabel>Invoice Amount</FormLabel>
              <Input disabled readOnly value={invoice.amount} />
            </FormItem>

            {/* {invoice.items?.map((item, i) => (
              <React.Fragment key={i}>
                <FormLabel className="flex flex-col col-span-2">Item {i + 1}</FormLabel>
                <FormItem className="flex flex-col col-span-1">
                  <FormLabel>Description</FormLabel>
                  <Textarea disabled readOnly value={item.description} />
                </FormItem>
                <FormItem className="flex flex-col col-span-1">
                  <FormLabel>Amount</FormLabel>
                  <Input disabled readOnly value={item.amount} />
                </FormItem>
              </React.Fragment>
            ))} */}

            <Separator className="flex flex-col col-span-2" />
            <FormField
              control={form.control}
              name="issued_date"
              rules={{ required: 'Issued Date cannot be empty' }}
              render={({ field }) => (
                <FormItem className="flex flex-col col-span-2">
                  <FormLabel>Issued Date</FormLabel>
                  <CalendarDatePicker
                    mode="single"
                    buttonLabel={
                      field.value
                        ? DateTime.fromISO(field.value as string).toFormat('yyyy-MM-dd')
                        : undefined
                    }
                    onSelect={(e) => {
                      field.onChange(
                        DateTime.fromISO(e?.toISOString() ?? '')
                          .set({
                            hour: 0,
                            minute: 0,
                            second: 0,
                            millisecond: 0
                          })
                          .toISO()
                      )
                    }}
                    disabled={(date) => date < new Date('1900-01-01')}
                  />
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="term"
              rules={{
                required: 'Term cannot be empty'
                // validate: (value) => {
                //   if (
                //     DateTime.fromISO(value as string) <
                //     DateTime.fromISO(form.getValues('issued_date') as string)
                //   ) {
                //     return 'Issued date must be early than due date.'
                //   }
                //   return true
                // }
              }}
              render={({ field }) => (
                <FormItem className="flex flex-col col-span-2">
                  <FormLabel>Term (working days)</FormLabel>
                  <FormControl>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose an option" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem key={7} value={'7'}>
                          7
                        </SelectItem>
                        <SelectItem key={30} value={'30'}>
                          30
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}

export default GenerateNextInvoiceForm
