import * as React from 'react'
import { useForm } from 'react-hook-form'

import { cn } from '@/lib/utils'
import { Input } from '@/components/ui/input'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { CalendarDatePicker } from '@/components/DatePicker/CalendarDatePicker'
import { toast } from '@/components/ui/use-toast'
import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import EventService from '@/network/services/event'
import { UpdateEventInput } from '@/types/CreateEvent'
import { mutate } from 'swr'
import { DateTime } from 'luxon'
import { GeneralEventInfo } from '@/types/Event'

interface EventFormProps extends React.HTMLAttributes<HTMLDivElement> {
  initialValue: GeneralEventInfo
}

export function UpdateEventInfoForm({ className, initialValue }: EventFormProps) {
  const form = useForm<UpdateEventInput>({
    defaultValues: {
      event: {
        ...initialValue
      },
      event_info: {} // TODO
    }
  })

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      await EventService.updateEvent(initialValue.id, values)
      mutate((key) => typeof key === 'string' && key.startsWith(EventService.getEvents))
      form.reset()
      toast({
        title: 'Event created',
        variant: 'success'
      })
    } catch (error) {
      console.error(error)
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <Form {...form}>
      <form onSubmit={onSubmit} className={cn('grid gap-4', className)}>
        <FormField
          control={form.control}
          name="event.title"
          rules={{ required: 'Please enter the title' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Title</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex w-full justify-between">
          <FormField
            control={form.control}
            name="event.from"
            rules={{ required: 'Please enter the start date' }}
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Starting Date</FormLabel>
                <CalendarDatePicker
                  mode="single"
                  buttonLabel={
                    field.value
                      ? DateTime.fromISO(field.value as string).toFormat('yyyy-MM-dd')
                      : undefined
                  }
                  // selected={new Date((field.value as DateTime)?.toISO() ?? new Date())}
                  onSelect={(e) => {
                    field.onChange(DateTime.fromISO(e?.toISOString() ?? '').toISO())
                  }}
                  disabled={(date) => date > new Date() || date < new Date('1900-01-01')}
                />
                {<FormMessage /> ?? <FormDescription>The start date for the event</FormDescription>}
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="event.to"
            rules={{ required: 'Please enter the end date' }}
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>End Date</FormLabel>
                <CalendarDatePicker
                  mode="single"
                  buttonLabel={
                    field.value
                      ? DateTime.fromISO(field.value as string).toFormat('yyyy-MM-dd')
                      : undefined
                  }
                  // selected={new Date((field.value as DateTime)?.toISO() ?? new Date())}
                  onSelect={(e) => {
                    field.onChange(DateTime.fromISO(e?.toISOString() ?? '').toISO())
                  }}
                  disabled={(date) => date > new Date() || date < new Date('1900-01-01')}
                />
                <FormDescription>The end date for the event</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* need to be between from & to */}
        <FormField
          control={form.control}
          name="event.cut_off"
          rules={{ required: 'Please enter the cut off date' }}
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Final Registration Date</FormLabel>
              <CalendarDatePicker
                mode="single"
                buttonLabel={
                  field.value
                    ? DateTime.fromISO(field.value as string).toFormat('yyyy-MM-dd')
                    : undefined
                }
                // selected={new Date((field.value as DateTime)?.toISO() ?? new Date())}
                onSelect={(e) => {
                  field.onChange(DateTime.fromISO(e?.toISOString() ?? '').toISO())
                }}
                disabled={(date) => date > new Date() || date < new Date('1900-01-01')}
              />
              <FormDescription>The final date allowed to register for the event</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="event.location"
          rules={{ required: 'Please enter the location' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Location</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button
          type="submit"
          disabled={form.formState.isSubmitting}
          onClick={() => {
            form.clearErrors()
          }}
        >
          {form.formState.isSubmitting && <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />}
          Submit
        </Button>
      </form>
    </Form>
  )
}
