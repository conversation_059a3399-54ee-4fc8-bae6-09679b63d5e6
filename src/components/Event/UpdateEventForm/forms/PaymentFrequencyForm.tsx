import { Button } from '@/components/ui/button'
import { Form } from '@/components/ui/form'
import { Sheet, SheetClose, <PERSON>et<PERSON>ontent, <PERSON>etHeader, SheetTitle } from '@/components/ui/sheet'
import { FC, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { Contract, CreateBoothInvoiceInput } from '@/types/Contract'
import CreateInvoiceForm from './CreateInvoiceForm'

export const PaymentFrequencyForm: FC<{
  contract: Contract
  isSheetOpen: boolean
  setIsSheetOpen: (value: boolean) => void
}> = ({ contract, isSheetOpen, setIsSheetOpen }) => {
  const form = useForm<CreateBoothInvoiceInput>({
    defaultValues: {
      contract_id: contract.id,
      payment_frequency: contract.payment_frequency?.length.toString() ?? '',
      payments: contract.payment_frequency ?? []
    },
    shouldUseNativeValidation: false
  })

  useEffect(() => {
    form.reset({ ...form.getValues() })
  }, [contract])

  useEffect(() => {
    form.reset({
      contract_id: contract.id,
      payment_frequency: contract.payment_frequency?.length.toString() ?? '',
      payments: contract.payment_frequency ?? []
    })
  }, [isSheetOpen])

  return (
    <div className="flex justify-end">
      <Sheet
        open={isSheetOpen}
        onOpenChange={(isOpen) => {
          setIsSheetOpen(isOpen)
        }}
      >
        <SheetContent side="bottom" className="w-full h-full">
          <SheetHeader className="max-w-4xl mx-auto pt-10 pb-6">
            <SheetTitle className="flex justify-between items-center">
              {(contract.payment_frequency?.length ?? 0) > 0 ? 'Update ' : 'Add '}
              Payment Frequency for {contract.contract_no ?? '-'}
              <div className="flex space-x-2">
                <SheetClose asChild>
                  <Button
                    variant="outline"
                    onClick={() => {
                      form.reset()
                    }}
                  >
                    Cancel
                  </Button>
                </SheetClose>
                <Button
                  type="submit"
                  form="create-booth-invoice-form"
                  disabled={form.formState.isSubmitting}
                  onClick={() => {
                    form.clearErrors()
                  }}
                >
                  Submit
                </Button>
              </div>
            </SheetTitle>
          </SheetHeader>
          <Form {...form}>
            <CreateInvoiceForm contract={contract} setIsDialogOpen={setIsSheetOpen} />
          </Form>
        </SheetContent>
      </Sheet>
    </div>
  )
}
