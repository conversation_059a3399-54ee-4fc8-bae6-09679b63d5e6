import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from '@/components/ui/accordion'
import { toast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import { UpdateBoothContractInput } from '@/types/Contract'
import { useFormContext } from 'react-hook-form'
import useSWR, { useSWRConfig } from 'swr'
import ContractGeneralInformation from './contract/ContractGeneralInformation'
import ContractAttentions from './contract/ContractAttentions'
import ContractCompanyDetails from './contract/ContractCompanyDetails'
import ContractEventDetails from './contract/ContractEventDetails'
import ContractBoothDetails from './contract/ContractBoothDetails'
import ContractFeesDetails from './contract/ContractFeesDetails'
import { IPostResponse } from '@/network/request'
import { ContractFormData } from '@/types/Contract'
import { FormDescription } from '@/components/ui/form'
import ContractService from '@/network/services/contract'

interface ContractFormProps extends React.HTMLAttributes<HTMLDivElement> {
  setIsDialogOpen: (value: boolean) => void
  // eventStoreId: number
}

function UpdateContractForm({ className, setIsDialogOpen }: ContractFormProps) {
  const { mutate } = useSWRConfig()
  const form = useFormContext<UpdateBoothContractInput>()

  const eventId = form.getValues('event.id')
  const storeId = form.getValues('store_id')

  const { data: contractFormData } = useSWR<IPostResponse<ContractFormData>>(
    ContractService.getContractFormData(eventId, storeId)
  )

  const onSubmit = form.handleSubmit(async (values) => {
    console.log('submit values', values)
    // block if no changes
    // if (values.stores.length <= 0) {
    //   toast({
    //     title: 'No vendors selected',
    //     variant: 'destructive'
    //   })

    //   return
    // }

    try {
      const { data } = await ContractService.createContract(values)
      console.log('response', data)

      if (data.success) {
        mutate((key) => typeof key === 'string' && key.includes(ContractService.getContracts))
        toast({
          title: 'Contract created',
          variant: 'success'
        })

        setIsDialogOpen(false)

        return
      } else {
        toast({
          title: 'Action failed, please try again',
          variant: 'destructive'
        })
      }
    } catch (e) {
      console.log(e)
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <form
      id="update-booth-contract-form"
      className={cn('flex flex-col space-y-8 max-w-4xl mx-auto', className)}
      onSubmit={onSubmit}
    >
      <FormDescription className="text-md text-red-600">
        *Only the latest contract will be saved for each company. Please check if the desired contract
        exists before you create.
      </FormDescription>
      <Accordion
        type="multiple"
        className="w-full"
        defaultValue={[
          'contract-general-information',
          'contract-attentions',
          'contract-company-details',
          'contract-event-details',
          'contract-booth-details',
          'contract-fees-details'
        ]}
      >
        <AccordionItem value="contract-general-information">
          <AccordionTrigger disabled>General Information*</AccordionTrigger>
          <AccordionContent>
            <ContractGeneralInformation type="update" />
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="contract-attentions">
          <AccordionTrigger disabled>Attentions</AccordionTrigger>
          <AccordionContent>
            <ContractAttentions attention={contractFormData?.data.attention} />
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="contract-company-details">
          <AccordionTrigger disabled>Company Details</AccordionTrigger>
          <AccordionContent>
            <ContractCompanyDetails company={contractFormData?.data.company} />
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="contract-event-details">
          <AccordionTrigger disabled>Event Details</AccordionTrigger>
          <AccordionContent>
            <ContractEventDetails event={contractFormData?.data.event} />
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="contract-booth-details">
          <AccordionTrigger disabled>Booth Details</AccordionTrigger>
          <AccordionContent>
            <ContractBoothDetails booths={contractFormData?.data.booths} />
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="contract-fees-details">
          <AccordionTrigger disabled>Fees Details</AccordionTrigger>
          <AccordionContent>
            <ContractFeesDetails />
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </form>
  )
}

export default UpdateContractForm
