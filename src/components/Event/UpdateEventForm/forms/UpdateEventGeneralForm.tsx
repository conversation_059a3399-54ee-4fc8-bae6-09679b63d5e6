import { CalendarDatePicker } from '@/components/DatePicker/CalendarDatePicker'
import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import { DialogClose, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { toast } from '@/components/ui/use-toast'
import EventService from '@/network/services/event'
import { UpdateEventInput } from '@/types/CreateEvent'
import { Event } from '@/types/Event'
import isEqual from 'lodash.isequal'
import { DateTime } from 'luxon'
import { FC, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { mutate } from 'swr'

const UpdateEventGeneralForm: FC<{
  event: Event
  setIsDialogOpen: (value: boolean) => void
}> = ({ event, setIsDialogOpen }) => {
  const form = useForm<UpdateEventInput>({
    shouldUseNativeValidation: false,
    defaultValues: {
      event: {
        title: event.title,
        from: event.from,
        to: event.to,
        cut_off: event.cut_off,
        location: event.location
      }
    }
  })

  const fromDate = form.watch('event.from')
  const toDate = form.watch('event.to')

  const onSubmit = form.handleSubmit(async (values) => {
    console.log('submit values', values)

    // block if no changes
    if (
      isEqual(values, {
        event: {
          title: event.title,
          from: event.from,
          to: event.to,
          cut_off: event.cut_off,
          location: event.location
        }
      })
    ) {
      console.log('same')
      toast({
        title: 'No changes applied',
        variant: 'destructive'
      })

      return
    }

    try {
      const { data } = await EventService.updateEvent(event.id, values)
      console.log('response', data)

      if (data.success) {
        setIsDialogOpen(false)
        mutate((key) => typeof key === 'string' && key.includes(EventService.getEvent(event.id)))
        toast({
          title: 'Event General Updated',
          variant: 'success'
        })
      } else {
        toast({
          title: 'Action failed, please try again',
          variant: 'destructive'
        })
      }
    } catch (e) {
      console.log(e)
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
    // }
  })

  useEffect(() => {
    form.reset({
      event: {
        title: event.title,
        from: event.from,
        to: event.to,
        cut_off: event.cut_off,
        location: event.location
      }
    })
  }, [event])

  return (
    <DialogContent className="max-w-3xl">
      <DialogHeader>
        <DialogTitle className="flex items-center justify-between">
          Edit Event General
          <div className="flex space-x-2">
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button
              type="submit"
              form="update-info-form"
              disabled={form.formState.isSubmitting}
              onClick={() => {
                form.clearErrors()
              }}
            >
              {form.formState.isSubmitting && (
                <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
              )}
              Save
            </Button>
          </div>
        </DialogTitle>
      </DialogHeader>
      <Form {...form}>
        <form id="update-info-form" onSubmit={onSubmit} className="space-y-4">
          <FormField
            control={form.control}
            name="event.title"
            rules={{ required: 'Please provide an event title' }}
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Title</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="event.from"
            rules={{ required: 'Please enter the start date' }}
            render={({ field, fieldState }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Starting Date</FormLabel>
                <CalendarDatePicker
                  mode="single"
                  includeTime={true}
                  buttonLabel={
                    field.value
                      ? DateTime.fromISO(field.value as string).toFormat('yyyy-MM-dd HH:mm:ss')
                      : undefined
                  }
                  // selected={new Date((field.value as DateTime)?.toISO() ?? new Date())}
                  // onSelect={(e) => {
                  //   console.log(e)
                  //   field.onChange(DateTime.fromISO(e?.toISOString() ?? '').toISO())
                  // }}
                  value={field.value as string}
                  onChange={(v) => {
                    console.log(v)
                    field.onChange(DateTime.fromISO(v?.toISOString() ?? '').toISO())
                  }}
                  disabled={(date) => date < new Date() || date < new Date('1900-01-01')}
                />
                {fieldState.error || fieldState.invalid ? (
                  <FormMessage />
                ) : (
                  <FormDescription>The start date for the event</FormDescription>
                )}
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="event.to"
            rules={{ required: 'Please enter the end date' }}
            render={({ field, fieldState }) => (
              <FormItem className="flex flex-col">
                <FormLabel>End Date</FormLabel>
                <CalendarDatePicker
                  mode="single"
                  includeTime={true}
                  buttonLabel={
                    field.value
                      ? DateTime.fromISO(field.value as string).toFormat('yyyy-MM-dd HH:mm:ss')
                      : undefined
                  }
                  // selected={new Date((field.value as DateTime)?.toISO() ?? new Date())}
                  // onSelect={(e) => {
                  //   field.onChange(DateTime.fromISO(e?.toISOString() ?? '').toISO())
                  // }}
                  value={field.value as string}
                  onChange={(v) => {
                    field.onChange(DateTime.fromISO(v?.toISOString() ?? '').toISO())
                  }}
                  disabled={(date) => date < DateTime.fromISO(fromDate as string).toJSDate()}
                />
                {fieldState.error || fieldState.invalid ? (
                  <FormMessage />
                ) : (
                  <FormDescription>The end date for the event</FormDescription>
                )}
              </FormItem>
            )}
          />

          {/* need to be between from & to */}
          <FormField
            control={form.control}
            name="event.cut_off"
            rules={{ required: 'Please enter the cut off date' }}
            render={({ field, fieldState }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Final Registration Date</FormLabel>
                <CalendarDatePicker
                  mode="single"
                  includeTime={true}
                  buttonLabel={
                    field.value
                      ? DateTime.fromISO(field.value as string).toFormat('yyyy-MM-dd HH:mm:ss')
                      : undefined
                  }
                  // selected={new Date((field.value as DateTime)?.toISO() ?? new Date())}
                  // onSelect={(e) => {
                  //   field.onChange(DateTime.fromISO(e?.toISOString() ?? '').toISO())
                  // }}
                  value={field.value as string}
                  onChange={(v) => {
                    field.onChange(DateTime.fromISO(v?.toISOString() ?? '').toISO())
                  }}
                  disabled={(date) =>
                    fromDate == undefined ||
                    toDate == undefined ||
                    date >= DateTime.fromISO(fromDate as string).toJSDate()
                  }
                />
                {fieldState.error || fieldState.invalid ? (
                  <FormMessage />
                ) : (
                  <FormDescription>
                    The final date allowed to register for the event
                  </FormDescription>
                )}
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="event.location"
            rules={{ required: 'Please provide an event location' }}
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Venue</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </form>
      </Form>
    </DialogContent>
  )
}

export default UpdateEventGeneralForm
