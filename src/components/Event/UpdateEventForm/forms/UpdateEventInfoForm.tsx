import EditorComponent from '@/components/Editor/Editor'
import { Dropzone } from '@/components/Form/Dropzone'
import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import { DialogClose, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Label } from '@/components/ui/label'
import { toast } from '@/components/ui/use-toast'
import { bytesToSize, convertValuesToFormData } from '@/lib/utils'
import EventService from '@/network/services/event'
import { UpdateEventInfoInput } from '@/types/CreateEvent'
import { EventInfo } from '@/types/Event'
import { Image } from '@unpic/react'
import isEqual from 'lodash.isequal'
import { MoreHorizontal, Trash2Icon } from 'lucide-react'
import { title } from 'radash'
import { FC, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { mutate } from 'swr'

const UpdateEventInfoForm: FC<{
  eventInfo: EventInfo
  setIsDialogOpen: (value: boolean) => void
  eventId: number
}> = ({ eventInfo, setIsDialogOpen, eventId }) => {
  // const [imagePreview, setImagePreview] = useState<string>('')
  const form = useForm<UpdateEventInfoInput>({
    shouldUseNativeValidation: false,
    defaultValues: {
      image_file: undefined,
      // image_url: eventInfo?.image_url,
      banner_text: eventInfo?.banner_text,
      description: eventInfo?.description,
      gallery: eventInfo?.gallery
    }
  })

  const gallery = form.watch('gallery')

  const onSubmit = form.handleSubmit(async (values) => {
    if (values.gallery && (values.gallery?.length ?? 0) > 0) {
      for (const i in values.gallery) {
        const item = values.gallery[i]
        if (item.media_file) {
          if (!values.media) values.media = []
          values.media[i] = item.media_file
          delete values.gallery[i].media_file
        }
      }
    }

    // const tempValues = values
    const formData = new FormData()
    convertValuesToFormData(formData, values)

    let totalRequestSize = 0
    Array.from(formData.entries(), ([key, prop]) => {
      totalRequestSize += typeof prop === 'string' ? prop.length : prop.size
      return {
        [key]: {
          ContentLength: typeof prop === 'string' ? prop.length : prop.size,
          value: prop
        }
      }
    })
    totalRequestSize /= 1024 * 1024

    // server config size limit 128mb
    if (totalRequestSize > 128) {
      toast({
        title: `Request size too large (${totalRequestSize.toFixed(2)}mb > 128mb)`,
        variant: 'destructive'
      })
      return
    }

    // block if no changes
    if (
      isEqual(values, {
        image_file: undefined,
        image_url: eventInfo?.image_url,
        banner_text: eventInfo?.banner_text,
        description: eventInfo?.description,
        gallery: eventInfo?.gallery
      })
    ) {
      toast({
        title: 'No changes applied',
        variant: 'destructive'
      })

      return
    }

    // if (values.image_file) {
    // console.log('upload')
    // const formData = new FormData()
    // convertValuesToFormData(formData, { file: values.image_file })

    // try {
    //   // Upload image
    //   const { data: uploadResponse } = await FileService.uploadImage(formData)
    //   console.log('upload response', uploadResponse)

    //   if (uploadResponse.success) {
    //     console.log('upload url', uploadResponse.data.url)
    //     // Set url into form body
    //     tempValues.image_url = uploadResponse.data.url

    //     console.log('upload > set url', tempValues)
    //     console.log('apiBody', tempValues)

    //     // post api
    //     const { data: submitResponse } = await EventService.updateEventInfo(
    //       eventInfo.id,
    //       tempValues
    //     )
    //     console.log('response', submitResponse)

    //     if (submitResponse.success) {
    //       setIsDialogOpen(false)
    //       mutate(
    //         (key) =>
    //           typeof key === 'string' && key.startsWith(EventService.getEvent(eventInfo.event_id))
    //       )
    //       toast({
    //         title: 'Event Information Updated',
    //         variant: 'success'
    //       })
    //       return
    //     }
    //   }
    //   toast({
    //     title: 'Action failed, please try again',
    //     variant: 'destructive'
    //   })
    // } catch (error) {
    //   console.error(error)
    //   toast({
    //     title: 'Action failed, please try again',
    //     variant: 'destructive'
    //   })
    // }
    // } else {
    // console.log('no upload')
    try {
      let success: boolean
      if (!eventInfo) {
        // create
        const { data } = await EventService.createEventInfo(eventId, formData)
        success = data.success
      } else {
        // update
        const { data } = await EventService.updateEventInfo(eventInfo.id, formData)
        success = data.success
      }

      if (success) {
        setIsDialogOpen(false)
        mutate(
          (key) =>
            typeof key === 'string' && key.includes(EventService.getEvent(eventId))
        )
        toast({
          title: 'Event Information Updated',
          variant: 'success'
        })
      } else {
        toast({
          title: 'Action failed, please try again',
          variant: 'destructive'
        })
      }
    } catch (e) {
      console.log(e)
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
    // }
  })

  useEffect(() => {
    form.reset({
      image_file: undefined,
      // image_url: eventInfo?.image_url,
      banner_text: eventInfo?.banner_text,
      description: eventInfo?.description,
      gallery: eventInfo?.gallery
    })
  }, [eventInfo])

  return (
    <DialogContent className="max-w-3xl">
      <DialogHeader>
        <DialogTitle className="flex items-center justify-between">
          Edit Event Information
          <div className="flex space-x-2">
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button
              type="submit"
              form="update-info-form"
              disabled={form.formState.isSubmitting}
              onClick={() => {
                form.clearErrors()
              }}
            >
              {form.formState.isSubmitting && (
                <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
              )}
              Save
            </Button>
          </div>
        </DialogTitle>
      </DialogHeader>
      <Form {...form}>
        <form id="update-info-form" onSubmit={onSubmit} className="space-y-4">
          {/* <FormField
            control={form.control}
            name="image_file"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Primary Banner</FormLabel>
                {imagePreview != '' && (
                  <img className="w-[320px] py-4" src={imagePreview} alt="event-image" />
                )}
                <FormControl>
                  <Input
                    type="file"
                    onChange={(e) => {
                      if (e.target.files) {
                        field.onChange(e.target.files[0])
                        setImagePreview(URL.createObjectURL(e.target.files[0]))
                      }
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          /> */}
          <FormField
            control={form.control}
            name="gallery"
            // rules={{ required: 'Please insert the image' }}
            render={({ field }) => (
              <>
                <FormItem>
                  {/* <FormDescription>Replace the thumbnail</FormDescription> */}
                  <FormControl>
                    <Dropzone
                      accept={{ 'image/*': ['.jpeg', '.jpg', '.png'], 'video/*': ['.mp4'] }}
                      description="Drop your videos or images here, or click to browse"
                      multiple={true}
                      onDrop={(acceptedFiles) => {
                        console.log('ondrop')
                        const galleryTmp = gallery ? [...gallery] : []

                        // remove existing already uploaded file
                        const acceptedFiles2 = acceptedFiles.filter((acceptedFile) =>
                          galleryTmp?.every((file) => file.name != acceptedFile.name)
                        )
                        for (const index in acceptedFiles2) {
                          const file = acceptedFiles2[index]
                          const findIndex = gallery?.findIndex(
                            (f) => f.media_file?.path == file.path
                          )

                          if ((findIndex ?? -1) == -1) {
                            const preview = Object.assign(file, {
                              preview: URL.createObjectURL(file)
                            })

                            galleryTmp.push({
                              name: preview.name,
                              type: preview.type.split('/')[0] as 'video' | 'image',
                              media_file: preview
                            })
                          }
                        }

                        console.log(galleryTmp)
                        form.setValue('gallery', galleryTmp, {
                          shouldValidate: true
                        })
                      }}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>

                {(gallery ?? []).length > 0 && (
                  <div className="flex flex-col space-y-4 mt-2">
                    {gallery?.map((file, index) => {
                      return (
                        <div
                          key={index}
                          className="grid grid-cols-[150px_1fr_36px] space-x-2 items-center"
                        >
                          {file.type.startsWith('image') && (
                            <Image
                              key={index}
                              src={file.url ?? file.media_file?.preview ?? ''}
                              height={150}
                              width={150}
                              objectFit="contain"
                              className="rounded-md"
                            />
                          )}
                          {file.type.startsWith('video') && (
                            <video
                              // controls
                              key={index}
                              src={file.url ?? file.media_file?.preview ?? ''}
                              height={150}
                              width={150}
                              className="rounded-md"
                            />
                          )}
                          <div className="flex flex-col">
                            {file.url ? (
                              <Label className="text-xs font-normal">
                                {title(file.type)} uploaded
                              </Label>
                            ) : (
                              <>
                                <Label className="text-xs font-normal">
                                  {file.media_file?.path}
                                </Label>
                                <Label className="text-xs font-normal text-gray-500">
                                  {file.media_file?.size && bytesToSize(file.media_file!.size!)}
                                </Label>
                              </>
                            )}
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.stopPropagation()
                                  const galleryTmp = [...gallery]

                                  galleryTmp?.splice(index, 1)
                                  console.log(galleryTmp)
                                  form.setValue('gallery', galleryTmp, {
                                    shouldValidate: true
                                  })
                                }}
                                className="space-x-2"
                              >
                                <Trash2Icon size="16" />
                                <span>Delete</span>
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      )
                    })}
                  </div>
                )}
              </>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            rules={{ required: 'Please provide an event description' }}
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Description</FormLabel>
                <FormControl>
                  <EditorComponent content={field.value} onChange={field.onChange} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="banner_text"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Secondary Banner Text</FormLabel>
                <FormControl>
                  <EditorComponent content={field.value} onChange={field.onChange} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </form>
      </Form>
    </DialogContent>
  )
}

export default UpdateEventInfoForm
