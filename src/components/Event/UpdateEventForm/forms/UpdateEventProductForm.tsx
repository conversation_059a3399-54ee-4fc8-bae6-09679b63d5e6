import { DataTable } from '@/components/Table/DataTable'
import IndeterminateCheckbox from '@/components/Table/IndeterminateCheckbox'
import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import { DialogClose, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import {
  Form,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { toast } from '@/components/ui/use-toast'
import { IDataResponse } from '@/network/request'
import EventService from '@/network/services/event'
import { EventProduct } from '@/types/Event'
import { CoreProduct } from '@/types/Product'
import { ColumnDef, Row } from '@tanstack/react-table'
import isEqual from 'lodash.isequal'
import { PlusCircleIcon } from 'lucide-react'
import { FC } from 'react'
import { useForm, useFormContext } from 'react-hook-form'
import { useNavigate } from 'react-router-dom'
import { mutate } from 'swr'

interface UpdateEventProductFormProps {
  initialValues: EventProduct[]
  setIsDialogOpen: (value: boolean) => void
}

const UpdateEventProductForm: FC<UpdateEventProductFormProps> = ({
  initialValues,
  setIsDialogOpen
}) => {
  const form = useForm<{ products: number[] }>({
    shouldUseNativeValidation: false,
    defaultValues: {
      products:
        initialValues?.map((eventProduct) => {
          return eventProduct.product_id
        }) ?? []
    }
  })

  const onSubmit = form.handleSubmit(async (values) => {
    console.log(
      'submit values',
      values,
      initialValues?.map((eventProduct) => {
        return eventProduct.product_id
      })
    )

    // block if no changes
    if (
      isEqual(
        values.products.sort(),
        initialValues
          ?.map((eventExplore) => {
            return eventExplore.id
          })
          .sort() ?? []
      )
    ) {
      console.log('same')
      toast({
        title: 'No changes applied',
        variant: 'destructive'
      })

      return
    }

    try {
      const { data } = await EventService.updateVendorEventProducts(values.products)
      console.log('response', data)

      if (data.success) {
        setIsDialogOpen(false)
        mutate(
          (key) => typeof key === 'string' && key.includes(EventService.getVendorEventProducts)
        )
        toast({
          title: 'Event product updated',
          variant: 'success'
        })

        return
      } else {
        toast({
          title: 'Action failed, please try again',
          variant: 'destructive'
        })
      }
    } catch (e) {
      console.log(e)
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <DialogContent className="max-h-[80vh] max-w-4xl overflow-y-auto">
      <DialogHeader>
        <DialogTitle className="flex items-center justify-between">
          Edit Your Event Products
          <div className="flex space-x-2">
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button
              type="submit"
              form="update-explore-form"
              disabled={form.formState.isSubmitting}
              onClick={() => {
                form.clearErrors()
              }}
            >
              {form.formState.isSubmitting && (
                <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
              )}
              Save
            </Button>
          </div>
        </DialogTitle>
      </DialogHeader>

      <Form {...form}>
        <form id="update-explore-form" className="grid grid-cols-1 gap-4" onSubmit={onSubmit}>
          <FormField
            control={form.control}
            name="products"
            rules={{ required: 'Please select at least one' }}
            render={() => {
              return (
                <FormItem className="col-span-2 flex flex-col">
                  <FormLabel>Event Products</FormLabel>
                  <FormDescription>Products elligible to be added for this event</FormDescription>
                  <FormMessage />
                </FormItem>
              )
            }}
          />

          <EventProductList
            defaultCards={initialValues.map((eventProduct) => eventProduct.product_id) ?? []}
          />
        </form>
      </Form>
    </DialogContent>
  )
}

const EventProductList: FC<{ defaultCards: number[] }> = ({ defaultCards }) => {
  const nav = useNavigate()

  return (
    <div>
      {/* redirect to product page to create product */}
      <Button variant="outline" type="button" onClick={() => nav('/products')}>
        <PlusCircleIcon size="16" className="mr-2" />
        Add a new product
      </Button>

      {/* Selectable table for all options */}
      <EventProductOptionsTable defaultCards={defaultCards} />
    </div>
  )
}

const EventProductOptionsTable: FC<{ defaultCards: number[] }> = ({ defaultCards }) => {
  const form = useFormContext<{ products: number[] }>()

  const columns: ColumnDef<CoreProduct>[] = [
    {
      id: 'select',
      header: ({ table }) => (
        <IndeterminateCheckbox
          {...{
            checked: table.getIsAllRowsSelected(),
            indeterminate: table.getIsSomeRowsSelected(),
            onChange: table.getToggleAllPageRowsSelectedHandler()
          }}
        />
      ),
      cell: ({ row }) => (
        <div className="px-1">
          <IndeterminateCheckbox
            {...{
              checked: row.getIsSelected(),
              disabled: !row.getCanSelect(),
              indeterminate: row.getIsSomeSelected(),
              onChange: row.getToggleSelectedHandler()
            }}
          />
        </div>
      )
    },
    {
      accessorKey: 'id',
      header: 'Id'
    },
    {
      accessorKey: 'title',
      header: 'Title'
    },
    {
      accessorKey: 'thumbnail',
      header: 'Image',
      cell: (props) => {
        return <img className="w-[80px]" src={props.getValue<string>()} />
      }
    }
    // TODO: dialog to edit explore option
    // columnHelper.display({
    //   id: "actions",
    //   cell: (props) => <RowActions row={props.row} />,
    // }),
  ]

  const toRow = (data: IDataResponse<CoreProduct> | undefined): CoreProduct[] => {
    if (data?.data && data?.data?.length > 0) {
      return data?.data?.map((element) => {
        return {
          ...element,
          key: element.id
        }
      })
    }

    return []
  }

  const toPaginate = (data: IDataResponse<CoreProduct> | undefined) => {
    return {
      total: data?.meta?.total ?? 0,
      lastPage: data?.meta?.last_page ?? 0
    }
  }

  return (
    <DataTable<CoreProduct, unknown, IDataResponse<CoreProduct>>
      columns={columns}
      swrService={EventService.getVendorEventProductsOptions}
      toRow={toRow}
      toPaginate={toPaginate}
      setSelectedRows={(selectedRows) => form.setValue('products', selectedRows as number[])}
      initialSelected={defaultCards}
      onRowClick={(row: Row<CoreProduct>) => {
        row.toggleSelected()
      }}
    />
  )
}

export default UpdateEventProductForm
