import { DataTable } from '@/components/Table/DataTable'
import IndeterminateCheckbox from '@/components/Table/IndeterminateCheckbox'
import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import {
  Form,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { toast } from '@/components/ui/use-toast'
import { IDataResponse } from '@/network/request'
import EventService from '@/network/services/event'
import { UpdateEventExploreInput } from '@/types/CreateEvent'
import { Event } from '@/types/Event'
import { ExploreCard } from '@/types/ExploreCard'
import { ColumnDef, Row, createColumnHelper } from '@tanstack/react-table'
import isEqual from 'lodash.isequal'
import { EditIcon, MoreHorizontal, PlusCircleIcon } from 'lucide-react'
import { FC, useEffect, useState } from 'react'
import { useForm, useFormContext } from 'react-hook-form'
import { mutate } from 'swr'
import CreateExploreForm from '../../CreateEventForm/CreateExploreForm'

interface UpdateExploreCardFormProps {
  event: Event
  setIsDialogOpen: (value: boolean) => void
}

const UpdateExploreCardForm: FC<UpdateExploreCardFormProps> = ({ event, setIsDialogOpen }) => {
  const defaultCards =
    event.event_explores.map((eventExplore) => eventExplore.explore_card_id) ?? []
  const form = useForm<UpdateEventExploreInput>({
    shouldUseNativeValidation: false,
    defaultValues: {
      cards:
        event.event_explores?.map((eventExplore) => {
          return eventExplore.explore_card_id
        }) ?? []
    }
  })

  const onSubmit = form.handleSubmit(async (values) => {
    console.log(
      'submit values',
      values,
      event.event_explores?.map((eventExplore) => {
        return eventExplore.explore_card_id
      })
    )

    // block if no changes
    if (
      isEqual(
        values.cards.sort(),
        event.event_explores
          ?.map((eventExplore) => {
            return eventExplore.explore_card_id
          })
          .sort() ?? []
      )
    ) {
      console.log('same')
      toast({
        title: 'No changes applied',
        variant: 'destructive'
      })

      return
    }

    try {
      const { data } = await EventService.updateEventExplore(event.id, values)
      console.log('response', data)

      if (data.success) {
        setIsDialogOpen(false)
        mutate((key) => typeof key === 'string' && key.includes(EventService.getEvents))
        toast({
          title: 'Explore options updated',
          variant: 'success'
        })

        return
      } else {
        toast({
          title: 'Action failed, please try again',
          variant: 'destructive'
        })
      }
    } catch (e) {
      console.log(e)
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <DialogContent className="max-w-4xl">
      <DialogHeader>
        <DialogTitle className="flex items-center justify-between">
          Edit Explore Options
          <div className="flex space-x-2">
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button
              type="submit"
              form="update-explore-form"
              disabled={form.formState.isSubmitting}
              onClick={() => {
                form.clearErrors()
              }}
            >
              {form.formState.isSubmitting && (
                <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
              )}
              Save
            </Button>
          </div>
        </DialogTitle>
      </DialogHeader>

      <Form {...form}>
        <form id="update-explore-form" onSubmit={onSubmit}>
          <FormField
            control={form.control}
            name="cards"
            rules={{ required: 'Please select at least one' }}
            render={() => {
              return (
                <FormItem className="col-span-2 flex flex-col">
                  <FormLabel>Explore options</FormLabel>
                  <FormDescription>Areas near the fair that can be explored</FormDescription>
                  <FormMessage />
                </FormItem>
              )
            }}
          />

          <ExploreOptionList defaultCards={defaultCards} />
        </form>
      </Form>
    </DialogContent>
  )
}

const ExploreOptionList: FC<{ defaultCards: number[] }> = ({ defaultCards }) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  return (
    <div className="grid gap-4">
      {/* dialog to create new explore options */}
      <Dialog
        open={isDialogOpen}
        onOpenChange={(isOpen) => {
          setIsDialogOpen(isOpen)
        }}
      >
        <DialogTrigger asChild>
          <Button variant="outline" type="button">
            <PlusCircleIcon size="16" className="mr-2" />
            Add a new option
          </Button>
        </DialogTrigger>

        {/* Explore option form */}
        <CreateExploreForm
          setIsDialogOpen={setIsDialogOpen}
          initialValues={{
            type: undefined,
            title: '',
            description: '',
            image: undefined
          }}
        />
      </Dialog>

      {/* Selectable table for all options */}
      <ExploreOptionTable defaultCards={defaultCards} />
    </div>
  )
}

const ExploreOptionTable: FC<{ defaultCards: number[] }> = ({ defaultCards }) => {
  const form = useFormContext<UpdateEventExploreInput>()

  useEffect(() => {
    console.log(defaultCards)
  }, [defaultCards])
  const columnHelper = createColumnHelper<ExploreCard>()
  const columns: ColumnDef<ExploreCard>[] = [
    {
      id: 'select',
      header: ({ table }) => (
        <IndeterminateCheckbox
          {...{
            checked: table.getIsAllRowsSelected(),
            indeterminate: table.getIsSomeRowsSelected(),
            onChange: table.getToggleAllPageRowsSelectedHandler()
          }}
        />
      ),
      cell: ({ row }) => (
        <div className="px-1">
          <IndeterminateCheckbox
            {...{
              checked: row.getIsSelected(),
              disabled: !row.getCanSelect(),
              indeterminate: row.getIsSomeSelected(),
              onChange: row.getToggleSelectedHandler()
            }}
          />
        </div>
      )
    },
    {
      accessorKey: 'type',
      header: 'Type'
    },
    {
      accessorKey: 'title',
      header: 'Title'
    },
    {
      accessorKey: 'description',
      header: 'Description'
    },
    {
      accessorKey: 'image_url',
      header: 'Image',
      cell: (props) => {
        return <img className="w-[80px]" src={props.getValue<string>()} />
      }
    },
    columnHelper.display({
      id: 'actions',
      cell: (props) => <RowActions row={props.row} />
    })
  ]

  const toRow = (data: IDataResponse<ExploreCard> | undefined): ExploreCard[] => {
    if (data?.data && data?.data?.length > 0) {
      return data?.data?.map((element) => {
        return {
          ...element,
          key: element.id
        }
      })
    }

    return []
  }

  const toPaginate = (data: IDataResponse<ExploreCard> | undefined) => {
    return {
      total: data?.meta?.total ?? 0,
      lastPage: data?.meta?.last_page ?? 0
    }
  }

  return (
    <DataTable<ExploreCard, unknown, IDataResponse<ExploreCard>>
      columns={columns}
      swrService={EventService.getEventExplores}
      toRow={toRow}
      toPaginate={toPaginate}
      setSelectedRows={(selectedRows) => form.setValue('cards', selectedRows as number[])}
      initialSelected={defaultCards}
      onRowClick={(row: Row<ExploreCard>) => {
        row.toggleSelected()
      }}
    />
  )
}

const RowActions: FC<{ row: Row<ExploreCard> }> = ({ row }) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  return (
    <div className="flex w-full justify-end" onClick={(e) => e.stopPropagation()}>
      <Dialog
        open={isDialogOpen}
        onOpenChange={(isOpen) => {
          setIsDialogOpen(isOpen)
        }}
      >
        {/* edit visit us form */}
        <CreateExploreForm
          setIsDialogOpen={setIsDialogOpen}
          initialValues={{ ...row.original, image: undefined }}
          isEdit
        />

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DialogTrigger asChild>
              <DropdownMenuItem className="space-x-2">
                <EditIcon size="16" />
                <span>Edit</span>
              </DropdownMenuItem>
            </DialogTrigger>
          </DropdownMenuContent>
        </DropdownMenu>
      </Dialog>
    </div>
  )
}

export default UpdateExploreCardForm
