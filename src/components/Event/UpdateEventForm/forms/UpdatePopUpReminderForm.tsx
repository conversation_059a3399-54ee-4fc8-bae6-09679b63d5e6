import TextEditorComponent from '@/components/Editor/TextEditor'
import { Dropzone } from '@/components/Form/Dropzone'
import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import { DialogClose, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import { toast } from '@/components/ui/use-toast'
import { bytesToSize } from '@/lib/utils'
import EventService from '@/network/services/event'
import FileService from '@/network/services/file'
import { UpdateEventInput } from '@/types/CreateEvent'
import { Event } from '@/types/Event'
import { Image } from '@unpic/react'
import isEqual from 'lodash.isequal'
import { MoreHorizontal, Trash2Icon } from 'lucide-react'
import { FC, useEffect } from 'react'
import { FileWithPath } from 'react-dropzone'
import { useForm } from 'react-hook-form'
import { mutate } from 'swr'

interface UpdateExploreFormProps {
  initialValues: Event
  isDialogOpen: boolean
  setIsDialogOpen: (value: boolean) => void
}

const UpdatePopUpReminderForm: FC<UpdateExploreFormProps> = ({
  initialValues,
  isDialogOpen,
  setIsDialogOpen
}) => {
  const form = useForm<UpdateEventInput>({
    shouldUseNativeValidation: false,
    defaultValues: {
      event: {
        pop_up: {
          description: initialValues.pop_up_description ?? '',
          media_url: initialValues.pop_up_media,
          media: undefined,
          show: initialValues.show_pop_up
        }
      }
    }
  })

  useEffect(() => {
    form.reset({
      event: {
        pop_up: {
          description: initialValues.pop_up_description ?? '',
          media_url: initialValues.pop_up_media,
          media: undefined,
          show: initialValues.show_pop_up
        }
      }
    })
  }, [isDialogOpen])

  const mediaFile = form.watch('event.pop_up.media')
  const mediaFileWithPath = mediaFile
    ? (mediaFile as unknown as FileWithPath & { preview: string })
    : undefined

  const onSubmit = form.handleSubmit(async (values) => {
    // block if no changes
    if (
      isEqual(values, {
        event: {
          pop_up: {
            description: initialValues.pop_up_description,
            media_url: initialValues.pop_up_media,
            media: undefined,
            show: initialValues.show_pop_up ? 'true' : 'false'
          }
        }
      })
    ) {
      toast({
        title: 'No changes applied',
        variant: 'destructive'
      })

      return
    }

    try {
      if (values.event.pop_up.media) {
        const { data: uploadResponse } = await FileService.upload([values.event.pop_up.media])
        values.event.pop_up.media_url = uploadResponse.uploads[0].url
        delete values.event.pop_up.media
      }

      console.log('submit values', values)

      const { data } = await EventService.updateEvent(initialValues.id, values)

      if (data.success) {
        setIsDialogOpen(false)
        mutate((key) => typeof key === 'string' && key.includes(EventService.getEvents))
        toast({
          title: 'Explore description updated',
          variant: 'success'
        })
      } else {
        toast({
          title: 'Action failed, please try again',
          variant: 'destructive'
        })
      }
    } catch (e) {
      console.log(e)
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <DialogContent className="max-w-3xl">
      <DialogHeader>
        <DialogTitle className="flex items-center justify-between">
          Edit Pop-up Reminder
          <div className="flex space-x-2">
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button
              type="submit"
              form="update-pop-up-reminder-form"
              disabled={form.formState.isSubmitting}
              onClick={() => {
                form.clearErrors()
              }}
            >
              {form.formState.isSubmitting && (
                <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
              )}
              Save
            </Button>
          </div>
        </DialogTitle>
      </DialogHeader>

      <Form {...form}>
        <form
          id="update-pop-up-reminder-form"
          onSubmit={onSubmit}
          className="grid grid-cols-1 gap-4"
        >
          <FormField
            control={form.control}
            name="event.pop_up.description"
            rules={{ required: 'This is required' }}
            render={({ field }) => (
              <FormItem className="col-span-2 flex flex-col">
                <FormLabel>Description</FormLabel>
                <FormControl>
                  <TextEditorComponent content={field.value} onChange={field.onChange} />
                </FormControl>
                <FormDescription>Description in pop-up</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="event.pop_up.media"
            rules={{ required: initialValues.pop_up_media ? false : 'This is required' }}
            render={({ field }) => (
              <>
                <FormItem className="col-span-2 flex flex-col">
                  <FormLabel>Media</FormLabel>
                  <FormControl>
                    <FormControl>
                      <Dropzone
                        multiple={false}
                        onDrop={(acceptedFiles) => {
                          if (mediaFileWithPath?.path != acceptedFiles[0].path) {
                            const preview = Object.assign(acceptedFiles[0], {
                              preview: URL.createObjectURL(acceptedFiles[0])
                            })

                            form.setValue('event.pop_up.media', preview as File, {
                              shouldValidate: true
                            })
                          }
                        }}
                        {...field}
                      />
                    </FormControl>
                  </FormControl>
                  <FormDescription>Image show in pop-up</FormDescription>
                  <FormMessage />
                </FormItem>
                <div className="flex flex-col space-y-4 mt-2">
                  {mediaFileWithPath ? (
                    <div className="grid grid-cols-[150px_1fr_36px] space-x-2 items-center">
                      {mediaFileWithPath.type.startsWith('image') && (
                        <Image
                          key={mediaFileWithPath.path}
                          src={mediaFileWithPath.preview ?? ''}
                          height={150}
                          width={150}
                          objectFit="contain"
                          className="rounded-md"
                        />
                      )}
                      {mediaFileWithPath.type.startsWith('video') && (
                        <video
                          // controls
                          key={mediaFileWithPath.path}
                          src={mediaFileWithPath.preview ?? ''}
                          height={150}
                          width={150}
                          className="rounded-md"
                        />
                      )}
                      <div className="flex flex-col">
                        <Label className="text-xs font-normal">{mediaFileWithPath.path}</Label>
                        <Label className="text-xs font-normal text-gray-500">
                          {bytesToSize(mediaFileWithPath.size)}
                        </Label>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation()

                              form.setValue('event.pop_up.media', undefined, {
                                shouldValidate: true
                              })
                            }}
                            className="space-x-2"
                          >
                            <Trash2Icon size="16" />
                            <span>Delete</span>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  ) : initialValues.pop_up_media ? (
                    <div className="grid grid-cols-[150px_1fr] space-x-2 items-center">
                      <Image
                        src={initialValues.pop_up_media ?? ''}
                        height={150}
                        width={150}
                        objectFit="contain"
                        className="rounded-md"
                      />
                      <Label className="text-xs font-normal">Uploaded</Label>
                    </div>
                  ) : (
                    <></>
                  )}
                </div>
              </>
            )}
          />
          <FormField
            control={form.control}
            name="event.pop_up.show"
            // rules={{ required: 'This is required' }}
            render={({ field }) => (
              <FormItem className="col-span-2 flex flex-col">
                <FormLabel>Show</FormLabel>
                <FormControl>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value ? 'true' : 'false'}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose an option" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {['true', 'false'].map((value) => {
                        return (
                          <SelectItem key={value} value={value}>
                            {value === 'true' ? 'Yes' : 'No'}
                          </SelectItem>
                        )
                      })}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormDescription>To show pop-up</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </form>
      </Form>
    </DialogContent>
  )
}

export default UpdatePopUpReminderForm
