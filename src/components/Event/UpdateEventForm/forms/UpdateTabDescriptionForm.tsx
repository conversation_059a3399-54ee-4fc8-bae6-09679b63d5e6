import EditorComponent from '@/components/Editor/Editor'
import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import { DialogClose, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { toast } from '@/components/ui/use-toast'
import EventService from '@/network/services/event'
import { UpdateEventInput } from '@/types/CreateEvent'
import { Event } from '@/types/Event'
import isEqual from 'lodash.isequal'
import { FC } from 'react'
import { useForm } from 'react-hook-form'
import { mutate } from 'swr'

interface UpdateExploreFormProps {
  initialValues: Event
  type: 'deal' | 'layout' | 'visit' | 'explore'
  setIsDialogOpen: (value: boolean) => void
}

const UpdateTabDescriptionForm: FC<UpdateExploreFormProps> = ({
  initialValues,
  type,
  setIsDialogOpen
}) => {
  const form = useForm<UpdateEventInput>({
    shouldUseNativeValidation: false,
    defaultValues: {
      event: {
        deal_description: initialValues.deal_description,
        layout_description: initialValues.layout_description,
        visit_description: initialValues.visit_description,
        explore_description: initialValues.explore_description
      }
    }
  })

  const onSubmit = form.handleSubmit(async (values) => {
    console.log('submit values', values)

    // block if no changes
    if (
      isEqual(values, {
        deal_description: initialValues.deal_description,
        layout_description: initialValues.layout_description,
        visit_description: initialValues.visit_description,
        explore_description: initialValues.explore_description
      })
    ) {
      console.log('same')
      toast({
        title: 'No changes applied',
        variant: 'destructive'
      })

      return
    }

    try {
      const { data } = await EventService.updateEvent(initialValues.id, values)
      console.log('response', data)

      if (data.success) {
        setIsDialogOpen(false)
        mutate((key) => typeof key === 'string' && key.includes(EventService.getEvents))
        toast({
          title: 'Explore description updated',
          variant: 'success'
        })
      } else {
        toast({
          title: 'Action failed, please try again',
          variant: 'destructive'
        })
      }
    } catch (e) {
      console.log(e)
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <DialogContent className="max-w-3xl">
      <DialogHeader>
        <DialogTitle className="flex items-center justify-between">
          Edit Description
          <div className="flex space-x-2">
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button
              type="submit"
              form="update-description-form"
              disabled={form.formState.isSubmitting}
              onClick={() => {
                form.clearErrors()
              }}
            >
              {form.formState.isSubmitting && (
                <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
              )}
              Save
            </Button>
          </div>
        </DialogTitle>
      </DialogHeader>

      <Form {...form}>
        <form id="update-description-form" onSubmit={onSubmit} className="grid grid-cols-1 gap-4">
          {type == 'deal' && (
            <FormField
              control={form.control}
              name="event.deal_description"
              rules={{ required: 'This is required' }}
              render={({ field }) => (
                <FormItem className="col-span-2 flex flex-col">
                  <FormLabel>Deals Description</FormLabel>
                  <FormControl>
                    <EditorComponent content={field.value} onChange={field.onChange} />
                  </FormControl>

                  <FormMessage />
                </FormItem>
              )}
            />
          )}
          {type == 'layout' && (
            <FormField
              control={form.control}
              name="event.layout_description"
              rules={{ required: 'This is required' }}
              render={({ field }) => (
                <FormItem className="col-span-2 flex flex-col">
                  <FormLabel>Layout Map Description</FormLabel>
                  <FormControl>
                    <EditorComponent content={field.value} onChange={field.onChange} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
          {type == 'visit' && (
            <FormField
              control={form.control}
              name="event.visit_description"
              rules={{ required: 'This is required' }}
              render={({ field }) => (
                <FormItem className="col-span-2 flex flex-col">
                  <FormLabel>Visit Us Description</FormLabel>
                  <FormControl>
                    <EditorComponent content={field.value} onChange={field.onChange} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
          {type == 'explore' && (
            <FormField
              control={form.control}
              name="event.explore_description"
              rules={{ required: 'This is required' }}
              render={({ field }) => (
                <FormItem className="col-span-2 flex flex-col">
                  <FormLabel>Explore Description</FormLabel>
                  <FormControl>
                    <EditorComponent content={field.value} onChange={field.onChange} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
        </form>
      </Form>
    </DialogContent>
  )
}

export default UpdateTabDescriptionForm
