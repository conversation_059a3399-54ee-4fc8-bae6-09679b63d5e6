import EditorComponent from '@/components/Editor/Editor'
import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import { DialogClose, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { toast } from '@/components/ui/use-toast'
import EventService from '@/network/services/event'
import { UpdateEventInput } from '@/types/CreateEvent'
import { Event } from '@/types/Event'
import isEqual from 'lodash.isequal'
import { Trash2Icon } from 'lucide-react'
import React, { FC, useEffect } from 'react'
import { useFieldArray, useForm } from 'react-hook-form'
import { mutate } from 'swr'

interface UpdateExploreFormProps {
  initialValues: Event
  isDialogOpen: boolean
  setIsDialogOpen: (value: boolean) => void
}

const UpdateUserAgreementsForm: FC<UpdateExploreFormProps> = ({
  initialValues,
  isDialogOpen,
  setIsDialogOpen
}) => {
  const form = useForm<UpdateEventInput>({
    shouldUseNativeValidation: false,
    defaultValues: {
      event: {
        metadata: initialValues.metadata ?? { user_agreements: [''] }
      }
    }
  })

  const {
    fields: userAgreements,
    append: appendAgreement,
    remove: removeAgreement
  } = useFieldArray({ name: 'event.metadata.user_agreements', control: form.control })

  useEffect(() => {
    form.reset({
      event: {
        metadata: initialValues.metadata ?? { user_agreements: [''] }
      }
    })
  }, [isDialogOpen])

  const onSubmit = form.handleSubmit(async (values) => {
    // block if no changes
    if (
      isEqual(values, {
        event: {
          metadata: initialValues.metadata
        }
      })
    ) {
      toast({
        title: 'No changes applied',
        variant: 'destructive'
      })

      return
    }

    try {
      console.log('submit values', values)

      const { data } = await EventService.updateEvent(initialValues.id, values)

      if (data.success) {
        setIsDialogOpen(false)
        mutate((key) => typeof key === 'string' && key.includes(EventService.getEvents))
        toast({
          title: 'Event purchase user agreements updated',
          variant: 'success'
        })
      } else {
        toast({
          title: 'Action failed, please try again',
          variant: 'destructive'
        })
      }
    } catch (e) {
      console.log(e)
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <DialogContent className="max-w-3xl">
      <DialogHeader>
        <DialogTitle className="flex items-center justify-between">
          Edit User Agreements
          <div className="flex space-x-2">
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button
              type="submit"
              form="update-pop-up-reminder-form"
              disabled={form.formState.isSubmitting}
              onClick={() => {
                form.clearErrors()
              }}
            >
              {form.formState.isSubmitting && (
                <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
              )}
              Save
            </Button>
          </div>
        </DialogTitle>
      </DialogHeader>

      <Form {...form}>
        <form
          id="update-pop-up-reminder-form"
          onSubmit={onSubmit}
          className="grid grid-cols-1 gap-4"
        >
          {userAgreements.map((item, i) => {
            return (
              <React.Fragment key={item.id}>
                <FormField
                  control={form.control}
                  name={`event.metadata.user_agreements.${i}`}
                  rules={{ required: 'This is required' }}
                  render={({ field }) => {
                    return (
                      <FormItem className="flex flex-col col-span-2">
                        <FormLabel className="flex gap-2">
                          <span>User Agreement {i + 1}</span>
                          {userAgreements.length > 1 && (
                            <Trash2Icon size={16} color="red" onClick={() => removeAgreement(i)} />
                          )}
                        </FormLabel>
                        <EditorComponent content={field.value} onChange={field.onChange} />
                        <FormMessage />
                      </FormItem>
                    )
                  }}
                />
              </React.Fragment>
            )
          })}
          <FormItem className="flex flex-col col-span-2">
            <Button type="button" onClick={() => appendAgreement('')}>
              Add User Agreement
            </Button>
          </FormItem>
        </form>
      </Form>
    </DialogContent>
  )
}

export default UpdateUserAgreementsForm
