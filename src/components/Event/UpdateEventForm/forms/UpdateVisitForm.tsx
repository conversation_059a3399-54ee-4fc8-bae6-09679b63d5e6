import { DataTable } from '@/components/Table/DataTable'
import IndeterminateCheckbox from '@/components/Table/IndeterminateCheckbox'
import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  <PERSON>alogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import {
  Form,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { toast } from '@/components/ui/use-toast'
import { IDataResponse } from '@/network/request'
import EventService from '@/network/services/event'
import { Event } from '@/types/Event'
import { VisitUs } from '@/types/VisitUs'
import { ColumnDef, Row, Table, createColumnHelper } from '@tanstack/react-table'
import { EditIcon, MoreHorizontal, PlusCircleIcon } from 'lucide-react'
import { FC, useState } from 'react'
import { useForm, useFormContext } from 'react-hook-form'
import { mutate } from 'swr'
import CreateVisitUsForm from '../../CreateEventForm/CreateVisitUsForm'

const UpdateVisitForm: FC<{
  event: Event
  setIsDialogOpen: (value: boolean) => void
}> = ({ event, setIsDialogOpen }) => {
  const event_id = event?.id
  const visit_id = event?.event_visit?.visit_id

  const form = useForm<{ visit_us: number }>({
    shouldUseNativeValidation: false,
    defaultValues: {
      visit_us: visit_id
    }
  })

  const onSubmit = form.handleSubmit(async (values) => {
    console.log(values)

    // block if no changes
    if (values.visit_us === visit_id) {
      toast({
        title: 'No changes applied',
        variant: 'destructive'
      })
      return
    }

    try {
      const { data } = await EventService.updateEventVisit(event_id, values)
      console.log('response', data)

      if (data.success) {
        setIsDialogOpen(false)
        mutate((key) => typeof key === 'string' && key.includes(EventService.getEvents))

        toast({
          title: 'Venue Information Updated',
          variant: 'success'
        })
      } else {
        toast({
          title: 'Action failed, please try again',
          variant: 'destructive'
        })
      }
    } catch (e) {
      console.log(e)
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <DialogContent className="max-w-4xl">
      <DialogHeader>
        <DialogTitle className="flex justify-between items-center">
          Edit Event Visit Us
          <div className="flex space-x-2">
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button
              type="submit"
              form="update-visit-form"
              disabled={form.formState.isSubmitting}
              onClick={() => {
                form.clearErrors()
              }}
            >
              {form.formState.isSubmitting && (
                <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
              )}
              Save
            </Button>
          </div>
        </DialogTitle>
      </DialogHeader>
      <Form {...form}>
        <form id="update-visit-form" onSubmit={onSubmit}>
          <FormField
            control={form.control}
            name="visit_us"
            rules={{ required: 'Please select a venue' }}
            render={() => (
              <FormItem className="flex flex-col col-span-2">
                <FormLabel>Venue options</FormLabel>
                <FormDescription>Venue of the event</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <VisitUsList visitId={visit_id} />
        </form>
      </Form>
    </DialogContent>
  )
}

const VisitUsList: FC<{ visitId?: number }> = ({ visitId }) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  return (
    <div className="grid gap-4">
      {/* dialog to create new visit us options */}
      <Dialog
        open={isDialogOpen}
        onOpenChange={(isOpen) => {
          setIsDialogOpen(isOpen)
        }}
      >
        <DialogTrigger asChild>
          <Button variant="outline" type="button">
            <PlusCircleIcon size="16" className="mr-2" />
            Add a new venue
          </Button>
        </DialogTrigger>

        {/* visit us form */}
        <CreateVisitUsForm
          setIsDialogOpen={setIsDialogOpen}
          initialValues={{
            location_url: '',
            venue_name: '',
            venue_address: '',
            contact_mobile_number: '',
            contact_email: '',
            website_url: '',
            facebook_url: '',
            instagram_url: '',
            getting_here: [],
            parking_and_rate: [],
            shuttle_van_service: { image: undefined, image_url: '', description: '' }
          }}
        />
      </Dialog>

      {/* Selectable table for all options */}
      <VisitUsTable visitId={visitId} />
    </div>
  )
}

const VisitUsTable: FC<{ visitId?: number }> = ({ visitId }) => {
  const form = useFormContext<{
    visit_us: number
  }>()
  const defaultVisit = visitId ? [visitId] : undefined

  const columnHelper = createColumnHelper<VisitUs>()
  const columns: ColumnDef<VisitUs>[] = [
    {
      id: 'select',
      cell: ({ row, table }) => (
        <div className="px-1">
          <IndeterminateCheckbox
            {...{
              checked: row.getIsSelected(),
              disabled: !row.getCanSelect(),
              indeterminate: row.getIsSomeSelected(),
              onChange: () => {
                if (row.getIsSelected()) return
                // Reset all selection 1st to allow 1 selected only
                table.resetRowSelection()
                row.toggleSelected()
              }
            }}
          />
        </div>
      )
    },
    {
      accessorKey: 'id',
      header: 'id'
    },
    {
      accessorKey: 'venue_name',
      header: 'Venue'
    },
    {
      accessorKey: 'venue_address',
      header: 'Address'
    },
    columnHelper.display({
      id: 'actions',
      cell: (props) => <RowActions row={props.row} />
    })
  ]

  const toRow = (data: IDataResponse<VisitUs> | undefined): VisitUs[] => {
    if (data?.data && data?.data?.length > 0) {
      return data?.data?.map((element) => {
        return {
          ...element,
          key: element.id
        }
      })
    }

    return []
  }

  const toPaginate = (data: IDataResponse<VisitUs> | undefined) => {
    return {
      total: data?.meta?.total ?? 0,
      lastPage: data?.meta?.last_page ?? 0
    }
  }

  return (
    <DataTable<VisitUs, unknown, IDataResponse<VisitUs>>
      columns={columns}
      swrService={EventService.getEventVisitUs}
      toRow={toRow}
      toPaginate={toPaginate}
      setSelectedRows={(selectedRows) => form.setValue('visit_us', selectedRows[0] as number)}
      initialSelected={defaultVisit}
      onRowClick={(row: Row<VisitUs>, table: Table<VisitUs>) => {
        if (row.getIsSelected()) return
        table.resetRowSelection()
        row.toggleSelected()
      }}
    />
  )
}

const RowActions: FC<{ row: Row<VisitUs> }> = ({ row }) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  return (
    <div className="w-full flex justify-end" onClick={(e) => e.stopPropagation()}>
      <Dialog
        open={isDialogOpen}
        onOpenChange={(isOpen) => {
          setIsDialogOpen(isOpen)
        }}
      >
        {/* edit visit us form */}
        <CreateVisitUsForm setIsDialogOpen={setIsDialogOpen} initialValues={row.original} isEdit />

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DialogTrigger asChild>
              <DropdownMenuItem className="space-x-2">
                <EditIcon size="16" />
                <span>Edit</span>
              </DropdownMenuItem>
            </DialogTrigger>
          </DropdownMenuContent>
        </DropdownMenu>
      </Dialog>
    </div>
  )
}

export default UpdateVisitForm
