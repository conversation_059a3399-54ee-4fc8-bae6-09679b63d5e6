import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import { ContractAttentionData, CreateBoothContractInput } from '@/types/Contract'
import { capitalize } from 'radash'
import { useEffect } from 'react'
import { useFormContext } from 'react-hook-form'

interface ContractAttentionProps {
  attention: ContractAttentionData | undefined
}

const ContractAttentions = ({ attention }: ContractAttentionProps) => {
  const form = useFormContext<CreateBoothContractInput>()
  const target = form.watch('attention.target')

  useEffect(() => {
    if (target == 'custom') {
      form.setValue('attention.email', '')
      form.setValue('attention.full_name', '')
      form.setValue('attention.contact_number', '')
    } else {
      form.setValue('attention.email', attention?.[target]?.email ?? '')
      form.setValue('attention.full_name', attention?.[target]?.full_name ?? '')
      form.setValue('attention.contact_number', attention?.[target]?.contact_number ?? '')
    }
  }, [target, attention])

  return (
    <div className="max-w-4xl mx-auto grid grid-cols-2 gap-x-4 gap-y-2">
      <FormField
        control={form.control}
        name="attention.target"
        rules={{ required: 'Please select the attention' }}
        render={({ field }) => (
          <FormItem className="flex flex-col col-span-2">
            <FormLabel>Attentions</FormLabel>
            <FormControl>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                {/* <FormControl> */}
                <SelectTrigger>
                  <SelectValue placeholder="Choose an option" />
                </SelectTrigger>
                {/* </FormControl> */}
                <SelectContent>
                  {['contact', 'finance', 'marketing', 'custom'].map((value) => {
                    return (
                      <SelectItem key={value} value={value}>
                        {capitalize(value)}
                      </SelectItem>
                    )
                  })}
                </SelectContent>
              </Select>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="attention.full_name"
        render={({ field }) => (
          <FormItem className="flex flex-col col-span-1">
            <FormLabel>Full Name</FormLabel>
            <FormControl>
              <Input {...field} disabled={target != 'custom'} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="attention.designation"
        rules={{ required: 'Please enter the attention designation' }}
        render={({ field }) => (
          <FormItem className="flex flex-col col-span-1">
            <FormLabel>Designation</FormLabel>
            <FormControl>
              <Input {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="attention.contact_number"
        render={({ field }) => (
          <FormItem className="flex flex-col col-span-1">
            <FormLabel>Contact Number</FormLabel>
            <FormControl>
              <Input {...field} disabled={target != 'custom'} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="attention.fax"
        render={({ field }) => (
          <FormItem className="flex flex-col col-span-1">
            <FormLabel>Fax</FormLabel>
            <FormControl>
              <Input {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="attention.email"
        render={({ field }) => (
          <FormItem className="flex flex-col col-span-1">
            <FormLabel>Email</FormLabel>
            <FormControl>
              <Input {...field} disabled={target != 'custom'} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  )
}

export default ContractAttentions
