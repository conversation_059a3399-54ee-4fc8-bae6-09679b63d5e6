import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { ContractBoothData, CreateBoothContractInput } from '@/types/Contract'
import React from 'react'
import { useFormContext } from 'react-hook-form'

interface ContractBoothDetailsProps {
  booths: ContractBoothData[] | undefined
}

const ContractBoothDetails = ({ booths }: ContractBoothDetailsProps) => {
  const form = useFormContext<CreateBoothContractInput>()

  return (
    <div className="flex flex-col gap-y-2">
      <div className="max-w-4xl grid grid-cols-2 gap-x-4 gap-y-2">
        <div className="flex justify gap-x-2">
          <FormLabel>Event Space Type</FormLabel>
        </div>

        <div className="flex justify gap-x-2">
          <FormLabel>Brands</FormLabel>
        </div>
      </div>

      {booths?.map((_booth, i) => {
        return (
          <React.Fragment>
            <div className="max-w-4xl grid grid-cols-2 gap-x-4 gap-y-2">
              <FormField
                control={form.control}
                name={`brands.${i}.event_space_type`}
                rules={{
                  required: 'Please enter the event space type',
                  validate: (value) => {
                    if (Object.values(value).includes('')) {
                      return 'Please enter the event space type'
                    }

                    return true
                  }
                }}
                render={() => {
                  return (
                    <FormItem className="flex flex-col col-span-1">
                      <FormField
                        control={form.control}
                        name={`brands.${i}.event_space_type.line_1`}
                        rules={{ required: 'Please enter the event space type' }}
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input {...field} placeholder="0 x Space" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name={`brands.${i}.event_space_type.line_2`}
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input disabled {...field} placeholder="00sqm (Total Booth Size)" />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name={`brands.${i}.event_space_type.line_3`}
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input disabled {...field} placeholder="Hall 0, Booth No.000" />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </FormItem>
                  )
                }}
              />

              <FormField
                control={form.control}
                name={`brands.${i}.title`}
                render={({ field }) => (
                  <FormItem className="flex flex-col col-span-1">
                    <FormControl>
                      <Input disabled {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </React.Fragment>
        )
      })}
    </div>
  )
}

export default ContractBoothDetails
