import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { ContractCompanyData, CreateBoothContractInput } from '@/types/Contract'
import { useEffect } from 'react'
import { useFormContext } from 'react-hook-form'

interface ContractCompanyDetailsProps {
  company: ContractCompanyData | undefined
}

const ContractCompanyDetails = ({ company }: ContractCompanyDetailsProps) => {
  const form = useFormContext<CreateBoothContractInput>()

  useEffect(() => {
    if (company) {
      form.setValue('company', { ...company, fax: form.getValues('company.fax') ?? '' })
    }
  }, [company])

  return (
    <div className="max-w-4xl mx-auto grid grid-cols-2 gap-x-4 gap-y-2">
      <FormField
        control={form.control}
        name="company.name"
        render={({ field }) => (
          <FormItem className="flex flex-col col-span-1">
            <FormLabel>Name</FormLabel>
            <FormControl>
              <Input disabled {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="company.reg_no"
        render={({ field }) => (
          <FormItem className="flex flex-col col-span-1">
            <FormLabel>Registration Number</FormLabel>
            <FormControl>
              <Input disabled {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="company.contact_number"
        render={({ field }) => (
          <FormItem className="flex flex-col col-span-1">
            <FormLabel>Contact Number</FormLabel>
            <FormControl>
              <Input disabled {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="company.address_1"
        render={({ field }) => (
          <FormItem className="flex flex-col col-span-1">
            <FormLabel>Address Line 1</FormLabel>
            <FormControl>
              <Input disabled {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="company.address_2"
        render={({ field }) => (
          <FormItem className="flex flex-col col-span-1">
            <FormLabel>Address Line 2</FormLabel>
            <FormControl>
              <Input disabled {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="company.postcode"
        render={({ field }) => (
          <FormItem className="flex flex-col col-span-1">
            <FormLabel>Postcode</FormLabel>
            <FormControl>
              <Input disabled {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="company.city"
        render={({ field }) => (
          <FormItem className="flex flex-col col-span-1">
            <FormLabel>City</FormLabel>
            <FormControl>
              <Input disabled {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="company.state"
        render={({ field }) => (
          <FormItem className="flex flex-col col-span-1">
            <FormLabel>State</FormLabel>
            <FormControl>
              <Input disabled {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="company.country"
        render={({ field }) => (
          <FormItem className="flex flex-col col-span-1">
            <FormLabel>Country</FormLabel>
            <FormControl>
              <Input disabled {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="company.fax"
        render={({ field }) => (
          <FormItem className="flex flex-col col-span-1">
            <FormLabel>Fax</FormLabel>
            <FormControl>
              <Input {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  )
}

export default ContractCompanyDetails
