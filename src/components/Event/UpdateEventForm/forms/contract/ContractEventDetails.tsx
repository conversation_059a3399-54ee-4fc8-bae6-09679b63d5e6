import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { ContractEventData, CreateBoothContractInput } from '@/types/Contract'
import { useEffect } from 'react'
import { useFormContext } from 'react-hook-form'

interface ContractEventDetailsProps {
  event: ContractEventData | undefined
}

const ContractEventDetails = ({ event }: ContractEventDetailsProps) => {
  const form = useFormContext<CreateBoothContractInput>()

  useEffect(() => {
    if (event) {
      form.setValue('event', { ...event })
    }
  }, [event])

  return (
    <div className="max-w-4xl mx-auto grid grid-cols-2 gap-x-4 gap-y-2">
      {/* <FormField
        control={form.control}
        name="event.id"
        render={({ field }) => (
          <FormItem className="flex flex-col col-span-1 display:hidden">
            <FormLabel>Title</FormLabel>
            <FormControl>
              <Input disabled {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      /> */}

      <FormField
        control={form.control}
        name="event.title"
        render={({ field }) => (
          <FormItem className="flex flex-col col-span-1">
            <FormLabel>Title</FormLabel>
            <FormControl>
              <Input disabled {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="event.event_date"
        render={({ field }) => (
          <FormItem className="flex flex-col col-span-1">
            <FormLabel>Date</FormLabel>
            <FormControl>
              <Input disabled {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="event.venue"
        render={({ field }) => (
          <FormItem className="flex flex-col col-span-1">
            <FormLabel>Venue</FormLabel>
            <FormControl>
              <Input disabled {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  )
}

export default ContractEventDetails
