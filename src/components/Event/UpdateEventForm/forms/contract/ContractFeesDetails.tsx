import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { CreateBoothContractInput } from '@/types/Contract'
import { useFormContext } from 'react-hook-form'

const ContractFeesDetails = () => {
  const form = useFormContext<CreateBoothContractInput>()

  return (
    <div className="max-w-4xl mx-auto grid grid-cols-2 gap-x-4 gap-y-2">
      {
        <>
          <FormItem key="label_business_nature" className="flex flex-col col-span-1">
            <FormLabel>Business Nature</FormLabel>
          </FormItem>
          <FormItem key="label.participation_fees" className="flex flex-col col-span-1">
            <FormLabel>Event Participation Fees (numbers only, max 2 decimals)</FormLabel>
          </FormItem>
        </>
      }
      <div className="flex flex-col col-span-2"></div>

      <FormField
        control={form.control}
        key="business_nature"
        name="business_nature"
        rules={{
          required: 'Please enter the business nature'
        }}
        render={({ field }) => (
          <FormItem className="flex flex-col col-span-1">
            <FormControl>
              <Input {...field} placeholder="Baby Hardware" />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        key="participation_fees"
        name="participation_fees"
        rules={{
          required: 'Please enter the participation fee',
          pattern: {
            value: /^\d+(\.\d{1,2})?$/,
            message: 'Enter a valid price (numbers only, max 2 decimals)'
          },
          validate: (value) => {
            if (value <= 0) {
              return 'The participation fee cannot be 0'
            }
            return true
          }
        }}
        render={({ field }) => (
          <FormItem className="flex flex-col col-span-1">
            <FormControl>
              <Input {...field} placeholder="Event participation fees" />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      {/* {brandsField.length > 1 && (
            <FormItem key={`brands.${arg[1]}.delete`} className="flex flex-col col-span-1">
              <FormControl>
                <Button
                  type="button"
                  onClick={() => {
                    const tmp = [...brandsField]
                    tmp.splice(arg[1], 1)
                    form.setValue('brands', tmp)
                  }}
                >
                  <Trash2Icon size="16" />
                </Button>
              </FormControl>
            </FormItem>
          )} */}

      {/* <FormItem key="add-brand" className="flex flex-col col-span-11">
        <FormControl>
          <Button
            type="button"
            onClick={() =>
              form.setValue('brands', [...brandsField, { title: '', participation_fees: 0 }])
            }
          >
            Create Brand
          </Button>
        </FormControl>
      </FormItem> */}
    </div>
  )
}

export default ContractFeesDetails
