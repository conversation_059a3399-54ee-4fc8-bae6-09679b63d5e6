import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { useFormContext } from 'react-hook-form'
import { ColumnDef, Row, Table } from '@tanstack/react-table'
import IndeterminateCheckbox from '@/components/Table/IndeterminateCheckbox'
import { DataTable, FilterColumn } from '@/components/Table/DataTable'
import { IDataResponse, serialize } from '@/network/request'
import { CreateBoothContractInput } from '@/types/Contract'
import BoothService from '@/network/services/booth'
import { CalendarDatePicker } from '@/components/DatePicker/CalendarDatePicker'
import { DateTime } from 'luxon'
import { FC } from 'react'
import { Input } from '@/components/ui/input'
import { CoreExhibitor } from '@/types/Exhibitor'

interface ContractGeneralInformationProps {
  type?: 'create' | 'update'
}

const ContractGeneralInformation: FC<ContractGeneralInformationProps> = ({ type = 'create' }) => {
  const form = useFormContext<CreateBoothContractInput>()

  return (
    <div className="max-w-4xl mx-auto space-y-4">
      {type === 'create' ? (
        <FormField
          control={form.control}
          name="store_id"
          rules={{ required: 'Please select a booth' }}
          render={() => (
            <FormItem className="flex flex-col col-span-2">
              <FormLabel>Booths Elligible for Contract</FormLabel>
              <FormControl>
                <CompanyOptionsTable />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      ) : (
        <FormField
          control={form.control}
          name="store_id"
          rules={{ required: 'Please select a booth' }}
          render={() => (
            <FormItem className="hidden">
              <FormLabel>Booths Elligible for Contract</FormLabel>
              <FormControl>
                <Input />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      <FormField
        control={form.control}
        name="contract_date"
        rules={{ required: 'Please enter the contract date' }}
        render={({ field }) => (
          <FormItem className="flex flex-col col-span-1">
            <FormLabel>Contract Date</FormLabel>
            <CalendarDatePicker
              mode="single"
              buttonLabel={
                field.value
                  ? DateTime.fromISO(field.value as string).toFormat('yyyy-MM-dd')
                  : undefined
              }
              // selected={new Date((field.value as DateTime)?.toISO() ?? new Date())}
              onSelect={(e) => {
                field.onChange(DateTime.fromISO(e?.toISOString() ?? '').toISO())
              }}
              disabled={(date) => date < new Date('1900-01-01')}
            />
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  )
}

const CompanyOptionsTable = () => {
  const form = useFormContext<CreateBoothContractInput>()
  const eventId = form.watch('event.id')

  const columns: ColumnDef<CoreExhibitor>[] = [
    {
      id: 'select',
      // header: ({ table }) => (
      //   <IndeterminateCheckbox
      //     {...{
      //       checked: table.getIsAllRowsSelected(),
      //       indeterminate: table.getIsSomeRowsSelected(),
      //       onChange: table.getToggleAllPageRowsSelectedHandler()
      //     }}
      //   />
      // ),
      cell: ({ row, table }) => (
        <div className="px-1">
          <IndeterminateCheckbox
            {...{
              multiple: !row.getCanMultiSelect(),
              checked: row.getIsSelected(),
              disabled: !row.getCanSelect(),
              indeterminate: row.getIsSomeSelected(),
              onChange: () => {
                if (!row.getIsSelected()) {
                  table.resetRowSelection()
                  row.toggleSelected()
                }
              }
            }}
          />
        </div>
      )
    },
    {
      accessorKey: 'name',
      header: 'Company'
    }
  ]

  const columnFilter: FilterColumn[] = [
    { columnKey: 'name', header: 'Company', dataType: 'string' }
  ]

  return (
    <DataTable<CoreExhibitor, unknown, IDataResponse<CoreExhibitor>>
      columns={columns}
      filterColumns={columnFilter}
      swrService={serialize(BoothService.getEligibleBoothStores(eventId), {})}
      toRow={BoothService.toRow}
      toPaginate={BoothService.toPaginate}
      setSelectedRows={(selectedRows) => form.setValue('store_id', selectedRows[0] as number)}
      onRowClick={(row: Row<CoreExhibitor>, table: Table<CoreExhibitor>) => {
        if (!row.getIsSelected()) {
          table.resetRowSelection()
          row.toggleSelected()
        }
      }}
    />
  )
}

export default ContractGeneralInformation
