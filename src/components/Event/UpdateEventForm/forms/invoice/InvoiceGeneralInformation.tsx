import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { useFieldArray, useFormContext } from 'react-hook-form'
import { Contract, CreateBoothInvoiceInput, InvoicePaymentData } from '@/types/Contract'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import React, { FC, useEffect, useMemo } from 'react'
import { Input } from '@/components/ui/input'
import { CalendarDatePicker } from '@/components/DatePicker/CalendarDatePicker'
import { DateTime } from 'luxon'

const InvoiceGeneralInformation: FC<{
  contract: Contract
}> = ({ contract }) => {
  const form = useFormContext<CreateBoothInvoiceInput>()

  const { fields, append, remove } = useFieldArray({ control: form.control, name: 'payments' })

  const paymentFrequency = form.watch('payment_frequency')

  const totalPayments = useMemo(() => {
    if (!contract) return 0

    return contract?.metadata.participation_fees ?? 0
  }, [contract])

  useEffect(() => {
    const count = Number(paymentFrequency)
    // const metadata = contract?.metadata
    if (fields.length < count) {
      const arr: InvoicePaymentData[] = Array(count - fields.length)
        .fill(null)
        .map((_item, i) => {
          if (i === 0 && count - fields.length === count) {
            return {
              percentage: 0,
              amount: 0,
              // issued_date: '',
              due_date: ''
              // items: [
              //   {
              //     description: `First Payment\r0% of Participation Fee of RM${totalPayments}\r\r${metadata?.event.title}\r${metadata?.event.event_date}\r${metadata?.event.venue}\r${metadata?.brands[0].event_space_type.line_3}\r${metadata?.brands[0].event_space_type.line_2}`,
              //     amount: 0
              //   }
              // ]
            }
          }
          return {
            percentage: 0,
            amount: 0,
            // issued_date: '',
            due_date: ''
            // items: [{ description: '', amount: 0 }]
          }
        })
      append(arr)
    } else if (fields.length > count) {
      const arr: number[] = Array(fields.length - count)
        .fill(null)
        .map((_v, i) => fields.length - i - 1)
      remove(arr)
    }
  }, [totalPayments, paymentFrequency])

  return (
    <div className="max-w-4xl mx-auto grid grid-cols-2 gap-x-4 gap-y-2">
      {/* <FormField
        control={form.control}
        name="contract_id"
        rules={{ required: 'Contract must be required.' }}
        render={() => (
          <FormItem className="flex flex-col col-span-2">
            <FormLabel>Contracts Elligible for Invoice</FormLabel>
            <FormControl>
              <ContractOptionsTable setSelectedContract={setSelectedContract} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      /> */}

      <FormField
        control={form.control}
        name="payment_frequency"
        rules={{ required: 'Payment frequency must be required.' }}
        render={({ field }) => (
          <FormItem className="flex flex-col col-span-3">
            <FormLabel>Payment Frequency</FormLabel>
            <FormControl>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose an option" />
                </SelectTrigger>
                <SelectContent>
                  {contract ? (
                    ['1', '2', '3', '4'].map((value) => {
                      return (
                        <SelectItem key={value} value={value}>
                          {value}
                        </SelectItem>
                      )
                    })
                  ) : (
                    <SelectItem disabled value="select a contract">
                      Select a contract
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
            </FormControl>
            {contract && (
              <FormDescription>Total participation fees are RM{totalPayments}.</FormDescription>
            )}
            <FormMessage />
          </FormItem>
        )}
      />

      {fields.map((field, i) => {
        // const itemsField = form.watch(`payments.${i}.items`)

        return (
          <React.Fragment key={field.id}>
            <br />
            <FormLabel className="flex flex-col col-span-2">Invoice {i + 1}</FormLabel>

            <FormField
              control={form.control}
              name={`payments.${i}.amount`}
              rules={{
                required: true,
                validate: () => {
                  const totalInvoiceAmount = form
                    .getValues('payments')
                    .reduce((accum, payment) => (accum += payment.amount), 0)
                  if (totalInvoiceAmount !== totalPayments) {
                    return "The sums of invoices's amount must be equal to total participation fees."
                  }
                  return true
                }
              }}
              render={({ field }) => (
                <FormItem className="flex flex-col col-span-1">
                  <FormLabel>Amount (RM)</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="number"
                      step={0.01}
                      max={totalPayments}
                      onChange={(e) => {
                        let targetValueStr = e.target.value.toString()
                        let targetValue = targetValueStr ? parseInt(e.target.value) : 0
                        if (targetValue > totalPayments) {
                          targetValue = totalPayments
                        } else if (targetValue < 0) {
                          targetValue = 0
                        }
                        form.setValue(`payments.${i}.amount`, targetValueStr ? targetValue : NaN)
                        const percentage = parseFloat(
                          ((targetValue / totalPayments) * 100).toFixed(2)
                        )
                        form.setValue(`payments.${i}.percentage`, percentage)
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name={`payments.${i}.percentage`}
              rules={{
                required: true,
                validate: (value: number) => {
                  if (value <= 0) {
                    return 'The payment percentage must be larger than 0.'
                  }
                  return true
                }
              }}
              render={({ field }) => (
                <FormItem className="flex flex-col col-span-1">
                  <FormLabel>Percentage (%)</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="number"
                      disabled
                      min={0}
                      max={100}
                      step={1}
                      onChange={(e) => {
                        let targetValue = Math.round(Number(e.target.value))
                        if (targetValue > 100) {
                          targetValue = 100
                        } else if (targetValue < 0) {
                          targetValue = 0
                        }
                        form.setValue(`payments.${i}.percentage`, targetValue)
                        const amount = (targetValue * totalPayments) / 100
                        form.setValue(`payments.${i}.amount`, amount)
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name={`payments.${i}.due_date`}
              rules={{ required: 'Due date is required.' }}
              render={({ field }) => (
                <FormItem className="flex flex-col col-span-1">
                  <FormLabel>Contract Date</FormLabel>
                  <CalendarDatePicker
                    mode="single"
                    buttonLabel={
                      field.value
                        ? DateTime.fromISO(field.value as string).toFormat('yyyy-MM-dd')
                        : undefined
                    }
                    // selected={new Date((field.value as DateTime)?.toISO() ?? new Date())}
                    onSelect={(e) => {
                      field.onChange(
                        DateTime.fromISO(e?.toISOString() ?? '').toISO({ includeOffset: false })
                      )
                    }}
                    disabled={(date) => date < new Date('1900-01-01')}
                  />
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* <FormField
              control={form.control}
              name={`payments.${i}.issued_date`}
              rules={{ required: 'Issued date must be required.' }}
              render={({ field }) => (
                <FormItem className="flex flex-col col-span-1">
                  <FormLabel>Issued Date</FormLabel>
                  <CalendarDatePicker
                    mode="single"
                    buttonLabel={
                      field.value
                        ? DateTime.fromISO(field.value as string).toFormat('yyyy-MM-dd')
                        : undefined
                    }
                    // selected={new Date((field.value as DateTime)?.toISO() ?? new Date())}
                    onSelect={(e) => {
                      field.onChange(DateTime.fromISO(e?.toISOString() ?? '').toISO())
                    }}
                    disabled={(date) => date < new Date('1900-01-01')}
                  />
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name={`payments.${i}.due_date`}
              rules={{ required: 'Due date must be required.' }}
              render={({ field }) => (
                <FormItem className="flex flex-col col-span-1">
                  <FormLabel>Due Date</FormLabel>
                  <CalendarDatePicker
                    mode="single"
                    buttonLabel={
                      field.value
                        ? DateTime.fromISO(field.value as string).toFormat('yyyy-MM-dd')
                        : undefined
                    }
                    selected={new Date((field.value as DateTime)?.toISO() ?? new Date())}
                    onSelect={(e) => {
                      field.onChange(DateTime.fromISO(e?.toISOString() ?? '').toISO())
                    }}
                    disabled={(date) => date < new Date('1900-01-01')}
                  />
                  <FormMessage />
                </FormItem>
              )}
            /> */}

            {/* {itemsField.map((_item, j) => (
              <React.Fragment key={j}>
                <FormLabel className="flex flex-col col-span-2">
                  <div className="flex gap-2">
                    <span>Item {j + 1}</span>
                    {itemsField.length > 1 && (
                      <Trash2Icon
                        size={16}
                        color="red"
                        onClick={() => {
                          const tmp = [...itemsField]
                          tmp.splice(j, 1)
                          form.setValue(`payments.${i}.items`, tmp)
                        }}
                      />
                    )}
                  </div>
                </FormLabel>
                <FormField
                  control={form.control}
                  name={`payments.${i}.items.${j}.description`}
                  rules={{ required: 'Description must be required.' }}
                  render={({ field }) => (
                    <FormItem className="flex flex-col col-span-1">
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea
                          {...field}
                          disabled={i === 0 && j === 0} // only disable first payment first item description
                          onChange={(e) => {
                            field.onChange(e)
                          }}
                          style={{ height: i === 0 && j === 0 ? '180px' : undefined }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name={`payments.${i}.items.${j}.amount`}
                  rules={{
                    required: true,
                    validate: () => {
                      const totalItemsAmount = form
                        .getValues(`payments.${i}.items`)
                        .reduce((accum, item) => {
                          return (accum += Number(item.amount ?? 0))
                        }, 0)
                      const invoiceAmount = form.getValues(`payments.${i}.amount`)
                      if (totalItemsAmount !== invoiceAmount) {
                        return "The sums of items's amount must be equal to invoice amount."
                      }
                      return true
                    }
                  }}
                  render={({ field }) => (
                    <FormItem className="flex flex-col col-span-1wwwwww">
                      <FormLabel>Amount (RM)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          {...field}
                          onChange={(e) => {
                            if (i === 0 && j === 0) {
                              console.log(typeof e.target.value)
                              const amount = Number(e.target.value)
                              form.setValue(
                                `payments.0.items.0.description`,
                                `First Payment\r${BigNumber((amount / totalPayments) * 100)
                                  .decimalPlaces(2)
                                  .toString()}% of Participation Fee of RM${totalPayments}\r\r${
                                  contract?.metadata?.event.title
                                }\r${contract?.metadata?.event.event_date}\r${
                                  contract?.metadata?.event.venue
                                }\r${contract?.metadata?.brands[0].event_space_type.line_3}\r${
                                  contract?.metadata?.brands[0].event_space_type.line_2
                                }`
                              )
                            }
                            field.onChange(e)
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </React.Fragment>
            ))} */}

            {/* <FormItem key="add-brand" className="flex flex-col col-span-2">
              <FormControl>
                <Button
                  type="button"
                  onClick={() =>
                    form.setValue(`payments.${i}.items`, [
                      ...itemsField,
                      { description: '', amount: 0 }
                    ])
                  }
                >
                  Add an item
                </Button>
              </FormControl>
            </FormItem> */}
          </React.Fragment>
        )
      })}
    </div>
  )
}

// const ContractOptionsTable: FC<{
//   setSelectedContract: Dispatch<SetStateAction<Contract | undefined>>
// }> = ({ setSelectedContract }) => {
//   const { event_id } = useParams()
//   const form = useFormContext<CreateBoothInvoiceInput>()

//   const columns: ColumnDef<Contract>[] = [
//     {
//       id: 'select',
//       // header: ({ table }) => (
//       //   <IndeterminateCheckbox
//       //     {...{
//       //       checked: table.getIsAllRowsSelected(),
//       //       indeterminate: table.getIsSomeRowsSelected(),
//       //       onChange: table.getToggleAllPageRowsSelectedHandler()
//       //     }}
//       //   />
//       // ),
//       cell: ({ row, table }) => (
//         <div className="px-1">
//           <IndeterminateCheckbox
//             {...{
//               checked: row.getIsSelected(),
//               disabled: !row.getCanSelect(),
//               indeterminate: row.getIsSomeSelected(),
//               onChange: (e) => {
//                 e.stopPropagation()
//                 if (!row.getIsSelected()) {
//                   table.resetRowSelection()
//                   row.toggleSelected()
//                   setSelectedContract(row.original)
//                 }
//               }
//             }}
//           />
//         </div>
//       )
//     },
//     {
//       accessorKey: 'contract_no',
//       header: 'Contract No.'
//     },
//     {
//       accessorKey: 'event_name',
//       header: 'Event'
//     },
//     {
//       accessorKey: 'exhibitor_name',
//       header: 'Exhibitor'
//     }
//   ]

//   const filterColumns: FilterColumn[] = [
//     { columnKey: 'contract_no', header: 'Contract No', dataType: 'string' }
//   ]

//   return (
//     <DataTable<Contract, unknown, IDataResponse<Contract>>
//       columns={columns}
//       swrService={serialize(ContractService.getContracts, {
//         event_id: event_id,
//         payment_frequency: 'null',
//         status: 'signed'
//       })}
//       filterColumns={filterColumns}
//       toRow={ContractService.toRow}
//       toPaginate={ContractService.toPaginate}
//       setSelectedRows={(selectedRows) => {
//         form.setValue('contract_id', selectedRows[0] as number)
//       }}
//       onRowClick={(row: Row<Contract>, table: Table<Contract>) => {
//         if (!row.getIsSelected()) {
//           table.resetRowSelection()
//           row.toggleSelected()
//           setSelectedContract(row.original)
//         }
//       }}
//     />
//   )
// }

export default InvoiceGeneralInformation
