import * as React from 'react'

import EventInfoCard from './cards/EventInfoCard'
import EventVisitCard from './cards/EventVisitCard'
import EventExploreCard from './cards/EventExploreCard'
import { Event } from '@/types/Event'
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import useAuth from '@/hooks/useAuth'
import EventProductCard from './cards/EventProductCard'
import EventStoreCard from './cards/EventStoreCard'
import EventContractCard from './cards/EventContractCard'
import CampaignTable from '@/components/Campaign/CampaignTable'
import AddCampaignButton from '@/components/Campaign/AddCampaignButton'
import EventTabDescriptionCard from './cards/EventTabDescriptionCard'
import EventPurchaseTableCard from './cards/EventPurchaseTableCard'

interface UpdateEventFormProps extends React.HTMLAttributes<HTMLDivElement> {
  initialValue: Event
  initialTab?: string
}

export function UpdateEventForm({ initialValue, initialTab }: UpdateEventFormProps) {
  const { role } = useAuth()

  return (
    <Tabs
      defaultValue={
        role === 'vendor' && initialValue.joined ? 'product' : initialTab ? initialTab : 'event'
      }
      className="space-y-4"
    >
      <TabsList>
        {/* Both */}
        <TabsTrigger value="event">Event</TabsTrigger>
        {/* Vendor Tabs */}
        {role === 'vendor' && initialValue.joined && (
          <>
            <TabsTrigger value="product">Products</TabsTrigger>
          </>
        )}
        {/* Admin Tabs */}
        {role !== 'vendor' && (
          <>
            <TabsTrigger value="visit-us">Visit Us</TabsTrigger>
            <TabsTrigger value="explore">Explore</TabsTrigger>
            <TabsTrigger value="deals">Deals</TabsTrigger>
            <TabsTrigger value="booth">Layout Map/Booths</TabsTrigger>
            <TabsTrigger value="contract">Contract</TabsTrigger>
            <TabsTrigger value="purchases">Purchases</TabsTrigger>
          </>
        )}
      </TabsList>

      <TabsContent value="event">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-4 col-span-2">
            <EventInfoCard event={initialValue} />
          </div>
        </div>
      </TabsContent>

      <TabsContent value="visit-us">
        <div className="space-y-4">
          <EventVisitCard event={initialValue} />
        </div>
      </TabsContent>

      <TabsContent value="explore">
        <div className="space-y-4">
          <EventExploreCard event={initialValue} />
        </div>
      </TabsContent>

      <TabsContent value="deals">
        <div className="space-y-4">
          <EventTabDescriptionCard event={initialValue} type="deal" />

          <div className="flex justify-end space-x-2">
            {/* <ImportCampaignButton /> */}
            <AddCampaignButton type="deal" eventId={initialValue.id} />
          </div>

          <CampaignTable type="deal" eventId={initialValue.id} />
        </div>
      </TabsContent>

      <TabsContent value="product">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-4 col-span-2">
            <EventProductCard event={initialValue} />
          </div>
        </div>
      </TabsContent>

      <TabsContent value="booth">
        <div className="space-y-4">
          <EventTabDescriptionCard event={initialValue} type="layout" />
          <EventStoreCard event={initialValue} />
        </div>
      </TabsContent>

      <TabsContent value="contract">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-4 col-span-2">
            <EventContractCard event={initialValue} />
          </div>
        </div>
      </TabsContent>

      <TabsContent value="purchases">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-4 col-span-2">
            <EventPurchaseTableCard event={initialValue} />
          </div>
        </div>
      </TabsContent>
    </Tabs>
  )
}
