import {
  <PERSON>,
  CardContent,
  <PERSON>Footer,
  CardHeader,
  CardTitle,
  Content,
  Title
} from '@/components/ui/card'
import { EventProduct } from '@/types/Event'
import { FC } from 'react'

const EventProductInfoCard: FC<{ eventProduct: EventProduct }> = ({ eventProduct }) => {
  return (
    <>
      <Card>
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <CardTitle>Event Product</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-2">
            {/* Event Product general */}
            <Title>Product</Title>
            <Content>{eventProduct?.product?.title ?? '-'}</Content>
            <Title>Event</Title>
            <Content>{eventProduct?.event?.title}</Content>
            <Title>Store</Title>
            <Content>{eventProduct?.store?.name ?? '-'}</Content>
          </div>
        </CardContent>
        <CardFooter></CardFooter>
      </Card>
    </>
  )
}

export default EventProductInfoCard
