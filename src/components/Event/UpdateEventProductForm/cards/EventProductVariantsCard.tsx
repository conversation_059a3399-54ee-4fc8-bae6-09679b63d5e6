import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>ooter, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
// import { ProductVariant } from '@/types/Product'
import { FC } from 'react'
import { Button } from '@/components/ui/button'
import { MoreHorizontal, EditIcon } from 'lucide-react'
import { EventProduct } from '@/types/Event'
import { useProduct } from '@/hooks/products/useProduct'
// import { UpdateEventProductVariantPricesForm } from '../forms/UpdateEventProductVariantPrices'
// import { usePriceLists } from '@/hooks/price_list/usePriceLists'
// import { useParams } from 'react-router-dom'

const EventProductVariantsCard: FC<{ eventProduct: EventProduct }> = ({ eventProduct }) => {
  // const { event_id } = useParams()
  const { product, isLoading, error } = useProduct(
    eventProduct.product?.medusa_product_id ?? '',
    {
      expand: [
        'variants',
        'variants.options',
        'variants.prices',
        'variants.prices.price_list',
        'options',
        'options.values',
        'store',
        'collection',
        'categories',
        'images'
      ]
    },
    'comma'
  )
  // const { priceLists } = usePriceLists(Number(event_id))
  // const [editVariant, setEditVariant] = useState<ProductVariant | null>(null)

  if (!product || isLoading || error) {
    return <></>
  }

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <CardTitle>Variants Prices</CardTitle>
          {/* <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" forceMount>
              <DropdownMenuGroup>
                <DropdownMenuItem
                  onClick={async (event) => {
                    event.stopPropagation()
                  }}
                  className="space-x-2"
                >
                  <PlusIcon size="16" />
                  <span>Add Variant</span>
                </DropdownMenuItem>
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu> */}
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex flex-row flex-wrap space-x-6">
            {/* {product.options?.map((opt) => {
              return (
                <div key={opt.title} className="flex flex-col space-y-1">
                  <p className="font-medium">{opt.title}</p>
                  <div className="flex flex-row space-x-1">
                    {opt.values?.map((val, index) => {
                      return (
                        <Badge key={index} variant="secondary">
                          {val.value}
                        </Badge>
                      )
                    })}
                  </div>
                </div>
              )
            })} */}
          </div>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Title</TableHead>
                <TableHead>Original Price</TableHead>
                <TableHead className="w-12"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {product.variants.map((variant) => {
                const amount = variant.prices.find((price) => !price.price_list_id)?.amount
                return (
                  <TableRow key={variant.id}>
                    <TableCell>{variant.title}</TableCell>
                    <TableCell>{amount ? `MYR ${(amount / 100).toFixed(2)}` : '-'}</TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" forceMount>
                          <DropdownMenuGroup>
                            <DropdownMenuItem
                              onClick={async (event) => {
                                event.stopPropagation()
                                // setEditVariant(variant)
                              }}
                              className="space-x-2"
                            >
                              <EditIcon size="16" />
                              <span>Edit Variant Sale Price</span>
                            </DropdownMenuItem>
                          </DropdownMenuGroup>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
          </Table>
        </CardContent>
        <CardFooter></CardFooter>
      </Card>

      {/* {editVariant && (
        <UpdateEventProductVariantPricesForm
          {...{
            setEditVariant,
            variant: editVariant,
            priceLists: priceLists.filter(
              (priceList) => priceList.event_id && priceList.campaign_id
            )
          }}
        />
      )} */}
    </>
  )
}

export default EventProductVariantsCard
