import { Input } from '@/components/ui/input'
import {
  FormControl,
  // FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { useFormContext } from 'react-hook-form'
import { UpdateProductVariantSalePrices } from '@/types/CreateProduct'
import { PriceList, ProductVariant } from '@/types/Product'

interface EventProductVariantSalePricingProps {
  priceLists: PriceList[]
  variant: ProductVariant
}

const EventProductVariantSalePricing = ({
  priceLists,
  variant
}: EventProductVariantSalePricingProps) => {
  const form = useFormContext<UpdateProductVariantSalePrices>()

  return (
    <div className="flex flex-col space-y-4">
      <p>* Set Price in sen, E.g. MYR 10.50 = 1050 sen.</p>
      <div className="grid grid-cols-2 gap-4">
        <FormItem className="flex flex-col">
          <FormLabel>Title</FormLabel>
          <Input readOnly value={variant.title} disabled />
        </FormItem>

        <FormItem className="flex flex-col">
          <FormLabel>Currency</FormLabel>
          <Input readOnly value={'myr'} disabled />
        </FormItem>

        <FormItem className="flex flex-col">
          <FormLabel>Original Price</FormLabel>
          <FormControl>
            <Input
              readOnly
              value={variant.prices.find((price) => !price.price_list_id)?.amount}
              disabled
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      </div>

      <div className="grid grid-cols-2 gap-4">
        {priceLists.map((priceList, index) => {
          return (
            <FormField
              key={index}
              control={form.control}
              name={`price_lists.${index}.prices.0.amount`}
              //   rules={{ required: 'Please enter the amount' }}
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>{priceList.name}</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} min={0} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )
        })}
      </div>
    </div>
  )
}

export default EventProductVariantSalePricing
