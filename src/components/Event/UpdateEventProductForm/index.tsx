import { useParams } from 'react-router-dom'
import { FC } from 'react'
import { useEventProduct } from '@/hooks/event_products/useEventProduct'
import EventProductInfoCard from './cards/EventProductInfoCard'
import EventProductVariantsCard from './cards/EventProductVariantsCard'

const EventProductDetails = () => {
  const { event_product_id } = useParams()

  if (!event_product_id) {
    return <></>
  }

  return <UpdateEventProductForm {...{ event_product_id }} />
}

const UpdateEventProductForm: FC<{ event_product_id: string | number }> = ({
  event_product_id
}) => {
  // find event product with event_product_id
  const { eventProduct, isLoading } = useEventProduct(event_product_id)

  return (
    <div className="space-y-6">
      <div className="p-8">
        <div className="grid grid-cols-[4fr_2fr] gap-4">
          <div className="space-y-4">
            {!isLoading && <EventProductInfoCard eventProduct={eventProduct} />}
            {!isLoading && <EventProductVariantsCard eventProduct={eventProduct} />}
          </div>
          <div className="space-y-4"></div>
        </div>
      </div>
    </div>
  )
}

export default EventProductDetails
