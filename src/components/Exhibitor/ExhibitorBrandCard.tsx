import { Exhibitor } from '@/types/Exhibitor'
import { FC } from 'react'
import { Card, CardHeader, CardTitle, CardContent, Title, Content } from '../ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'
import useSWR from 'swr'
import { serialize } from '@/network/request'
import ProductCollectionService, {
  ProductCollectionResponse
} from '@/network/services/product_collection'
import { cn, statusToColor } from '@/lib/utils'

const ExhibitorBrandCard: FC<{ exhibitor: Exhibitor }> = ({ exhibitor }) => {
  const { data, error } = useSWR<ProductCollectionResponse>(
    serialize(ProductCollectionService.getProductCollections, {
      store_id: exhibitor.id
    })
  )

  if (error) {
    return <></>
  }

  if (!data) {
    return <></>
  }

  const brands = data.collections
  return (
    <>
      <Card className="h-fit">
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <CardTitle>Brands</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Thumbnail</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Social Medias</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {brands?.map((brand, index) => {
                return (
                  <TableRow key={index} onClick={() => {}}>
                    <TableCell>{brand.title}</TableCell>
                    <TableCell>{brand.description}</TableCell>
                    <TableCell align="center">
                      <span className="flex flex-row items-center gap-4">
                        <img
                          className="max-h-[86px]"
                          src={brand?.thumbnail_url ?? ''}
                          alt={brand.title}
                        />
                      </span>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <div
                          className={cn(
                            'h-1.5 w-1.5 self-center rounded-full',
                            statusToColor(brand.status)
                          )}
                        />
                        <span className="capitalize">{brand.status}</span>
                      </div>
                    </TableCell>
                    <TableCell className='w-[260px] overflow-ellipsis'>
                      {Object.keys(brand.metadata).map((key) => {
                        return (
                          <div className='grid grid-cols-[1fr_2fr] gap-2'>
                            <Title className="capitalize">{key}</Title>
                            <Content>{brand.metadata[key]}</Content>
                          </div>
                        )
                      })}
                    </TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </>
  )
}

export default ExhibitorBrandCard
