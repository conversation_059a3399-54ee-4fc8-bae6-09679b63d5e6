import { Exhibitor } from '@/types/Exhibitor'
import { FC, useState } from 'react'
import { Card, CardHeader, CardTitle, CardContent, Title, Content } from '../ui/card'
import { Dialog } from '../ui/dialog'
import UpdateExhibitorContactForm from './UpdateExhibitorForm/UpdateExhibitorContactForm'
import useAuth from '@/hooks/useAuth'
import { MoreHorizontal, SettingsIcon } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu'
import { Button } from '../ui/button'

const ExhibitorContactCard: FC<{ exhibitor: Exhibitor; variant?: 'finance' | 'marketing' }> = ({
  exhibitor,
  variant
}) => {
  const { role } = useAuth()
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  const fullName = variant
    ? variant == 'finance'
      ? exhibitor?.finance_full_name
      : exhibitor?.marketing_full_name
    : exhibitor?.contact_full_name

  const email = variant
    ? variant == 'finance'
      ? exhibitor?.finance_email
      : exhibitor?.marketing_email
    : exhibitor?.contact_email

  const contactNumber = variant
    ? variant == 'finance'
      ? exhibitor?.finance_mobile_number
      : exhibitor?.marketing_mobile_number
    : exhibitor?.contact_mobile_number

  return (
    <>
      <Card className="h-fit">
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <CardTitle>
            {variant ? (variant == 'finance' ? 'Finance Contact' : 'Marketing Contact') : 'Contact'}
          </CardTitle>
          {role === 'super_admin' && (
            <div className="flex flex-row space-x-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-8 w-8 p-0">
                    <span className="sr-only">Open menu</span>
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" forceMount>
                  <DropdownMenuGroup>
                    <DropdownMenuItem
                      onClick={async (event) => {
                        event.stopPropagation()
                        setIsDialogOpen(true)
                      }}
                      className="space-x-2"
                    >
                      <SettingsIcon size="16" />
                      <span>Edit</span>
                    </DropdownMenuItem>
                  </DropdownMenuGroup>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          )}
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-2">
            <Title>Full Name</Title>
            <Content className="break-all">{fullName ?? '-'}</Content>
            <Title>Email</Title>
            <Content className="break-all">{email ?? '-'}</Content>
            <Title>Contact Number</Title>
            <Content className="break-all">{contactNumber ?? '-'}</Content>
          </div>
        </CardContent>
      </Card>
      {/* Update exhibitor contact */}
      <Dialog
        open={isDialogOpen}
        onOpenChange={(isOpen) => {
          setIsDialogOpen(isOpen)
        }}
      >
        <UpdateExhibitorContactForm
          variant={variant}
          initialValues={exhibitor}
          setIsDialogOpen={setIsDialogOpen}
        />
      </Dialog>{' '}
    </>
  )
}

export default ExhibitorContactCard
