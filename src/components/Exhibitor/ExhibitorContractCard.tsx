import { IDataResponse, serialize } from '@/network/request'
import ContractService from '@/network/services/contract'
import { Contract } from '@/types/Contract'
import { DataTable, FilterColumn } from '../Table/DataTable'
import { Card, CardHeader, CardTitle, CardContent } from '../ui/card'
import { ColumnDef } from '@tanstack/react-table'
import { FC } from 'react'
import useSWR from 'swr'
import ExhibitorService from '@/network/services/exhibitor'

const ExhibitorContractCard: FC<{ exhibitorId: number }> = ({ exhibitorId }) => {
  // const columnHelper = createColumnHelper<Contract>()
  const columns: ColumnDef<Contract>[] = [
    {
      accessorKey: 'contract_no',
      header: 'Contract No.'
    },
    {
      accessorKey: 'event_name',
      header: 'Event'
    },
    {
      accessorKey: 'hall_no',
      header: 'Hall'
    },
    {
      accessorKey: 'booth_no',
      header: 'Booth'
    },
    {
      accessorKey: 'store_name',
      header: 'Exhibitor'
    },
    {
      accessorKey: 'status',
      header: 'Status'
    }
    // columnHelper.display({
    //   id: 'actions',
    //   cell: (props) => <RowActions row={props.row} />
    // })
  ]

  const columnFilter: FilterColumn[] = [
    { columnKey: 'contract_no', header: 'Contract No', dataType: 'string' },
    { columnKey: 'hall_no', header: 'Hall No', dataType: 'string' },
    { columnKey: 'booth_no', header: 'Booth No', dataType: 'string' },
    {
      columnKey: 'status',
      header: 'Status',
      dataType: 'faceted',
      options: [
        { value: 'pending', label: 'PENDING', icon: undefined },
        { value: 'accepted', label: 'ACCEPTED', icon: undefined },
        { value: 'rejected', label: 'REJECTED', icon: undefined }
      ]
    }
  ]

  // get core.store_id from store.exhibitor_id
  const { data, error } = useSWR(serialize(ExhibitorService.getStore(exhibitorId), {}))

  if (error) {
    return <></>
  }

  if (!data) {
    return <></>
  }

  return (
    <>
      <Card className="h-fit">
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <CardTitle>Contracts</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable<Contract, unknown, IDataResponse<Contract>>
            columns={columns}
            filterColumns={columnFilter}
            swrService={serialize(ContractService.getContracts, { store: data.data.id })}
            toRow={ContractService.toRow}
            toPaginate={ContractService.toPaginate}
          />
        </CardContent>
      </Card>
    </>
  )
}

export default ExhibitorContractCard
