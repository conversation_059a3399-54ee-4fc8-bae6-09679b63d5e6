import { FC, useMemo, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle, Content, Title } from '../ui/card'
import ExhibitorService from '@/network/services/exhibitor'
import useAuth from '@/hooks/useAuth'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu'
import { Button } from '../ui/button'
import { cn, statusToColor } from '@/lib/utils'
import { toast } from '../ui/use-toast'
import { mutate } from 'swr'
import { Exhibitor } from '@/types/Exhibitor'
import { Separator } from '../ui/separator'
import { MoreHorizontal, SettingsIcon } from 'lucide-react'
import { Dialog } from '../ui/dialog'
import UpdateExhibitorGeneralForm from './UpdateExhibitorForm/UpdateExhibitorGeneralForm'

const ExhibitorGeneralInfoCard: FC<{
  exhibitor: Exhibitor
}> = ({ exhibitor }) => {
  const { role } = useAuth()
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <CardTitle>General Information</CardTitle>

          <div className="flex flex-row space-x-2">
            <ExhibitorStatusBadge {...{ exhibitor }} />
            {role === 'super_admin' && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-8 w-8 p-0">
                    <span className="sr-only">Open menu</span>
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" forceMount>
                  <DropdownMenuGroup>
                    <DropdownMenuItem
                      onClick={async (event) => {
                        event.stopPropagation()
                        setIsDialogOpen(true)
                      }}
                      className="space-x-2"
                    >
                      <SettingsIcon size="16" />
                      <span>Edit</span>
                    </DropdownMenuItem>
                  </DropdownMenuGroup>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-2">
            <Title>Store Id</Title>
            <Content className="break-all">{exhibitor?.id ?? '-'}</Content>
            <Title>Type</Title>
            <Content>{exhibitor?.type ?? '-'}</Content>
            <Title>Name</Title>
            <Content>{exhibitor?.name ?? '-'}</Content>
            <Title>GST Number</Title>
            <Content className="break-all">{exhibitor?.gst_no ?? '-'}</Content>
            <Title>E-Invoice</Title>
            <Content className="break-all">{exhibitor?.e_invoice ?? '-'}</Content>
            <Title>Registration Number</Title>
            <Content className="break-all">{exhibitor?.company_reg_no ?? '-'}</Content>
            {/* <Title>Social Media</Title>
            <Content className="break-word">{exhibitor?.facebook_or_instagram ?? '-'}</Content> */}
            <Title>Website</Title>
            <Content className="break-word">{exhibitor?.website ?? '-'}</Content>

            <Separator className="col-span-2 my-2" />

            <Title>Address 1</Title>
            <Content className="break-word">{exhibitor?.address_1 ?? '-'}</Content>
            <Title>Address 2</Title>
            <Content className="break-word">{exhibitor?.address_2 ?? '-'}</Content>
            <Title>City</Title>
            <Content>{exhibitor?.city ?? '-'}</Content>
            <Title>Postcode</Title>
            <Content>{exhibitor?.postcode ?? '-'}</Content>
            <Title>State</Title>
            <Content>{exhibitor?.state ?? '-'}</Content>
            <Title>Country</Title>
            <Content>{exhibitor?.country ?? '-'}</Content>
          </div>
        </CardContent>
      </Card>

      {/* Update explore description */}
      <Dialog
        open={isDialogOpen}
        onOpenChange={(isOpen) => {
          setIsDialogOpen(isOpen)
        }}
      >
        <UpdateExhibitorGeneralForm initialValues={exhibitor} setIsDialogOpen={setIsDialogOpen} />
      </Dialog>
    </>
  )
}

const ExhibitorStatusBadge: FC<{ exhibitor: Exhibitor }> = ({ exhibitor }) => {
  const { role } = useAuth()
  const color = useMemo(() => {
    return statusToColor(exhibitor.status)
  }, [exhibitor.status])

  const updateStatus = async (exhibitor: Exhibitor, approved: boolean) => {
    try {
      await ExhibitorService.exhibitorApproval(exhibitor.id, approved)
      mutate(
        (key) =>
          typeof key === 'string' && key.startsWith(ExhibitorService.getExhibitor(exhibitor.id))
      )
      approved
        ? toast({
            title: `Exhibitor approved`,
            variant: 'success'
          })
        : toast({
            title: `Exhibitor rejected`,
            variant: 'destructive'
          })
    } catch (error) {
      console.log(error)
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="h-8">
          <div className="flex items-center space-x-2">
            <div className={cn('h-1.5 w-1.5 self-center rounded-full', color)} />
            <span className="capitalize text-xs">{exhibitor.status}</span>
          </div>
        </Button>
      </DropdownMenuTrigger>
      {role != 'vendor' && exhibitor.status == 'pending' && (
        <DropdownMenuContent align="end" forceMount>
          <DropdownMenuGroup>
            <DropdownMenuItem
              onClick={async (event) => {
                event.stopPropagation()
                await updateStatus(exhibitor, true)
              }}
              className="space-x-2"
            >
              <div
                className={cn('h-1.5 w-1.5 self-center rounded-full', statusToColor('approved'))}
              />
              <span className="capitalize text-xs">Approve</span>
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={async (event) => {
                event.stopPropagation()
                await updateStatus(exhibitor, false)
              }}
              className="space-x-2"
            >
              <div
                className={cn('h-1.5 w-1.5 self-center rounded-full', statusToColor('rejected'))}
              />
              <span className="capitalize text-xs">Reject</span>
            </DropdownMenuItem>
          </DropdownMenuGroup>
        </DropdownMenuContent>
      )}
    </DropdownMenu>
  )
}

export default ExhibitorGeneralInfoCard
