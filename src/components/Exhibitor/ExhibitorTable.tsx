/* eslint-disable @typescript-eslint/ban-ts-comment */
import ExhibitorService from '@/network/services/exhibitor'
import { ColumnDef, Row, createColumnHelper } from '@tanstack/react-table'
import { DataTable, FilterColumn } from '@/components/Table/DataTable'
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuGroup
} from '@/components/ui/dropdown-menu'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger
} from '@/components/ui/alert-dialog'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { toast } from '@/components/ui/use-toast'
import { serialize } from '@/network/request'
import useAuth from '@/hooks/useAuth'
import { useNavigate } from 'react-router-dom'
import { useSWRConfig } from 'swr'
import { first } from 'radash'
import {
  MonitorCheckIcon,
  MonitorXIcon,
  MoreHorizontal,
  Trash2Icon,
  UserCog2Icon
} from 'lucide-react'
import { FC } from 'react'
import { Exhibitor, ExhibitorResponse } from '@/types/Exhibitor'
import { isAxiosError } from 'axios'

// column declaration for DataTable
const columnHelper = createColumnHelper<Exhibitor>()
const columns: ColumnDef<Exhibitor>[] = [
  {
    accessorKey: 'name',
    header: 'Name'
  },
  {
    accessorKey: 'company_reg_no',
    header: 'Registration Number'
  },
  {
    accessorKey: 'contact_email',
    header: 'Contact',
    cell: (props) => {
      const value = props.row.original

      return (
        <div className="flex flex-col">
          <span>{value.contact_full_name}</span>
          <span>{value.contact_email}</span>
          <span>{value.contact_mobile_number}</span>
        </div>
      )
    }
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: (props) => {
      const value = props.getValue<string>()
      let color = 'bg-primary'

      switch (value) {
        case 'approved':
          color = 'bg-success'
          break
        case 'rejected':
          color = 'bg-destructive'
          break
        default:
          color = 'bg-yellow-500'
          break
      }

      return (
        <div className="flex items-center space-x-2">
          <div className={cn('h-1.5 w-1.5 self-center rounded-full', color)} />
          <span className="capitalize">{value}</span>
        </div>
      )
    }
  },
  {
    accessorKey: 'availability',
    header: ''
  },
  columnHelper.display({
    id: 'actions',
    cell: (props) => <RowActions row={props.row} />
  })
]

const RowActions: FC<{ row: Row<Exhibitor> }> = ({ row }) => {
  const { mutate } = useSWRConfig()
  const { role, masquerade } = useAuth()

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0" onClick={(e) => e.stopPropagation()}>
          <span className="sr-only">Open menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56" align="end">
        {role == 'super_admin' && (
          <>
            <DropdownMenuItem
              className="space-x-2"
              onClick={(e) => {
                e.stopPropagation()

                try {
                  // temporarily masquerade as this user
                  const user = first(row.original.users)

                  if (!user) {
                    // user must not be null
                    // toast error
                    return
                  }

                  masquerade(
                    {
                      id: user.id,
                      username: `${user.custom_role}-${user.email}`,
                      role: user.custom_role,
                      email: `${user.email}`,
                      blocked: false
                    },
                    row.original
                  )

                  toast({
                    title: `Switch to user ${user.email}`,
                    variant: 'success'
                  })
                } catch (error) {
                  toast({
                    title: `Failed to switch user`,
                    variant: 'destructive'
                  })
                }
              }}
              disabled={row.original.status != 'approved'}
            >
              <UserCog2Icon size="16" />
              <span>Sign in as {row.original.name}</span>
            </DropdownMenuItem>
            <DropdownMenuItem className="space-x-2" disabled={row.original.status != 'rejected'}>
              <AlertDialog key="delete-exhibitor">
                <AlertDialogTrigger
                  type="button"
                  onClick={(e) => e.stopPropagation()}
                  className="flex space-x-2 w-full"
                >
                  <Trash2Icon size="16" />
                  <span>Delete</span>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>
                      Are you absolutely sure to delete {row.original.name}?
                    </AlertDialogTitle>
                    <AlertDialogDescription>This action cannot be undone</AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={async (event) => {
                        event.stopPropagation()

                        try {
                          await ExhibitorService.deleteExhibitor(row.original.id)
                          mutate(
                            (key) =>
                              typeof key === 'string' &&
                              key.startsWith(ExhibitorService.getExhibitors)
                          )

                          toast({
                            description: 'Company deleted',
                            variant: 'destructive'
                          })
                        } catch (error) {
                          let message = 'Failed to delete company'
                          // get error message from axios
                          if (isAxiosError(error)) {
                            message = error?.response?.data.error
                          }

                          toast({
                            title: message,
                            variant: 'destructive'
                          })
                        }
                      }}
                    >
                      Confirm
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
          </>
        )}
        <DropdownMenuGroup>
          <DropdownMenuItem
            onClick={async (e) => {
              e.stopPropagation()

              try {
                // Approve exhibitor/vendors
                const { data: response } = await ExhibitorService.exhibitorApproval(
                  row.original.id,
                  true
                )

                if (response.success == true) {
                  mutate(
                    (key) =>
                      typeof key === 'string' && key.startsWith(ExhibitorService.getExhibitors)
                  )
                  toast({
                    title: 'Exhibitor approved',
                    variant: 'success'
                  })
                }

                console.log(response)
              } catch (e) {
                toast({
                  title: 'Action failed, please try again',
                  variant: 'destructive'
                })
                console.log(e)
              }
            }}
            className="space-x-2"
            disabled={row.original.status !== 'pending'}
          >
            <MonitorCheckIcon size="16" />
            <span>Approve</span>
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={async (e) => {
              e.stopPropagation()

              try {
                // Approve exhibitor/vendors
                const { data: response } = await ExhibitorService.exhibitorApproval(
                  row.original.id,
                  false
                )

                if (response.success == true) {
                  mutate(
                    (key) =>
                      typeof key === 'string' && key.startsWith(ExhibitorService.getExhibitors)
                  )
                  toast({
                    title: 'Exhibitor rejected',
                    variant: 'success'
                  })
                }

                console.log(response)
              } catch (e) {
                console.log(e)

                toast({
                  title: 'Action failed, please try again',
                  variant: 'destructive'
                })
              }
            }}
            className="space-x-2"
            disabled={row.original.status !== 'pending'}
          >
            <MonitorXIcon size="16" />
            <span>Reject</span>
          </DropdownMenuItem>
          {/* <DropdownMenuItem
            onClick={(e) => {
              e.stopPropagation()
            }}
            className="space-x-2"
          >
            <Trash2Icon size="16" />
            <span>Delete</span>
          </DropdownMenuItem> */}
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

// Declaration of which column can be filtered
const columnFilter: FilterColumn[] = [
  {
    columnKey: 'name',
    header: 'Search',
    dataType: 'string'
  }
]

const ExhibitorTable = () => {
  const nav = useNavigate()

  return (
    <DataTable<Exhibitor, unknown, ExhibitorResponse>
      columns={columns}
      filterColumns={columnFilter}
      swrService={serialize(ExhibitorService.getExhibitors, { expand: 'users' })}
      pageParam="page"
      limitParam="limit"
      filterParam="q"
      sortParam="sort"
      sortColumns={['name', 'company_reg_no', 'status']}
      toRow={ExhibitorService.toRow}
      toPaginate={ExhibitorService.toPaginate}
      onRowClick={(row: Row<Exhibitor>) => {
        nav(`/companies/${row.original.id}`)
      }}
    />
  )
}

export default ExhibitorTable
