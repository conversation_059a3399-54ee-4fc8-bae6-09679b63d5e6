/* eslint-disable @typescript-eslint/ban-ts-comment */
import * as React from 'react'

import { cn } from '@/lib/utils'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Dropzone } from '@/components/Form/Dropzone'
import { useFormContext } from 'react-hook-form'
import FileService from '@/network/services/file'
import { CreateBatchJobInput } from '@/types/BatchJob'
import BatchJobService from '@/network/services/batch-job'
import { toast } from '@/components/ui/use-toast'
import { isAxiosError } from 'axios'
import { FileWithPath } from 'react-dropzone'
import { Separator } from '@/components/ui/separator'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from '@/components/ui/accordion'

interface ImportExhibitorForm extends React.HTMLAttributes<HTMLDivElement> {}

export function ImportExhibitorForm({ className }: ImportExhibitorForm) {
  const form = useFormContext<CreateBatchJobInput & { file?: File }>()
  const file = form.watch('file') as FileWithPath

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      if (!values.file) {
        return
      }

      const { data } = await FileService.uploadPrivate([values.file])
      const fileKey = data.uploads[0].key
      values.context.fileKey = fileKey
      values.type = 'store-import'

      delete values.file

      await BatchJobService.createBatchJob(values)

      toast({
        title: 'File uploaded',
        variant: 'success'
      })
    } catch (error) {
      console.error(error)
      if (isAxiosError(error) && error.response?.data.type == 'duplicate_error') {
        // TODO: need to handle errors here
      } else {
        form.setError('root', {})
      }

      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <>
      <form
        id="import-exhibitor-form"
        onSubmit={onSubmit}
        className={cn('mx-auto flex max-w-4xl flex-col space-y-8', className)}
      >
        <FormField
          control={form.control}
          name="file"
          rules={{ required: 'Please upload the csv file' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Upload CSV file</FormLabel>
              <FormControl>
                <Dropzone
                  multiple={false}
                  onDrop={(acceptedFiles) => {
                    const file = Object.assign(acceptedFiles[0])

                    form.setValue('file', file as File, {
                      shouldValidate: true
                    })
                  }}
                  description="Drop your csv file here, or click to browse"
                  accept=".csv"
                  files={file ? [file] : []}
                  {...field}
                />
              </FormControl>
              <FormDescription>Please follow the format exactly</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
      </form>
      <div className="pt-5 pb-5">
        <Separator className={cn('mx-auto flex max-w-4xl flex-col space-y-8', className)} />
      </div>
      <div className={cn('mx-auto flex max-w-4xl flex-col space-y-2', className)}>
        <h1 className="font-semibold">Guidelines</h1>

        <Accordion type="multiple" className="w-full">
          <AccordionItem value="create-company">
            <AccordionTrigger>A. Create/Update New Company</AccordionTrigger>
            <AccordionContent>
              <div className="space-y-5">
                'Company Name', 'Company Reg No', 'Company Address 1', 'Company City', 'Company
                Postcode', 'Company State', 'Company Country', 'Company Website', 'Company Contact
                Full Name', 'Company Contact Email', 'Company Contact Mobile Number' are{' '}
                <span className="font-semibold text-red-900">compulsory</span> fields. 'Company
                Contact Email' will be used to uniquely identify each row, create/update process
                will be carried out depending on if the Contact Email already exist.
                <br />
                <div className="bg-gray-300 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md">
                  1. 'Company Status' can only be 'approved', 'rejected', 'pending'. When leave
                  blank it will be assigned with 'approved'.
                </div>
                <div className="bg-gray-300 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md">
                  2. 'Company Blocked' or 'Company Admin Store' can only be 'TRUE' or 'FALSE'. Both
                  will be set to false when leave blank.
                </div>
                <div className="bg-gray-300 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md">
                  3. Please ensure 'Company Gst No', 'Company Name', 'Company Reg No' is unique and
                  does not belongs to any existing company. Each company must have different value
                  for these columns.
                </div>
                <div className="bg-gray-300 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md">
                  4. 'Company Password' will be assinged with 'abcd1234' when leave blank.
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
    </>
  )
}
