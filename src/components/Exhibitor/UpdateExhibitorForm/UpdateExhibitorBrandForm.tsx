import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import { <PERSON>alogClose, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { toast } from '@/components/ui/use-toast'
import ExhibitorService from '@/network/services/exhibitor'
import { Exhibitor } from '@/types/Exhibitor'
import isEqual from 'lodash.isequal'
import { FC } from 'react'
import { useForm } from 'react-hook-form'
import { mutate } from 'swr'

const UpdateExhibitorBrandForm: FC<{
  initialValues: Exhibitor
  setIsDialogOpen: (value: boolean) => void
}> = ({ initialValues, setIsDialogOpen }) => {
  const form = useForm<Exhibitor>({
    shouldUseNativeValidation: false,
    defaultValues: {
      brands: initialValues?.brands,
      brand_description: initialValues?.brand_description
    }
  })

  const onSubmit = form.handleSubmit(async (values) => {
    console.log('submit values', values)
    setIsDialogOpen(false)

    // block if no changes
    if (isEqual(values, initialValues)) {
      console.log('same')
      toast({
        title: 'No changes applied',
        variant: 'destructive'
      })

      return
    }

    try {
      const { data } = await ExhibitorService.updateExhibitor(initialValues.id, values)
      console.log('response', data)

      if (data.success) {
        setIsDialogOpen(false)
        mutate(
          (key) =>
            typeof key === 'string' && key.includes(ExhibitorService.getExhibitor(initialValues.id))
        )
        toast({
          title: 'Exhibitor brand updated',
          variant: 'success'
        })
      } else {
        toast({
          title: 'Action failed, please try again',
          variant: 'destructive'
        })
      }
    } catch (e) {
      console.log(e)
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <DialogContent className="max-h-[80vh] max-w-3xl overflow-y-auto">
      <DialogHeader>
        <DialogTitle className="flex items-center justify-between">
          Edit Exhibitor Brand
          <div className="flex space-x-2">
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button
              type="submit"
              form="update-exhibitor-brand-form"
              disabled={form.formState.isSubmitting}
              onClick={() => {
                form.clearErrors()
              }}
            >
              {form.formState.isSubmitting && (
                <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
              )}
              Save
            </Button>
          </div>
        </DialogTitle>
      </DialogHeader>

      <Form {...form}>
        <form id="update-exhibitor-brand-form" className="grid gap-4" onSubmit={onSubmit}>
          <FormField
            control={form.control}
            name="brands"
            rules={{ required: 'This is required' }}
            render={({ field }) => {
              return (
                <FormItem className="col-span-2 flex flex-col">
                  <FormLabel>Brands</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )
            }}
          />

          <FormField
            control={form.control}
            name="brand_description"
            rules={{ required: 'This is required' }}
            render={({ field }) => {
              return (
                <FormItem className="col-span-2 flex flex-col">
                  <FormLabel>Brand Description</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )
            }}
          />
        </form>
      </Form>
    </DialogContent>
  )
}

export default UpdateExhibitorBrandForm
