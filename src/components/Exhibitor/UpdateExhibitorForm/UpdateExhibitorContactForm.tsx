import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import { DialogClose, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { toast } from '@/components/ui/use-toast'
import ExhibitorService from '@/network/services/exhibitor'
import { Exhibitor } from '@/types/Exhibitor'
import isEqual from 'lodash.isequal'
import { FC } from 'react'
import { useForm } from 'react-hook-form'
import { mutate } from 'swr'

const UpdateExhibitorContactForm: FC<{
  variant?: 'finance' | 'marketing'
  initialValues: Exhibitor
  setIsDialogOpen: (value: boolean) => void
}> = ({ variant, initialValues, setIsDialogOpen }) => {
  const defaultContactValue = variant
    ? variant == 'finance'
      ? {
          finance_full_name: initialValues?.finance_full_name,
          finance_email: initialValues?.finance_email,
          finance_mobile_number: initialValues?.finance_mobile_number
        }
      : {
          marketing_full_name: initialValues?.marketing_full_name,
          marketing_email: initialValues?.marketing_email,
          marketing_mobile_number: initialValues?.marketing_mobile_number
        }
    : {
        contact_full_name: initialValues?.contact_full_name,
        contact_email: initialValues?.contact_email,
        contact_mobile_number: initialValues?.contact_mobile_number
      }

  const fullName = variant
    ? variant === 'finance'
      ? 'finance_full_name'
      : 'marketing_full_name'
    : 'contact_full_name'
  const email = variant
    ? variant === 'finance'
      ? 'finance_email'
      : 'marketing_email'
    : 'contact_email'
  const contactNumber = variant
    ? variant === 'finance'
      ? 'finance_mobile_number'
      : 'marketing_mobile_number'
    : 'contact_mobile_number'
  const position = variant
    ? variant == 'finance'
      ? 'Finance Contact'
      : 'Marketing Contact'
    : 'Contact'

  const form = useForm<Exhibitor>({
    shouldUseNativeValidation: false,
    defaultValues: defaultContactValue
  })

  const onSubmit = form.handleSubmit(async (values) => {
    console.log('submit values', values)
    setIsDialogOpen(false)

    // block if no changes
    if (isEqual(values, initialValues)) {
      console.log('same')
      toast({
        title: 'No changes applied',
        variant: 'destructive'
      })

      return
    }

    try {
      const { data } = await ExhibitorService.updateExhibitor(initialValues.id, values)
      console.log('response', data)

      if (data.success) {
        setIsDialogOpen(false)
        mutate(
          (key) =>
            typeof key === 'string' &&
            key.includes(ExhibitorService.getExhibitor(initialValues?.id))
        )
        toast({
          title: `${position} updated`,
          variant: 'success'
        })
      } else {
        toast({
          title: 'Action failed, please try again',
          variant: 'destructive'
        })
      }
    } catch (e) {
      console.log(e)
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <DialogContent className="max-w-3xl">
      <DialogHeader>
        <DialogTitle className="flex items-center justify-between">
          {`Edit Exhibitor ${position}`}
          <div className="flex space-x-2">
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button
              type="submit"
              form="update-exhibitor-contact-form"
              disabled={form.formState.isSubmitting}
              onClick={() => {
                form.clearErrors()
              }}
            >
              {form.formState.isSubmitting && (
                <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
              )}
              Save
            </Button>
          </div>
        </DialogTitle>
      </DialogHeader>

      <Form {...form}>
        <form id="update-exhibitor-contact-form" className="grid gap-4" onSubmit={onSubmit}>
          <FormField
            control={form.control}
            name={fullName}
            rules={{ required: 'This is required' }}
            render={({ field }) => {
              return (
                <FormItem className="col-span-2 flex flex-col">
                  <FormLabel>{position} Full Name</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )
            }}
          />

          <FormField
            control={form.control}
            name={email}
            rules={{ required: 'This is required' }}
            render={({ field }) => {
              return (
                <FormItem className="col-span-2 flex flex-col">
                  <FormLabel>{position} Email</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )
            }}
          />

          <FormField
            control={form.control}
            name={contactNumber}
            rules={{ required: 'This is required' }}
            render={({ field }) => {
              return (
                <FormItem className="col-span-2 flex flex-col">
                  <FormLabel>{position} Contact Number</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )
            }}
          />
        </form>
      </Form>
    </DialogContent>
  )
}

export default UpdateExhibitorContactForm
