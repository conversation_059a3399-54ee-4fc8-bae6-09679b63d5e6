import { Icons } from '@/components/icons'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from '@/components/ui/accordion'
import { Button } from '@/components/ui/button'
import { DialogClose, DialogContent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import { toast } from '@/components/ui/use-toast'
import ExhibitorService from '@/network/services/exhibitor'
import { Exhibitor } from '@/types/Exhibitor'
import isEqual from 'lodash.isequal'
import { FC } from 'react'
import { useForm } from 'react-hook-form'
import { mutate } from 'swr'

const UpdateExhibitorGeneralForm: FC<{
  initialValues: Exhibitor
  setIsDialogOpen: (value: boolean) => void
}> = ({ initialValues, setIsDialogOpen }) => {
  const form = useForm<Exhibitor>({
    shouldUseNativeValidation: false,
    defaultValues: {
      type: initialValues?.type,
      name: initialValues?.name,
      gst_no: initialValues?.gst_no,
      e_invoice: initialValues.e_invoice,
      company_reg_no: initialValues?.company_reg_no,
      facebook_or_instagram: initialValues?.facebook_or_instagram,
      website: initialValues?.website,
      address_1: initialValues?.address_1,
      address_2: initialValues?.address_2,
      state: initialValues?.state,
      country: initialValues?.country
    }
  })

  const onSubmit = form.handleSubmit(async (values) => {
    console.log('submit values', values)
    setIsDialogOpen(false)

    // block if no changes
    if (isEqual(values, initialValues)) {
      console.log('same')
      toast({
        title: 'No changes applied',
        variant: 'destructive'
      })

      return
    }

    try {
      const { data } = await ExhibitorService.updateExhibitor(initialValues.id, values)
      console.log('response', data)

      if (data.success) {
        setIsDialogOpen(false)
        mutate(
          (key) =>
            typeof key === 'string' && key.includes(ExhibitorService.getExhibitor(initialValues.id))
        )
        toast({
          title: 'Exhibitor general information updated',
          variant: 'success'
        })
      } else {
        toast({
          title: 'Action failed, please try again',
          variant: 'destructive'
        })
      }
    } catch (e) {
      console.log(e)
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle className="flex justify-between items-center">
          Edit Exhibitor Information
          <div className="flex space-x-2">
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button
              type="submit"
              form="update-exhibitor-contact-form"
              disabled={form.formState.isSubmitting}
              onClick={() => {
                form.clearErrors()
              }}
            >
              {form.formState.isSubmitting && (
                <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
              )}
              Save
            </Button>
          </div>
        </DialogTitle>
      </DialogHeader>

      <Form {...form}>
        <form id="update-exhibitor-contact-form" onSubmit={onSubmit}>
          <Accordion type="multiple" className="w-full" defaultValue={['general']}>
            <AccordionItem value="general">
              <AccordionTrigger disabled>General</AccordionTrigger>
              <AccordionContent>
                <div className="grid gap-4">
                  <FormField
                    control={form.control}
                    name="name"
                    rules={{ required: 'This is required' }}
                    render={({ field }) => {
                      return (
                        <FormItem className="flex flex-col col-span-2">
                          <FormLabel>Exhibitor Name</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )
                    }}
                  />

                  <FormField
                    control={form.control}
                    name="type"
                    rules={{ required: 'This is required' }}
                    render={({ field }) => {
                      return (
                        <FormItem className="flex flex-col col-span-2">
                          <FormLabel>Exhibitor Type</FormLabel>
                          <FormControl>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Choose an option" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="partner">Partners & Sponsors</SelectItem>
                                <SelectItem value="exhibitor">Exhibitor</SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )
                    }}
                  />

                  <FormField
                    control={form.control}
                    name="gst_no"
                    // rules={{ required: 'This is required' }}
                    render={({ field }) => {
                      return (
                        <FormItem className="flex flex-col col-span-2">
                          <FormLabel>GST Number</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )
                    }}
                  />

                  <FormField
                    control={form.control}
                    name="e_invoice"
                    // rules={{ required: 'This is required' }}
                    render={({ field }) => {
                      return (
                        <FormItem className="flex flex-col col-span-2">
                          <FormLabel>E-Invoice</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )
                    }}
                  />

                  <FormField
                    control={form.control}
                    name="company_reg_no"
                    rules={{ required: 'This is required' }}
                    render={({ field }) => {
                      return (
                        <FormItem className="flex flex-col col-span-2">
                          <FormLabel>Registration Number</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )
                    }}
                  />

                  {/* <FormField
                    control={form.control}
                    name="facebook_or_instagram"
                    rules={{ required: 'This is required' }}
                    render={({ field }) => {
                      return (
                        <FormItem className="flex flex-col col-span-2">
                          <FormLabel>Social Media</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )
                    }}
                  /> */}

                  <FormField
                    control={form.control}
                    name="website"
                    rules={{ required: 'This is required' }}
                    render={({ field }) => {
                      return (
                        <FormItem className="flex flex-col col-span-2">
                          <FormLabel>Website</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )
                    }}
                  />
                </div>
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="location">
              <AccordionTrigger>Location</AccordionTrigger>
              <AccordionContent>
                <div className="grid gap-4">
                  <FormField
                    control={form.control}
                    name="address_1"
                    rules={{ required: 'This is required' }}
                    render={({ field }) => {
                      return (
                        <FormItem className="flex flex-col col-span-2">
                          <FormLabel>Address Line 1</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )
                    }}
                  />

                  <FormField
                    control={form.control}
                    name="address_2"
                    rules={{ required: 'This is required' }}
                    render={({ field }) => {
                      return (
                        <FormItem className="flex flex-col col-span-2">
                          <FormLabel>Address Line 2</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )
                    }}
                  />

                  <FormField
                    control={form.control}
                    name="city"
                    rules={{ required: 'This is required' }}
                    render={({ field }) => {
                      return (
                        <FormItem className="flex flex-col col-span-2">
                          <FormLabel>City</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )
                    }}
                  />

                  <FormField
                    control={form.control}
                    name="postcode"
                    rules={{ required: 'This is required' }}
                    render={({ field }) => {
                      return (
                        <FormItem className="flex flex-col col-span-2">
                          <FormLabel>Postcode</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )
                    }}
                  />

                  <FormField
                    control={form.control}
                    name="state"
                    rules={{ required: 'This is required' }}
                    render={({ field }) => {
                      return (
                        <FormItem className="flex flex-col col-span-2">
                          <FormLabel>State</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )
                    }}
                  />

                  <FormField
                    control={form.control}
                    name="country"
                    rules={{ required: 'This is required' }}
                    render={({ field }) => {
                      return (
                        <FormItem className="flex flex-col col-span-2">
                          <FormLabel>Country</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )
                    }}
                  />
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </form>
      </Form>
    </DialogContent>
  )
}

export default UpdateExhibitorGeneralForm
