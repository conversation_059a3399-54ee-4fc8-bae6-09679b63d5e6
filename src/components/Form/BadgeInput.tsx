import * as React from 'react'

import { InputProps } from '../ui/input'
import { cn } from '@/lib/utils'
import { Badge } from '../ui/badge'
import { Cross2Icon } from '@radix-ui/react-icons'
import { useEnterKeydown } from '@/hooks/useEnterKeydown'
import { unique } from 'radash'

export interface BadgeInputProps extends InputProps {
  onTokenised: (values: string[]) => void
}

const BadgeInput = React.forwardRef<HTMLInputElement, BadgeInputProps>(
  ({ className, onTokenised, ...props }, ref) => {
    const innerRef = React.useRef<HTMLInputElement>(null)
    React.useImperativeHandle(ref, () => innerRef.current!, [])

    const [tokens, setTokens] = React.useState<string[]>([])

    const tokenise = (value: string) => {
      // split by comma
      const commaValues = value.split(',')
      commaValues.forEach((v) => v.trim())
      const newTokens = commaValues.filter((v) => v != '')
      // unique values only
      setTokens((prev) => {
        const mergedTokens = [...prev, ...newTokens]
        const uniqueTokens = unique(mergedTokens)
        return uniqueTokens
      })
    }

    const removeToken = (index: number) => {
      tokens.splice(index, 1)
      setTokens([...tokens])
    }

    const onBlur = (event: React.FocusEvent<HTMLInputElement>) => {
      const value = event.target.value

      if (value != '') {
        tokenise(value)
        event.target.value = ''
      }
    }

    const onChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      const value = event.target.value

      if (value.includes(',')) {
        tokenise(value)
        event.target.value = ''
      }
    }

    useEnterKeydown((e) => {
      e.preventDefault()
    }, innerRef)

    React.useEffect(() => {
      onTokenised(tokens)
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [tokens])

    return (
      <div
        className={cn(
          'flex flex-wrap w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 gap-2',
          className
        )}
      >
        {tokens.map((token, index) => (
          <Badge>
            {token}
            <Cross2Icon
              className="ml-1 h-3 w-3 cursor-pointer"
              onClick={() => {
                removeToken(index)
              }}
            />
          </Badge>
        ))}
        <input
          className="flex-1 bg-transparent focus:outline-none"
          placeholder={tokens.length == 0 ? 'Blue, Red, Black...' : ''}
          ref={innerRef}
          onBlur={onBlur}
          onChange={onChange}
          {...props}
        />
      </div>
    )
  }
)

BadgeInput.displayName = 'BadgeInput'

export { BadgeInput }
