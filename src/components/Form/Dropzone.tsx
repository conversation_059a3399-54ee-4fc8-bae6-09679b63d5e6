import React from 'react'
import { FileRejection, FileWithPath, useDropzone } from 'react-dropzone'

interface DropzoneProps {
  onDrop?: (acceptedFiles: FileWithPath[]) => void
  multiple?: boolean
  accept?: any
  description?: string
  files?: FileWithPath[] // uploaded files
  formName?: string
}

const Dropzone = React.forwardRef<HTMLInputElement, DropzoneProps>(
  (
    {
      onDrop,
      multiple = false,
      accept = { 'image/*': ['.jpeg', '.png', '.jpg'] },
      description,
      formName,
      ...props
    },
    ref
  ) => {
    const innerRef = React.useRef<HTMLInputElement>(null)
    React.useImperativeHandle(ref, () => innerRef.current!, [])

    const { getRootProps, getInputProps, fileRejections } = useDropzone({
      onDrop: onDrop,
      accept,
      multiple,
      ...props
    })

    const fileRejectionItems = fileRejections.map((file: FileRejection, index: number) => {
      return (
        <li key={index}>
          <p>File: {(file.file as FileWithPath)?.path} error</p>
        </li>
      )
    })

    return (
      <>
        <section className="container flex h-20 w-full cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300">
          <div {...getRootProps({ className: 'dropzone w-full h-full' })}>
            <input {...getInputProps()} />
            {(props.files?.length ?? 0) > 0 && multiple == false ? (
              <div className="text-greyLight flex h-full items-center justify-center">
                <p className="flex text-xs">File added: {props.files?.at(0)?.path}</p>
              </div>
            ) : (
              <div className="text-greyLight flex h-full flex-col items-center justify-center">
                <p className="text-xs">
                  {description ?? 'Drop your images here, or click to browse.'}
                </p>
                <p className="text-xs">
                  {formName == 'bulk-create-memory-form'
                    ? 'Recommended file size is less than 3mb.'
                    : 'Recommended file size is less than 3mb for slideshow banners & less than 1mb for thumbnails.'}
                </p>
                <p className="text-xs">
                  {formName == 'bulk-create-memory-form'
                    ? 'Recommended ratio is 2:1.'
                    : 'Recommended ratio is 2:1 for slideshow banners.'}
                </p>
              </div>
            )}
          </div>
        </section>
        {fileRejectionItems.length > 0 && (
          <div>
            <h4>Rejected files</h4>
            <ul className={'text-redMain'}>{fileRejectionItems}</ul>
          </div>
        )}
      </>
    )
  }
)

Dropzone.displayName = 'Dropzone'

export { Dropzone }
