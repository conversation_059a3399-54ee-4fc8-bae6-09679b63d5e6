import * as React from 'react'

import { Input, InputProps } from '../ui/input'
import { Button } from '../ui/button'
import { useState } from 'react'
import { EditIcon, SaveIcon, XIcon } from 'lucide-react'
import { Icons } from '../icons'
import { toast } from '../ui/use-toast'
import { AxiosResponse } from 'axios'
import { mutate } from 'swr'

export interface EditableSingleFieldProps extends InputProps {
  initialValue?: string | null
  fieldKey: string
  updateTargetId: number
  updateApiService: (id: number, data: any) => Promise<AxiosResponse<any, any>>
  mutateUrl: string
}

const EditableSingleField: React.FC<EditableSingleFieldProps> = ({
  initialValue,
  fieldKey,
  updateTargetId,
  updateApiService,
  mutateUrl
}) => {
  const [isEditting, setIsEditting] = useState<boolean>(false)
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false)
  const [fieldValue, setFieldValue] = useState<string | null>(null)

  // update function
  const updateField = async () => {
    if (fieldValue === initialValue && !fieldValue) {
      toast({
        title: 'No changes applied',
        variant: 'destructive'
      })
    }

    setIsSubmitting(true)
    try {
      const { data: response } = await updateApiService(updateTargetId, {
        [fieldKey]: fieldValue
      })

      if (response != null) {
        mutate((key) => typeof key === 'string' && key.startsWith(mutateUrl))
        toast({
          title: 'Update successful',
          variant: 'success'
        })
        setIsEditting(false)
      } else {
        toast({
          title: 'Action failed, please try again',
          variant: 'destructive'
        })
      }

      setIsSubmitting(false)
    } catch (e) {
      console.log(e)
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
      setIsSubmitting(false)
    }
  }

  return (
    <div className="flex w-full max-w-sm items-center space-x-2">
      <Input
        defaultValue={initialValue ?? ''}
        readOnly={!isEditting}
        onChange={(e) => setFieldValue(e.currentTarget.value)}
        onKeyUp={(e) => {
          if (isEditting && e.key === 'Enter' && !isSubmitting) {
            updateField()
          }
        }}
      />
      {isEditting && (
        <Button
          onClick={() => {
            if (!isSubmitting) {
              updateField()
            }
          }}
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <Icons.spinner className="h-4 w-4 animate-spin" />
          ) : (
            <SaveIcon size="16" />
          )}
        </Button>
      )}
      <Button onClick={() => setIsEditting(!isEditting)} disabled={isSubmitting}>
        {!isEditting ? <EditIcon size="16" /> : <XIcon size="16" />}
      </Button>
    </div>
  )
}

export default EditableSingleField
