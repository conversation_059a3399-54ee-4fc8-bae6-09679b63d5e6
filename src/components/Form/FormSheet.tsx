import { FC, PropsWithChildren, createContext, useEffect, useState } from 'react'

import { Button } from '../ui/button'
import { DrawerProps } from './schema'
import { Sheet, SheetClose, SheetContent, Sheet<PERSON>eader, Sheet<PERSON>itle, SheetTrigger } from '../ui/sheet'
import { PlusCircleIcon, SettingsIcon } from 'lucide-react'
import { useForm } from 'react-hook-form'

export const SheetContext = createContext<DrawerProps | undefined>(undefined)

const FormSheet: FC<
  PropsWithChildren<{ title: string; button: string; edit?: boolean; formId?: string }>
> = ({ children, edit = false, title, button, formId = '' }) => {
  const [open, setOpen] = useState(false)
  const form = useForm()

  useEffect(() => {
    if (form.formState.isSubmitSuccessful) {
      form.reset()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [form.formState.isSubmitSuccessful])

  return (
    <SheetContext.Provider
      value={{
        isOpen: open,
        setIsOpen: setOpen
      }}
    >
      <Sheet open={open} onOpenChange={setOpen}>
        <SheetTrigger asChild>
          <Button>
            {edit ? (
              <SettingsIcon size="16" className="mr-2" />
            ) : (
              <PlusCircleIcon size="16" className="mr-2" />
            )}
            {button}
          </Button>
        </SheetTrigger>

        <SheetContent side="bottom" className="w-full h-full overflow-scroll">
          <div className="mx-auto max-w-4xl  ">
            <SheetHeader className=" mx-auto pt-10 pb-6">
              <SheetTitle className="flex justify-between items-center">
                {title}
                <div className="flex space-x-2">
                  <Button
                    type="submit"
                    form={formId}
                    disabled={form.formState.isSubmitting}
                    onClick={() => {
                      form.clearErrors()
                    }}
                  >
                    Submit
                  </Button>

                  <SheetClose>
                    <Button
                      variant="outline"
                      onClick={() => {
                        form.reset()
                      }}
                      type="button"
                    >
                      Close
                    </Button>
                  </SheetClose>
                </div>
              </SheetTitle>
            </SheetHeader>
            {children}
          </div>
        </SheetContent>
      </Sheet>
    </SheetContext.Provider>
  )
}

export default FormSheet
