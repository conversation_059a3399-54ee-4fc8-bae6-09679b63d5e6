import { FC } from 'react'
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle, Content, Title } from '../ui/card'
import { Inquiry } from '@/types/Inquiry'
import { DateTime } from 'luxon'

const InquiryInfoCard: FC<{
  inquiry: Inquiry
}> = ({ inquiry }) => {
  return (
    <>
      <Card>
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <CardTitle>General Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-2">
            <Title>Id</Title>
            <Content>{inquiry?.id ?? '-'}</Content>
            <Title>First Name</Title>
            <Content>{inquiry?.first_name ?? '-'}</Content>
            <Title>Email</Title>
            <Content>{inquiry?.email ?? '-'}</Content>
            <Title>Mobile Number</Title>
            <Content>{inquiry?.mobile_number ?? '-'}</Content>
            <Title>Inquiry Type</Title>
            <Content>{inquiry?.inquiry_type ?? '-'}</Content>
            <Title>Message</Title>
            <Content>{inquiry?.message ?? '-'}</Content>
            <Title>Date</Title>
            <Content>
              {DateTime.fromISO(inquiry?.created_at).toFormat('yyyy-MM-dd HH:mm:ss') + '' ?? '-'}
            </Content>
          </div>
        </CardContent>
      </Card>
    </>
  )
}

export default InquiryInfoCard
