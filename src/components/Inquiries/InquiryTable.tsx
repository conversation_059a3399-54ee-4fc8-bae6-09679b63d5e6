import InquiryService, { InquiryResponse } from '@/network/services/inquiry'
import { ColumnDef, Row, createColumnHelper } from '@tanstack/react-table'
import { DataTable, FilterColumn } from '@/components/Table/DataTable'
import { useNavigate } from 'react-router-dom'
import { serialize } from '@/network/request'
import { Inquiry } from '@/types/Inquiry'
import { DateTime } from 'luxon'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu'
import { Button } from '../ui/button'
import { MoreHorizontal, Trash2Icon } from 'lucide-react'
import { FC } from 'react'
import { useToast } from '../ui/use-toast'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger
} from '../ui/alert-dialog'
import { mutate } from 'swr'

const columnHelper = createColumnHelper<Inquiry>()
const columns: ColumnDef<Inquiry>[] = [
  {
    accessorKey: 'id',
    header: 'Id'
  },
  {
    accessorKey: 'first_name',
    header: 'First Name'
  },
  {
    accessorKey: 'last_name',
    header: 'Last Name'
  },
  {
    accessorKey: 'email',
    header: 'Email'
  },
  {
    accessorKey: 'mobile_number',
    header: 'Mobile Number'
  },
  {
    accessorKey: 'inquiry_type',
    header: 'Inquiry Type'
  },
  {
    accessorKey: 'created_at',
    header: 'Date',
    cell: (props) => DateTime.fromISO(props.row.original.created_at).toFormat('yyyy-MM-dd HH:mm:ss')
  },
  columnHelper.display({
    id: 'actions',
    cell: (props) => <RowActions row={props.row} />
  })
]

const columnFilter: FilterColumn[] = [
  { columnKey: 'email', header: 'Email', dataType: 'string' },
  // { columnKey: 'mobile_number', header: 'Mobile Number', dataType: 'string' },
  {
    columnKey: 'inquiry_type',
    header: 'Inquiry Type',
    dataType: 'faceted',
    options: [
      { value: 'general_enquiry', label: 'GENERAL INQUIRY', icon: undefined },
      { value: 'business', label: 'BUSINESS', icon: undefined },
      { value: 'collab', label: 'COLLAB', icon: undefined },
      { value: 'press', label: 'PRESS', icon: undefined },
      { value: 'career', label: 'CAREER', icon: undefined }
    ]
  }
]

const RowActions: FC<{ row: Row<Inquiry> }> = ({ row }) => {
  const IinquiryId = row.original.id
  const { toast } = useToast()

  return (
    <div className="w-full flex justify-end">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0" onClick={(e) => e.stopPropagation()}>
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuGroup>
            <DropdownMenuItem className="space-x-2">
              <AlertDialog key="reject-brand">
                <AlertDialogTrigger
                  type="button"
                  onClick={(e) => e.stopPropagation()}
                  className="flex space-x-2 w-full"
                >
                  <Trash2Icon size="16" />
                  <span>Delete</span>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>
                      Are you absolutely sure to delete the inquiry with id {row.original.id}?
                    </AlertDialogTitle>
                    <AlertDialogDescription className="text-red-500">
                      *NOTE: this action cannot be undone
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={async (event) => {
                        event.stopPropagation()

                        try {
                          await InquiryService.deleteInquiry(IinquiryId)
                          mutate(
                            (key) =>
                              typeof key === 'string' && key.startsWith(InquiryService.getInquiries)
                          )
                          toast({
                            description: 'Inquiry deleted',
                            variant: 'destructive'
                          })
                        } catch (error) {
                          console.log(error)
                        }
                      }}
                    >
                      Confirm
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </DropdownMenuItem>
          </DropdownMenuGroup>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}

const InquiryTable = () => {
  const nav = useNavigate()

  return (
    <DataTable<Inquiry, unknown, InquiryResponse>
      columns={columns}
      filterColumns={columnFilter}
      swrService={serialize(InquiryService.getInquiries, {})}
      pageParam="offset"
      limitParam="limit"
      sortParam="sort"
      sortColumns={['id', 'first_name', 'last_name', 'email', 'inquiry_type', 'created_at']}
      toRow={InquiryService.toRow}
      toPaginate={InquiryService.toPaginate}
      onRowClick={(row: Row<Inquiry>) => {
        nav(`/inquiries/${row.original.id}`)
      }}
    />
  )
}

export default InquiryTable
