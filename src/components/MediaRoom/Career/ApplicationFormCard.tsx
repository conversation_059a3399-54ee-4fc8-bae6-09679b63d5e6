import { Icons } from '@/components/icons'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON>, CardContent, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card'
import SortableItem from '@/components/ui/drag-and-drop'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { useToast } from '@/components/ui/use-toast'
import { IDataResponse } from '@/network/request'
import MediaRoomService from '@/network/services/media_room'
import { ApplicationFormField } from '@/types/Cms'
import {
  DndContext,
  DragEndEvent,
  KeyboardSensor,
  PointerSensor,
  UniqueIdentifier,
  closestCenter,
  useSensor,
  useSensors
} from '@dnd-kit/core'
import {
  SortableContext,
  arrayMove,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy
} from '@dnd-kit/sortable'
import {
  EditIcon,
  GripHorizontalIcon,
  MoreHorizontal,
  PlusCircleIcon,
  SaveIcon,
  Trash2Icon
} from 'lucide-react'
import { FC, useEffect, useState } from 'react'
import useSWR, { mutate } from 'swr'
import { CreateApplicationFormFieldForm } from './CreateApplicationFormFieldForm'
import { UpdateApplicationFormFieldForm } from './UpdateApplicationFormFieldForm'

const ApplicationFormCard = () => {
  const { data, isLoading, error } = useSWR<IDataResponse<ApplicationFormField>>(
    MediaRoomService.findApplicationFormFields
  )

  if (error) {
    return <></>
  }

  if (isLoading) {
    return <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
  }

  const formFields = MediaRoomService.toRow(data)

  return <ApplicationForm initialValue={formFields} />
}

const ApplicationForm: FC<{ initialValue: ApplicationFormField[] }> = ({ initialValue }) => {
  const { toast } = useToast()
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false)
  const [fieldSelected, setFieldSelected] = useState<ApplicationFormField | null>(null)
  const [isSorting, setIsSorting] = useState(false)

  // sorting sections
  const [sortableFormField, setSortableFormField] = useState(
    initialValue.map((field) => ({ ...field, id: field.id as UniqueIdentifier }))
  )
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates
    })
  )

  function handleDragEnd(event: DragEndEvent) {
    const { active, over } = event

    if (active.id !== over?.id) {
      setSortableFormField((items) => {
        const oldIndex = items.map((item) => item.id).indexOf(active.id)
        const newIndex = items.map((item) => item.id).indexOf(over!.id)

        return arrayMove(items, oldIndex, newIndex)
      })
    }
  }

  useEffect(() => {
    setSortableFormField(
      initialValue.map((field) => ({ ...field, id: field.id as UniqueIdentifier }))
    )
  }, [initialValue])

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0">
        <CardTitle>Application Form</CardTitle>
        <div className="flex flex-row gap-x-2">
          <Button type="button" onClick={() => setIsCreateDialogOpen(true)}>
            <PlusCircleIcon size="16" className="mr-2" />
            Add Question
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-8">
        <div className="mt-2 flex flex-col space-y-4">
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <SortableContext items={sortableFormField ?? []} strategy={verticalListSortingStrategy}>
              {sortableFormField.map((field, i) => (
                <div key={field.id} className="grid grid-cols-[1fr_36px] items-center gap-x-2">
                  <SortableItem
                    id={field.id as UniqueIdentifier}
                    className="grid grid-cols-[18px_1fr] items-center gap-x-2"
                  >
                    <GripHorizontalIcon size={16} />
                    <div className="flex flex-col">
                      <p>
                        Question {i + 1} - {field.name}
                      </p>
                    </div>
                  </SortableItem>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        className="space-x-2"
                        onClick={() => {
                          setIsUpdateDialogOpen(true)
                          setFieldSelected({
                            ...field,
                            id: field.id as number
                          })
                        }}
                      >
                        <EditIcon size={16} />
                        <span>Edit field</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={async (e) => {
                          e.stopPropagation()

                          try {
                            const { data: response } =
                              await MediaRoomService.deleteApplicationFormField(field.id)

                            if (response.success) {
                              toast({
                                title: `Delete question successfully`,
                                variant: 'success'
                              })

                              mutate(
                                (key) =>
                                  typeof key === 'string' &&
                                  key.startsWith(MediaRoomService.findApplicationFormFields)
                              )
                            }
                          } catch (error) {
                            toast({
                              title: 'Action failed, please try again',
                              variant: 'destructive'
                            })
                          }
                        }}
                        className="space-x-2"
                      >
                        <Trash2Icon size="16" color="red" />
                        <span className="text-red-500">Delete</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              ))}
            </SortableContext>
          </DndContext>
        </div>

        <CreateApplicationFormFieldForm
          isDialogOpen={isCreateDialogOpen}
          setIsDialogOpen={setIsCreateDialogOpen}
        />
        {fieldSelected && (
          <>
            <UpdateApplicationFormFieldForm
              isDialogOpen={isUpdateDialogOpen}
              setIsDialogOpen={setIsUpdateDialogOpen}
              formField={fieldSelected}
            />
          </>
        )}
      </CardContent>
      <CardFooter className="flex justify-end">
        {sortableFormField.length > 0 && (
          <Button
            type="button"
            variant="outline"
            disabled={isSorting}
            onClick={async () => {
              setIsSorting(true)

              try {
                const ids = sortableFormField.map((field) => field.id) as number[]

                const { data: response } = await MediaRoomService.sortApplicationFormField({ ids })
                if (response.success) {
                  toast({
                    title: 'Sort questions successfully',
                    variant: 'success'
                  })
                  mutate(
                    (key) =>
                      typeof key === 'string' &&
                      key.startsWith(MediaRoomService.findApplicationFormFields)
                  )
                }
              } catch (error) {
                toast({
                  title: 'Action failed, please try again',
                  variant: 'destructive'
                })
                setSortableFormField(
                  initialValue.map((field) => ({
                    ...field,
                    id: field.id as UniqueIdentifier
                  }))
                )
              }

              setIsSorting(false)
            }}
          >
            {isSorting ? (
              <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <SaveIcon size="16" className="mr-2" />
            )}
            Save Ordering
          </Button>
        )}
      </CardFooter>
    </Card>
  )
}

export default ApplicationFormCard
