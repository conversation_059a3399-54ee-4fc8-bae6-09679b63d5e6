import { CampaignFileExtensionsInput } from '@/components/Campaign/UpdateCampaignForm/CampaignFileExtensionsInput'
import { CampaignOptionsInput } from '@/components/Campaign/UpdateCampaignForm/CampaignOptionsInput'
import { Icons } from '@/components/icons'
import { But<PERSON> } from '@/components/ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { Switch } from '@/components/ui/switch'
import { useToast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import MediaRoomService from '@/network/services/media_room'
import { CreateApplicationFormFieldInput } from '@/types/Cms'
import { capitalize } from 'radash'
import { FC } from 'react'
import { useForm } from 'react-hook-form'
import { mutate } from 'swr'

interface CreateApplicationFormFieldFormProps extends React.HTMLAttributes<HTMLDivElement> {
  isDialogOpen: boolean
  setIsDialogOpen: (value: boolean) => void
}

export const CreateApplicationFormFieldForm: FC<CreateApplicationFormFieldFormProps> = ({
  isDialogOpen,
  setIsDialogOpen,
  className
}) => {
  const { toast } = useToast()
  const form = useForm<CreateApplicationFormFieldInput>({
    defaultValues: {
      props: {
        text_props: {
          textarea: false
        }
      }
    },
    shouldUseNativeValidation: false
  })

  const onSubmit = form.handleSubmit(async (values) => {
    console.log('submit values: ', values)
    try {
      const { data: response } = await MediaRoomService.createApplicationFormField(values)
      console.log(response)
      if (response.success) {
        mutate(
          (key) =>
            typeof key === 'string' && key.startsWith(MediaRoomService.findApplicationFormFields)
        )
        toast({
          title: 'Application question created successfully',
          variant: 'success'
        })
        setIsDialogOpen(false)
        form.reset()
      }
    } catch (error) {
      console.error(error)
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <Dialog
      open={isDialogOpen}
      onOpenChange={(isOpen) => {
        setIsDialogOpen(isOpen)
      }}
    >
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            New Question
            <div className="flex space-x-2">
              <DialogClose type="button" asChild>
                <Button variant="outline">Cancel</Button>
              </DialogClose>
              <Button
                type="submit"
                form="create-application-form-field-form"
                disabled={form.formState.isSubmitting}
                onClick={() => {
                  form.clearErrors()
                }}
              >
                {form.formState.isSubmitting && (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                )}
                Save
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form
            id="create-application-form-field-form"
            onSubmit={onSubmit}
            className={cn('flex flex-col space-y-8', className)}
          >
            <FormField
              control={form.control}
              name="name"
              rules={{ required: 'Please enter the name' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name*</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="type"
              rules={{ required: 'Please enter the type' }}
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Type</FormLabel>
                  <FormControl>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose an option" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {['email', 'text', 'number', 'datetime', 'file', 'options', 'checkbox'].map(
                          (value) => {
                            return (
                              <SelectItem key={value} value={value}>
                                {capitalize(value)}
                              </SelectItem>
                            )
                          }
                        )}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="required"
              // rules={{ required: 'Please select' }}
              render={({ field }) => (
                <FormItem className="flex items-center space-y-0 space-x-2">
                  <FormControl>
                    <Switch checked={field.value} onCheckedChange={field.onChange} aria-readonly />
                  </FormControl>
                  <FormLabel>{field.value ? 'Required' : 'Optional'}</FormLabel>
                  <FormMessage />
                </FormItem>
              )}
            />

            {form.watch('type') == 'text' && (
              <>
                <Separator className="col-span-2 my-2" />
                <div className="col-span-2 text-sm font-semibold">Properties</div>
                <FormField
                  control={form.control}
                  name="props.text_props.textarea"
                  render={({ field }) => (
                    <FormItem className="flex items-center space-y-0 space-x-2">
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          aria-readonly
                        />
                      </FormControl>
                      <FormLabel>{field.value ? 'TextArea' : 'Text'}</FormLabel>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {form.watch('props.text_props.textarea') == true && (
                  <>
                    <FormField
                      control={form.control}
                      name="props.text_props.min"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Min</FormLabel>
                          <FormControl>
                            <Input type="number" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="props.text_props.max"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Max</FormLabel>
                          <FormControl>
                            <Input type="number" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                )}
              </>
            )}

            {form.watch('type') == 'number' && (
              <>
                <Separator className="col-span-2 my-2" />
                <div className="col-span-2 text-sm font-semibold">Properties</div>

                <FormField
                  control={form.control}
                  name="props.number_props.min"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Min</FormLabel>
                      <FormControl>
                        <Input type="number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="props.number_props.max"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Max</FormLabel>
                      <FormControl>
                        <Input type="number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </>
            )}

            {form.watch('type') == 'file' && (
              <>
                <Separator className="col-span-2 my-2" />
                <div className="col-span-2 text-sm font-semibold">Properties</div>

                <CampaignFileExtensionsInput />

                <FormField
                  control={form.control}
                  name="props.file_props.size_limit"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Size Limit</FormLabel>
                      <FormControl>
                        <Input placeholder="in MB" type="number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </>
            )}

            {form.watch('type') == 'options' && (
              <>
                <Separator className="col-span-2 my-2" />
                <div className="col-span-2 text-sm font-semibold">Properties</div>

                <CampaignOptionsInput />
              </>
            )}
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
