import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>lose,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { useToast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import MediaRoomService from '@/network/services/media_room'
import { CreateJobCategoryInput } from '@/types/Cms'
import { FC } from 'react'
import { useForm } from 'react-hook-form'
import { mutate } from 'swr'

interface CreateJobCategoryFormProps extends React.HTMLAttributes<HTMLDivElement> {
  isDialogOpen: boolean
  setIsDialogOpen: (value: boolean) => void
}

export const CreateJobCategoryForm: FC<CreateJobCategoryFormProps> = ({
  isDialogOpen,
  setIsDialogOpen,
  className
}) => {
  const { toast } = useToast()
  const form = useForm<CreateJobCategoryInput>({
    shouldUseNativeValidation: false
  })

  const onSubmit = form.handleSubmit(async (values) => {
    console.log('submit values: ', values)
    try {
      const { data: response } = await MediaRoomService.createJobCategory(values)
      console.log(response)
      if (response.success) {
        mutate((key) => typeof key === 'string' && key.startsWith(MediaRoomService.findJobCategories))
        toast({
          title: 'Job category created successfully',
          variant: 'success'
        })
        setIsDialogOpen(false)
        form.reset()
      }
    } catch (error) {
      console.error(error)
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <Dialog
      open={isDialogOpen}
      onOpenChange={(isOpen) => {
        setIsDialogOpen(isOpen)
      }}
    >
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            New Job Category
            <div className="flex space-x-2">
              <DialogClose type="button" asChild>
                <Button variant="outline">Cancel</Button>
              </DialogClose>
              <Button
                type="submit"
                form="create-job-category-form"
                disabled={form.formState.isSubmitting}
                onClick={() => {
                  form.clearErrors()
                }}
              >
                {form.formState.isSubmitting && (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                )}
                Save
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form
            id="create-job-category-form"
            onSubmit={onSubmit}
            className={cn('flex flex-col space-y-8', className)}
          >
            <FormField
              control={form.control}
              name="title"
              rules={{ required: 'Please enter the title' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Title*</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
