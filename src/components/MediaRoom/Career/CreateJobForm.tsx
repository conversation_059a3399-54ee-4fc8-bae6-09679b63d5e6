import EditorComponent from '@/components/Editor/Editor'
import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import { useToast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import MediaRoomService from '@/network/services/media_room'
import { CreateJobInput, JobNature } from '@/types/Cms'
import { capitalize, title } from 'radash'
import { FC } from 'react'
import { useForm } from 'react-hook-form'
import { mutate } from 'swr'

interface CreateJobFormProps extends React.HTMLAttributes<HTMLDivElement> {
  jobCategoryId: number
  isDialogOpen: boolean
  setIsDialogOpen: (value: boolean) => void
}

export const CreateJobForm: FC<CreateJobFormProps> = ({
  jobCategoryId,
  isDialogOpen,
  setIsDialogOpen,
  className
}) => {
  const { toast } = useToast()
  const form = useForm<CreateJobInput>({
    shouldUseNativeValidation: false,
    defaultValues: {
      category_id: jobCategoryId
    }
  })

  const onSubmit = form.handleSubmit(async (values) => {
    console.log('submit values: ', values)
    try {
      const { data: response } = await MediaRoomService.createJob(values)

      if (response.success) {
        mutate((key) => typeof key === 'string' && key.startsWith(MediaRoomService.findJobs))
        // TODO: auto refresh in FAQForm doesn't work
        mutate(
          (key) => typeof key === 'string' && key.startsWith(MediaRoomService.findJobCategories)
        )
        toast({
          title: 'Job created successfully',
          variant: 'success'
        })
        setIsDialogOpen(false)
      }
    } catch (error) {
      console.error(error)
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <Dialog
      open={isDialogOpen}
      onOpenChange={(isOpen) => {
        setIsDialogOpen(isOpen)
      }}
    >
      <DialogContent className="max-w-3xl overflow-y-auto max-h-screen max-w-fit">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            New Job
            <div className="flex space-x-2">
              <DialogClose type="button" asChild>
                <Button variant="outline">Cancel</Button>
              </DialogClose>
              <Button
                type="submit"
                form="create-job-form"
                disabled={form.formState.isSubmitting}
                onClick={() => {
                  form.clearErrors()
                }}
              >
                {form.formState.isSubmitting && (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                )}
                Save
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form
            id="create-job-form"
            onSubmit={onSubmit}
            className={cn('flex flex-col space-y-8', className)}
          >
            <FormField
              control={form.control}
              name="title"
              rules={{ required: 'Please enter the title' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Title*</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              rules={{ required: 'Please enter the description' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description*</FormLabel>
                  <FormControl>
                    <EditorComponent content={field.value} onChange={field.onChange} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="nature"
              rules={{ required: 'Please enter the nature' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nature*</FormLabel>
                  <FormControl>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose an option" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.values(JobNature).map((value) => {
                          return (
                            <SelectItem key={value} value={value}>
                              {title(value)}
                            </SelectItem>
                          )
                        })}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="state"
              rules={{ required: 'Please enter the state' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>State*</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="country"
              rules={{ required: 'Please enter the country' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Country*</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="vacancy_no"
              rules={{ required: 'Please enter the number of vacancies' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Number of vacancies*</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="status"
              rules={{ required: 'Please enter the status' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Status*</FormLabel>
                  <FormControl>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose an option" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {['open', 'close'].map((value) => {
                          return (
                            <SelectItem key={value} value={value}>
                              {capitalize(value)}
                            </SelectItem>
                          )
                        })}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="metadata.roles_and_responsibilities"
              rules={{ required: 'Please enter the description' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Your roles & responsibilities</FormLabel>
                  <FormControl>
                    <EditorComponent content={field.value} onChange={field.onChange} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="metadata.other_responsibilities"
              rules={{ required: 'Please enter the description' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Your other responsibilities</FormLabel>
                  <FormControl>
                    <EditorComponent content={field.value} onChange={field.onChange} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="metadata.be_awegood"
              rules={{ required: 'Please enter the description' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Why be awegood</FormLabel>
                  <FormControl>
                    <EditorComponent content={field.value} onChange={field.onChange} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="metadata.perks"
              rules={{ required: 'Please enter the description' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>The perks</FormLabel>
                  <FormControl>
                    <EditorComponent content={field.value} onChange={field.onChange} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="metadata.disclaimer"
              rules={{ required: 'Please enter the description' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Disclaimer</FormLabel>
                  <FormControl>
                    <EditorComponent content={field.value} onChange={field.onChange} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
