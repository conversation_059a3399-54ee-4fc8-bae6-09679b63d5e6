import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card'
import SortableItem from '@/components/ui/drag-and-drop'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { Sheet, SheetClose, SheetContent, SheetHeader, SheetTitle } from '@/components/ui/sheet'
import { useToast } from '@/components/ui/use-toast'
import { IDataResponse, serialize } from '@/network/request'
import MediaRoomService from '@/network/services/media_room'
import { Job, JobCategory } from '@/types/Cms'
import {
  DndContext,
  DragEndEvent,
  KeyboardSensor,
  PointerSensor,
  UniqueIdentifier,
  closestCenter,
  useSensor,
  useSensors
} from '@dnd-kit/core'
import {
  SortableContext,
  arrayMove,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy
} from '@dnd-kit/sortable'
import {
  EditIcon,
  GripHorizontalIcon,
  MoreHorizontal,
  PlusCircleIcon,
  SaveIcon,
  Trash2Icon
} from 'lucide-react'
import { FC, useEffect, useState } from 'react'
import useSWR, { mutate } from 'swr'
import { CreateJobForm } from './CreateJobForm'
import { CreateJobCategoryForm } from './CreateJobCategoryForm'
import { UpdateJobForm } from './UpdateJobForm'
import { UpdateJobCategoryForm } from './UpdateJobCategoryForm'
import { title } from 'radash'

const JobCategoryCard = () => {
  const { data, isLoading, error } = useSWR<IDataResponse<JobCategory>>(
    serialize(MediaRoomService.findJobCategories, { pagination: 0 })
  )

  if (error) {
    return <></>
  }

  if (isLoading) {
    return <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
  }

  const jobCategories = MediaRoomService.toRow(data)

  return <JobCategoryForm initialValue={jobCategories} />
}

const JobCategoryForm: FC<{ initialValue: JobCategory[] }> = ({ initialValue }) => {
  const { toast } = useToast()
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false)
  const [isEditJobDialogOpen, setIsEditJobDialogOpen] = useState(false)
  const [jobCategorySelected, setJobCategorySelected] = useState<JobCategory | null>(null)
  const [isSorting, setIsSorting] = useState(false)

  // sorting sections
  const [sortableJobCategory, setSortableJobCategory] = useState(
    initialValue.map((faqGroup) => ({ ...faqGroup, id: faqGroup.id as UniqueIdentifier }))
  )
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates
    })
  )

  function handleDragEnd(event: DragEndEvent) {
    const { active, over } = event

    if (active.id !== over?.id) {
      setSortableJobCategory((items) => {
        const oldIndex = items.map((item) => item.id).indexOf(active.id)
        const newIndex = items.map((item) => item.id).indexOf(over!.id)

        return arrayMove(items, oldIndex, newIndex)
      })
    }
  }

  useEffect(() => {
    setSortableJobCategory(
      initialValue.map((faqGroup) => ({ ...faqGroup, id: faqGroup.id as UniqueIdentifier }))
    )
  }, [initialValue])

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0">
        <CardTitle>Job Categories</CardTitle>
        <div className="flex flex-row gap-x-2">
          <Button type="button" onClick={() => setIsCreateDialogOpen(true)}>
            <PlusCircleIcon size="16" className="mr-2" />
            Add Job Category
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-8">
        <div className="mt-2 flex flex-col space-y-4">
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <SortableContext
              items={sortableJobCategory ?? []}
              strategy={verticalListSortingStrategy}
            >
              {sortableJobCategory.map((jobCategory) => (
                <div
                  key={jobCategory.id}
                  className="grid grid-cols-[1fr_36px] items-center gap-x-2"
                >
                  <SortableItem
                    id={jobCategory.id as UniqueIdentifier}
                    className="grid grid-cols-[18px_1fr] items-center gap-x-2"
                  >
                    <GripHorizontalIcon size={16} />
                    <div className="flex flex-col">
                      <p>{jobCategory.title}</p>
                    </div>
                  </SortableItem>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        className="space-x-2"
                        onClick={() => {
                          setIsEditJobDialogOpen(true)
                          setJobCategorySelected({
                            ...jobCategory,
                            id: jobCategory.id as number
                          })
                        }}
                      >
                        <EditIcon size={16} />
                        <span>Edit jobs</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="space-x-2"
                        onClick={() => {
                          setIsUpdateDialogOpen(true)
                          setJobCategorySelected({
                            ...jobCategory,
                            id: jobCategory.id as number
                          })
                        }}
                      >
                        <EditIcon size={16} />
                        <span>Edit category</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={async (e) => {
                          e.stopPropagation()

                          try {
                            const { data: response } = await MediaRoomService.deleteJobCategory(
                              jobCategory.id
                            )

                            if (response.success) {
                              toast({
                                title: `Delete job category successfully`,
                                variant: 'success'
                              })

                              mutate(
                                (key) =>
                                  typeof key === 'string' &&
                                  key.startsWith(MediaRoomService.findJobCategories)
                              )
                            }
                          } catch (error) {
                            toast({
                              title: 'Action failed, please try again',
                              variant: 'destructive'
                            })
                          }
                        }}
                        className="space-x-2"
                      >
                        <Trash2Icon size="16" color="red" />
                        <span className="text-red-500">Delete</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              ))}
            </SortableContext>
          </DndContext>
        </div>

        <CreateJobCategoryForm
          isDialogOpen={isCreateDialogOpen}
          setIsDialogOpen={setIsCreateDialogOpen}
        />
        {jobCategorySelected && (
          <>
            <UpdateJobCategoryForm
              isDialogOpen={isUpdateDialogOpen}
              setIsDialogOpen={setIsUpdateDialogOpen}
              jobCategory={jobCategorySelected}
            />
            <JobForm
              isSheetOpen={isEditJobDialogOpen}
              setIsSheetOpen={setIsEditJobDialogOpen}
              jobCategory={jobCategorySelected}
            />
          </>
        )}
      </CardContent>
      <CardFooter className="flex justify-end">
        {sortableJobCategory.length > 0 && (
          <Button
            type="button"
            variant="outline"
            disabled={isSorting}
            onClick={async () => {
              setIsSorting(true)

              try {
                const ids = sortableJobCategory.map((jobCategory) => jobCategory.id) as number[]

                const { data: response } = await MediaRoomService.sortFaqGroup({ ids })
                if (response.success) {
                  toast({
                    title: 'Sort job categories successfully',
                    variant: 'success'
                  })
                  mutate(
                    (key) =>
                      typeof key === 'string' && key.startsWith(MediaRoomService.findJobCategories)
                  )
                }
              } catch (error) {
                toast({
                  title: 'Action failed, please try again',
                  variant: 'destructive'
                })
                setSortableJobCategory(
                  initialValue.map((jobCategory) => ({
                    ...jobCategory,
                    id: jobCategory.id as UniqueIdentifier
                  }))
                )
              }

              setIsSorting(false)
            }}
          >
            {isSorting ? (
              <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <SaveIcon size="16" className="mr-2" />
            )}
            Save Ordering
          </Button>
        )}
      </CardFooter>
    </Card>
  )
}

const JobForm: FC<{
  jobCategory: JobCategory
  isSheetOpen: boolean
  setIsSheetOpen: (value: boolean) => void
}> = ({ jobCategory, isSheetOpen, setIsSheetOpen }) => {
  const { toast } = useToast()
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false)
  const [selectedJob, setSelectedJob] = useState<Job | null>(null)
  const [isSorting, setIsSorting] = useState(false)

  // sorting sections
  const [sortableJob, setSortableJob] = useState(
    jobCategory.jobs.map((job) => ({ ...job, id: job.id as UniqueIdentifier }))
  )
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates
    })
  )

  function handleDragEnd(event: DragEndEvent) {
    const { active, over } = event

    if (active.id !== over?.id) {
      setSortableJob((items) => {
        const oldIndex = items.map((item) => item.id).indexOf(active.id)
        const newIndex = items.map((item) => item.id).indexOf(over!.id)

        return arrayMove(items, oldIndex, newIndex)
      })
    }
  }

  useEffect(() => {
    setSortableJob(jobCategory.jobs.map((job) => ({ ...job, id: job.id as UniqueIdentifier })))
  }, [jobCategory.jobs])

  return (
    <Sheet
      open={isSheetOpen}
      onOpenChange={(isOpen) => {
        setIsSheetOpen(isOpen)
      }}
    >
      <SheetContent side="bottom" className="h-full w-full">
        <SheetHeader className="mx-auto max-w-4xl pb-6 pt-10">
          <SheetTitle className="flex items-center justify-between">
            {`${jobCategory.title} Jobs`}
            <div className="flex space-x-2">
              <SheetClose>
                <Button variant="outline">Cancel</Button>
              </SheetClose>
              <div className="flex flex-row gap-x-2">
                <Button type="button" onClick={() => setIsCreateDialogOpen(true)}>
                  <PlusCircleIcon size="16" className="mr-2" />
                  New Job
                </Button>
              </div>
            </div>
          </SheetTitle>
        </SheetHeader>
        <div className={'mx-auto flex max-w-4xl flex-col space-y-8'}>
          <Card>
            <CardContent className="space-y-8">
              <div className="mt-2 flex flex-col space-y-4">
                <DndContext
                  sensors={sensors}
                  collisionDetection={closestCenter}
                  onDragEnd={handleDragEnd}
                >
                  <SortableContext items={sortableJob ?? []} strategy={verticalListSortingStrategy}>
                    {sortableJob.map((job) => (
                      <div key={job.id} className="grid grid-cols-[1fr_36px] items-center gap-x-2">
                        <SortableItem
                          id={job.id as UniqueIdentifier}
                          className="grid grid-cols-[18px_1fr] items-center gap-x-2"
                        >
                          <GripHorizontalIcon size={16} />
                          <div className="flex flex-col">
                            <p>
                              {job.title}{' '}
                              <span className="text-gray-500">
                                - {job.vacancy_no} {job.vacancy_no > 1 ? 'vacancies' : 'vacancy'}
                              </span>
                            </p>
                            <p className="text-gray-500 text-sm">
                              {title(job.nature)} - {title(job.status)}
                            </p>
                            <p className="text-gray-500 text-sm">
                              {job.state}, {job.country}
                            </p>
                          </div>
                        </SortableItem>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              className="space-x-2"
                              onClick={() => {
                                setIsUpdateDialogOpen(true)
                                setSelectedJob({
                                  ...job,
                                  id: job.id as number
                                })
                              }}
                            >
                              <EditIcon size={16} />
                              <span>Edit</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={async (e) => {
                                e.stopPropagation()

                                try {
                                  const { data: response } = await MediaRoomService.deleteJob(
                                    job.id
                                  )

                                  if (response.success) {
                                    toast({
                                      title: `Delete job successfully`,
                                      variant: 'success'
                                    })

                                    mutate(
                                      (key) =>
                                        typeof key === 'string' &&
                                        key.startsWith(MediaRoomService.findJobCategories)
                                    )
                                  }
                                } catch (error) {
                                  toast({
                                    title: 'Action failed, please try again',
                                    variant: 'destructive'
                                  })
                                }
                              }}
                              className="space-x-2"
                            >
                              <Trash2Icon size="16" color="red" />
                              <span className="text-red-500">Delete</span>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    ))}
                  </SortableContext>
                </DndContext>
              </div>

              {isCreateDialogOpen && (
                <CreateJobForm
                  jobCategoryId={jobCategory.id}
                  isDialogOpen={isCreateDialogOpen}
                  setIsDialogOpen={setIsCreateDialogOpen}
                />
              )}
              {selectedJob && (
                <UpdateJobForm
                  isDialogOpen={isUpdateDialogOpen}
                  setIsDialogOpen={setIsUpdateDialogOpen}
                  job={selectedJob}
                />
              )}
            </CardContent>
            <CardFooter className="flex justify-end">
              {sortableJob.length > 0 && (
                <Button
                  type="button"
                  variant="outline"
                  disabled={isSorting}
                  onClick={async () => {
                    setIsSorting(true)

                    try {
                      const ids = sortableJob.map((job) => job.id) as number[]

                      const { data: response } = await MediaRoomService.sortJob({ ids })
                      if (response.success) {
                        toast({
                          title: 'Sort Jobs successfully',
                          variant: 'success'
                        })
                        mutate(
                          (key) =>
                            typeof key === 'string' &&
                            key.startsWith(MediaRoomService.findJobCategories)
                        )
                      }
                    } catch (error) {
                      toast({
                        title: 'Action failed, please try again',
                        variant: 'destructive'
                      })
                      setSortableJob(
                        jobCategory.jobs.map((job) => ({ ...job, id: job.id as UniqueIdentifier }))
                      )
                    }

                    setIsSorting(false)
                  }}
                >
                  {isSorting ? (
                    <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <SaveIcon size="16" className="mr-2" />
                  )}
                  Save Ordering
                </Button>
              )}
            </CardFooter>
          </Card>
        </div>
      </SheetContent>
    </Sheet>
  )
}

export default JobCategoryCard
