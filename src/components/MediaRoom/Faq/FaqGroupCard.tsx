import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card'
import SortableItem from '@/components/ui/drag-and-drop'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { Sheet, SheetClose, SheetContent, SheetHeader, SheetTitle } from '@/components/ui/sheet'
import { useToast } from '@/components/ui/use-toast'
import { IDataResponse, serialize } from '@/network/request'
import MediaRoomService from '@/network/services/media_room'
import { Faq, FaqGroup } from '@/types/Cms'
import {
  DndContext,
  DragEndEvent,
  KeyboardSensor,
  PointerSensor,
  UniqueIdentifier,
  closestCenter,
  useSensor,
  useSensors
} from '@dnd-kit/core'
import {
  SortableContext,
  arrayMove,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy
} from '@dnd-kit/sortable'
import {
  EditIcon,
  GripHorizontalIcon,
  MoreHorizontal,
  PlusCircleIcon,
  SaveIcon,
  Trash2Icon
} from 'lucide-react'
import { FC, useEffect, useState } from 'react'
import useSWR, { mutate } from 'swr'
import { CreateFaqForm } from './CreateFaqForm'
import { CreateFaqGroupForm } from './CreateFaqGroupForm'
import { UpdateFaqForm } from './UpdateFaqForm'
import { UpdateFaqGroupForm } from './UpdateFaqGroupForm'

const FaqGroupCard = () => {
  const { data, isLoading, error } = useSWR<IDataResponse<FaqGroup>>(
    serialize(MediaRoomService.findFaqGroups, { pagination: 0 })
  )

  if (error) {
    return <></>
  }

  if (isLoading) {
    return <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
  }

  const faqGroups = MediaRoomService.toRow(data)

  return <FaqGroupForm initialValue={faqGroups} />
}

const FaqGroupForm: FC<{ initialValue: FaqGroup[] }> = ({ initialValue }) => {
  const { toast } = useToast()
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false)
  const [isEditQuestionDialogOpen, setIsEditQuestionDialogOpen] = useState(false)
  const [faqGroupSelected, setFaqGroupSelected] = useState<FaqGroup | null>(null)
  const [isSorting, setIsSorting] = useState(false)

  // sorting sections
  const [sortableFaqGroup, setSortableFaqGroup] = useState(
    initialValue.map((faqGroup) => ({ ...faqGroup, id: faqGroup.id as UniqueIdentifier }))
  )
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates
    })
  )

  function handleDragEnd(event: DragEndEvent) {
    const { active, over } = event

    if (active.id !== over?.id) {
      setSortableFaqGroup((items) => {
        const oldIndex = items.map((item) => item.id).indexOf(active.id)
        const newIndex = items.map((item) => item.id).indexOf(over!.id)

        return arrayMove(items, oldIndex, newIndex)
      })
    }
  }

  useEffect(() => {
    setSortableFaqGroup(
      initialValue.map((faqGroup) => ({ ...faqGroup, id: faqGroup.id as UniqueIdentifier }))
    )
  }, [initialValue])

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0">
        <CardTitle>FAQ Groups</CardTitle>
        <div className="flex flex-row gap-x-2">
          <Button type="button" onClick={() => setIsCreateDialogOpen(true)}>
            <PlusCircleIcon size="16" className="mr-2" />
            Add FAQ Group
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-8">
        <div className="mt-2 flex flex-col space-y-4">
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <SortableContext items={sortableFaqGroup ?? []} strategy={verticalListSortingStrategy}>
              {sortableFaqGroup.map((faqGroup) => (
                <div key={faqGroup.id} className="grid grid-cols-[1fr_36px] items-center gap-x-2">
                  <SortableItem
                    id={faqGroup.id as UniqueIdentifier}
                    className="grid grid-cols-[18px_1fr] items-center gap-x-2"
                  >
                    <GripHorizontalIcon size={16} />
                    <div className="flex flex-col">
                      <p>{faqGroup.title}</p>
                    </div>
                  </SortableItem>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        className="space-x-2"
                        onClick={() => {
                          setIsEditQuestionDialogOpen(true)
                          setFaqGroupSelected({
                            ...faqGroup,
                            id: faqGroup.id as number
                          })
                        }}
                      >
                        <EditIcon size={16} />
                        <span>Edit questions</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="space-x-2"
                        onClick={() => {
                          setIsUpdateDialogOpen(true)
                          setFaqGroupSelected({
                            ...faqGroup,
                            id: faqGroup.id as number
                          })
                        }}
                      >
                        <EditIcon size={16} />
                        <span>Edit group</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={async (e) => {
                          e.stopPropagation()

                          try {
                            const { data: response } = await MediaRoomService.deleteFaqGroup(
                              faqGroup.id
                            )

                            if (response.success) {
                              toast({
                                title: `Delete faq group successfully`,
                                variant: 'success'
                              })

                              mutate(
                                (key) =>
                                  typeof key === 'string' &&
                                  key.startsWith(MediaRoomService.findFaqGroups)
                              )
                            }
                          } catch (error) {
                            toast({
                              title: 'Action failed, please try again',
                              variant: 'destructive'
                            })
                          }
                        }}
                        className="space-x-2"
                      >
                        <Trash2Icon size="16" color="red" />
                        <span className="text-red-500">Delete</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              ))}
            </SortableContext>
          </DndContext>
        </div>

        <CreateFaqGroupForm
          isDialogOpen={isCreateDialogOpen}
          setIsDialogOpen={setIsCreateDialogOpen}
        />
        {faqGroupSelected && (
          <>
            <UpdateFaqGroupForm
              isDialogOpen={isUpdateDialogOpen}
              setIsDialogOpen={setIsUpdateDialogOpen}
              faqGroup={faqGroupSelected}
            />
            <FaqForm
              isSheetOpen={isEditQuestionDialogOpen}
              setIsSheetOpen={setIsEditQuestionDialogOpen}
              faqGroup={faqGroupSelected}
            />
          </>
        )}
      </CardContent>
      <CardFooter className="flex justify-end">
        {sortableFaqGroup.length > 0 && (
          <Button
            type="button"
            variant="outline"
            disabled={isSorting}
            onClick={async () => {
              setIsSorting(true)

              try {
                const ids = sortableFaqGroup.map((faq) => faq.id) as number[]

                const { data: response } = await MediaRoomService.sortFaqGroup({ ids })
                if (response.success) {
                  toast({
                    title: 'Sort faq groups successfully',
                    variant: 'success'
                  })
                  mutate(
                    (key) =>
                      typeof key === 'string' && key.startsWith(MediaRoomService.findFaqGroups)
                  )
                }
              } catch (error) {
                toast({
                  title: 'Action failed, please try again',
                  variant: 'destructive'
                })
                setSortableFaqGroup(
                  initialValue.map((faq) => ({ ...faq, id: faq.id as UniqueIdentifier }))
                )
              }

              setIsSorting(false)
            }}
          >
            {isSorting ? (
              <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <SaveIcon size="16" className="mr-2" />
            )}
            Save Ordering
          </Button>
        )}
      </CardFooter>
    </Card>
  )
}

const FaqForm: FC<{
  faqGroup: FaqGroup
  isSheetOpen: boolean
  setIsSheetOpen: (value: boolean) => void
}> = ({ faqGroup, isSheetOpen, setIsSheetOpen }) => {
  const { toast } = useToast()
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false)
  const [selectedFaq, setSelectedFaq] = useState<Faq | null>(null)
  const [isSorting, setIsSorting] = useState(false)

  // sorting sections
  const [sortableFaq, setSortableFaq] = useState(
    faqGroup.faqs.map((faq) => ({ ...faq, id: faq.id as UniqueIdentifier }))
  )
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates
    })
  )

  function handleDragEnd(event: DragEndEvent) {
    const { active, over } = event

    if (active.id !== over?.id) {
      setSortableFaq((items) => {
        const oldIndex = items.map((item) => item.id).indexOf(active.id)
        const newIndex = items.map((item) => item.id).indexOf(over!.id)

        return arrayMove(items, oldIndex, newIndex)
      })
    }
  }

  useEffect(() => {
    setSortableFaq(faqGroup.faqs.map((faq) => ({ ...faq, id: faq.id as UniqueIdentifier })))
  }, [faqGroup.faqs])

  return (
    <Sheet
      open={isSheetOpen}
      onOpenChange={(isOpen) => {
        setIsSheetOpen(isOpen)
      }}
    >
      <SheetContent side="bottom" className="h-full w-full">
        <SheetHeader className="mx-auto max-w-4xl pb-6 pt-10">
          <SheetTitle className="flex items-center justify-between">
            {`${faqGroup.title} FAQs`}
            <div className="flex space-x-2">
              <SheetClose>
                <Button variant="outline">Cancel</Button>
              </SheetClose>
              <div className="flex flex-row gap-x-2">
                <Button type="button" onClick={() => setIsCreateDialogOpen(true)}>
                  <PlusCircleIcon size="16" className="mr-2" />
                  New Question
                </Button>
              </div>
            </div>
          </SheetTitle>
        </SheetHeader>
        <div className={'mx-auto flex max-w-4xl flex-col space-y-8'}>
          <Card>
            <CardContent className="space-y-8">
              <div className="mt-2 flex flex-col space-y-4">
                <DndContext
                  sensors={sensors}
                  collisionDetection={closestCenter}
                  onDragEnd={handleDragEnd}
                >
                  <SortableContext items={sortableFaq ?? []} strategy={verticalListSortingStrategy}>
                    {sortableFaq.map((faq) => (
                      <div key={faq.id} className="grid grid-cols-[1fr_36px] items-center gap-x-2">
                        <SortableItem
                          id={faq.id as UniqueIdentifier}
                          className="grid grid-cols-[18px_1fr] items-center gap-x-2"
                        >
                          <GripHorizontalIcon size={16} />
                          <div className="flex flex-col">
                            <p>{faq.title}</p>
                          </div>
                        </SortableItem>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              className="space-x-2"
                              onClick={() => {
                                setIsUpdateDialogOpen(true)
                                setSelectedFaq({
                                  ...faq,
                                  id: faq.id as number
                                })
                              }}
                            >
                              <EditIcon size={16} />
                              <span>Edit</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={async (e) => {
                                e.stopPropagation()

                                try {
                                  const { data: response } = await MediaRoomService.deleteFaq(
                                    faq.id
                                  )

                                  if (response.success) {
                                    toast({
                                      title: `Delete faq successfully`,
                                      variant: 'success'
                                    })

                                    mutate(
                                      (key) =>
                                        typeof key === 'string' &&
                                        key.startsWith(MediaRoomService.findFaqGroups)
                                    )
                                  }
                                } catch (error) {
                                  toast({
                                    title: 'Action failed, please try again',
                                    variant: 'destructive'
                                  })
                                }
                              }}
                              className="space-x-2"
                            >
                              <Trash2Icon size="16" color="red" />
                              <span className="text-red-500">Delete</span>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    ))}
                  </SortableContext>
                </DndContext>
              </div>

              {isCreateDialogOpen && (
                <CreateFaqForm
                  faqGroupId={faqGroup.id}
                  isDialogOpen={isCreateDialogOpen}
                  setIsDialogOpen={setIsCreateDialogOpen}
                />
              )}
              {selectedFaq && (
                <UpdateFaqForm
                  isDialogOpen={isUpdateDialogOpen}
                  setIsDialogOpen={setIsUpdateDialogOpen}
                  faq={selectedFaq}
                />
              )}
            </CardContent>
            <CardFooter className="flex justify-end">
              {sortableFaq.length > 0 && (
                <Button
                  type="button"
                  variant="outline"
                  disabled={isSorting}
                  onClick={async () => {
                    setIsSorting(true)

                    try {
                      const ids = sortableFaq.map((faq) => faq.id) as number[]

                      const { data: response } = await MediaRoomService.sortFaq({ ids })
                      if (response.success) {
                        toast({
                          title: 'Sort FAQs successfully',
                          variant: 'success'
                        })
                        mutate(
                          (key) =>
                            typeof key === 'string' &&
                            key.startsWith(MediaRoomService.findFaqGroups)
                        )
                      }
                    } catch (error) {
                      toast({
                        title: 'Action failed, please try again',
                        variant: 'destructive'
                      })
                      setSortableFaq(
                        faqGroup.faqs.map((faq) => ({ ...faq, id: faq.id as UniqueIdentifier }))
                      )
                    }

                    setIsSorting(false)
                  }}
                >
                  {isSorting ? (
                    <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <SaveIcon size="16" className="mr-2" />
                  )}
                  Save Ordering
                </Button>
              )}
            </CardFooter>
          </Card>
        </div>
      </SheetContent>
    </Sheet>
  )
}

export default FaqGroupCard
