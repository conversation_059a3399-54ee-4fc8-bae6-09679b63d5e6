import EditorComponent from '@/components/Editor/Editor'
import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { useToast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import MediaRoomService from '@/network/services/media_room'
import { Faq, UpdateFaqInput } from '@/types/Cms'
import { FC, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { mutate } from 'swr'

interface UpdateFaqFormProps extends React.HTMLAttributes<HTMLDivElement> {
  isDialogOpen: boolean
  setIsDialogOpen: (value: boolean) => void
  faq: Faq
}

export const UpdateFaqForm: FC<UpdateFaqFormProps> = ({
  isDialogOpen,
  setIsDialogOpen,
  faq,
  className
}) => {
  const { toast } = useToast()
  const form = useForm<UpdateFaqInput>({
    defaultValues: {
      title: faq.title ?? '',
      subtitle: faq.subtitle ?? '',
      description: faq.description ?? ''
    },
    shouldUseNativeValidation: false
  })

  useEffect(() => {
    form.reset({
      title: faq.title ?? '',
      subtitle: faq.subtitle ?? '',
      description: faq.description ?? ''
    })
  }, [faq])

  const onSubmit = form.handleSubmit(async (values) => {
    console.log('submit values: ', values)
    try {
      const { data: response } = await MediaRoomService.updateFaq(faq.id, values)

      if (response.success) {
        mutate((key) => typeof key === 'string' && key.startsWith(MediaRoomService.findFaqGroups))
        toast({
          title: 'Faq updated successfully',
          variant: 'success'
        })
        setIsDialogOpen(false)
      }
    } catch (error) {
      console.error(error)
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <Dialog
      open={isDialogOpen}
      onOpenChange={(isOpen) => {
        setIsDialogOpen(isOpen)
      }}
    >
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            Update FAQ
            <div className="flex space-x-2">
              <DialogClose type="button" asChild>
                <Button variant="outline">Cancel</Button>
              </DialogClose>
              <Button
                type="submit"
                form="update-faq-form"
                disabled={form.formState.isSubmitting}
                onClick={() => {
                  form.clearErrors()
                }}
              >
                {form.formState.isSubmitting && (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                )}
                Save
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form
            id="update-faq-form"
            onSubmit={onSubmit}
            className={cn('flex flex-col space-y-8', className)}
          >
            <FormField
              control={form.control}
              name="title"
              rules={{ required: 'Please enter the title' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Title*</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              rules={{ required: 'Please enter the description' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description*</FormLabel>
                  <FormControl>
                    <EditorComponent content={field.value} onChange={field.onChange} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
