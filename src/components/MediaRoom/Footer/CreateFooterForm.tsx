import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import {
  <PERSON>alog,
  DialogClose,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { useToast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import { serialize } from '@/network/request'
import MediaRoomService from '@/network/services/media_room'
import { CreateFooterInput } from '@/types/Cms'
import { FC, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { mutate } from 'swr'

interface CreateFooterFormProps extends React.HTMLAttributes<HTMLDivElement> {
  isDialogOpen: boolean
  setIsDialogOpen: (value: boolean) => void
  footerType: 'shop' | 'learn' | 'support' | 'social'
}

export const CreateFooterForm: FC<CreateFooterFormProps> = ({
  isDialogOpen,
  setIsDialogOpen,
  footerType,
  className
}) => {
  const { toast } = useToast()
  const form = useForm<CreateFooterInput>({
    defaultValues: {
      type: footerType,
      title: '',
      link: ''
    },
    shouldUseNativeValidation: false
  })

  useEffect(() => {
    form.reset({
      type: footerType,
      title: '',
      link: ''
    })
  }, [isDialogOpen])

  const onSubmit = form.handleSubmit(async (values) => {
    console.log('submit values: ', values)
    try {
      const { data: response } = await MediaRoomService.createFooter(values)
      console.log(response)

      if (response.success) {
        mutate(
          (key) =>
            typeof key === 'string' &&
            key.startsWith(serialize(MediaRoomService.findFooters, { 'type[0]': footerType }))
        )
        toast({
          title: 'Footer created successfully',
          variant: 'success'
        })
        setIsDialogOpen(false)
      }
    } catch (error) {
      console.error(error)
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <Dialog
      open={isDialogOpen}
      onOpenChange={(isOpen) => {
        setIsDialogOpen(isOpen)
      }}
    >
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle className="flex justify-between items-center">
            New Footer
            <div className="flex space-x-2">
              <DialogClose type="button" asChild>
                <Button variant="outline">Cancel</Button>
              </DialogClose>
              <Button
                type="submit"
                form="create-footer-form"
                disabled={form.formState.isSubmitting}
                onClick={() => {
                  form.clearErrors()
                }}
              >
                {form.formState.isSubmitting && (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                )}
                Save
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form
            id="create-footer-form"
            onSubmit={onSubmit}
            className={cn('flex flex-col space-y-8', className)}
          >
            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem className="hidden">
                  <FormLabel>Type</FormLabel>
                  <FormControl>
                    <Input {...field} disabled />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="title"
              rules={{ required: 'Please enter the title' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Title*</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="link"
              rules={{ required: 'Please enter the title' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Link*</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
