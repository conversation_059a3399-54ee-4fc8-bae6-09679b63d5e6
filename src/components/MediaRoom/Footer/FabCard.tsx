import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Footer } from '@/types/Cms'
import { FC, useState } from 'react'
import useSWR from 'swr'
import { Icons } from '@/components/icons'
import MediaRoomService from '@/network/services/media_room'
import { IDataResponse, serialize } from '@/network/request'
import { EditIcon, MoreHorizontal } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { UpdateFooterForm } from './UpdateFooterForm'

const FabCard = () => {
  const { data, isLoading, error } = useSWR<IDataResponse<Footer>>(
    serialize(MediaRoomService.findFooters, { 'type[0]': 'fab' })
  )

  if (error) {
    return <></>
  }

  if (isLoading) {
    return <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
  }

  const footers = MediaRoomService.toRow(data)

  return <FabForm initialValue={footers} />
}

const FabForm: FC<{ initialValue: Footer[] }> = ({ initialValue }) => {
  //   const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false)
  const [selectedFooter, setSelectedFooter] = useState<Footer | null>(null)

  // sorting sections
  //   const [sortableFooter, setSortableFooter] = useState(
  //     initialValue.map((footer) => ({ ...footer, id: footer.id as UniqueIdentifier }))
  //   )
  //   const sensors = useSensors(
  //     useSensor(PointerSensor),
  //     useSensor(KeyboardSensor, {
  //       coordinateGetter: sortableKeyboardCoordinates
  //     })
  //   )

  //   function handleDragEnd(event: DragEndEvent) {
  //     const { active, over } = event

  //     if (active.id !== over?.id) {
  //       setSortableFooter((items) => {
  //         const oldIndex = items.map((item) => item.id).indexOf(active.id)
  //         const newIndex = items.map((item) => item.id).indexOf(over!.id)

  //         return arrayMove(items, oldIndex, newIndex)
  //       })
  //     }
  //   }

  //   useEffect(() => {
  //     setSortableFooter(
  //       initialValue.map((footer) => ({ ...footer, id: footer.id as UniqueIdentifier }))
  //     )
  //   }, [initialValue])

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0">
        <CardTitle>Floating Action Button</CardTitle>
        <div className="flex flex-row gap-x-2">
          {/* <Button type="button" onClick={() => setIsCreateDialogOpen(true)}>
            <PlusCircleIcon size="16" className="mr-2" />
            New Fab
          </Button> */}
        </div>
      </CardHeader>
      <CardContent className="space-y-8">
        {/* {sortableFooter.length > 0 && (
          <Button
            type="button"
            onClick={async () => {
              try {
                const ids = sortableFooter.map((footer) => footer.id) as number[]

                const { data: response } = await MediaRoomService.sortFooter({ ids })
                if (response.success) {
                  toast({
                    title: 'Sort footers successfully',
                    variant: 'success'
                  })
                  mutate(
                    (key) => typeof key === 'string' && key.startsWith(MediaRoomService.findFooters)
                  )
                }
              } catch (error) {
                toast({
                  title: 'Action failed, please try again',
                  variant: 'destructive'
                })
                setSortableFooter(
                  initialValue.map((footer) => ({ ...footer, id: footer.id as UniqueIdentifier }))
                )
              }
            }}
          >
            <SaveIcon size="16" className="mr-2" />
            Save Ordering
          </Button>
        )} */}
        <div className="mt-2 flex flex-col space-y-4">
          {/* <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <SortableContext items={sortableFooter ?? []} strategy={verticalListSortingStrategy}> */}
          {initialValue.map((footer) => (
            <div key={footer.id} className="grid grid-cols-[1fr_36px] items-center gap-x-2">
              {/* <SortableItem
                id={footer.id as UniqueIdentifier}
                className="grid grid-cols-[1fr] items-center gap-x-2"
              > */}
              {/* <GripHorizontalIcon size={16} /> */}
              <div className="flex flex-col">
                {footer.slug == 'fab-before' && <h4 className='text-rose-400'>Before Login</h4>}
                {footer.slug == 'fab-after' && <h4 className='text-success'>After Login</h4>}
                <p className="font-semibold">{footer.title}</p>
                <p className="text-muted-foreground text-sm">{footer.link}</p>
              </div>
              {/* </SortableItem> */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                  <Button variant="ghost" className="h-8 w-8 p-0">
                    <span className="sr-only">Open menu</span>
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem
                    className="space-x-2"
                    onClick={() => {
                      setIsUpdateDialogOpen(true)
                      setSelectedFooter({
                        ...footer,
                        id: footer.id as number
                      })
                    }}
                  >
                    <EditIcon size={16} />
                    <span>Edit</span>
                  </DropdownMenuItem>
                  {/* <DropdownMenuItem
                    onClick={async (e) => {
                      e.stopPropagation()

                      try {
                        const { data: response } = await MediaRoomService.deleteFooter(footer.id)

                        if (response.success) {
                          toast({
                            title: `Delete footer successfully`,
                            variant: 'success'
                          })

                          mutate(
                            (key) =>
                              typeof key === 'string' &&
                              key.startsWith(MediaRoomService.findFooters)
                          )
                        }
                      } catch (error) {
                        toast({
                          title: 'Action failed, please try again',
                          variant: 'destructive'
                        })
                      }
                    }}
                    className="space-x-2"
                  >
                    <Trash2Icon size="16" color="red" />
                    <span className="text-red-500">Delete</span>
                  </DropdownMenuItem> */}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          ))}
          {/* </SortableContext>
          </DndContext> */}
        </div>

        {/* <CreateFooterForm
          isDialogOpen={isCreateDialogOpen}
          setIsDialogOpen={setIsCreateDialogOpen}
          footerType="fab"
        /> */}
        {selectedFooter && (
          <UpdateFooterForm
            isDialogOpen={isUpdateDialogOpen}
            setIsDialogOpen={setIsUpdateDialogOpen}
            footer={selectedFooter}
          />
        )}
      </CardContent>
    </Card>
  )
}

export default FabCard
