import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Footer } from '@/types/Cms'
import { FC, useEffect, useState } from 'react'
import useSWR, { mutate } from 'swr'
import { Icons } from '@/components/icons'
import MediaRoomService from '@/network/services/media_room'
import { IDataResponse, serialize } from '@/network/request'
import {
  EditIcon,
  GripHorizontalIcon,
  MoreHorizontal,
  PlusCircleIcon,
  SaveIcon,
  Trash2Icon
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import {
  DndContext,
  DragEndEvent,
  KeyboardSensor,
  PointerSensor,
  UniqueIdentifier,
  closestCenter,
  useSensor,
  useSensors
} from '@dnd-kit/core'
import {
  SortableContext,
  arrayMove,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy
} from '@dnd-kit/sortable'
import SortableItem from '@/components/ui/drag-and-drop'
import { useToast } from '@/components/ui/use-toast'
import { CreateFooterForm } from './CreateFooterForm'
import { UpdateFooterForm } from './UpdateFooterForm'

const ShopCard = () => {
  const { data, isLoading, error } = useSWR<IDataResponse<Footer>>(
    serialize(MediaRoomService.findFooters, { 'type[0]': 'shop', pagination: 0 })
  )

  if (error) {
    return <></>
  }

  if (isLoading) {
    return <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
  }

  const footers = MediaRoomService.toRow(data)

  return <ShopForm initialValue={footers} />
}

const ShopForm: FC<{ initialValue: Footer[] }> = ({ initialValue }) => {
  const { toast } = useToast()
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false)
  const [selectedFooter, setSelectedFooter] = useState<Footer | null>(null)
  const [isSorting, setIsSorting] = useState(false)

  // sorting sections
  const [sortableFooter, setSortableFooter] = useState(
    initialValue.map((footer) => ({ ...footer, id: footer.id as UniqueIdentifier }))
  )
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates
    })
  )

  function handleDragEnd(event: DragEndEvent) {
    const { active, over } = event

    if (active.id !== over?.id) {
      setSortableFooter((items) => {
        const oldIndex = items.map((item) => item.id).indexOf(active.id)
        const newIndex = items.map((item) => item.id).indexOf(over!.id)

        return arrayMove(items, oldIndex, newIndex)
      })
    }
  }

  useEffect(() => {
    setSortableFooter(
      initialValue.map((footer) => ({ ...footer, id: footer.id as UniqueIdentifier }))
    )
  }, [initialValue])

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0">
        <CardTitle>Shop</CardTitle>
        <div className="flex flex-row gap-x-2">
          <Button type="button" onClick={() => setIsCreateDialogOpen(true)}>
            <PlusCircleIcon size="16" className="mr-2" />
            New Shop
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-8">
        <div className="mt-2 flex flex-col space-y-4">
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <SortableContext items={sortableFooter ?? []} strategy={verticalListSortingStrategy}>
              {sortableFooter.map((footer, i) => (
                <div key={footer.id} className="grid grid-cols-[1fr_36px] items-center gap-x-2">
                  <SortableItem
                    id={footer.id as UniqueIdentifier}
                    className="grid grid-cols-[18px_1fr] items-center gap-x-2"
                  >
                    <GripHorizontalIcon size={16} />
                    <div className="flex flex-col">
                      <p className="font-semibold">
                        Shop {i + 1} - {footer.title}
                      </p>
                      <p className="text-muted-foreground text-sm">{footer.link}</p>
                    </div>
                  </SortableItem>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        className="space-x-2"
                        onClick={() => {
                          setIsUpdateDialogOpen(true)
                          setSelectedFooter({
                            ...footer,
                            id: footer.id as number
                          })
                        }}
                      >
                        <EditIcon size={16} />
                        <span>Edit</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={async (e) => {
                          e.stopPropagation()

                          try {
                            const { data: response } = await MediaRoomService.deleteFooter(
                              footer.id
                            )

                            if (response.success) {
                              toast({
                                title: `Delete footer successfully`,
                                variant: 'success'
                              })

                              mutate(
                                (key) =>
                                  typeof key === 'string' &&
                                  key.startsWith(MediaRoomService.findFooters)
                              )
                            }
                          } catch (error) {
                            toast({
                              title: 'Action failed, please try again',
                              variant: 'destructive'
                            })
                          }
                        }}
                        className="space-x-2"
                      >
                        <Trash2Icon size="16" color="red" />
                        <span className="text-red-500">Delete</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              ))}
            </SortableContext>
          </DndContext>
        </div>

        <CreateFooterForm
          isDialogOpen={isCreateDialogOpen}
          setIsDialogOpen={setIsCreateDialogOpen}
          footerType="shop"
        />
        {selectedFooter && (
          <UpdateFooterForm
            isDialogOpen={isUpdateDialogOpen}
            setIsDialogOpen={setIsUpdateDialogOpen}
            footer={selectedFooter}
          />
        )}
      </CardContent>
      <CardFooter className="flex justify-end">
        {sortableFooter.length > 0 && (
          <Button
            type="button"
            variant="outline"
            disabled={isSorting}
            onClick={async () => {
              try {
                setIsSorting(true)
                const ids = sortableFooter.map((footer) => footer.id) as number[]

                const { data: response } = await MediaRoomService.sortFooter({ ids })
                if (response.success) {
                  toast({
                    title: 'Sort footers successfully',
                    variant: 'success'
                  })
                  mutate(
                    (key) => typeof key === 'string' && key.startsWith(MediaRoomService.findFooters)
                  )
                }
                setIsSorting(false)
              } catch (error) {
                toast({
                  title: 'Action failed, please try again',
                  variant: 'destructive'
                })
                setSortableFooter(
                  initialValue.map((footer) => ({ ...footer, id: footer.id as UniqueIdentifier }))
                )
                setIsSorting(false)
              }
            }}
          >
            {isSorting ? (
              <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <SaveIcon size="16" className="mr-2" />
            )}
            Save Ordering
          </Button>
        )}
      </CardFooter>
    </Card>
  )
}

export default ShopCard
