import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Footer } from '@/types/Cms'
import { FC, useState } from 'react'
import useSWR from 'swr'
import { Icons } from '@/components/icons'
import MediaRoomService from '@/network/services/media_room'
import { IDataResponse, serialize } from '@/network/request'
import { EditIcon, MoreHorizontal, PlusCircleIcon } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { UpdateFooterForm } from './UpdateFooterForm'
import { CreateFooterForm } from './CreateFooterForm'

const SocialCard = () => {
  const { data, isLoading, error } = useSWR<IDataResponse<Footer>>(
    serialize(MediaRoomService.findFooters, { 'type[0]': 'social' })
  )

  if (error) {
    return <></>
  }

  if (isLoading) {
    return <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
  }

  const footers = MediaRoomService.toRow(data)

  return <SocialForm initialValue={footers} />
}

const SocialForm: FC<{ initialValue: Footer[] }> = ({ initialValue }) => {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false)
  const [selectedFooter, setSelectedFooter] = useState<Footer | null>(null)

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0">
        <CardTitle>Social</CardTitle>
        <div className="flex flex-row gap-x-2">
          <Button type="button" onClick={() => setIsCreateDialogOpen(true)}>
            <PlusCircleIcon size="16" className="mr-2" />
            New Social
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-8">
        <div className="mt-2 flex flex-col space-y-4">
          {initialValue.map((footer) => (
            <div key={footer.id} className="grid grid-cols-[1fr_36px] items-center gap-x-2">
              <div className="flex flex-col">
                <p className="font-semibold">{footer.title}</p>
                <p className="text-muted-foreground text-sm">{footer.link}</p>
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                  <Button variant="ghost" className="h-8 w-8 p-0">
                    <span className="sr-only">Open menu</span>
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem
                    className="space-x-2"
                    onClick={() => {
                      setIsUpdateDialogOpen(true)
                      setSelectedFooter({
                        ...footer,
                        id: footer.id as number
                      })
                    }}
                  >
                    <EditIcon size={16} />
                    <span>Edit</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          ))}
        </div>

        <CreateFooterForm
          isDialogOpen={isCreateDialogOpen}
          setIsDialogOpen={setIsCreateDialogOpen}
          footerType="social"
        />
        {selectedFooter && (
          <UpdateFooterForm
            isDialogOpen={isUpdateDialogOpen}
            setIsDialogOpen={setIsUpdateDialogOpen}
            footer={selectedFooter}
          />
        )}
      </CardContent>
    </Card>
  )
}

export default SocialCard
