import { Dropzone } from '@/components/Form/Dropzone'
import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import SortableItem from '@/components/ui/drag-and-drop'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Label } from '@/components/ui/label'
import { useToast } from '@/components/ui/use-toast'
import { bytesToSize } from '@/lib/utils'
import FileService from '@/network/services/file'
import MediaRoomService from '@/network/services/media_room'
import { PageCms, UpdatePageCmsInput } from '@/types/Cms'
import type { UniqueIdentifier } from '@dnd-kit/core'
import {
  DndContext,
  KeyboardSensor,
  PointerSensor,
  closestCenter,
  useSensor,
  useSensors,
  type DragEndEvent
} from '@dnd-kit/core'
import {
  SortableContext,
  arrayMove,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy
} from '@dnd-kit/sortable'
import { Image } from '@unpic/react'
import { GripHorizontalIcon, MoreHorizontal, Trash2Icon } from 'lucide-react'
import { FC, useEffect, useState } from 'react'
import { FileWithPath } from 'react-dropzone'
import { useForm } from 'react-hook-form'
import useSWR, { mutate } from 'swr'
import TextEditorComponent from '@/components/Editor/TextEditor'
import { Input } from '@/components/ui/input'
import { title } from 'radash'
import OrSeparator from '@/components/ui/orSeparator'

const SLUG = 'home'

const HomeCard = () => {
  return <HomeView />
}

const HomeView = () => {
  const { data, isLoading, error } = useSWR(MediaRoomService.findPage(SLUG))

  if (error) {
    return <></>
  }

  if (isLoading) {
    return <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
  }

  const home = data.data

  return <HomeForm initialValue={home} />
}

const HomeForm: FC<{ initialValue: PageCms }> = ({ initialValue }) => {
  const { toast } = useToast()
  const form = useForm<UpdatePageCmsInput>({
    shouldUseNativeValidation: false,
    defaultValues: {
      ...initialValue,
      title: initialValue.title ?? '',
      subtitle: initialValue.subtitle ?? ''
    }
  })

  const banners = form.watch('banners')
  const media = (form.watch('media') as ((FileWithPath & { preview: string }) | undefined)[]) ?? []

  const seoThumbnailUrl = form.watch('metadata.seo.thumbnail')
  const seoMedia = form.watch('tmp_seo_media') as (FileWithPath & { preview: string }) | undefined

  useEffect(() => {
    form.reset({
      ...initialValue,
      title: initialValue.title ?? '',
      subtitle: initialValue.subtitle ?? ''
    })
  }, [initialValue])

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      if (values.media && (values.media?.length ?? 0) > 0) {
        const { data: mediaResponse } = await FileService.upload(values.media as File[])

        const sortTmp = sortableBanner ? [...sortableBanner] : []
        for (const i in values.banners!) {
          const banner = values.banners[i]
          if (banner.desktop?.url) {
            continue
          }

          const mediaObj = mediaResponse.uploads.splice(0, 1)
          if (mediaObj.length == 0) {
            continue
          }

          if (banner.desktop) {
            banner.desktop.url = mediaObj[0].url
          }
          if (banner.mobile) {
            banner.mobile.url = mediaObj[0].url
          }
          if (sortTmp[i]?.desktop) {
            sortTmp[i].desktop.url = mediaObj[0].url
          }
          if (sortTmp[i]?.mobile) {
            sortTmp[i].mobile.url = mediaObj[0].url
          }

          if (mediaResponse.uploads.length <= 0) {
            break
          }
        }

        setSortableBanner(sortTmp)
        delete values.media
      }

      if (values.tmp_seo_media) {
        // only upload if no url provided
        if (!values.metadata.seo.thumbnail) {
          const { data: mediaResponse } = await FileService.upload([values.tmp_seo_media] as File[])
          values.metadata.seo.thumbnail = mediaResponse.uploads[0].url
        }
        delete values.tmp_seo_media
      }

      if (values.metadata.seo.thumbnail) {
        delete values.tmp_seo_media
      }

      console.log('submit values: ', values)

      const { data: response } = await MediaRoomService.updatePage(initialValue.id, values)

      if (response.success) {
        toast({
          title: 'Update successfully',
          variant: 'success'
        })

        mutate((key) => typeof key === 'string' && key.startsWith(MediaRoomService.findPage(SLUG)))
      }
    } catch (error) {
      console.log(error)
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  // sorting sections
  const [sortableBanner, setSortableBanner] = useState(
    banners?.map((banner) => ({ ...banner, id: banner.desktop?.name as UniqueIdentifier }))
  )
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates
    })
  )

  function handleDragEnd(event: DragEndEvent) {
    const { active, over } = event

    if (active.id !== over?.id) {
      setSortableBanner((items) => {
        if (!items || !over) {
          return
        }

        const oldIndex = items.map((item) => item.id).indexOf(active.id)
        const newIndex = items.map((item) => item.id).indexOf(over.id)

        const banners = form.getValues('banners') ?? []
        const [banner] = banners.splice(oldIndex, 1)
        banners.splice(newIndex, 0, banner)
        form.setValue('banners', banners)

        const medias = form.getValues('media')
        if (medias) {
          const [media] = medias.splice(oldIndex, 1)
          if (medias[newIndex]) {
            medias.splice(newIndex, 0, media)
          } else {
            medias[newIndex] = media
          }

          form.setValue('media', medias)
        }

        return arrayMove(items, oldIndex, newIndex)
      })
    }
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0">
        <CardTitle>Home</CardTitle>
        <Button
          type="submit"
          form="home-form"
          disabled={form.formState.isSubmitting}
          onClick={() => {
            form.clearErrors()
          }}
          className="gap-1"
        >
          Save
          {form.formState.isSubmitting && <Icons.spinner className="h-4 w-4 animate-spin" />}
        </Button>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form id="home-form" onSubmit={onSubmit} className="space-y-4">
            <div className="grid gap-3">
              <FormField
                control={form.control}
                name="banners"
                // rules={{ required: 'Please insert the image' }}
                render={({ field }) => (
                  <>
                    <FormItem>
                      <FormLabel>Primary Banners</FormLabel>
                      <FormControl>
                        <Dropzone
                          accept={{ 'image/*': ['.jpeg', '.jpg', '.png'], 'video/*': ['.mp4'] }}
                          description="Drop your videos or images here, or click to browse"
                          multiple={true}
                          onDrop={(acceptedFiles) => {
                            const bannersTmp = banners ? [...banners] : []
                            const mediaTmp = media ? [...media] : []

                            // remove existing already uploaded file
                            const acceptedFiles2 = acceptedFiles.filter((acceptedFile) =>
                              bannersTmp?.every((file) => file.desktop?.name != acceptedFile.name)
                            )
                            for (const index in acceptedFiles2) {
                              const file = acceptedFiles2[index]
                              const findIndex = media?.findIndex(
                                (f) => (f as FileWithPath)?.path == file.path
                              )

                              if ((findIndex ?? -1) == -1) {
                                const preview = Object.assign(file, {
                                  preview: URL.createObjectURL(file)
                                })

                                mediaTmp[bannersTmp.length] = preview
                                const newBanner = {
                                  desktop: {
                                    name: preview.name,
                                    type: preview.type.split('/')[0] as 'video' | 'image',
                                    media_file: preview
                                  },
                                  mobile: {
                                    name: preview.name,
                                    type: preview.type.split('/')[0] as 'video' | 'image',
                                    media_file: preview
                                  }
                                }
                                bannersTmp.push(newBanner)

                                setSortableBanner(
                                  sortableBanner
                                    ? [
                                        ...sortableBanner,
                                        {
                                          id: preview.name as UniqueIdentifier,
                                          desktop: {
                                            name: preview.name,
                                            type: preview.type.split('/')[0] as 'video' | 'image',
                                            media_file: preview
                                          },
                                          mobile: {
                                            name: preview.name,
                                            type: preview.type.split('/')[0] as 'video' | 'image',
                                            media_file: preview
                                          }
                                        }
                                      ]
                                    : [
                                        {
                                          id: preview.name as UniqueIdentifier,
                                          desktop: {
                                            name: preview.name,
                                            type: preview.type.split('/')[0] as 'video' | 'image',
                                            media_file: preview
                                          },
                                          mobile: {
                                            name: preview.name,
                                            type: preview.type.split('/')[0] as 'video' | 'image',
                                            media_file: preview
                                          }
                                        }
                                      ]
                                )
                              }
                            }

                            form.setValue('banners', bannersTmp, {
                              shouldValidate: true
                            })
                            form.setValue('media', mediaTmp as File[])
                          }}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>

                    {(banners ?? []).length > 0 && (
                      <div className="mt-2 flex flex-col space-y-4">
                        <DndContext
                          sensors={sensors}
                          collisionDetection={closestCenter}
                          onDragEnd={handleDragEnd}
                        >
                          <SortableContext
                            items={sortableBanner ?? []}
                            strategy={verticalListSortingStrategy}
                          >
                            {sortableBanner?.map((file, index) => {
                              return (
                                <div
                                  key={file.desktop?.name}
                                  className="grid grid-cols-[1fr_36px] items-center gap-x-2"
                                >
                                  <SortableItem
                                    id={file.desktop?.name as UniqueIdentifier}
                                    className="grid grid-cols-[18px_160px_1fr] items-center gap-x-2"
                                  >
                                    <GripHorizontalIcon size={16} />
                                    {file.desktop?.type.startsWith('image') && (
                                      <Image
                                        key={index}
                                        src={file.desktop?.url ?? media[index]?.preview ?? ''}
                                        layout="fullWidth"
                                        className="rounded-md"
                                      />
                                    )}
                                    {file.desktop?.type.startsWith('video') && (
                                      <video
                                        // controls
                                        key={index}
                                        src={file.desktop?.url ?? media[index]?.preview ?? ''}
                                        className="rounded-md"
                                      />
                                    )}
                                    <div className="flex flex-col">
                                      {file.desktop?.url ? (
                                        <Label className="text-xs font-normal">
                                          {title(file.desktop?.type)} uploaded
                                        </Label>
                                      ) : (
                                        <>
                                          <Label className="text-xs font-normal">
                                            {media[index]!.path}
                                          </Label>
                                          <Label className="text-xs font-normal text-gray-500">
                                            {bytesToSize(media[index]!.size)}
                                          </Label>
                                        </>
                                      )}
                                    </div>
                                  </SortableItem>
                                  <DropdownMenu>
                                    <DropdownMenuTrigger
                                      asChild
                                      onClick={(e) => e.stopPropagation()}
                                    >
                                      <Button variant="ghost" className="h-8 w-8 p-0">
                                        <span className="sr-only">Open menu</span>
                                        <MoreHorizontal className="h-4 w-4" />
                                      </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                      <DropdownMenuItem
                                        onClick={(e) => {
                                          console.log('trigger')
                                          e.stopPropagation()
                                          const galleryTmp = banners && [...banners]
                                          const mediaTmp = media ? [...media] : []

                                          if (!file.desktop?.url) {
                                            mediaTmp.splice(index, 1)
                                          }

                                          galleryTmp?.splice(index, 1)
                                          form.setValue('media', mediaTmp as File[])
                                          form.setValue('banners', galleryTmp, {
                                            shouldValidate: true
                                          })

                                          const sortableTmp = [...sortableBanner]
                                          sortableTmp.splice(index, 1)
                                          setSortableBanner(sortableTmp)
                                        }}
                                        className="space-x-2"
                                      >
                                        <Trash2Icon size="16" />
                                        <span>Delete</span>
                                      </DropdownMenuItem>
                                    </DropdownMenuContent>
                                  </DropdownMenu>
                                </div>
                              )
                            })}
                          </SortableContext>
                        </DndContext>
                      </div>
                    )}
                  </>
                )}
              />
              <FormField
                control={form.control}
                name="title"
                rules={{ required: 'Please enter the banner text' }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Secondary Banner Text</FormLabel>
                    <FormControl>
                      <TextEditorComponent content={field.value} onChange={field.onChange} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="subtitle"
                rules={{ required: 'Please enter the banner text' }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tertiary Banner Text</FormLabel>
                    <FormControl>
                      <TextEditorComponent content={field.value} onChange={field.onChange} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="metadata.seo.title"
                // rules={{ required: 'Please enter the SEO title' }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>SEO Title</FormLabel>
                    <FormControl>
                      <TextEditorComponent content={field.value} onChange={field.onChange} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="metadata.seo.description"
                // rules={{ required: 'Please enter the SEO description' }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>SEO Description</FormLabel>
                    <FormControl>
                      <TextEditorComponent content={field.value} onChange={field.onChange} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="metadata.seo.thumbnail"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>SEO Thumbnail</FormLabel>
                    <FormLabel>Image URL</FormLabel>
                    <FormControl>
                      <Input placeholder="Image Url" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <OrSeparator />

              <FormField
                control={form.control}
                name="tmp_seo_media"
                render={({ field }) => (
                  <>
                    <FormItem>
                      <FormLabel>Upload Image</FormLabel>
                      <FormControl>
                        <Dropzone
                          accept={{ 'image/*': ['.jpeg', '.jpg', '.png'] }}
                          description="Drop your image here, or click to browse"
                          multiple={false}
                          onDrop={(acceptedFiles) => {
                            const file = acceptedFiles[0]
                            const exist = seoMedia?.path === file.path

                            if (!exist) {
                              Object.assign(file, {
                                preview: URL.createObjectURL(file)
                              })
                            }

                            form.setValue('tmp_seo_media', file)
                          }}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>

                    {(seoThumbnailUrl || seoMedia?.preview) && (
                      <div className="mt-2 flex flex-col space-y-4">
                        <Image
                          src={seoMedia?.preview ?? seoThumbnailUrl ?? ''}
                          layout="constrained"
                          className="rounded-md"
                          width={320}
                          height={320}
                        />
                      </div>
                    )}
                  </>
                )}
              />
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}

export default HomeCard
