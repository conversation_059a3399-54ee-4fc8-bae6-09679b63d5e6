import { Icons } from '@/components/icons'
import { But<PERSON> } from '@/components/ui/button'
import {
  <PERSON>alog,
  DialogClose,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import { useToast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import HospitalService from '@/network/services/hospital'
import { CreateHospitalInput } from '@/types/Cms'
import { FC } from 'react'
import { useForm } from 'react-hook-form'
import { mutate } from 'swr'

interface CreateHospitalFormProps extends React.HTMLAttributes<HTMLDivElement> {
  isDialogOpen: boolean
  setIsDialogOpen: (value: boolean) => void
}

export const CreateHospitalForm: FC<CreateHospitalFormProps> = ({
  isDialogOpen,
  setIsDialogOpen,
  className
}) => {
  const { toast } = useToast()
  const form = useForm<CreateHospitalInput>({
    shouldUseNativeValidation: false
  })

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      // split hospital by comma
      const input = values.title.split(',')

      const { data: response } = await HospitalService.createHospitals({
        title: input,
        type: values.type
      })

      if (response.success) {
        mutate((key) => typeof key === 'string' && key.startsWith(HospitalService.getHospitals))
        toast({
          title: 'Hospitals created successfully',
          variant: 'success'
        })
        setIsDialogOpen(false)
        form.reset()
      }
    } catch (error) {
      console.error(error)
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <Dialog
      open={isDialogOpen}
      onOpenChange={(isOpen) => {
        setIsDialogOpen(isOpen)
      }}
    >
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            New Hospital
            <div className="flex space-x-2">
              <DialogClose type="button" asChild>
                <Button variant="outline">Cancel</Button>
              </DialogClose>
              <Button
                type="submit"
                form="create-faq-form"
                disabled={form.formState.isSubmitting}
                onClick={() => {
                  form.clearErrors()
                }}
              >
                {form.formState.isSubmitting && (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                )}
                Save
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form
            id="create-faq-form"
            onSubmit={onSubmit}
            className={cn('flex flex-col space-y-8', className)}
          >
            <FormField
              control={form.control}
              name="title"
              rules={{ required: 'Please enter the title' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Title*</FormLabel>
                  <FormDescription>Split by comma for multiple hospitals</FormDescription>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="type"
              rules={{ required: 'Please speficy hospital type' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Type*</FormLabel>
                  <FormControl>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose an option" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="public">Public</SelectItem>
                        <SelectItem value="private">Private</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
