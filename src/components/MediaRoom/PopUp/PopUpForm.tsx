import { CalendarDatePicker } from '@/components/DatePicker/CalendarDatePicker'
import { Dropzone } from '@/components/Form/Dropzone'
import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { useToast } from '@/components/ui/use-toast'
import { bytesToSize, cn } from '@/lib/utils'
import FileService from '@/network/services/file'
import PopUpService from '@/network/services/popups'
import { CreatePopUp, PopUp } from '@/types/Cms'
import { SelectTrigger } from '@radix-ui/react-select'
import { Image } from '@unpic/react'
import { MoreHorizontal, Trash2Icon } from 'lucide-react'
import { DateTime } from 'luxon'
import { FC } from 'react'
import { FileWithPath } from 'react-dropzone'
import { useForm } from 'react-hook-form'
import { mutate } from 'swr'

interface PopUpFormProps extends React.HTMLAttributes<HTMLDivElement> {
  isDialogOpen: boolean
  setIsDialogOpen: (value: boolean) => void
  initialValues?: PopUp
}

export const PopUpForm: FC<PopUpFormProps> = ({
  isDialogOpen,
  setIsDialogOpen,
  className,
  initialValues
}) => {
  const { toast } = useToast()
  const form = useForm<CreatePopUp>({
    shouldUseNativeValidation: false,
    defaultValues: initialValues
  })

  const mediaFile = form.watch('media_file')
  const mediaFileWithPath = mediaFile
    ? (mediaFile as unknown as FileWithPath & { preview: string })
    : undefined

  const onSubmit = form.handleSubmit(async (values) => {
    if (values.media_file) {
      const { data: uploadResponse } = await FileService.upload([values.media_file])
      values.media = uploadResponse.uploads[0].url
      delete values.media_file
    }

    try {
      const { data: response } = initialValues
        ? await PopUpService.updatePopUp(initialValues.id, values)
        : await PopUpService.createPopUp(values)

      if (response.success) {
        mutate((key) => typeof key === 'string' && key.startsWith(PopUpService.findPopUps))
        toast({
          title: initialValues ? 'Pop-up updated successfully' : 'Pop-up created successfully',
          variant: 'success'
        })
        setIsDialogOpen(false)
        form.reset()
      }
    } catch (error) {
      console.error(error)
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <Dialog
      open={isDialogOpen}
      onOpenChange={(isOpen) => {
        setIsDialogOpen(isOpen)
      }}
    >
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            {initialValues ? 'Edit Pop-up' : 'New Pop-up'}
            <div className="flex space-x-2">
              <DialogClose type="button" asChild>
                <Button variant="outline">Cancel</Button>
              </DialogClose>
              <Button
                type="submit"
                form="create-faq-form"
                disabled={form.formState.isSubmitting}
                onClick={() => {
                  form.clearErrors()
                }}
              >
                {form.formState.isSubmitting && (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                )}
                Save
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form
            id="create-faq-form"
            onSubmit={onSubmit}
            className={cn('flex flex-col space-y-8', className)}
          >
            <FormField
              control={form.control}
              name="title"
              rules={{ required: 'This is required' }}
              render={({ field }) => (
                <FormItem className="col-span-2 flex flex-col">
                  <FormLabel>Title</FormLabel>
                  <FormControl>
                    <Textarea {...field} />
                  </FormControl>
                  <FormDescription>Title in pop-up</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="date"
              rules={{ required: 'Please enter the date' }}
              render={({ field, fieldState }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Date</FormLabel>
                  <CalendarDatePicker
                    mode="single"
                    includeTime={true}
                    buttonLabel={
                      field.value
                        ? DateTime.fromISO(field.value as string).toFormat('yyyy-MM-dd HH:mm:ss')
                        : undefined
                    }
                    // selected={new Date((field.value as DateTime)?.toISO() ?? new Date())}
                    // onSelect={(e) => {
                    //   console.log(e)
                    //   field.onChange(DateTime.fromISO(e?.toISOString() ?? '').toISO())
                    // }}
                    value={field.value as string}
                    onChange={(v) => {
                      console.log(v)
                      field.onChange(DateTime.fromISO(v?.toISOString() ?? '').toISO())
                    }}
                    disabled={(date) => date < new Date() || date < new Date('1900-01-01')}
                  />
                  {fieldState.error || fieldState.invalid ? (
                    <FormMessage />
                  ) : (
                    <FormDescription>The date to be displayed on the pop-up</FormDescription>
                  )}
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              rules={{ required: 'This is required' }}
              render={({ field }) => (
                <FormItem className="col-span-2 flex flex-col">
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea {...field} />
                  </FormControl>
                  <FormDescription>Description in pop-up</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="media_file"
              rules={{ required: initialValues?.media ? false : 'This is required' }}
              render={({ field }) => (
                <>
                  <FormItem className="col-span-2 flex flex-col">
                    <FormLabel>Media</FormLabel>
                    <FormControl>
                      <FormControl>
                        <Dropzone
                          multiple={false}
                          onDrop={(acceptedFiles) => {
                            if (mediaFileWithPath?.path != acceptedFiles[0].path) {
                              const preview = Object.assign(acceptedFiles[0], {
                                preview: URL.createObjectURL(acceptedFiles[0])
                              })

                              form.setValue('media_file', preview as File, {
                                shouldValidate: true
                              })
                            }
                          }}
                          {...field}
                        />
                      </FormControl>
                    </FormControl>
                    <FormDescription>Image show in pop-up</FormDescription>
                    <FormMessage />
                  </FormItem>
                  <div className="flex flex-col space-y-4 mt-2">
                    {mediaFileWithPath ? (
                      <div className="grid grid-cols-[150px_1fr_36px] space-x-2 items-center">
                        {mediaFileWithPath.type.startsWith('image') && (
                          <Image
                            key={mediaFileWithPath.path}
                            src={mediaFileWithPath.preview ?? ''}
                            height={150}
                            width={150}
                            objectFit="contain"
                            className="rounded-md"
                          />
                        )}
                        {mediaFileWithPath.type.startsWith('video') && (
                          <video
                            // controls
                            key={mediaFileWithPath.path}
                            src={mediaFileWithPath.preview ?? ''}
                            height={150}
                            width={150}
                            className="rounded-md"
                          />
                        )}
                        <div className="flex flex-col">
                          <Label className="text-xs font-normal">{mediaFileWithPath.path}</Label>
                          <Label className="text-xs font-normal text-gray-500">
                            {bytesToSize(mediaFileWithPath.size)}
                          </Label>
                        </div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={(e) => {
                                e.stopPropagation()

                                form.setValue('media_file', undefined, {
                                  shouldValidate: true
                                })
                              }}
                              className="space-x-2"
                            >
                              <Trash2Icon size="16" />
                              <span>Delete</span>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    ) : initialValues?.media ? (
                      <div className="grid grid-cols-[150px_1fr] space-x-2 items-center">
                        <Image
                          src={initialValues?.media ?? ''}
                          height={150}
                          width={150}
                          objectFit="contain"
                          className="rounded-md"
                        />
                        <Label className="text-xs font-normal">Uploaded</Label>
                      </div>
                    ) : (
                      <></>
                    )}
                  </div>
                </>
              )}
            />
            <FormField
              control={form.control}
              name="show"
              // rules={{ required: 'This is required' }}
              render={({ field }) => (
                <FormItem className="col-span-2 flex flex-col">
                  <FormLabel>Show</FormLabel>
                  <FormControl>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value ? 'true' : 'false'}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose an option" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {['true', 'false'].map((value) => {
                          return (
                            <SelectItem key={value} value={value}>
                              {value === 'true' ? 'Yes' : 'No'}
                            </SelectItem>
                          )
                        })}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormDescription>To show pop-up</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
