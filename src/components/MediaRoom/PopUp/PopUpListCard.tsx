import { FC, useState } from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '../../ui/card'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../../ui/dropdown-menu'
import { CheckIcon, MoreHorizontal, SettingsIcon, Trash2Icon } from 'lucide-react'
import { Button } from '../../ui/button'
import { IDataResponse } from '@/network/request'
import { DataTable, FilterColumn } from '@/components/Table/DataTable'
import { ColumnDef, Row, createColumnHelper } from '@tanstack/react-table'
import { useToast } from '@/components/ui/use-toast'
import { mutate } from 'swr'
import { PopUp } from '@/types/Cms'
import PopUpService from '@/network/services/popups'
import { PopUpForm } from '@/components/MediaRoom/PopUp/PopUpForm'
import { Cross2Icon } from '@radix-ui/react-icons'

const PopUpListCard: FC = () => {
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <CardTitle>Active Pop-up list</CardTitle>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" forceMount>
              <DropdownMenuGroup>
                <DropdownMenuItem
                  onClick={async (event) => {
                    event.stopPropagation()
                    setIsDialogOpen(true)
                  }}
                  className="space-x-2"
                >
                  <SettingsIcon size="16" />
                  <span>Create Pop-up</span>
                </DropdownMenuItem>
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>
        </CardHeader>
        <CardContent>
          <PopUpTable />
        </CardContent>
      </Card>

      <PopUpForm {...{ isDialogOpen, setIsDialogOpen }} />
    </>
  )
}

function PopUpTable() {
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [popUp, setPopUp] = useState<PopUp | null>(null)

  const columnHelper = createColumnHelper<PopUp>()
  const columns: ColumnDef<PopUp>[] = [
    {
      accessorKey: 'title',
      header: 'Title'
    },
    {
      header: 'Is Showing',
      cell: ({ row: { original: record } }) =>
        record.show ? (
          <CheckIcon className="h-4 w-4 text-success" />
        ) : (
          <Cross2Icon className="h-4 w-4 text-destructive" />
        )
    },
    columnHelper.display({
      id: 'actions',
      cell: (props) => <RowActions row={props.row} />
    })
  ]
  const columnFilter: FilterColumn[] = [{ columnKey: 'title', header: 'Title', dataType: 'string' }]

  return (
    <>
      <DataTable<PopUp, unknown, IDataResponse<PopUp>>
        columns={columns}
        filterColumns={columnFilter}
        swrService={PopUpService.findPopUps}
        pageParam="page"
        limitParam="limit"
        sortParam="sort"
        sortColumns={['title']}
        toRow={PopUpService.toRow}
        toPaginate={PopUpService.toPaginate}
        onRowClick={(row: Row<PopUp>) => {
          setPopUp(row.original)
          setIsDialogOpen(true)
        }}
      />

      {popUp && (
        <PopUpForm
          {...{
            initialValues: popUp,
            isDialogOpen,
            setIsDialogOpen: (value: boolean) => {
              setIsDialogOpen(value)
              if (value == false) {
                setPopUp(null)
              }
            }
          }}
        />
      )}
    </>
  )
}

const RowActions: FC<{ row: Row<PopUp> }> = ({ row }) => {
  const popUpId = row.original.id
  const { toast } = useToast()

  return (
    <div className="w-full flex justify-end">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem
            onClick={async (event) => {
              event.stopPropagation()

              try {
                await PopUpService.deletePopUp(popUpId)
                mutate((key) => typeof key === 'string' && key.startsWith(PopUpService.findPopUps))
                toast({
                  description: 'Pop-up deleted',
                  variant: 'destructive'
                })
              } catch (error) {
                console.log(error)
              }
            }}
            className="space-x-2"
          >
            <Trash2Icon size="16" />
            <span>Delete</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}

export default PopUpListCard
