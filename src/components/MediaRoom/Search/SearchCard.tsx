import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Search } from '@/types/Cms'
import { FC, useEffect, useState } from 'react'
import useSWR, { mutate } from 'swr'
import { Icons } from '@/components/icons'
import MediaRoomService from '@/network/services/media_room'
import { IDataResponse, serialize } from '@/network/request'
import {
  EditIcon,
  GripHorizontalIcon,
  MoreHorizontal,
  PlusCircleIcon,
  SaveIcon,
  Trash2Icon
} from 'lucide-react'
import { CreateSearchForm } from './CreateSearchForm'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { UpdateSearchForm } from './UpdateSearchForm'
import {
  DndContext,
  DragEndEvent,
  KeyboardSensor,
  PointerSensor,
  UniqueIdentifier,
  closestCenter,
  useSensor,
  useSensors
} from '@dnd-kit/core'
import {
  SortableContext,
  arrayMove,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy
} from '@dnd-kit/sortable'
import SortableItem from '@/components/ui/drag-and-drop'
import { useToast } from '@/components/ui/use-toast'

const SearchCard = () => {
  const { data, isLoading, error } = useSWR<IDataResponse<Search>>(
    serialize(MediaRoomService.findSearches, { pagination: 0 })
  )

  if (error) {
    return <></>
  }

  if (isLoading) {
    return <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
  }

  const searches = MediaRoomService.toRow(data)

  return <SearchForm initialValue={searches} />
}

const SearchForm: FC<{ initialValue: Search[] }> = ({ initialValue }) => {
  const { toast } = useToast()
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false)
  const [selectedSearch, setSelectedSearch] = useState<Search | null>(null)
  const [isSorting, setIsSorting] = useState(false)

  // sorting sections
  const [sortableSearch, setSortableSearch] = useState(
    initialValue.map((search) => ({ ...search, id: search.id as UniqueIdentifier }))
  )
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates
    })
  )

  function handleDragEnd(event: DragEndEvent) {
    const { active, over } = event

    if (active.id !== over?.id) {
      setSortableSearch((items) => {
        const oldIndex = items.map((item) => item.id).indexOf(active.id)
        const newIndex = items.map((item) => item.id).indexOf(over!.id)

        return arrayMove(items, oldIndex, newIndex)
      })
    }
  }

  useEffect(() => {
    setSortableSearch(initialValue.map((faq) => ({ ...faq, id: faq.id as UniqueIdentifier })))
  }, [initialValue])

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0">
        <CardTitle>Searches</CardTitle>
        <div className="flex flex-row gap-x-2">
          <Button type="button" onClick={() => setIsCreateDialogOpen(true)}>
            <PlusCircleIcon size="16" className="mr-2" />
            New Search
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-8">
        <div className="mt-2 flex flex-col space-y-4">
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <SortableContext items={sortableSearch ?? []} strategy={verticalListSortingStrategy}>
              {sortableSearch.map((search, i) => (
                <div key={search.id} className="grid grid-cols-[1fr_36px] items-center gap-x-2">
                  <SortableItem
                    id={search.id as UniqueIdentifier}
                    className="grid grid-cols-[18px_1fr] items-center gap-x-2"
                  >
                    <GripHorizontalIcon size={16} />
                    <div className="flex flex-col">
                      <p>
                        Search {i + 1} - {search.title}
                      </p>
                      <p className="text-muted-foreground text-sm">{search.link}</p>
                    </div>
                  </SortableItem>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        className="space-x-2"
                        onClick={() => {
                          setIsUpdateDialogOpen(true)
                          setSelectedSearch({
                            ...search,
                            id: search.id as number
                          })
                        }}
                      >
                        <EditIcon size={16} />
                        <span>Edit</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={async (e) => {
                          e.stopPropagation()

                          try {
                            const { data: response } = await MediaRoomService.deleteSearch(
                              search.id
                            )

                            if (response.success) {
                              toast({
                                title: `Delete search successfully`,
                                variant: 'success'
                              })

                              mutate(
                                (key) =>
                                  typeof key === 'string' &&
                                  key.startsWith(MediaRoomService.findSearches)
                              )
                            }
                          } catch (error) {
                            toast({
                              title: 'Action failed, please try again',
                              variant: 'destructive'
                            })
                          }
                        }}
                        className="space-x-2"
                      >
                        <Trash2Icon size="16" color="red" />
                        <span className="text-red-500">Delete</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              ))}
            </SortableContext>
          </DndContext>
        </div>

        <CreateSearchForm
          isDialogOpen={isCreateDialogOpen}
          setIsDialogOpen={setIsCreateDialogOpen}
        />
        {selectedSearch && (
          <UpdateSearchForm
            isDialogOpen={isUpdateDialogOpen}
            setIsDialogOpen={setIsUpdateDialogOpen}
            search={selectedSearch}
          />
        )}
      </CardContent>
      <CardFooter className="flex justify-end">
        {sortableSearch.length > 0 && (
          <Button
            type="button"
            variant="outline"
            disabled={isSorting}
            onClick={async () => {
              setIsSorting(true)

              try {
                const ids = sortableSearch.map((faq) => faq.id) as number[]

                const { data: response } = await MediaRoomService.sortSearch({ ids })
                if (response.success) {
                  toast({
                    title: 'Sort searches successfully',
                    variant: 'success'
                  })
                  mutate(
                    (key) =>
                      typeof key === 'string' && key.startsWith(MediaRoomService.findSearches)
                  )
                }
              } catch (error) {
                toast({
                  title: 'Action failed, please try again',
                  variant: 'destructive'
                })
                setSortableSearch(
                  initialValue.map((faq) => ({ ...faq, id: faq.id as UniqueIdentifier }))
                )
              }

              setIsSorting(false)
            }}
          >
            {isSorting ? (
              <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <SaveIcon size="16" className="mr-2" />
            )}
            Save Ordering
          </Button>
        )}
      </CardFooter>
    </Card>
  )
}

export default SearchCard
