import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import {
  <PERSON>alog,
  <PERSON>alogClose,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { useToast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import MediaRoomService from '@/network/services/media_room'
import { Search, UpdateSearchInput } from '@/types/Cms'
import { FC, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { mutate } from 'swr'

interface UpdateSearchFormProps extends React.HTMLAttributes<HTMLDivElement> {
  isDialogOpen: boolean
  setIsDialogOpen: (value: boolean) => void
  search: Search
}

export const UpdateSearchForm: FC<UpdateSearchFormProps> = ({
  isDialogOpen,
  setIsDialogOpen,
  search,
  className
}) => {
  const { toast } = useToast()
  const form = useForm<UpdateSearchInput>({
    defaultValues: {
      title: search.title ?? '',
      link: search.link ?? '',
    },
    shouldUseNativeValidation: false
  })

  useEffect(() => {
    form.reset({
      title: search.title ?? '',
      link: search.link ?? ''
    })
  }, [search])

  const onSubmit = form.handleSubmit(async (values) => {
    console.log('submit values: ', values)
    try {
      const { data: response } = await MediaRoomService.updateSearch(search.id, values)

      if (response.success) {
        mutate((key) => typeof key === 'string' && key.startsWith(MediaRoomService.findSearches))
        toast({
          title: 'Search updated successfully',
          variant: 'success'
        })
        setIsDialogOpen(false)
      }
    } catch (error) {
      console.error(error)
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <Dialog
      open={isDialogOpen}
      onOpenChange={(isOpen) => {
        setIsDialogOpen(isOpen)
      }}
    >
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle className="flex justify-between items-center">
            Update Search
            <div className="flex space-x-2">
              <DialogClose type="button" asChild>
                <Button variant="outline">Cancel</Button>
              </DialogClose>
              <Button
                type="submit"
                form="update-search-form"
                disabled={form.formState.isSubmitting}
                onClick={() => {
                  form.clearErrors()
                }}
              >
                {form.formState.isSubmitting && (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                )}
                Save
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form
            id="update-search-form"
            onSubmit={onSubmit}
            className={cn('flex flex-col space-y-8', className)}
          >
            <FormField
              control={form.control}
              name="title"
              rules={{ required: 'Please enter the title' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Title*</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="link"
              rules={{ required: 'Please enter the link' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Link*</FormLabel>
                  <FormControl>
                    <Textarea {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
