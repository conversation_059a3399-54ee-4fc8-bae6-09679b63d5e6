import EditorComponent from '@/components/Editor/Editor'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { UpdatePageCmsInput, PageCms } from '@/types/Cms'
import { FC, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import useSWR, { mutate } from 'swr'
import { Icons } from '@/components/icons'
import MediaRoomService from '@/network/services/media_room'
import { useToast } from '@/components/ui/use-toast'
import TextEditorComponent from '@/components/Editor/TextEditor'
import { FileWithPath } from 'react-dropzone'
import FileService from '@/network/services/file'
import { Image } from '@unpic/react'
import { Dropzone } from '@/components/Form/Dropzone'
import { Input } from '@/components/ui/input'
import OrSeparator from '@/components/ui/orSeparator'

const SLUG = 'terms-of-use'

const TermsOfUseCard = () => {
  const { data, isLoading, error } = useSWR(MediaRoomService.findPage(SLUG))

  if (error) {
    return <></>
  }

  if (isLoading) {
    return <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
  }

  const terms = data.data

  return <TermsOfUseForm initialValue={terms} />
}

const TermsOfUseForm: FC<{ initialValue: PageCms }> = ({ initialValue }) => {
  const { toast } = useToast()
  const form = useForm<UpdatePageCmsInput>({
    shouldUseNativeValidation: false,
    defaultValues: {
      ...initialValue,
      title: initialValue.title ?? '',
      subtitle: initialValue.subtitle ?? '',
      metadata: initialValue.metadata
    }
  })

  useEffect(() => {
    form.reset({
      ...initialValue,
      title: initialValue.title ?? '',
      subtitle: initialValue.subtitle ?? '',
      metadata: initialValue.metadata
    })
  }, [initialValue])

  const seoThumbnailUrl = form.watch('metadata.seo.thumbnail')
  const seoMedia = form.watch('tmp_seo_media') as (FileWithPath & { preview: string }) | undefined

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      if (values.tmp_seo_media) {
        // only upload if no url provided
        if (!values.metadata.seo.thumbnail) {
          const { data: mediaResponse } = await FileService.upload([values.tmp_seo_media] as File[])
          values.metadata.seo.thumbnail = mediaResponse.uploads[0].url
        }
        delete values.tmp_seo_media
      }

      if (values.metadata.seo.thumbnail) {
        delete values.tmp_seo_media
      }

      console.log('submit values: ', values)

      const { data: response } = await MediaRoomService.updatePage(initialValue.id, values)

      if (response.success) {
        toast({
          title: 'Update successfully',
          variant: 'success'
        })

        mutate((key) => typeof key === 'string' && key.startsWith(MediaRoomService.findPage(SLUG)))
      }
    } catch (error) {
      console.log(error)
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0">
        <CardTitle>Terms Of Use</CardTitle>
        <Button
          type="submit"
          form="terms-of-use-form"
          disabled={form.formState.isSubmitting}
          onClick={() => {
            form.clearErrors()
          }}
          className="gap-1"
        >
          Save
          {form.formState.isSubmitting && <Icons.spinner className="h-4 w-4 animate-spin" />}
        </Button>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form id="terms-of-use-form" onSubmit={onSubmit} className="space-y-4">
            <div className="grid gap-3">
              <FormField
                control={form.control}
                name="title"
                rules={{ required: 'Please enter the title' }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Title*</FormLabel>
                    <FormControl>
                      <TextEditorComponent content={field.value} onChange={field.onChange} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            {/* <div className="grid gap-3">
              <FormField
                control={form.control}
                name="subtitle"
                // rules={{ required: 'Please enter the subtitle' }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Subtitle</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div> */}
            <div className="grid gap-3">
              <FormField
                control={form.control}
                name="description"
                rules={{ required: 'Please enter the description' }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description*</FormLabel>
                    <FormControl>
                      <EditorComponent content={field.value} onChange={field.onChange} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="metadata.seo.title"
                // rules={{ required: 'Please enter the SEO title' }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>SEO Title</FormLabel>
                    <FormControl>
                      <TextEditorComponent content={field.value} onChange={field.onChange} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="metadata.seo.description"
                // rules={{ required: 'Please enter the SEO description' }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>SEO Description</FormLabel>
                    <FormControl>
                      <TextEditorComponent content={field.value} onChange={field.onChange} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="metadata.seo.thumbnail"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>SEO Thumbnail</FormLabel>
                    <FormLabel>Image URL</FormLabel>
                    <FormControl>
                      <Input placeholder="Image Url" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <OrSeparator />

              <FormField
                control={form.control}
                name="tmp_seo_media"
                render={({ field }) => (
                  <>
                    <FormItem>
                      <FormLabel>Upload Image</FormLabel>
                      <FormControl>
                        <Dropzone
                          accept={{ 'image/*': ['.jpeg', '.jpg', '.png'] }}
                          description="Drop your image here, or click to browse"
                          multiple={false}
                          onDrop={(acceptedFiles) => {
                            const file = acceptedFiles[0]
                            const exist = seoMedia?.path === file.path

                            if (!exist) {
                              Object.assign(file, {
                                preview: URL.createObjectURL(file)
                              })
                            }

                            form.setValue('tmp_seo_media', file)
                          }}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>

                    {(seoThumbnailUrl || seoMedia?.preview) && (
                      <div className="mt-2 flex flex-col space-y-4">
                        <Image
                          src={seoMedia?.preview ?? seoThumbnailUrl ?? ''}
                          layout="constrained"
                          className="rounded-md"
                          width={320}
                          height={320}
                        />
                      </div>
                    )}
                  </>
                )}
              />
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}

export default TermsOfUseCard
