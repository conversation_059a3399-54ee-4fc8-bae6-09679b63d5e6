import TextEditorComponent from '@/components/Editor/TextEditor'
import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { useToast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import TestimonialService from '@/network/services/testimonial'
import { CreateTestimonialInput } from '@/types/CreateTestimonial'
import { FC } from 'react'
import { useForm } from 'react-hook-form'
import { mutate } from 'swr'

interface CreateTestimonialFormProps extends React.HTMLAttributes<HTMLDivElement> {
  isDialogOpen: boolean
  setIsDialogOpen: (value: boolean) => void
}

export const CreateTestimonialForm: FC<CreateTestimonialFormProps> = ({
  isDialogOpen,
  setIsDialogOpen,
  className
}) => {
  const { toast } = useToast()
  const form = useForm<CreateTestimonialInput>({
    shouldUseNativeValidation: false
  })

  const onSubmit = form.handleSubmit(async (values) => {
    console.log('submit values: ', values)
    try {
      const { data: response } = await TestimonialService.createTestimonial(values)

      if (response.success) {
        mutate(
          (key) => typeof key === 'string' && key.startsWith(TestimonialService.getTestimonials)
        )
        toast({
          title: 'Testimonial created successfully',
          variant: 'success'
        })
        setIsDialogOpen(false)
      }
    } catch (error) {
      console.error(error)
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <Dialog
      open={isDialogOpen}
      onOpenChange={(isOpen) => {
        setIsDialogOpen(isOpen)
      }}
    >
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            New Testimonial
            <div className="flex space-x-2">
              <DialogClose type="button" asChild>
                <Button variant="outline">Cancel</Button>
              </DialogClose>
              <Button
                type="submit"
                form="create-testimonial-form"
                disabled={form.formState.isSubmitting}
                onClick={() => {
                  form.clearErrors()
                }}
              >
                {form.formState.isSubmitting && (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                )}
                Save
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form
            id="create-testimonial-form"
            onSubmit={onSubmit}
            className={cn('flex flex-col space-y-8', className)}
          >
            <FormField
              control={form.control}
              name="name"
              rules={{ required: 'Please enter the name' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <TextEditorComponent content={field.value} onChange={field.onChange} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              rules={{ required: 'Please enter the description' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <TextEditorComponent content={field.value} onChange={field.onChange} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="brand"
              rules={{ required: 'Please enter the brand' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Brand</FormLabel>
                  <FormControl>
                    <TextEditorComponent content={field.value} onChange={field.onChange} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="designation"
              rules={{ required: 'Please enter the designation' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Designation</FormLabel>
                  <FormControl>
                    <TextEditorComponent content={field.value} onChange={field.onChange} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
