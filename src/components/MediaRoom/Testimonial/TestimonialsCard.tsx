import { Button } from '@/components/ui/button'
import { <PERSON>, Card<PERSON>eader, Card<PERSON><PERSON><PERSON>, CardContent, CardFooter } from '@/components/ui/card'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import {
  EditIcon,
  GripHorizontalIcon,
  MoreHorizontal,
  PlusCircleIcon,
  SaveIcon,
  Trash2Icon
} from 'lucide-react'
import { FC, useEffect, useState } from 'react'
import useAuth from '@/hooks/useAuth'
import { Testimonials } from '@/types/CreateTestimonial'
import {
  DndContext,
  DragEndEvent,
  KeyboardSensor,
  PointerSensor,
  UniqueIdentifier,
  closestCenter,
  useSensor,
  useSensors
} from '@dnd-kit/core'
import {
  SortableContext,
  arrayMove,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy
} from '@dnd-kit/sortable'
import SortableItem from '@/components/ui/drag-and-drop'
import TestimonialService from '@/network/services/testimonial'
import useSWR, { mutate } from 'swr'
import { useToast } from '@/components/ui/use-toast'
import { Icons } from '@/components/icons'
import { CreateTestimonialForm } from './CreateTestimonialForm'
import { UpdateTestimonialForm } from './UpdateTestimonialForm'
import MediaRoomService from '@/network/services/media_room'
import { PageCms, UpdatePageCmsInput } from '@/types/Cms'
import { useForm } from 'react-hook-form'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'

const TestimonialsCard = () => {
  const { data, isLoading, error } = useSWR(TestimonialService.getTestimonials)
  const {
    data: homeCms,
    isLoading: homeLoading,
    error: homeError
  } = useSWR(MediaRoomService.findPage('home'))

  if (error || homeError) {
    return <></>
  }

  if (isLoading || homeLoading) {
    return <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
  }

  const testimonials = data.data
  const home = homeCms.data as PageCms

  return <TestimonialView homePage={home} testimonials={testimonials} />
}

const TestimonialView: FC<{
  homePage: PageCms
  testimonials: Testimonials[]
}> = ({ homePage, testimonials }) => {
  const { role } = useAuth()
  const { toast } = useToast()

  const form = useForm<UpdatePageCmsInput>({
    shouldUseNativeValidation: false,
    defaultValues: {
      ...homePage,
      metadata: homePage.metadata ?? {
        testimonial_headlines: 'Testimonial Headlines'
      }
    }
  })

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false)
  const [testimonialSelected, setTestimonialSelected] = useState<Testimonials | null>(null)
  const [isSorting, setIsSorting] = useState(false)

  // sorting sections
  const [sortableTestimonial, setSortableTestimonial] = useState(
    testimonials.map((testimonial) => ({
      ...testimonial,
      id: testimonial.id as UniqueIdentifier
    }))
  )
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates
    })
  )

  function handleDragEnd(event: DragEndEvent) {
    const { active, over } = event

    if (active.id !== over?.id) {
      setSortableTestimonial((items) => {
        const oldIndex = items.map((item) => item.id).indexOf(active.id)
        const newIndex = items.map((item) => item.id).indexOf(over!.id)

        return arrayMove(items, oldIndex, newIndex)
      })
    }
  }

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      console.log('submit values: ', values)

      const { data: response } = await MediaRoomService.updatePage(homePage.id, values)

      if (response.success) {
        toast({
          title: 'Update successfully',
          variant: 'success'
        })

        mutate(
          (key) => typeof key === 'string' && key.startsWith(MediaRoomService.findPage('home'))
        )
      }
    } catch (error) {
      console.log(error)
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  useEffect(() => {
    setSortableTestimonial(
      testimonials.map((testimonial) => ({
        ...testimonial,
        id: testimonial.id as UniqueIdentifier
      }))
    )
  }, [testimonials])

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <CardTitle>Testimonial Headlines</CardTitle>
          <Button
            type="submit"
            form="home-form"
            disabled={form.formState.isSubmitting}
            onClick={() => {
              form.clearErrors()
            }}
            className="gap-1"
          >
            Save
            {form.formState.isSubmitting && <Icons.spinner className="h-4 w-4 animate-spin" />}
          </Button>
        </CardHeader>
        <CardContent>
          <div className="mt-2 flex flex-col space-y-4">
            <Form {...form}>
              <form id="home-form" onSubmit={onSubmit} className="space-y-4 mb-4">
                <FormField
                  control={form.control}
                  name="metadata.testimonial_headlines"
                  rules={{ required: 'Please enter the testimonial headlines' }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Testimonial Headlines Title</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </form>
            </Form>
            {role != 'vendor' && (
              <Button type="button" onClick={() => setIsCreateDialogOpen(true)}>
                <PlusCircleIcon size="16" className="mr-2" />
                Create Testimonial
              </Button>
            )}
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              <SortableContext
                items={sortableTestimonial ?? []}
                strategy={verticalListSortingStrategy}
              >
                {sortableTestimonial.map((testimonial) => (
                  <div
                    key={testimonial.id}
                    className="grid grid-cols-[1fr_36px] items-center gap-x-2"
                  >
                    <SortableItem
                      id={testimonial.id as UniqueIdentifier}
                      className="grid grid-cols-[18px_1fr] items-center gap-x-2"
                    >
                      <GripHorizontalIcon size={16} />
                      <div className="flex flex-col">
                        <p className="text-xs">{testimonial.designation}</p>
                        <p className="font-semibold">{testimonial.name}</p>
                        <p className="text-sm text-muted-foreground">{testimonial.brand}</p>
                        <p className="text-sm">{testimonial.description}</p>
                      </div>
                    </SortableItem>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          className="space-x-2"
                          onClick={() => {
                            setIsUpdateDialogOpen(true)
                            setTestimonialSelected({
                              ...testimonial,
                              id: testimonial.id as number
                            })
                          }}
                        >
                          <EditIcon size={16} />
                          <span>Edit</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={async (e) => {
                            e.stopPropagation()

                            try {
                              const { data: response } = await TestimonialService.deleteTestimonial(
                                testimonial.id
                              )

                              if (response.success) {
                                toast({
                                  title: `Delete testimonial successfully`,
                                  variant: 'success'
                                })

                                mutate(
                                  (key) =>
                                    typeof key === 'string' &&
                                    key.startsWith(TestimonialService.getTestimonials)
                                )
                              }
                            } catch (error) {
                              toast({
                                title: 'Action failed, please try again',
                                variant: 'destructive'
                              })
                            }
                          }}
                          className="space-x-2"
                        >
                          <Trash2Icon size="16" color="red" />
                          <span className="text-red-500">Delete</span>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                ))}
              </SortableContext>
            </DndContext>
          </div>

          {isCreateDialogOpen && (
            <CreateTestimonialForm
              isDialogOpen={isCreateDialogOpen}
              setIsDialogOpen={setIsCreateDialogOpen}
            />
          )}
          {testimonialSelected && (
            <UpdateTestimonialForm
              testimonial={testimonialSelected}
              isDialogOpen={isUpdateDialogOpen}
              setIsDialogOpen={(value: boolean) => {
                setTestimonialSelected(null)
                setIsUpdateDialogOpen(value)
              }}
            />
          )}
        </CardContent>
        <CardFooter className="flex justify-end">
          {sortableTestimonial.length > 0 && (
            <Button
              type="button"
              variant="outline"
              disabled={isSorting}
              onClick={async () => {
                setIsSorting(true)

                try {
                  const ids = sortableTestimonial.map((testimonial) => testimonial.id) as number[]

                  const { data: response } = await TestimonialService.sortTestimonials({ ids })
                  if (response.success) {
                    toast({
                      title: 'Sort testimonials successfully',
                      variant: 'success'
                    })
                    mutate(
                      (key) =>
                        typeof key === 'string' &&
                        key.startsWith(TestimonialService.getTestimonials)
                    )
                  }
                } catch (error) {
                  toast({
                    title: 'Action failed, please try again',
                    variant: 'destructive'
                  })
                  setSortableTestimonial(
                    testimonials.map((testimonial) => ({
                      ...testimonial,
                      id: testimonial.id as UniqueIdentifier
                    }))
                  )
                }

                setIsSorting(false)
              }}
            >
              {isSorting ? (
                <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <SaveIcon size="16" className="mr-2" />
              )}
              Save Ordering
            </Button>
          )}
        </CardFooter>
      </Card>
    </>
  )
}

export default TestimonialsCard
