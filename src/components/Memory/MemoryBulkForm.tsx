/* eslint-disable @typescript-eslint/ban-ts-comment */
import * as React from 'react'
import { useFormContext } from 'react-hook-form'
import { mutate } from 'swr'

import { bytesToSize, cn } from '@/lib/utils'
import {
  FormControl,
  // FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { toast } from '@/components/ui/use-toast'
import { BulkCreateMemoryInput } from '@/types/Memory'
import MemoryService from '@/network/services/memory'
import { Label } from '../ui/label'
import { Button } from '../ui/button'
import { Trash2Icon, MoreHorizontal } from 'lucide-react'
import { Dropzone } from '../Form/Dropzone'
import { FileWithPath } from 'react-dropzone'
import { Image } from '@unpic/react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { MemoryCollectionSearchBoxAll } from './MemoryCollectionSearchbox'

interface MemoryFormProps extends React.HTMLAttributes<HTMLDivElement> {}

export function BulkCreateMemoryForm({ className }: MemoryFormProps) {
  const form = useFormContext<BulkCreateMemoryInput>()

  const thumbnails = form.watch('memory_thumbnail_files') ?? []
  const medias = form.watch('memory_media_files') ?? []

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      console.log(values)

      await MemoryService.bulkCreateMemory(values)
      mutate((key) => typeof key === 'string' && key.startsWith(MemoryService.findMemories))
      toast({
        title: 'Memory created',
        variant: 'success'
      })
    } catch (error) {
      console.error(error)
      form.setError('root', {})
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <form
      id="bulk-create-memory-form"
      onSubmit={onSubmit}
      className={cn('flex flex-col space-y-8 max-w-4xl mx-auto', className)}
    >
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="col-span-2">
          <FormField
            control={form.control}
            name="memory_media_files"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Memory File</FormLabel>
                <FormControl>
                  <Dropzone
                    formName="bulk-create-memory-form"
                    accept={{ 'image/*': ['.jpeg', '.jpg', '.png'], 'video/*': ['.mp4'] }}
                    description="Drop your videos or images here, or click to browse"
                    multiple={true}
                    onDrop={(acceptedFiles) => {
                      console.log('ondrop')
                      const mediaTmp = medias ? [...medias] : []

                      for (const index in acceptedFiles) {
                        const file = acceptedFiles[index]
                        const findIndex = medias?.findIndex(
                          (f) => (f as FileWithPath)?.path == file.path
                        )

                        if ((findIndex ?? -1) == -1) {
                          const preview = Object.assign(file, {
                            preview: URL.createObjectURL(file)
                          })

                          mediaTmp.push(preview)
                        }
                      }

                      console.log(form.getValues('memory_media_files'))
                      form.setValue('memory_media_files', mediaTmp)
                    }}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
                {(medias.length > 0 || thumbnails.length > 0) && (
                  <div className="flex flex-col space-y-4 mt-2">
                    {medias?.map((file, index) => {
                      return (
                        <div
                          key={index}
                          className="grid grid-cols-[150px_1fr_36px] space-x-2 items-center"
                        >
                          {file.type.startsWith('image') && (
                            <Image
                              key={index}
                              src={file.preview ?? ''}
                              height={150}
                              width={150}
                              objectFit="contain"
                              className="rounded-md"
                            />
                          )}
                          {file.type.startsWith('video') && (
                            <video
                              // controls
                              key={index}
                              src={file.preview ?? ''}
                              height={150}
                              width={150}
                              className="rounded-md"
                            />
                          )}
                          <div className="flex flex-col">
                            <Label className="text-xs font-normal">{file.name}</Label>
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.stopPropagation()
                                  const imagesTmp = [...medias]
                                  imagesTmp.splice(index, 1)
                                  form.setValue('memory_media_files', imagesTmp, {
                                    shouldValidate: true
                                  })
                                }}
                                className="space-x-2"
                              >
                                <Trash2Icon size="16" />
                                <span>Delete</span>
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      )
                    })}
                  </div>
                )}
              </FormItem>
            )}
          />
        </div>

        <div className="col-span-2">
          <FormField
            control={form.control}
            name="memory_thumbnail_files"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Memory Thumbnail</FormLabel>
                <FormControl>
                  <Dropzone
                    formName="bulk-create-memory-form"
                    accept={{ 'image/*': ['.jpeg', '.jpg', '.png'], 'video/*': ['.mp4'] }}
                    description="Drop your videos or images here, or click to browse"
                    multiple={true}
                    onDrop={(acceptedFiles) => {
                      console.log('ondrop')
                      const thumbnailTmp = thumbnails ? [...thumbnails] : []

                      for (const index in acceptedFiles) {
                        const file = acceptedFiles[index]
                        const findIndex = thumbnails?.findIndex(
                          (f) => (f as FileWithPath)?.path == file.path
                        )

                        if ((findIndex ?? -1) == -1) {
                          const preview = Object.assign(file, {
                            preview: URL.createObjectURL(file)
                          })

                          thumbnailTmp.push(preview)
                        }
                      }

                      form.setValue('memory_thumbnail_files', thumbnailTmp)
                    }}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
                {thumbnails?.map((file, index) => {
                  return (
                    <div
                      key={index}
                      className="grid grid-cols-[150px_1fr_36px] space-x-2 items-center"
                    >
                      {file?.type.startsWith('image') && (
                        <Image
                          key={index}
                          src={file.preview ?? ''}
                          height={150}
                          width={150}
                          objectFit="contain"
                          className="rounded-md"
                        />
                      )}
                      {file?.type.startsWith('video') && (
                        <video
                          // controls
                          key={index}
                          src={file.preview ?? ''}
                          height={150}
                          width={150}
                          className="rounded-md"
                        />
                      )}
                      <div className="flex flex-col">
                        <Label className="text-xs font-normal">{file?.path}</Label>
                        {file?.size && (
                          <Label className="text-xs font-normal text-gray-500">
                            {bytesToSize(file.size)}
                          </Label>
                        )}
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation()
                              const thumbnailTmp = thumbnails ? [...thumbnails] : []
                              thumbnailTmp.splice(index, 1)
                              form.setValue('memory_thumbnail_files', thumbnailTmp)
                            }}
                            className="space-x-2"
                          >
                            <Trash2Icon size="16" />
                            <span>Delete</span>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  )
                })}
              </FormItem>
            )}
          />
        </div>

        <div className="col-span-2">
          <FormField
            control={form.control}
            name={'memory_collection_id'}
            render={({ field }) => {
              return (
                <FormItem className="flex flex-col col-span-2">
                  <MemoryCollectionSearchBoxAll id={field.value + ''} name={field.name} />
                  <FormMessage />
                </FormItem>
              )
            }}
          />
        </div>
      </div>
    </form>
  )
}
