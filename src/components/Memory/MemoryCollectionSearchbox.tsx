import { cn } from '@/lib/utils'
import { ChevronsUpDown } from 'lucide-react'
import { isEmpty } from 'radash'
import { FC, useEffect, useState } from 'react'
import { useFormContext } from 'react-hook-form'
import { FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Button } from '@/components/ui/button'
import { Search } from '@/components/Form/Search'
import { MemoryCollection } from '@/types/MemoryCollection'
import MemoryCollectionService from '@/network/services/memory_collection'
import { useSearchMemoryCollections } from '@/hooks/memory_collections/useMemoryCollections'

// multiple select
export const MemoryCollectionSearchBoxAll: FC<{
  id: string | undefined
  name: string
}> = ({ id, name }) => {
  const [open, setOpen] = useState(false)
  const [memoryCollection, setMemoryCollection] = useState<MemoryCollection>()
  const form = useFormContext()

  useEffect(() => {
    const fetchMemory = async () => {
      const memoryCollectionData = await MemoryCollectionService.getMemoryCollection(id!)

      if (memoryCollectionData.data.data) {
        setMemoryCollection(memoryCollectionData.data.data)
      }
    }

    if (id) {
      fetchMemory()
    }
  }, [id])

  const handleSetActive = (memoryCollection: MemoryCollection) => {
    setMemoryCollection(memoryCollection)

    form.setValue(name, memoryCollection.id)
  }

  const displayName = !isEmpty(memoryCollection)
    ? memoryCollection?.title + ' | ' + memoryCollection?.id
    : 'Choose a memory collection'

  return (
    <FormItem className="flex flex-col">
      <FormLabel>Memory Collection to be Linked</FormLabel>

      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <FormControl>
            <Button
              variant="outline"
              role="combobox"
              className={cn(
                'w-[200px] justify-between',
                isEmpty(memoryCollection) && 'text-muted-foreground',
                'w-full'
              )}
            >
              <>{displayName}</>

              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </FormControl>
        </PopoverTrigger>

        <PopoverContent side="bottom" className={cn('p-0', 'w-[300px]')} withPortal={false}>
          <Search<MemoryCollection, MemoryCollection>
            fn={useSearchMemoryCollections}
            filterFn={(memoryCollections: MemoryCollection[] | undefined) => {
              if (!memoryCollections) {
                return []
              }
              return memoryCollections
            }}
            renderFn={(memoCollection: MemoryCollection) =>
              memoCollection.title + ' | ' + memoCollection.id
            }
            valueFn={(memoCollection: MemoryCollection) => memoCollection.id.toString()}
            compareFn={(memoCollection: MemoryCollection) => {
              if (isEmpty(memoryCollection)) {
                return false
              }

              const findIndex = memoryCollection?.id == memoCollection.id

              return findIndex
            }}
            selectedResult={memoryCollection}
            onSelectResult={handleSetActive}
            // onDeselectResult={handleSetInactive}
          />
        </PopoverContent>
      </Popover>
      <FormMessage />
    </FormItem>
  )
}
