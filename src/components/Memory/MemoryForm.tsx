/* eslint-disable @typescript-eslint/ban-ts-comment */
import * as React from 'react'
import { useFormContext } from 'react-hook-form'
import { mutate } from 'swr'

import { cn } from '@/lib/utils'
import {
  FormControl,
  // FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { toast } from '@/components/ui/use-toast'
import { Input } from '@/components/ui/input'
import { CreateMemoryInput } from '@/types/Memory'
import MemoryService from '@/network/services/memory'
import { ChangeEvent, useState } from 'react'
import { Label } from '../ui/label'
import { Card } from '../ui/card'

interface MemoryFormProps extends React.HTMLAttributes<HTMLDivElement> {}

export function CreateMemoryForm({ className }: MemoryFormProps) {
  const form = useFormContext<CreateMemoryInput>()

  const [mediaType, setMediaType] = useState('File')
  const [media, setMedia] = useState('')
  const [file, setFile] = useState<File>()

  function handleChange(e: ChangeEvent<HTMLInputElement>) {
    if (e.target.files && e.target.files[0]) {
      setMedia(URL.createObjectURL(e.target.files[0]))
      setFile(e.target.files[0])
    }
  }

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      if (file) {
        values['thumbnail_file'] = file
      }

      await MemoryService.createMemory(values)
      mutate((key) => typeof key === 'string' && key.startsWith(MemoryService.findMemories))
      toast({
        title: 'Memory created',
        variant: 'success'
      })
    } catch (error) {
      console.error(error)
      form.setError('root', {})
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <form
      id="create-memory-form"
      onSubmit={onSubmit}
      className={cn('flex flex-col space-y-8 max-w-4xl mx-auto', className)}
    >
      <div className="gap-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div>
          <FormLabel>Image Source Options</FormLabel>
          <div className="flex gap-2">
            <div className="flex gap-2">
              <Input
                type="radio"
                id="file"
                name="file_type"
                value="File"
                onChange={(e) => setMediaType(e.target.value)}
                className="h-6"
                defaultChecked
              />
              <div className="flex h-full content-center flex-wrap">
                <Label className="h-min">File</Label>
              </div>
            </div>

            <div className="flex gap-2">
              <Input
                type="radio"
                id="url"
                name="file_type"
                value="URL"
                onChange={(e) => setMediaType(e.target.value)}
                className="h-6"
              />
              <div className="flex h-full content-center flex-wrap">
                <Label className="h-min">URL</Label>
              </div>
            </div>
          </div>
        </div>
        {mediaType == 'File' && (
          <div className="mb-4">
            <FormLabel>Thumbnail</FormLabel>
            <div className="w-48 h-80 relative mt-2">
              <Card className="w-full h-full p-0  cursor-pointer">
                {media != '' ? (
                  <img
                    src={media != '' ? media : ''}
                    alt="File"
                    style={{ objectFit: 'contain', pointerEvents: 'none', height: '100%' }}
                  />
                ) : (
                  <div
                    className="w-full h-full justify-center items-center absolute flex"
                    style={{ pointerEvents: 'none' }}
                  >
                    <div className="text-3xl">+</div>
                  </div>
                )}
                <input
                  type="file"
                  accept="image/*"
                  id="fileInput"
                  className=" w-full h-full cursor-pointer opacity-0 absolute top-0"
                  onChange={handleChange}
                />
              </Card>
            </div>
          </div>
        )}

        {mediaType == 'URL' && (
          <FormField
            control={form.control}
            name="thumbnail_url"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Thumbnail URL</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        <FormField
          control={form.control}
          name="all_rank"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>General Rank</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="collection_rank"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Collection Rank</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </form>
  )
}
