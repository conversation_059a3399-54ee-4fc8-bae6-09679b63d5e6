import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle, Content, Title } from '@/components/ui/card'
import { Memory } from '@/types/Memory'
import { MediaType, MediaStatus } from '@/types/MemoryMedia'
import { DateTime } from 'luxon'
import { FC } from 'react'
import { Separator } from '../ui/separator'
import { FileImageIcon, FileVideoIcon } from 'lucide-react'

const MemoryInfoCard: FC<{ memory?: Memory }> = ({ memory }) => {
  return (
    <>
      <Card>
        <CardContent>
          <div className="grid grid-cols-2 gap-2 pt-10">
            <Title>ID</Title>
            <Content>{memory?.id}</Content>
            <Title>Memory Name</Title>
            <Content>{memory?.name}</Content>
            <Title>Memory General Rank</Title>
            <Content>{memory?.all_rank}</Content>
            <Title>Memory Collection Rank</Title>
            <Content>{memory?.collection_rank}</Content>
            <Title>Memory Thumbnail</Title>
            <Content>
              <img
                className="rounded-lg w-[250px] float-right"
                src={memory?.thumbnail}
                alt="event-info-image"
              />
            </Content>
            <Title>Created Date</Title>
            <Content>
              {memory?.created_at
                ? DateTime.fromISO(memory.created_at.toString()).toISODate()
                : '-'}
            </Content>
            <Title>Last Updated Date</Title>
            <Content>
              {memory?.updated_at
                ? DateTime.fromISO(memory.updated_at.toString()).toISODate()
                : '-'}
            </Content>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Memory's Media</CardTitle>
        </CardHeader>
        <CardContent>
          {memory?.medias.length == 0 ? (
            <p className="text-muted-foreground text-sm">No media linked with this memory</p>
          ) : (
            memory?.medias.map((media, index) => {
              let mediaContent = <></>

              switch (media?.type) {
                case MediaType.IMAGE:
                  mediaContent = (
                    <img
                      className="rounded-lg w-[250px] float-right"
                      src={media.url}
                      alt="event-info-image"
                    />
                  )
                  break
                case MediaType.VIDEO:
                  mediaContent = (
                    <iframe
                      className="rounded-lg w-[550px] h-[350px] float-right"
                      src={media.url}
                      title="YouTube video player"
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                    ></iframe>
                  )
                  break
              }

              return (
                <div className="grid grid-cols-2 gap-2 pt-10">
                  <Title>Status</Title>
                  <Content>
                    <span
                      style={{
                        display: 'inline-block',
                        width: '8px',
                        height: '8px',
                        borderRadius: '50%',
                        marginRight: '8px',
                        backgroundColor: media.status == MediaStatus.ACTIVE ? '#4CAF50' : '#F44336'
                      }}
                    ></span>

                    {media.status == MediaStatus.ACTIVE ? 'Active' : 'Inactive'}
                  </Content>
                  <Title>Type</Title>
                  <Content>
                    {media?.type == MediaType.VIDEO ? (
                      <FileVideoIcon
                        style={{
                          display: 'inline-block',
                          width: '20px',
                          height: '20px',
                          borderRadius: '50%',
                          marginRight: '3px',
                          marginBottom: '3px'
                        }}
                      />
                    ) : (
                      <FileImageIcon
                        style={{
                          display: 'inline-block',
                          width: '20px',
                          height: '20px',
                          borderRadius: '50%',
                          marginRight: '3px',
                          marginBottom: '3px'
                        }}
                      />
                    )}
                    {media?.type == MediaType.VIDEO ? 'Video' : 'Image'}
                  </Content>
                  <Title>Media</Title>
                  <Content>{mediaContent}</Content>
                  <Title>Created Date</Title>
                  <Content>
                    {memory?.created_at
                      ? DateTime.fromISO(memory.created_at.toString()).toISODate()
                      : '-'}
                  </Content>
                  <Title>Last Updated Date</Title>
                  <Content>
                    {memory?.updated_at
                      ? DateTime.fromISO(memory.updated_at.toString()).toISODate()
                      : '-'}
                  </Content>
                  {memory.medias.length > 1 && index + 1 != memory.medias.length && (
                    <Separator className="col-span-2" />
                  )}
                </div>
              )
            })
          )}
        </CardContent>
      </Card>
    </>
  )
}

export default MemoryInfoCard
