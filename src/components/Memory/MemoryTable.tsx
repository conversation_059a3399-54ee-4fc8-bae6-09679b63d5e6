import { DataTable } from '../Table/DataTable'
import { ColumnDef, createColumnHelper, Row } from '@tanstack/react-table'
import { useNavigate } from 'react-router-dom'
import MemoryService from '@/network/services/memory'
import { DateTime } from 'luxon'
import { Memory, MemoryResponse } from '@/types/Memory'
import { FC } from 'react'
import { useToast } from '../ui/use-toast'
import { mutate } from 'swr'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu'
import { Button } from '../ui/button'
import { MoreHorizontal, TrashIcon } from 'lucide-react'

const columnHelper = createColumnHelper<Memory>()

const columns: ColumnDef<Memory>[] = [
  {
    accessorKey: 'id',
    header: 'ID'
  },
  {
    accessorKey: 'name',
    header: 'Name'
  },
  {
    accessorKey: 'all_rank',
    header: 'General Rank'
  },
  {
    accessorKey: 'collection_rank',
    header: 'Collection Rank'
  },
  {
    accessorKey: 'created_at',
    header: 'Created Date',
    cell: (props) => {
      return DateTime.fromISO(props.getValue<DateTime>().toString()).toISODate()
    }
  },
  {
    accessorKey: 'updated_at',
    header: 'Last Updated Date',
    cell: (props) => {
      return DateTime.fromISO(props.getValue<DateTime>().toString()).toISODate()
    }
  },
  columnHelper.display({
    id: 'actions',
    cell: (props) => <RowActions row={props.row} />
  })
]

const RowActions: FC<{ row: Row<Memory> }> = ({ row }) => {
  const { toast } = useToast()

  const deleteMemory = async (memory: Memory) => {
    try {
      const { data: response } = await MemoryService.deleteMemory(row.original.id)

      if (response != null) {
        mutate((key) => typeof key === 'string' && key.startsWith(MemoryService.findMemories))
        toast({
          title: `Deleted memory ${memory.name}`,
          variant: 'success'
        })
      }

      console.log(response)
    } catch (e) {
      console.log(e)

      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  }
  return (
    <div className="w-full flex justify-end">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0" onClick={(e) => e.stopPropagation()}>
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuGroup>
            <DropdownMenuItem
              onClick={async (e) => {
                e.stopPropagation()
                await deleteMemory(row.original)
              }}
              className="space-x-2"
            >
              <TrashIcon size="16" className="mr-2" />
              <span className="capitalize text-xs">Delete</span>
            </DropdownMenuItem>
          </DropdownMenuGroup>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}

const MemoryTable = () => {
  const nav = useNavigate()
  return (
    <>
      <DataTable<Memory, unknown, MemoryResponse>
        columns={columns}
        swrService={MemoryService.findMemories}
        toRow={MemoryService.toRow}
        toPaginate={MemoryService.toPaginate}
        onRowClick={(row: Row<Memory>) => {
          nav(`/memories/${row.original.id}`)
        }}
      />
    </>
  )
}

export default MemoryTable
