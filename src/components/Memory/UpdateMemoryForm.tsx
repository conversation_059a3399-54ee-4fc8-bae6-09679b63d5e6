import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import {
  <PERSON>alog,
  <PERSON>alogClose,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { useToast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import MemoryService from '@/network/services/memory'
import { Memory, UpdateMemoryInput } from '@/types/Memory'
import { ChangeEvent, FC, useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { mutate } from 'swr'
import { Label } from '../ui/label'
import { Card } from '../ui/card'

interface UpdateMemoryFormProps extends React.HTMLAttributes<HTMLDivElement> {
  isDialogOpen: boolean
  setIsDialogOpen: (value: boolean) => void
  memory: Memory
}

export const UpdateMemoryForm: FC<UpdateMemoryFormProps> = ({
  isDialogOpen,
  setIsDialogOpen,
  className,
  memory
}) => {
  const { toast } = useToast()
  const form = useForm<UpdateMemoryInput>({
    defaultValues: memory,
    shouldUseNativeValidation: false
  })

  const [mediaType, setMediaType] = useState('File')
  const [media, setMedia] = useState('')
  const [file, setFile] = useState<File>()

  function handleChange(e: ChangeEvent<HTMLInputElement>) {
    if (e.target.files && e.target.files[0]) {
      setMedia(URL.createObjectURL(e.target.files[0]))
      setFile(e.target.files[0])
    }
  }

  useEffect(() => {
    form.reset(memory)
  }, [memory])

  const onSubmit = form.handleSubmit(async (values) => {
    console.log('submit values: ', values)
    try {
      if (file) {
        values['thumbnail_file'] = file
      }

      const { data: response } = await MemoryService.updateMemory(memory.id, values)

      if (response.success) {
        mutate((key) => typeof key === 'string' && key.startsWith(MemoryService.findMemories))
        toast({
          title: 'Memory updated successfully',
          variant: 'success'
        })
        setMediaType('File')
        setIsDialogOpen(false)
      }
    } catch (error) {
      console.error(error)
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <Dialog
      open={isDialogOpen}
      onOpenChange={(isOpen) => {
        setIsDialogOpen(isOpen)
        setMediaType('File')
      }}
    >
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            Edit Memory
            <div className="flex space-x-2">
              <DialogClose type="button" asChild>
                <Button variant="outline">Cancel</Button>
              </DialogClose>
              <Button
                type="submit"
                form="update-memory-form"
                disabled={form.formState.isSubmitting}
                onClick={() => {
                  form.clearErrors()
                }}
              >
                {form.formState.isSubmitting && (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                )}
                Save
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form
            id="update-memory-form"
            onSubmit={onSubmit}
            className={cn('flex flex-col space-y-8', className)}
          >
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div>
              <FormLabel>Image Source Options</FormLabel>
              <div className="flex gap-2">
                <div className="flex gap-2">
                  <Input
                    type="radio"
                    id="file"
                    name="file_type"
                    value="File"
                    onChange={(e) => setMediaType(e.target.value)}
                    className="h-6"
                    defaultChecked
                  />
                  <div className="flex h-full content-center flex-wrap">
                    <Label className="h-min">File</Label>
                  </div>
                </div>

                <div className="flex gap-2">
                  <Input
                    type="radio"
                    id="url"
                    name="file_type"
                    value="URL"
                    onChange={(e) => setMediaType(e.target.value)}
                    className="h-6"
                  />
                  <div className="flex h-full content-center flex-wrap">
                    <Label className="h-min">URL</Label>
                  </div>
                </div>
              </div>
            </div>
            {mediaType == 'File' && (
              <div className="mb-4">
                <FormLabel>Thumbnail</FormLabel>
                <div className="w-48 h-80 relative mt-2">
                  <Card className="w-full h-full p-0  cursor-pointer">
                    {media != '' ? (
                      <img
                        src={media != '' ? media : ''}
                        alt="File"
                        style={{ objectFit: 'contain', pointerEvents: 'none', height: '100%' }}
                      />
                    ) : (
                      <div
                        className="w-full h-full justify-center items-center absolute flex"
                        style={{ pointerEvents: 'none' }}
                      >
                        <div className="text-3xl">+</div>
                      </div>
                    )}
                    <input
                      type="file"
                      accept="image/*"
                      id="fileInput"
                      className=" w-full h-full cursor-pointer opacity-0 absolute top-0"
                      onChange={handleChange}
                    />
                  </Card>
                </div>
              </div>
            )}

            {mediaType == 'URL' && (
              <FormField
                control={form.control}
                name="thumbnail_url"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Thumbnail URL</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <FormField
              control={form.control}
              name="all_rank"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>General Rank</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="collection_rank"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Collection Rank</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
