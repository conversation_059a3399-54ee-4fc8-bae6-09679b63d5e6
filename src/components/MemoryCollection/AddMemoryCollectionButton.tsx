import { Button } from '@/components/ui/button'
import { Form } from '@/components/ui/form'
import {
  Sheet,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>et<PERSON><PERSON>nt,
  <PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger
} from '@/components/ui/sheet'
import { PlusCircleIcon } from 'lucide-react'
import { FC, useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import useAuth from '@/hooks/useAuth'
import { CreateMemoryCollectionInput, MemoryCollection } from '@/types/MemoryCollection'
import { CreateMemoryCollectionForm } from './CreateMemoryCollectionForm'

export const AddMemoryCollectionButton: FC<{ memoryCollection?: MemoryCollection }> = ({
  memoryCollection
}) => {
  const { role } = useAuth()
  const [isSheetOpen, setIsSheetOpen] = useState(false)
  const form = useForm<CreateMemoryCollectionInput>({
    defaultValues: { memory_id_arr: [], ...memoryCollection },
    shouldUseNativeValidation: false
  })

  useEffect(() => {
    if (form.formState.isSubmitSuccessful) {
      form.reset()
      setIsSheetOpen(false)
    }
  }, [form.formState.isSubmitSuccessful])

  if (role == 'vendor') {
    return <></>
  }

  return (
    <div className="flex justify-end">
      <Sheet
        open={isSheetOpen}
        onOpenChange={(isOpen) => {
          setIsSheetOpen(isOpen)
        }}
      >
        <SheetTrigger asChild>
          <Button>
            <PlusCircleIcon size="16" className="mr-2" />
            Create Memory Collection
          </Button>
        </SheetTrigger>
        <SheetContent side="bottom" className="w-full h-full">
          <SheetHeader className="max-w-4xl mx-auto pt-10 pb-6">
            <SheetTitle className="flex justify-between items-center">
              New Memory Collection
              <div className="flex space-x-2">
                <SheetClose asChild>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      form.reset()
                    }}
                  >
                    Cancel
                  </Button>
                </SheetClose>
                <Button
                  type="submit"
                  form="create-memory-collection-form"
                  disabled={form.formState.isSubmitting}
                  onClick={() => {
                    form.clearErrors()
                  }}
                >
                  Submit
                </Button>
              </div>
            </SheetTitle>
          </SheetHeader>
          <Form {...form}>
            <CreateMemoryCollectionForm />
          </Form>
        </SheetContent>
      </Sheet>
    </div>
  )
}
