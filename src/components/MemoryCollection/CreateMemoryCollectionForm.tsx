/* eslint-disable @typescript-eslint/ban-ts-comment */
import * as React from 'react'
import { useFormContext } from 'react-hook-form'
import { mutate } from 'swr'

import { cn } from '@/lib/utils'
import {
  FormControl,
  // FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { toast } from '@/components/ui/use-toast'
import { Input } from '@/components/ui/input'
import { CreateMemoryCollectionInput } from '@/types/MemoryCollection'
import MemoryCollectionService from '@/network/services/memory_collection'
import { MemorySearchBoxAll } from './MemorySearchboxAll'
import { Memory } from '@/types/Memory'

interface MemoryCollectionFormProps extends React.HTMLAttributes<HTMLDivElement> {}

export function CreateMemoryCollectionForm({ className }: MemoryCollectionFormProps) {
  const form = useFormContext<CreateMemoryCollectionInput>()

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      await MemoryCollectionService.createMemoryCollection({
        memory_id_arr: values.memory_id_arr?.map((memo: Memory | number) =>
          typeof memo == 'number' ? memo : typeof memo == 'object' ? memo.id : 0
        ),
        description: values.description,
        thumbnail: values.thumbnail,
        title: values.title
      })
      mutate(
        (key) =>
          typeof key === 'string' && key.startsWith(MemoryCollectionService.findMemoryCollections)
      )
      toast({
        title: 'Memory collection created',
        variant: 'success'
      })

      form.reset()
    } catch (error) {
      console.error(error)
      form.setError('root', {})
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <form
      id="create-memory-collection-form"
      onSubmit={onSubmit}
      className={cn('flex flex-col space-y-8 max-w-4xl mx-auto', className)}
    >
      <div className="gap-4 space-y-3">
        <FormField
          control={form.control}
          name="title"
          rules={{ required: 'Please enter the collection title' }}
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Title</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="thumbnail"
          rules={{ required: 'Please provide the collection thumbnail', maxLength: 255 }}
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Thumbnail</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Description (Optional)</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="memory_id_arr"
          render={({ field }) => (
            // @ts-ignore
            <MemorySearchBoxAll name={field.name} />
          )}
        />
      </div>
    </form>
  )
}
