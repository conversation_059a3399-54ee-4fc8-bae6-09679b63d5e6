import { <PERSON>, CardContent, CardHeader, CardTitle, Content, Title } from '@/components/ui/card'
import { DateTime } from 'luxon'
import { FC } from 'react'
import { Separator } from '../ui/separator'
import { MemoryCollection } from '@/types/MemoryCollection'

const MemoryInfoCard: FC<{ memoryCollection?: MemoryCollection }> = ({ memoryCollection }) => {
  return (
    <>
      <Card>
        <CardContent>
          <div className="grid grid-cols-2 gap-2 pt-10">
            <Title>ID</Title>
            <Content>{memoryCollection?.id}</Content>
            <Title>Memory Title</Title>
            <Content>{memoryCollection?.title}</Content>
            <Title>Thumbnail</Title>
            <Content>{memoryCollection?.thumbnail}</Content>
            <Title>Description</Title>
            <Content>{memoryCollection?.description}</Content>
            <Title>Created Date</Title>
            <Content>
              {memoryCollection?.created_at
                ? DateTime.fromISO(memoryCollection.created_at.toString()).toISODate()
                : '-'}
            </Content>
            <Title>Last Updated Date</Title>
            <Content>
              {memoryCollection?.updated_at
                ? DateTime.fromISO(memoryCollection.updated_at.toString()).toISODate()
                : '-'}
            </Content>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Collection Memories</CardTitle>
        </CardHeader>
        <CardContent>
          {memoryCollection?.memories.length == 0 ? (
            <p className="text-muted-foreground text-sm">No memory linked with this collection</p>
          ) : (
            memoryCollection?.memories.map((memory, index) => {
              return (
                <div className="grid grid-cols-2 gap-2 pt-10">
                  <Title>Memory ID</Title>
                  <Content>{memory.id}</Content>
                  <Title>Memory Name</Title>
                  <Content>{memory.name}</Content>
                  <Title>Memory </Title>
                  <Content>
                    {
                      <img
                        className="rounded-lg w-[250px] float-right"
                        src={memory.thumbnail}
                        alt="memory-media-image"
                      />
                    }
                  </Content>
                  <Title>Created Date</Title>
                  <Content>
                    {memory?.created_at
                      ? DateTime.fromISO(memory.created_at.toString()).toISODate()
                      : '-'}
                  </Content>
                  <Title>Last Updated Date</Title>
                  <Content>
                    {memory?.updated_at
                      ? DateTime.fromISO(memory.updated_at.toString()).toISODate()
                      : '-'}
                  </Content>
                  {memoryCollection?.memories.length > 1 &&
                    index + 1 != memoryCollection?.memories.length && (
                      <Separator className="col-span-2" />
                    )}
                </div>
              )
            })
          )}
        </CardContent>
      </Card>
    </>
  )
}

export default MemoryInfoCard
