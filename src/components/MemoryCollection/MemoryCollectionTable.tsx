import { DataTable } from '../Table/DataTable'
import { ColumnDef, Row } from '@tanstack/react-table'
import { useNavigate } from 'react-router-dom'
import { DateTime } from 'luxon'
import MemoryCollectionService from '@/network/services/memory_collection'
import { MemoryCollection, MemoryCollectionResponse } from '@/types/MemoryCollection'

const columns: ColumnDef<MemoryCollection>[] = [
  {
    accessorKey: 'id',
    header: 'ID'
  },
  {
    accessorKey: 'title',
    header: 'Title'
  },
  {
    accessorKey: 'created_at',
    header: 'Created Date',
    cell: (props) => {
      return DateTime.fromISO(props.getValue<DateTime>().toString()).toISODate()
    }
  },
  {
    accessorKey: 'updated_at',
    header: 'Last Updated Date',
    cell: (props) => {
      return DateTime.fromISO(props.getValue<DateTime>().toString()).toISODate()
    }
  }
]

const MemoryTable = () => {
  const nav = useNavigate()
  return (
    <>
      <DataTable<MemoryCollection, unknown, MemoryCollectionResponse>
        columns={columns}
        swrService={MemoryCollectionService.findMemoryCollections}
        toRow={MemoryCollectionService.toRow}
        toPaginate={MemoryCollectionService.toPaginate}
        onRowClick={(row: Row<MemoryCollection>) => {
          nav(`/memory-collections/${row.original.id}`)
        }}
      />
    </>
  )
}

export default MemoryTable
