import { cn } from '@/lib/utils'
import { ChevronsUpDown } from 'lucide-react'
import { isEmpty } from 'radash'
import { FC, useState } from 'react'
import { useFormContext } from 'react-hook-form'
import { FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Button } from '@/components/ui/button'
import { Search } from '@/components/Form/Search'
import { Memory } from '@/types/Memory'
import { Badge } from '../ui/badge'
import { useSearchMemoriesAll } from '@/hooks/memories/useSearchMemories'

const POPOVER_WIDTH = 'w-full'

// multiple select
export const MemorySearchBoxAll: FC<{
  initMemories?: Memory[]
  name: string
}> = ({ initMemories, name }) => {
  const [memories, setMemories] = useState<Memory[]>(initMemories ? initMemories : [])
  const [open, setOpen] = useState(false)
  const form = useFormContext()

  const handleSetActive = (memory: Memory) => {
    const newMemories = [...memories, memory]
    setMemories(newMemories)
    form.setValue(name, newMemories)
  }

  const handleSetInactive = (memory: Memory) => {
    const newMemories = [...memories]
    const findIndex = newMemories.findIndex((memo) => memo.id == memory.id)
    newMemories.splice(findIndex, 1)
    setMemories(newMemories)
    form.setValue(name, newMemories)
  }

  const displayName = !isEmpty(memories)
    ? memories.length == 1
      ? memories[0].name
      : memories.length
    : 'Choose a memory'

  return (
    <FormItem className="flex flex-col">
      <FormLabel>Memories</FormLabel>

      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <FormControl>
            <Button
              variant="outline"
              role="combobox"
              className={cn(
                'w-[200px] justify-between',
                isEmpty(memories) && 'text-muted-foreground',
                POPOVER_WIDTH
              )}
            >
              {(memories.length ?? 0) > 1 ? (
                <Badge variant="outline">{displayName}</Badge>
              ) : (
                <>{displayName}</>
              )}

              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </FormControl>
        </PopoverTrigger>

        <PopoverContent side="bottom" className={cn('p-0', POPOVER_WIDTH)} withPortal={false}>
          <Search<Memory, Memory[]>
            multiple
            fn={useSearchMemoriesAll}
            renderFn={(memo: Memory) => memo.name}
            valueFn={(memo: Memory) => memo.id + ''}
            compareFn={(memo: Memory) => {
              if (isEmpty(memories)) {
                return false
              }

              const findIndex = memories?.findIndex((memory) => {
                return memory.id == memo.id
              })

              return findIndex != -1
            }}
            selectedResult={memories}
            onSelectResult={handleSetActive}
            onDeselectResult={handleSetInactive}
          />
        </PopoverContent>
      </Popover>
      <FormMessage />
    </FormItem>
  )
}
