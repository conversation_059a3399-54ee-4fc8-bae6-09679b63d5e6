import { Button } from '@/components/ui/button'
import { Form } from '@/components/ui/form'
import {
  Sheet,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rigger
} from '@/components/ui/sheet'
import { EditIcon } from 'lucide-react'
import { FC, useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import useAuth from '@/hooks/useAuth'
import { MemoryCollection, UpdateMemoryCollectionInput } from '@/types/MemoryCollection'
import { UpdateMemoryCollectionForm } from './UpdateMemoryCollectionForm'

export const UpdateMemoryCollectionButton: FC<{ memoryCollection: MemoryCollection }> = ({
  memoryCollection
}) => {
  const { role } = useAuth()
  const [isSheetOpen, setIsSheetOpen] = useState(false)
  const form = useForm<UpdateMemoryCollectionInput>({
    defaultValues: {
      memory_id_arr: memoryCollection.memories.length > 0 ? memoryCollection.memories : [],
      ...memoryCollection
    },
    shouldUseNativeValidation: false
  })

  useEffect(() => {
    if (form.formState.isSubmitSuccessful) {
      form.reset()
      setIsSheetOpen(false)
    }
  }, [form.formState.isSubmitSuccessful])

  if (role == 'vendor') {
    return <></>
  }

  return (
    <div className="flex justify-end">
      <Sheet
        open={isSheetOpen}
        onOpenChange={(isOpen) => {
          setIsSheetOpen(isOpen)
        }}
      >
        <SheetTrigger asChild>
          <Button>
            <EditIcon size="16" className="mr-2" />
            Edit Memory Collection
          </Button>
        </SheetTrigger>
        <SheetContent side="bottom" className="w-full h-full">
          <SheetHeader className="max-w-4xl mx-auto pt-10 pb-6">
            <SheetTitle className="flex justify-between items-center">
              Memory Collection - {memoryCollection.title}
              <div className="flex space-x-2">
                <SheetClose asChild>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      form.reset()
                    }}
                  >
                    Cancel
                  </Button>
                </SheetClose>
                <Button
                  type="submit"
                  form="update-memory-collection-form"
                  disabled={form.formState.isSubmitting}
                  onClick={() => {
                    form.clearErrors()
                  }}
                >
                  Submit
                </Button>
              </div>
            </SheetTitle>
          </SheetHeader>
          <Form {...form}>
            <UpdateMemoryCollectionForm memoryCollection={memoryCollection} />
          </Form>
        </SheetContent>
      </Sheet>
    </div>
  )
}
