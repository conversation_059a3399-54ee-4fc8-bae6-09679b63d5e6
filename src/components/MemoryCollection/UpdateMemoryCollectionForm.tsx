import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { useToast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import { FC } from 'react'
import { useFormContext } from 'react-hook-form'
import { mutate } from 'swr'
import { MemoryCollection, UpdateMemoryCollectionInput } from '@/types/MemoryCollection'
import MemoryCollectionService from '@/network/services/memory_collection'
import { MemorySearchBoxAll } from './MemorySearchboxAll'
import React from 'react'
import { Memory } from '@/types/Memory'

interface UpdateMemoryCollectionFormProps extends React.HTMLAttributes<HTMLDivElement> {
  memoryCollection: MemoryCollection
}

export const UpdateMemoryCollectionForm: FC<UpdateMemoryCollectionFormProps> = ({
  className,
  memoryCollection
}) => {
  const { toast } = useToast()
  const form = useFormContext<UpdateMemoryCollectionInput>()

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      const { data: response } = await MemoryCollectionService.updateMemoryCollection(
        memoryCollection.id,
        {
          memory_id_arr: values.memory_id_arr?.map((memo: Memory | number) =>
            typeof memo == 'number' ? memo : typeof memo == 'object' ? memo.id : 0
          ),
          description: values.description,
          thumbnail: values.thumbnail,
          title: values.title
        }
      )

      if (response.success) {
        mutate(
          (key) =>
            typeof key === 'string' && key.startsWith(MemoryCollectionService.findMemoryCollections)
        )
        mutate(
          (key) =>
            typeof key === 'string' &&
            key.startsWith(MemoryCollectionService.findMemoryCollection(memoryCollection.id))
        )

        toast({
          title: 'Memory collection updated successfully',
          variant: 'success'
        })

        form.reset({
          memory_id_arr: values.memory_id_arr,
          description: values.description,
          thumbnail: values.thumbnail,
          title: values.title
        })
      }
    } catch (error) {
      console.error(error)
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <form
      id="update-memory-collection-form"
      onSubmit={onSubmit}
      className={cn('flex flex-col space-y-8 max-w-4xl mx-auto', className)}
    >
      <div className="gap-4 space-y-3">
        <FormField
          control={form.control}
          name="title"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Title</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="thumbnail"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Thumbnail</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="memory_id_arr"
          render={({ field }) => (
            // @ts-ignore
            <MemorySearchBoxAll initMemories={field.value} name={field.name} />
          )}
        />
      </div>
    </form>
  )
}
