/* eslint-disable @typescript-eslint/ban-ts-comment */
import * as React from 'react'
import { useFormContext } from 'react-hook-form'
import { mutate } from 'swr'

import { cn } from '@/lib/utils'
import {
  FormControl,
  // FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { toast } from '@/components/ui/use-toast'
import { Input } from '@/components/ui/input'
import { CreateMemoryMediaInput, MediaStatus, MediaType } from '@/types/MemoryMedia'
import MemoryMediaService from '@/network/services/memory_media'
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue
} from '../ui/select'
import { ChangeEvent, useState } from 'react'
import { Label } from '../ui/label'
import { Card } from '../ui/card'
import { MemorySearchBoxUnlinked } from './MemorySearchboxUnlinked'

interface MemoryFormProps extends React.HTMLAttributes<HTMLDivElement> {}

export function CreateMemoryMediaForm({ className }: MemoryFormProps) {
  const form = useFormContext<CreateMemoryMediaInput>()

  const [mediaType, setMediaType] = useState('File')
  const [media, setMedia] = useState('')
  const [file, setFile] = useState<File>()

  function handleChange(e: ChangeEvent<HTMLInputElement>) {
    if (e.target.files && e.target.files[0]) {
      setMedia(URL.createObjectURL(e.target.files[0]))
      setFile(e.target.files[0])
    }
  }

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      if (file) {
        values['media_file'] = file
      }

      await MemoryMediaService.createMemoryMedia(values)
      mutate(
        (key) => typeof key === 'string' && key.startsWith(MemoryMediaService.findMemoryMedias)
      )
      toast({
        title: 'Memory media created',
        variant: 'success'
      })
    } catch (error) {
      console.error(error)
      form.setError('root', {})
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <form
      id="create-memory-media-form"
      onSubmit={onSubmit}
      className={cn('flex flex-col space-y-8 max-w-4xl mx-auto', className)}
    >
      <div className="">
        <FormField
          control={form.control}
          name="memory_id"
          rules={{ required: 'Please select a memory id' }}
          render={({ field }) => {
            return <MemorySearchBoxUnlinked id={field.value + ''} name={'memory_id'} />
          }}
        />

        <div>
          <FormLabel>Image Source Options</FormLabel>
          <div className="flex gap-2">
            <div className="flex gap-2">
              <Input
                type="radio"
                id="file"
                name="file_type"
                value="File"
                onChange={(e) => setMediaType(e.target.value)}
                className="h-6"
                defaultChecked
              />
              <div className="flex h-full content-center flex-wrap">
                <Label className="h-min">File</Label>
              </div>
            </div>

            <div className="flex gap-2">
              <Input
                type="radio"
                id="url"
                name="file_type"
                value="URL"
                onChange={(e) => setMediaType(e.target.value)}
                className="h-6"
              />
              <div className="flex h-full content-center flex-wrap">
                <Label className="h-min">URL</Label>
              </div>
            </div>
          </div>
        </div>
        {mediaType == 'File' && (
          <div className="mb-4">
            <FormLabel>Media</FormLabel>
            <div className="w-48 h-80 relative mt-2">
              <Card className="w-full h-full p-0  cursor-pointer">
                {media != '' ? (
                  <img
                    src={media != '' ? media : ''}
                    alt="File"
                    style={{ objectFit: 'contain', pointerEvents: 'none', height: '100%' }}
                  />
                ) : (
                  <div
                    className="w-full h-full justify-center items-center absolute flex"
                    style={{ pointerEvents: 'none' }}
                  >
                    <div className="text-3xl">+</div>
                  </div>
                )}
                <input
                  type="file"
                  accept="image/*,video/*"
                  id="fileInput"
                  className=" w-full h-full cursor-pointer opacity-0 absolute top-0"
                  onChange={handleChange}
                />
              </Card>
            </div>
          </div>
        )}

        {mediaType == 'URL' && (
          <FormField
            control={form.control}
            name="media_url"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Media URL</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        <FormField
          control={form.control}
          name="status"
          rules={{ required: 'Please select a media status' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Media Status</FormLabel>
              <FormControl>
                <Select
                  value={field.value?.toLowerCase()}
                  onValueChange={(value) => field.onChange(value)}
                >
                  <SelectTrigger className="w-[200px]">
                    <SelectValue placeholder="Select a media status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectLabel>Media Status</SelectLabel>
                      {Object.keys(MediaStatus).map((status) => {
                        return (
                          <SelectItem value={status.toLowerCase()}>
                            {MediaStatus[status as keyof typeof MediaStatus].toLowerCase()}
                          </SelectItem>
                        )
                      })}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="type"
          rules={{ required: 'Please select a media type' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Media Type</FormLabel>
              <FormControl>
                <Select
                  value={field.value?.toLowerCase()}
                  onValueChange={(value) => field.onChange(value)}
                >
                  <SelectTrigger className="w-[200px]">
                    <SelectValue placeholder="Select a media type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectLabel>Media Type</SelectLabel>
                      {Object.keys(MediaType).map((status) => {
                        return (
                          <SelectItem value={status.toLowerCase()}>
                            {MediaType[status as keyof typeof MediaType].toLowerCase()}
                          </SelectItem>
                        )
                      })}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </form>
  )
}
