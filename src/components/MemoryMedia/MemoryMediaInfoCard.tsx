import { Card, CardContent, Content, Title } from '@/components/ui/card'
import { MemoryMedia } from '@/types/MemoryMedia'
import { MediaType, MediaStatus } from '@/types/MemoryMedia'
import { DateTime } from 'luxon'
import { FC } from 'react'
import { FileImageIcon, FileVideoIcon } from 'lucide-react'

const MemoryCard: FC<{ memoryMedia?: MemoryMedia }> = ({ memoryMedia }) => {
  let mediaContent = <></>

  switch (memoryMedia?.type) {
    case MediaType.IMAGE:
      mediaContent = (
        <img
          className="rounded-lg w-[250px] float-right"
          src={memoryMedia.url}
          alt="event-info-image"
        />
      )
      break
    case MediaType.VIDEO:
      mediaContent = (
        <iframe
          className="rounded-lg w-[550px] h-[350px] float-right"
          src={memoryMedia.url}
          title="YouTube video player"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
        ></iframe>
      )
      break
  }

  return (
    <>
      <Card>
        <CardContent>
          <div className="grid grid-cols-2 gap-2 pt-10">
            <Title>ID</Title>
            <Content>{memoryMedia?.id}</Content>
            <Title>Linked Memory</Title>
            <Content>{memoryMedia?.memory?.name ?? '-'}</Content>
            <Title>Memory Media </Title>
            <Content>{mediaContent}</Content>
            <Title>Memory Media Status</Title>
            <Content>
              <span
                style={{
                  display: 'inline-block',
                  width: '8px',
                  height: '8px',
                  borderRadius: '50%',
                  marginRight: '8px',
                  backgroundColor: memoryMedia?.status == MediaStatus.ACTIVE ? '#4CAF50' : '#F44336'
                }}
              ></span>

              {memoryMedia?.status == MediaStatus.ACTIVE ? 'Active' : 'Inactive'}
            </Content>
            <Title>Memory Media Type</Title>
            <Content>
              {memoryMedia?.type == MediaType.VIDEO ? (
                <FileVideoIcon
                  style={{
                    display: 'inline-block',
                    width: '20px',
                    height: '20px',
                    borderRadius: '50%',
                    marginRight: '3px',
                    marginBottom: '3px'
                  }}
                />
              ) : (
                <FileImageIcon
                  style={{
                    display: 'inline-block',
                    width: '20px',
                    height: '20px',
                    borderRadius: '50%',
                    marginRight: '3px',
                    marginBottom: '3px'
                  }}
                />
              )}
              {memoryMedia?.type == MediaType.VIDEO ? 'Video' : 'Image'}
            </Content>

            <Title>Memory Media Total Likes Received</Title>
            <Content>{memoryMedia?.liked_by_users.length}</Content>

            <Title>Created Date</Title>
            <Content>
              {memoryMedia?.created_at
                ? DateTime.fromISO(memoryMedia.created_at.toString()).toISODate()
                : '-'}
            </Content>
            <Title>Last Updated Date</Title>
            <Content>
              {memoryMedia?.updated_at
                ? DateTime.fromISO(memoryMedia.updated_at.toString()).toISODate()
                : '-'}
            </Content>
          </div>
        </CardContent>
      </Card>
    </>
  )
}

export default MemoryCard
