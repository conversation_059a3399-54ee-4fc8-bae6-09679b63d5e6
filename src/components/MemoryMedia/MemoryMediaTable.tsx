import { DataTable } from '../Table/DataTable'
import { ColumnDef, Row } from '@tanstack/react-table'
import { useNavigate } from 'react-router-dom'
import { DateTime } from 'luxon'
import { MemoryMedia, MemoryMediaResponse, PreloadMemory } from '@/types/MemoryMedia'
import MemoryMediaService from '@/network/services/memory_media'

const columns: ColumnDef<MemoryMedia>[] = [
  {
    accessorKey: 'id',
    header: 'ID'
  },
  {
    accessorKey: 'memory',
    header: 'Linked Memory',
    cell: (props) => {
      return props.getValue<PreloadMemory>()?.name ?? '-'
    }
  },
  {
    accessorKey: 'type',
    header: 'Type'
  },
  {
    accessorKey: 'status',
    header: 'Status'
  },
  {
    accessorKey: 'created_at',
    header: 'Created Date',
    cell: (props) => {
      return DateTime.fromISO(props.getValue<DateTime>().toString()).toISODate()
    }
  },
  {
    accessorKey: 'updated_at',
    header: 'Last Updated Date',
    cell: (props) => {
      return DateTime.fromISO(props.getValue<DateTime>().toString()).toISODate()
    }
  }
]

const MemoryTable = () => {
  const nav = useNavigate()
  return (
    <>
      <DataTable<MemoryMedia, unknown, MemoryMediaResponse>
        columns={columns}
        swrService={MemoryMediaService.findMemoryMedias}
        toRow={MemoryMediaService.toRow}
        toPaginate={MemoryMediaService.toPaginate}
        onRowClick={(row: Row<MemoryMedia>) => {
          nav(`/memory-medias/${row.original.id}`)
        }}
      />
    </>
  )
}

export default MemoryTable
