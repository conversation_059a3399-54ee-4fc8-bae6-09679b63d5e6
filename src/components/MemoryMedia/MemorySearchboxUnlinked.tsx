import { cn } from '@/lib/utils'
import { ChevronsUpDown } from 'lucide-react'
import { isEmpty } from 'radash'
import { FC, useEffect, useState } from 'react'
import { useFormContext } from 'react-hook-form'
import { FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Button } from '@/components/ui/button'
import { Search } from '@/components/Form/Search'
import MemoryService from '@/network/services/memory'
import { Memory } from '@/types/Memory'
import { useSearchMemoriesUnlinked } from '@/hooks/memories/useSearchMemories'

// multiple select
export const MemorySearchBoxUnlinked: FC<{ id: string | undefined; name: string }> = ({
  id,
  name
}) => {
  const [open, setOpen] = useState(false)
  const [memory, setMemory] = useState<Memory>()
  const form = useFormContext()

  useEffect(() => {
    const fetchMemory = async () => {
      const memoryData = await MemoryService.getMemory(id!)

      if (memoryData.data.data) {
        setMemory(memoryData.data.data)
      }
    }

    if (id) {
      fetchMemory()
    }
  }, [id])

  const handleSetActive = (memory: Memory) => {
    setMemory(memory)

    form.setValue(name, memory.id)
  }

  const displayName = !isEmpty(memory) ? memory?.name + ' | ' + memory?.id : 'Choose a memory'

  return (
    <FormItem className="flex flex-col">
      <FormLabel>Memory to be Linked</FormLabel>

      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <FormControl>
            <Button
              variant="outline"
              role="combobox"
              className={cn(
                'w-[200px] justify-between',
                isEmpty(memory) && 'text-muted-foreground',
                'w-full'
              )}
            >
              <>{displayName}</>

              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </FormControl>
        </PopoverTrigger>

        <PopoverContent side="bottom" className={cn('p-0', 'w-[300px]')} withPortal={false}>
          <Search<Memory, Memory>
            fn={useSearchMemoriesUnlinked}
            renderFn={(memo: Memory) => memo.name + ' | ' + memo.id}
            valueFn={(memo: Memory) => memo.id.toString()}
            compareFn={(memo: Memory) => {
              if (isEmpty(memory)) {
                return false
              }

              const findIndex = memory?.id == memo.id

              return findIndex
            }}
            selectedResult={memory}
            onSelectResult={handleSetActive}
            // onDeselectResult={handleSetInactive}
          />
        </PopoverContent>
      </Popover>
      <FormMessage />
    </FormItem>
  )
}
