import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import {
  <PERSON>alog,
  <PERSON>alogClose,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { useToast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import MemoryMediaService from '@/network/services/memory_media'
import { MediaStatus, MediaType, MemoryMedia, UpdateMemoryMediaInput } from '@/types/MemoryMedia'
import { ChangeEvent, FC, useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { mutate } from 'swr'
import { MemorySearchBoxUnlinked } from './MemorySearchboxUnlinked'
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue
} from '../ui/select'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Card } from '../ui/card'

interface UpdateMemoryFormProps extends React.HTMLAttributes<HTMLDivElement> {
  isDialogOpen: boolean
  setIsDialogOpen: (value: boolean) => void
  memoryMedia: MemoryMedia
}

export const UpdateMemoryMediaForm: FC<UpdateMemoryFormProps> = ({
  isDialogOpen,
  setIsDialogOpen,
  className,
  memoryMedia
}) => {
  const { toast } = useToast()
  const form = useForm<UpdateMemoryMediaInput>({
    defaultValues: memoryMedia,
    shouldUseNativeValidation: false
  })
  const [mediaType, setMediaType] = useState('File')
  const [media, setMedia] = useState('')
  const [file, setFile] = useState<File>()

  function handleChange(e: ChangeEvent<HTMLInputElement>) {
    if (e.target.files && e.target.files[0]) {
      setMedia(URL.createObjectURL(e.target.files[0]))
      setFile(e.target.files[0])
    }
  }

  useEffect(() => {
    if (!isDialogOpen) {
      form.reset()
    }

    form.reset(memoryMedia)
  }, [isDialogOpen, memoryMedia])

  const onSubmit = form.handleSubmit(async (values) => {
    console.log('submit values: ', values)
    try {
      if (file) {
        values['media_file'] = file
      }

      const { data: response } = await MemoryMediaService.updateMemoryMedia(memoryMedia.id, values)

      if (response.success) {
        mutate(
          (key) => typeof key === 'string' && key.startsWith(MemoryMediaService.findMemoryMedias)
        )
        toast({
          title: 'Memory media updated successfully',
          variant: 'success'
        })
        setIsDialogOpen(false)
      }
    } catch (error) {
      console.error(error)
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <Dialog
      open={isDialogOpen}
      onOpenChange={(isOpen) => {
        setIsDialogOpen(isOpen)
      }}
    >
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            Edit Memory Media
            <div className="flex space-x-2">
              <DialogClose type="button" asChild>
                <Button variant="outline">Cancel</Button>
              </DialogClose>
              <Button
                type="submit"
                form="update-memory-form"
                disabled={form.formState.isSubmitting}
                onClick={() => {
                  form.clearErrors()
                }}
              >
                {form.formState.isSubmitting && (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                )}
                Save
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form
            id="update-memory-form"
            onSubmit={onSubmit}
            className={cn('flex flex-col space-y-8', className)}
          >
            <FormField
              control={form.control}
              name="memory_id"
              rules={{ required: 'Please select a memory id' }}
              render={({ field }) => {
                return <MemorySearchBoxUnlinked id={field.value + ''} name={'memory_id'} />
              }}
            />

            <div>
              <FormLabel>Image Source Options</FormLabel>
              <div className="flex gap-2">
                <div className="flex gap-2">
                  <Input
                    type="radio"
                    id="file"
                    name="file_type"
                    value="File"
                    onChange={(e) => setMediaType(e.target.value)}
                    className="h-6"
                    defaultChecked
                  />
                  <div className="flex h-full content-center flex-wrap">
                    <Label className="h-min">File</Label>
                  </div>
                </div>

                <div className="flex gap-2">
                  <Input
                    type="radio"
                    id="url"
                    name="file_type"
                    value="URL"
                    onChange={(e) => setMediaType(e.target.value)}
                    className="h-6"
                  />
                  <div className="flex h-full content-center flex-wrap">
                    <Label className="h-min">URL</Label>
                  </div>
                </div>
              </div>
            </div>
            {mediaType == 'File' && (
              <div className="mb-4">
                <FormLabel>Media</FormLabel>
                <div className="w-48 h-80 relative mt-2">
                  <Card className="w-full h-full p-0  cursor-pointer">
                    {media != '' ? (
                      <img
                        src={media != '' ? media : ''}
                        alt="File"
                        style={{ objectFit: 'contain', pointerEvents: 'none', height: '100%' }}
                      />
                    ) : (
                      <div
                        className="w-full h-full justify-center items-center absolute flex"
                        style={{ pointerEvents: 'none' }}
                      >
                        <div className="text-3xl">+</div>
                      </div>
                    )}
                    <input
                      type="file"
                      accept="image/*,video/*"
                      id="fileInput"
                      className=" w-full h-full cursor-pointer opacity-0 absolute top-0"
                      onChange={handleChange}
                    />
                  </Card>
                </div>
              </div>
            )}

            {mediaType == 'URL' && (
              <FormField
                control={form.control}
                name="media_url"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Media URL</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <FormField
              control={form.control}
              name="status"
              rules={{ required: 'Please select a media status' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Media Status</FormLabel>
                  <FormControl>
                    <Select
                      value={field.value?.toLowerCase()}
                      onValueChange={(value) => field.onChange(value)}
                    >
                      <SelectTrigger className="w-[200px]">
                        <SelectValue placeholder="Select a media status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup>
                          <SelectLabel>Media Status</SelectLabel>
                          {Object.keys(MediaStatus).map((status) => {
                            return (
                              <SelectItem value={status.toLowerCase()}>
                                {MediaStatus[status as keyof typeof MediaStatus].toLowerCase()}
                              </SelectItem>
                            )
                          })}
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="type"
              rules={{ required: 'Please select a media type' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Media Type</FormLabel>
                  <FormControl>
                    <Select
                      value={field.value?.toLowerCase()}
                      onValueChange={(value) => field.onChange(value)}
                    >
                      <SelectTrigger className="w-[200px]">
                        <SelectValue placeholder="Select a media type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup>
                          <SelectLabel>Media Type</SelectLabel>
                          {Object.keys(MediaType).map((status) => {
                            return (
                              <SelectItem value={status.toLowerCase()}>
                                {MediaType[status as keyof typeof MediaType].toLowerCase()}
                              </SelectItem>
                            )
                          })}
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
