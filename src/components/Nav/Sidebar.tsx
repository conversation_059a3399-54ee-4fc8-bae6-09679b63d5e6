import { Button } from '@/components/ui/button'
import {
  BookPlusIcon,
  SettingsIcon,
  ShirtIcon,
  ShoppingBasketIcon,
  SlackIcon,
  StoreIcon,
  TagIcon,
  UsersIcon,
  PlayIcon,
  MailQuestionIcon,
  UserCircleIcon,
  BriefcaseIcon,
  BarChart4,
  ImageIcon
} from 'lucide-react'
import { NavLink, useLocation } from 'react-router-dom'
import useAuth from '@/hooks/useAuth'

interface SidebarProps extends React.HTMLAttributes<HTMLDivElement> {}

export function Sidebar({ className }: SidebarProps) {
  const { role, store } = useAuth()
  const location = useLocation()

  return (
    <div className={className}>
      <div className="space-y-4 py-4">
        <div className="px-3 py-2">
          <div className="mb-2 px-4">
            <p className="text-sm">Store</p>
            <h2 className="text-lg font-semibold tracking-tight">{store?.name}</h2>
          </div>
          <div className="space-y-1">
            {/* <NavLink to="/orders">
              <Button variant="secondary" className="w-full justify-start">
                <ShoppingCartIcon className="mr-2 h-4 w-4" />
                Orders
              </Button>
            </NavLink> */}
            <NavLink to="/media-room">
              {({ isActive }) => (
                <Button variant={isActive ? 'default' : 'ghost'} className="w-full justify-start">
                  <PlayIcon className="mr-2 h-4 w-4" />
                  Media Room
                </Button>
              )}
            </NavLink>
            <NavLink to="/statistics">
              {({ isActive }) => (
                <Button variant={isActive ? 'default' : 'ghost'} className="w-full justify-start">
                  <BarChart4 className="mr-2 h-4 w-4" />
                  Statistics
                </Button>
              )}
            </NavLink>
            <NavLink to="/products">
              {({ isActive }) => (
                <Button variant={isActive ? 'default' : 'ghost'} className="w-full justify-start">
                  <TagIcon className="mr-2 h-4 w-4" />
                  Products
                </Button>
              )}
            </NavLink>
            <NavLink to="/categories">
              {({ isActive }) => (
                <Button variant={isActive ? 'default' : 'ghost'} className="w-full justify-start">
                  <ShirtIcon className="mr-2 h-4 w-4" />
                  Categories
                </Button>
              )}
            </NavLink>
            <NavLink to="/brands">
              {({ isActive }) => (
                <Button variant={isActive ? 'default' : 'ghost'} className="w-full justify-start">
                  <SlackIcon className="mr-2 h-4 w-4" />
                  Brands
                </Button>
              )}
            </NavLink>
            {(role == 'admin' || role == 'super_admin') && (
              <>
                <NavLink to="/friends">
                  {({ isActive }) => (
                    <Button
                      variant={isActive ? 'default' : 'ghost'}
                      className="w-full justify-start"
                    >
                      <UsersIcon className="mr-2 h-4 w-4" />
                      Friends
                    </Button>
                  )}
                </NavLink>
                <NavLink to="/companies">
                  {({ isActive }) => (
                    <Button
                      variant={isActive ? 'default' : 'ghost'}
                      className="w-full justify-start"
                    >
                      <StoreIcon className="mr-2 h-4 w-4" />
                      Companies
                    </Button>
                  )}
                </NavLink>
                <NavLink to="/campaigns">
                  {({ isActive }) => (
                    <Button
                      variant={isActive ? 'default' : 'ghost'}
                      className="w-full justify-start"
                    >
                      <BookPlusIcon className="mr-2 h-4 w-4" />
                      Campaigns
                    </Button>
                  )}
                </NavLink>
                <NavLink to="/memories">
                  {({ isActive }) => (
                    <Button
                      variant={
                        isActive ||
                        //location.pathname.startsWith('/memory-medias') ||
                        location.pathname.startsWith('/memory-collections')
                          ? 'default'
                          : 'ghost'
                      }
                      className="w-full justify-start"
                    >
                      <ImageIcon className="mr-2 h-4 w-4" />
                      Memories
                    </Button>
                  )}
                </NavLink>
              </>
            )}
            {/* {role == 'vendor' && (
              <>
                <NavLink to="/teams">
                  {({ isActive }) => (
                    <Button
                      variant={isActive ? 'default' : 'ghost'}
                      className="w-full justify-start"
                    >
                      <RocketIcon className="mr-2 h-4 w-4" />
                      Teams
                    </Button>
                  )}
                </NavLink>
              </>
            )} */}
            <NavLink to="/events">
              {({ isActive }) => (
                <Button variant={isActive ? 'default' : 'ghost'} className="w-full justify-start">
                  <ShoppingBasketIcon className="mr-2 h-4 w-4" />
                  Events
                </Button>
              )}
            </NavLink>
            <NavLink to="/inquiries">
              {({ isActive }) => (
                <Button variant={isActive ? 'default' : 'ghost'} className="w-full justify-start">
                  <MailQuestionIcon className="mr-2 h-4 w-4" />
                  Inquiries
                </Button>
              )}
            </NavLink>
            <NavLink to="/job-applications">
              {({ isActive }) => (
                <Button variant={isActive ? 'default' : 'ghost'} className="w-full justify-start">
                  <BriefcaseIcon className="mr-2 h-4 w-4" />
                  Job Applications
                </Button>
              )}
            </NavLink>
            {role == 'super_admin' && (
              <NavLink to="/roles">
                {({ isActive }) => (
                  <Button variant={isActive ? 'default' : 'ghost'} className="w-full justify-start">
                    <UserCircleIcon className="mr-2 h-4 w-4" />
                    Roles
                  </Button>
                )}
              </NavLink>
            )}
            {(role == 'admin' || role == 'super_admin') && (
              <NavLink to="/settings">
                {({ isActive }) => (
                  <Button variant={isActive ? 'default' : 'ghost'} className="w-full justify-start">
                    <SettingsIcon className="mr-2 h-4 w-4" />
                    Settings
                  </Button>
                )}
              </NavLink>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
