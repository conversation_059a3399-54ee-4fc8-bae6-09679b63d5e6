import { CreateProductForm } from '@/components/Product/CreateProductForm'
import { Button } from '@/components/ui/button'
import { Form } from '@/components/ui/form'
import {
  Sheet,
  SheetClose,
  Sheet<PERSON>ontent,
  SheetHeader,
  Sheet<PERSON><PERSON>le,
  SheetTrigger
} from '@/components/ui/sheet'
import useAuth from '@/hooks/useAuth'
import { CreateProductInput } from '@/types/CreateProduct'
import { ProductStatus } from '@/types/Product'
import { PlusCircleIcon } from 'lucide-react'
import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'

export const AddProductButton = () => {
  const { role } = useAuth()
  const [isSheetOpen, setIsSheetOpen] = useState(false)
  const form = useForm<CreateProductInput>({
    shouldUseNativeValidation: false,
    defaultValues: { status: ProductStatus.PROPOSED }
  })

  useEffect(() => {
    if (form.formState.isSubmitSuccessful) {
      form.reset()
      setIsSheetOpen(false)
    }
  }, [form.formState.isSubmitSuccessful])

  if (role != 'vendor') {
    return <></>
  }

  return (
    <Sheet
      open={isSheetOpen}
      onOpenChange={(isOpen) => {
        setIsSheetOpen(isOpen)
      }}
    >
      <SheetTrigger asChild>
        <Button>
          <PlusCircleIcon size="16" className="mr-2" />
          Create Product
        </Button>
      </SheetTrigger>
      <SheetContent side="bottom" className="h-full w-full">
        <SheetHeader className="mx-auto max-w-4xl pb-6 pt-10">
          <SheetTitle className="flex items-center justify-between">
            New Product
            <div className="flex space-x-2">
              <SheetClose asChild>
                <Button
                  variant="outline"
                  onClick={() => {
                    form.reset()
                  }}
                >
                  Cancel
                </Button>
              </SheetClose>
              <Button
                type="submit"
                form="create-product-form"
                disabled={form.formState.isSubmitting}
                onClick={() => {
                  form.clearErrors()
                }}
              >
                Propose
              </Button>
            </div>
          </SheetTitle>
        </SheetHeader>
        <Form {...form}>
          <CreateProductForm />
        </Form>
      </SheetContent>
    </Sheet>
  )
}
