import { Input } from '@/components/ui/input'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { useFormContext } from 'react-hook-form'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { FC, useState } from 'react'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { isEmpty } from 'radash'
import { SearchProductCollections, useProductCollections } from '@/hooks/products/useProducts'
import { ChevronsUpDown } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { Search } from '@/components/Form/Search'
import TextEditorComponent from '@/components/Editor/TextEditor'

const POPOVER_WIDTH = 'w-[250px]'

const GeneralInformation = () => {
  const form = useFormContext()

  return (
    <div className="grid grid-cols-2 gap-4">
      <FormField
        control={form.control}
        name="title"
        rules={{ required: 'Please enter the title' }}
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Title*</FormLabel>
            <FormControl>
              <Input {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="subtitle"
        // rules={{ required: "Please enter the subtitle" }}
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Subtitle</FormLabel>
            <FormControl>
              <Input {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="handle"
        // rules={{ required: "Please enter the handle" }}
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Handle</FormLabel>
            <FormControl>
              <Input {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="description"
        // rules={{ required: "Please enter the description" }}
        render={({ field }) => (
          <FormItem className="flex flex-col col-span-2">
            <FormLabel>Description</FormLabel>
            <FormControl>
              <TextEditorComponent content={field.value} onChange={field.onChange} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="collection"
        rules={{ required: 'Please choose a brands' }}
        render={({ field }) => {
          return <CollectionsComboBox productCollections={field.value} name={field.name} />
        }}
      />
    </div>
  )
}

const CollectionsComboBox: FC<{ productCollections: SearchProductCollections; name: string }> = ({
  productCollections,
  name
}) => {
  const [open, setOpen] = useState(false)
  const form = useFormContext()

  const handleSetActive = (collection: SearchProductCollections) => {
    form.setValue(name, collection)
  }

  // const handleSetInactive = (category: SearchProductCollections) => {
  //   const newCategories = [...productCollections]
  //   const findIndex = newCategories.findIndex((c) => c.id == category.id)
  //   newCategories.splice(findIndex, 1)
  //   form.setValue(name, newCategories)
  // }

  const displayName = !isEmpty(productCollections)
    ? productCollections.title
    : 'Choose a collection'

  return (
    <FormItem className="flex flex-col">
      <FormLabel>Brands*</FormLabel>

      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <FormControl>
            <Button
              variant="outline"
              role="combobox"
              className={cn(
                'w-[200px] justify-between',
                isEmpty(productCollections) && 'text-muted-foreground',
                POPOVER_WIDTH
              )}
            >
              {productCollections ? (
                <Badge variant="outline">{displayName}</Badge>
              ) : (
                <>{displayName}</>
              )}

              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </FormControl>
        </PopoverTrigger>

        <PopoverContent side="bottom" className={cn('p-0', POPOVER_WIDTH)}>
          <Search<SearchProductCollections, SearchProductCollections[]>
            fn={useProductCollections}
            renderFn={(cat: SearchProductCollections) => cat.title}
            valueFn={(cat: SearchProductCollections) => cat.id}
            compareFn={(cat: SearchProductCollections) => {
              return cat.id == productCollections?.id
            }}
            selectedResult={[productCollections]}
            onSelectResult={handleSetActive}
            // onDeselectResult={handleSetInactive}
          />
        </PopoverContent>
      </Popover>
      <FormMessage />
    </FormItem>
  )
}

export default GeneralInformation
