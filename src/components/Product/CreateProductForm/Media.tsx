import { Dropzone } from '@/components/Form/Dropzone'
import { FileWithPath } from 'react-dropzone'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormMessage
} from '@/components/ui/form'
import { CreateProductInput } from '@/types/CreateProduct'
import { useFormContext } from 'react-hook-form'
import { isEmpty } from 'radash'
import { Image } from '@unpic/react'
import { Label } from '@/components/ui/label'
import { bytesToSize } from '@/lib/utils'
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem
} from '@/components/ui/dropdown-menu'
import { MoreHorizontal, Trash2Icon } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useEffect } from 'react'

const Media = () => {
  const form = useFormContext<CreateProductInput>()
  const fieldName = 'imageFiles'
  const imagesFiles = form.watch(fieldName)
  const multiple = true
  const imageFilesWithPath = imagesFiles as unknown as (FileWithPath & { preview: string })[]

  useEffect(() => {
    // Make sure to revoke the data uris to avoid memory leaks, will run on unmount
    return () => imageFilesWithPath?.forEach((file) => URL.revokeObjectURL(file.preview))
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return (
    <>
      <FormField
        control={form.control}
        name="imageFiles"
        // rules={{ required: 'Please insert the image' }}
        render={({ field }) => (
          <FormItem>
            <FormControl>
              <Dropzone
                multiple={multiple}
                onDrop={(acceptedFiles) => {
                  console.log('ondrop')
                  const filesToAdd: (FileWithPath & {
                    preview: string
                  })[] = []

                  // remove existing file
                  const fileWithPaths = acceptedFiles as unknown as FileWithPath[]
                  fileWithPaths.forEach((file) => {
                    const findIndex = imagesFiles?.findIndex((f) => {
                      const ff = f as unknown as FileWithPath
                      return ff.path == file.path
                    })

                    if ((findIndex ?? -1) == -1) {
                      const preview = Object.assign(file, {
                        preview: URL.createObjectURL(file)
                      })

                      filesToAdd.push(preview)
                    }
                  })

                  const newValue = [...(imagesFiles ?? []), ...filesToAdd]

                  form.setValue(fieldName, newValue, {
                    shouldValidate: true
                  })
                }}
                {...field}
              />
            </FormControl>
            <FormDescription>Add images to your product.</FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      {!isEmpty(imageFilesWithPath) && (
        <div className="flex flex-col space-y-4 mt-2">
          {imageFilesWithPath?.map((file, index) => {
            return (
              <div className="grid grid-cols-[150px_1fr_36px] space-x-2 items-center">
                <Image
                  key={file.path}
                  src={file.preview ?? ''}
                  height={150}
                  width={150}
                  objectFit="contain"
                  className="rounded-md"
                />
                <div className="flex flex-col">
                  <Label className="text-xs font-normal">{file.path}</Label>
                  <Label className="text-xs font-normal text-gray-500">
                    {bytesToSize(file.size)}
                  </Label>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <span className="sr-only">Open menu</span>
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem
                      onClick={(e) => {
                        e.stopPropagation()
                        imagesFiles?.splice(index, 1)

                        form.setValue(fieldName, imagesFiles, {
                          shouldValidate: true
                        })
                      }}
                      className="space-x-2"
                    >
                      <Trash2Icon size="16" />
                      <span>Delete</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            )
          })}
        </div>
      )}
    </>
  )
}

export default Media
