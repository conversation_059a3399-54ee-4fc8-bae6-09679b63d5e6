import { Input } from '@/components/ui/input'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { useFormContext } from 'react-hook-form'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger
} from '@/components/ui/alert-dialog'
import { Button } from '@/components/ui/button'
import { PlusIcon } from 'lucide-react'
import { snake } from 'radash'
import { useState } from 'react'

const Metadata = () => {
  const form = useFormContext()
  const [newKey, setNew<PERSON>ey] = useState<string>('')
  const metadata = form.watch('metadata')

  return (
    <div className="grid grid-cols-2 gap-4">
      {metadata &&
        (Object.keys(metadata)?.length ?? 0) > 0 &&
        Object.keys(metadata).map((key) => {
          return (
            <FormField
              key={key}
              control={form.control}
              name={`metadata.${key}`}
              //   rules={{ required: 'Please enter the value' }}
              render={({ field }) => (
                <FormItem className="flex flex-col col-span-2">
                  <FormLabel>{key}</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )
        })}

      <AlertDialog key="add-metadata-key">
        <AlertDialogTrigger
          asChild
          onClick={(e) => e.stopPropagation()}
        >
          <Button className="flex flex-col col-span-2">
            <PlusIcon size="16" /> Add Metadata
          </Button>
        </AlertDialogTrigger>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>New Key</AlertDialogTitle>
          </AlertDialogHeader>
          <Input placeholder="new_key" onChange={(e) => setNewKey(e.target.value)} />
          <AlertDialogFooter>
            <AlertDialogCancel>Close</AlertDialogCancel>
            <AlertDialogAction
              type="button"
              onClick={() => {
                if (newKey != '') {
                  const snakeKey = snake(newKey)
                  setNewKey('')
                  const tmp = form.getValues('metadata') ?? {}
                  if (!Object.keys(tmp).includes(snakeKey)) {
                    tmp[snakeKey] = ''
                  }
                  form.setValue('metadata', tmp)
                }
              }}
            >
              Add
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}

export default Metadata
