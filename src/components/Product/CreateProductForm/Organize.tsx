import { Search } from '@/components/Form/Search'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import {
  SearchProductCategories,
  // SearchProductType,
  useProductCategories
} from '@/hooks/products/useProducts'
import { cn } from '@/lib/utils'
import { ChevronsUpDown } from 'lucide-react'
import { isEmpty } from 'radash'
import { FC, useState } from 'react'
import { useFormContext } from 'react-hook-form'

const POPOVER_WIDTH = 'w-[250px]'

// const TypeComboBox: FC<{ productType: SearchProductType; name: string }> = ({
//   productType,
//   name
// }) => {
//   const [open, setOpen] = useState(false)
//   const form = useFormContext()

//   const handleSetActive = useCallback((productType: SearchProductType) => {
//     form.setValue(name, { id: productType.id, value: productType.value })
//     setOpen(false)
//     // eslint-disable-next-line react-hooks/exhaustive-deps
//   }, [])

//   const handleCreate = useCallback((value: string) => {
//     form.setValue(name, { value })
//     setOpen(false)
//     // eslint-disable-next-line react-hooks/exhaustive-deps
//   }, [])

//   const displayName = productType ? productType.value : 'Choose a type'

//   return (
//     <FormItem className="flex flex-col">
//       <FormLabel>Type</FormLabel>

//       <Popover open={open} onOpenChange={setOpen}>
//         <PopoverTrigger asChild>
//           <FormControl>
//             <Button
//               variant="outline"
//               role="combobox"
//               className={cn(
//                 'w-[200px] justify-between',
//                 !productType && 'text-muted-foreground',
//                 POPOVER_WIDTH
//               )}
//             >
//               {displayName}

//               <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
//             </Button>
//           </FormControl>
//         </PopoverTrigger>

//         <PopoverContent side="bottom" className={cn('p-0', POPOVER_WIDTH)}>
//           <Search<SearchProductType, SearchProductType>
//             fn={useProductTypes}
//             renderFn={(type: SearchProductType) => type.value}
//             valueFn={(type: SearchProductType) => type.id}
//             compareFn={(type: SearchProductType) => productType?.id == type.id}
//             createFn={handleCreate}
//             onSelectResult={handleSetActive}
//           />
//         </PopoverContent>
//       </Popover>
//       <FormMessage />
//     </FormItem>
//   )
// }

// multiple select
const CategoryComboBox: FC<{ productCategories: SearchProductCategories[]; name: string }> = ({
  productCategories,
  name
}) => {
  const [open, setOpen] = useState(false)
  const form = useFormContext()

  const handleSetActive = (category: SearchProductCategories) => {
    const newCategories = [...productCategories, category]
    form.setValue(name, newCategories)
  }

  const handleSetInactive = (category: SearchProductCategories) => {
    const newCategories = [...productCategories]
    const findIndex = newCategories.findIndex((c) => c.id == category.id)
    newCategories.splice(findIndex, 1)
    form.setValue(name, newCategories)
  }

  const displayName = !isEmpty(productCategories)
    ? productCategories.length == 1
      ? productCategories[0].name
      : productCategories.length
    : 'Choose a category'

  return (
    <FormItem className="flex flex-col">
      <FormLabel>Category</FormLabel>

      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <FormControl>
            <Button
              variant="outline"
              role="combobox"
              className={cn(
                'w-[200px] justify-between',
                isEmpty(productCategories) && 'text-muted-foreground',
                POPOVER_WIDTH
              )}
            >
              {(productCategories.length ?? 0) > 1 ? (
                <Badge variant="outline">{displayName}</Badge>
              ) : (
                <>{displayName}</>
              )}

              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </FormControl>
        </PopoverTrigger>

        <PopoverContent side="bottom" className={cn('p-0', POPOVER_WIDTH, 'overflow-y-auto')}>
          <Search<SearchProductCategories, SearchProductCategories[]>
            multiple
            fn={useProductCategories}
            renderFn={(cat: SearchProductCategories) => {
              const level = cat.parent_category_id
                ? cat.parent_category?.parent_category_id
                  ? 3
                  : 2
                : 1

              return `${cat.name} (L${level})`
            }}
            valueFn={(cat: SearchProductCategories) => cat.id}
            compareFn={(cat: SearchProductCategories) => {
              if (isEmpty(productCategories)) {
                return false
              }

              const findIndex = productCategories?.findIndex((category) => {
                return category.id == cat.id
              })

              return findIndex != -1
            }}
            selectedResult={productCategories}
            onSelectResult={handleSetActive}
            onDeselectResult={handleSetInactive}
          />
        </PopoverContent>
      </Popover>
      <FormMessage />
    </FormItem>
  )
}

const Organize = () => {
  const form = useFormContext()

  return (
    <div className="grid grid-cols-2 gap-4">
      {/* <FormField
        control={form.control}
        name="type"
        render={({ field }) => {
          return <TypeComboBox productType={field.value} name={field.name} />
        }}
      /> */}

      <FormField
        control={form.control}
        name="categories"
        render={({ field }) => {
          return <CategoryComboBox productCategories={field.value ?? []} name={field.name} />
        }}
      />
    </div>
  )
}

export default Organize
