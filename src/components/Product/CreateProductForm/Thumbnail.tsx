/* eslint-disable @typescript-eslint/ban-ts-comment */
import { Dropzone } from '@/components/Form/Dropzone'
import { FileWithPath } from 'react-dropzone'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { CreateProductInput } from '@/types/CreateProduct'
import { useFormContext } from 'react-hook-form'
import { Image } from '@unpic/react'
import { Label } from '@/components/ui/label'
import { bytesToSize } from '@/lib/utils'
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem
} from '@/components/ui/dropdown-menu'
import { MoreHorizontal, Trash2Icon } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useEffect } from 'react'
import { Input } from '@/components/ui/input'
import OrSeparator from '@/components/ui/orSeparator'

const Thumbnail = () => {
  const form = useFormContext<CreateProductInput>()
  const fieldName = 'thumbnailFile'
  const multiple = false
  const imagesFile = form.watch(fieldName)
  const imageFileWithPath = imagesFile
    ? (imagesFile as unknown as FileWithPath & { preview: string })
    : undefined

  useEffect(() => {
    // Make sure to revoke the data uris to avoid memory leaks, will run on unmount
    return () => {
      if (imageFileWithPath) {
        URL.revokeObjectURL(imageFileWithPath.preview)
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return (
    <>
      <FormField
        control={form.control}
        name="thumbnail"
        render={({ field }) => (
          <FormItem className="flex flex-col">
            <FormLabel>Image URL</FormLabel>
            <FormControl>
              <Input placeholder="Image Url" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <div className="my-4">
        <OrSeparator />
      </div>

      <FormField
        control={form.control}
        name={fieldName}
        // rules={{ required: 'Please insert the image' }}
        render={({ field }) => (
          <FormItem>
            <FormLabel>Upload Image</FormLabel>
            <FormControl>
              <Dropzone
                multiple={multiple}
                onDrop={(acceptedFiles) => {
                  if (imageFileWithPath?.path != acceptedFiles[0].path) {
                    const preview = Object.assign(acceptedFiles[0], {
                      preview: URL.createObjectURL(acceptedFiles[0])
                    })

                    form.setValue(fieldName, preview as unknown as File, {
                      shouldValidate: true
                    })
                  }
                }}
                {...field}
              />
            </FormControl>
            <FormDescription>Used to represent the product during checkout.</FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      {imageFileWithPath && (
        <div className="flex flex-col space-y-4 mt-2">
          <div className="grid grid-cols-[150px_1fr_36px] space-x-2 items-center">
            <Image
              key={imageFileWithPath.path}
              src={imageFileWithPath.preview ?? ''}
              height={150}
              width={150}
              objectFit="contain"
              className="rounded-md"
            />
            <div className="flex flex-col">
              <Label className="text-xs font-normal">{imageFileWithPath.path}</Label>
              <Label className="text-xs font-normal text-gray-500">
                {bytesToSize(imageFileWithPath.size)}
              </Label>
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation()

                    form.setValue(fieldName, undefined, {
                      shouldValidate: true
                    })
                  }}
                  className="space-x-2"
                >
                  <Trash2Icon size="16" />
                  <span>Delete</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      )}
    </>
  )
}

export default Thumbnail
