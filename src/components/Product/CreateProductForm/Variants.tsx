import { BadgeInput } from '@/components/Form/BadgeInput'
import { But<PERSON> } from '@/components/ui/button'
import { FormField, FormItem, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { CreateProductInput } from '@/types/CreateProduct'
import { CheckIcon, EditIcon, MoreHorizontalIcon, PlusCircleIcon, Trash2Icon } from 'lucide-react'
import { first, isEmpty, replaceOrAppend } from 'radash'
import { FC, useEffect, useState } from 'react'
import { UseFieldArrayRemove, useFieldArray, useFormContext, useWatch } from 'react-hook-form'
import { CreateProductCreateVariantForm } from '../VariantForm/CreateProductCreateVariantForm'
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem
} from '@/components/ui/dropdown-menu'
import { TooltipProvider, Tooltip, TooltipTrigger, TooltipContent } from '@/components/ui/tooltip'
import { Cross2Icon } from '@radix-ui/react-icons'
import { Dialog, DialogTrigger } from '@/components/ui/dialog'

export type OptionValue = {
  option: string
  values: string[]
}

const ProductOptionFields: FC<{
  fields: Record<'id', string>[]
  remove: UseFieldArrayRemove
  optionValues: OptionValue[]
  setOptionValues: (values: OptionValue[]) => void
}> = ({ fields, remove, optionValues, setOptionValues }) => {
  const form = useFormContext<CreateProductInput>()

  return (
    <div className="flex flex-col space-y-2">
      <div className="grid grid-cols-5 gap-4">
        <div className="col-span-2">
          <p className="text-sm">Option title</p>
        </div>
        <div className="col-span-3">
          <p className="text-sm">Variations (comma separated)</p>
        </div>
      </div>
      {fields.map((field, index) => {
        const value = form.watch(`options.${index}.title`)

        return (
          <div className="grid grid-cols-5 gap-4" key={field.id}>
            <FormField
              key={field.id}
              control={form.control}
              name={`options.${index}.title`}
              render={({ field }) => {
                return (
                  <FormItem className="flex flex-col col-span-2">
                    <Input placeholder="Color..." {...field} />
                    <FormMessage />
                  </FormItem>
                )
              }}
            />

            <FormItem className="flex flex-col col-span-3">
              <div className="flex w-full space-x-2 ">
                <div className="flex-1">
                  <BadgeInput
                    {...{
                      onTokenised: (tokens) => {
                        if (tokens.length == 0) {
                          return
                        }

                        const newValue = { option: value, values: tokens }
                        const newValues = replaceOrAppend(
                          optionValues,
                          newValue,
                          (f) => f.option == value
                        )
                        setOptionValues(newValues)
                      }
                    }}
                  />
                </div>
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  onClick={() => {
                    remove(index)
                    const newOptionValues = [...optionValues]
                    newOptionValues.splice(index, 1)
                    setOptionValues(newOptionValues)
                  }}
                >
                  <Trash2Icon className="h-4 w-4" />
                </Button>
              </div>
              <FormMessage />
            </FormItem>
          </div>
        )
      })}
    </div>
  )
}

const ProductVariantsFields: FC<{
  optionValues: OptionValue[]
  setVariantIndexSelected: (value: number) => void
}> = ({ optionValues, setVariantIndexSelected }) => {
  const form = useFormContext<CreateProductInput>()
  const variants = form.watch('variants')
  const { remove } = useFieldArray({
    name: 'variants'
  })

  return (
    <div className="flex flex-col space-y-2">
      <div className="grid grid-cols-[1fr_100px_100px_48px] gap-2 items-center">
        {!isEmpty(variants?.length) && (
          <>
            <p className="text-xs">Variant</p>
            <p className="text-xs">Price</p>
            <p className="text-xs">Status</p>
            <div />
          </>
        )}
        {variants?.map((variant, index) => {
          return (
            <>
              <p>{variant.title}</p>
              <div>{`${first(variant.prices ?? [])?.currency_code} ${
                first(variant.prices ?? [])?.amount ?? 0
              }`}</div>
              <div>
                {variant.options?.length == optionValues.length ? (
                  <CheckIcon className="h-4 w-4 text-success" />
                ) : (
                  <TooltipProvider delayDuration={100}>
                    <Tooltip>
                      <TooltipTrigger type="button">
                        <Cross2Icon className="h-4 w-4 text-destructive" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <div className="flex flex-col space-y-1">
                          <p>You are missing option values for the following options</p>
                          {optionValues
                            .filter((optionValue) => {
                              const hasValue = variant.options?.some((option) =>
                                optionValue.values.includes(option.value)
                              )

                              return !hasValue
                            })
                            .map((o) => (
                              <li>{o.option}</li>
                            ))}
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}
              </div>
              <div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button type="button" variant="outline" size="icon">
                      <MoreHorizontalIcon className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem
                      onClick={async (event) => {
                        event.stopPropagation()
                        setVariantIndexSelected(index)
                      }}
                      className="space-x-2"
                    >
                      <EditIcon size="16" />
                      <span>Edit</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={async (event) => {
                        event.stopPropagation()
                        remove(index)
                      }}
                      className="space-x-2"
                    >
                      <Trash2Icon size="16" />
                      <span>Delete</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </>
          )
        })}
      </div>
    </div>
  )
}

const Variants = () => {
  const { fields, append, remove } = useFieldArray({
    name: 'options'
  })
  const [optionValues, setOptionValues] = useState<OptionValue[]>([])
  const variants = useWatch({ name: 'variants' })
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [variantIndexSelected, setVariantIndexSelected] = useState<number | null>()

  useEffect(() => {
    if (variantIndexSelected != null) {
      setIsDialogOpen(true)
    }
  }, [variantIndexSelected])

  useEffect(() => {
    if (!isDialogOpen) {
      setVariantIndexSelected(null)
    }
  }, [isDialogOpen])

  return (
    <div className="grid gap-4">
      <>
        <Label>Product options</Label>

        <ProductOptionFields {...{ fields, remove, optionValues, setOptionValues }} />

        <Button
          variant="outline"
          type="button"
          onClick={() => {
            append({ title: '' })
          }}
        >
          <PlusCircleIcon size="16" className="mr-2" />
          Add an option
        </Button>
      </>

      <>
        <Label>Product variants</Label>

        <ProductVariantsFields {...{ optionValues, setVariantIndexSelected }} />

        <Dialog
          open={isDialogOpen}
          onOpenChange={(isOpen) => {
            setIsDialogOpen(isOpen)
          }}
        >
          <DialogTrigger asChild>
            <Button
              type="button"
              variant="outline"
              size="icon"
              className="w-full"
              disabled={optionValues.length == 0}
            >
              <PlusCircleIcon size="16" className="mr-2" />
              Add a variant
            </Button>
          </DialogTrigger>

          {/* HACKY WAY TO MAKE SURE INITIAL VALUE IS USED */}
          {variantIndexSelected == null ? (
            <>
              {isDialogOpen && (
                <CreateProductCreateVariantForm
                  {...{ optionValues, setIsDialogOpen }}
                  index={variants?.length ?? 0}
                />
              )}
            </>
          ) : (
            <>
              {variants[variantIndexSelected] && (
                <CreateProductCreateVariantForm
                  {...{ optionValues, setIsDialogOpen }}
                  index={variantIndexSelected}
                  initialValues={variants[variantIndexSelected]}
                />
              )}
            </>
          )}
        </Dialog>
      </>
    </div>
  )
}

export default Variants
