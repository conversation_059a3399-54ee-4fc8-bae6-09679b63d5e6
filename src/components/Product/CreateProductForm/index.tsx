/* eslint-disable @typescript-eslint/ban-ts-comment */
import * as React from 'react'
import { useFormContext } from 'react-hook-form'
import { mutate } from 'swr'

import { cn } from '@/lib/utils'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from '@/components/ui/accordion'
import { CreateProductInput } from '@/types/CreateProduct'
import FileService from '@/network/services/file'
import ProductService from '@/network/services/product'
import { toast } from '@/components/ui/use-toast'

import Variants from './Variants'
import Organize from './Organize'
import GeneralInformation from './GeneralInformation'
import Media from './Media'
import Thumbnail from './Thumbnail'
import { isAxiosError } from 'axios'
import ProductCollectionService from '@/network/services/product_collection'
import Metadata from './Metadata'

interface ProductFormProps extends React.HTMLAttributes<HTMLDivElement> {}

export function CreateProductForm({ className }: ProductFormProps) {
  const form = useFormContext<CreateProductInput>()

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      if (values.imageFiles) {
        // upload media
        const { data } = await FileService.upload(values.imageFiles)
        values.images = data.uploads.map((upload) => upload.url)
        delete values.imageFiles
      }

      if (values.thumbnailFile) {
        // only upload if no url provided
        if (!values.thumbnail) {
          // upload thumbnail
          const { data } = await FileService.upload([values.thumbnailFile])
          values.thumbnail = data.uploads[0].url
        }
        delete values.thumbnailFile
      }

      if (values.thumbnail) {
        delete values.thumbnailFile
      }

      if (values.collection) {
        values.collection_id = values.collection.id
        delete values.collection
      }

      if (values.categories) {
        values.categories = values.categories.map((category) => {
          return { id: category.id }
        })
      }
      console.log(values)
      await ProductService.createProduct(values)
      mutate((key) => typeof key === 'string' && key.startsWith(ProductService.getProducts))
      mutate(
        (key) =>
          typeof key === 'string' && key.startsWith(ProductCollectionService.getProductCollections)
      )
      toast({
        title: 'Product proposed',
        variant: 'success'
      })
    } catch (error) {
      console.error(error)

      if (isAxiosError(error) && error.response?.data.type == 'duplicate_error') {
        // likely is handle duplicated
        if (error.response.data.message.startsWith('Product with handle')) {
          form.setError('handle', {
            message: error.response.data.message
          })
        }
      } else {
        form.setError('root', {})
      }

      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <form
      id="create-product-form"
      onSubmit={onSubmit}
      className={cn('flex flex-col space-y-8 max-w-4xl mx-auto', className)}
    >
      <Accordion type="multiple" className="w-full" defaultValue={['general-information']}>
        <AccordionItem value="general-information">
          <AccordionTrigger>General Information</AccordionTrigger>
          <AccordionContent>
            <GeneralInformation />
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="organize">
          <AccordionTrigger>Organize</AccordionTrigger>
          <AccordionContent>
            <Organize />
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="variants">
          <AccordionTrigger>Variants</AccordionTrigger>
          <AccordionContent>
            <Variants />
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="thumbnail">
          <AccordionTrigger>Thumbnail</AccordionTrigger>
          <AccordionContent>
            <Thumbnail />
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="media">
          <AccordionTrigger>Media</AccordionTrigger>
          <AccordionContent>
            <Media />
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="metadata">
          <AccordionTrigger>Metadata</AccordionTrigger>
          <AccordionContent>
            <Metadata />
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </form>
  )
}
