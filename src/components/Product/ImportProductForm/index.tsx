/* eslint-disable @typescript-eslint/ban-ts-comment */
import * as React from 'react'

import { cn } from '@/lib/utils'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Dropzone } from '@/components/Form/Dropzone'
import { useFormContext } from 'react-hook-form'
import FileService from '@/network/services/file'
import { CreateBatchJobInput } from '@/types/BatchJob'
import BatchJobService from '@/network/services/batch-job'
import { toast } from '@/components/ui/use-toast'
import { isAxiosError } from 'axios'
import { FileWithPath } from 'react-dropzone'
import { Separator } from '@/components/ui/separator'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from '@/components/ui/accordion'

interface ImportProductForm extends React.HTMLAttributes<HTMLDivElement> {}

export function ImportProductForm({ className }: ImportProductForm) {
  const form = useFormContext<CreateBatchJobInput & { file?: File }>()
  const file = form.watch('file') as FileWithPath

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      if (!values.file) {
        return
      }

      const { data } = await FileService.uploadPrivate([values.file])
      const fileKey = data.uploads[0].key
      values.context.fileKey = fileKey

      delete values.file

      await BatchJobService.createBatchJob(values)

      toast({
        title: 'File uploaded',
        variant: 'success'
      })
    } catch (error) {
      console.error(error)
      if (isAxiosError(error) && error.response?.data.type == 'duplicate_error') {
        // TODO: need to handle errors here
      } else {
        form.setError('root', {})
      }

      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <>
      <form
        id="import-product-form"
        onSubmit={onSubmit}
        className={cn('mx-auto flex max-w-4xl flex-col space-y-8', className)}
      >
        <FormField
          control={form.control}
          name="file"
          rules={{ required: 'Please upload the csv file' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Upload CSV file</FormLabel>
              <FormControl>
                <Dropzone
                  multiple={false}
                  onDrop={(acceptedFiles) => {
                    const file = Object.assign(acceptedFiles[0])

                    form.setValue('file', file as File, {
                      shouldValidate: true
                    })
                  }}
                  description="Drop your csv file here, or click to browse"
                  accept=".csv"
                  files={file ? [file] : []}
                  {...field}
                />
              </FormControl>
              <FormDescription>Please follow the format exactly</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
      </form>
      <div className="pt-5 pb-5">
        <Separator className={cn('mx-auto flex max-w-4xl flex-col space-y-8', className)} />
      </div>
      <div className={cn('mx-auto flex max-w-4xl flex-col space-y-2', className)}>
        <h1 className="font-semibold">Guidelines</h1>

        <Accordion type="multiple" className="w-full">
          <AccordionItem value="create-product">
            <AccordionTrigger>
              A. Create New Product with New Variant and New Option
            </AccordionTrigger>
            <AccordionContent>
              <div className="space-y-5">
                'Product Handle', 'Product Title', 'Variant Title', 'Variant SKU' are{' '}
                <span className="font-semibold text-red-900">compulsory</span> fields.
                <div className="bg-gray-300 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md">
                  1. 'Product Handle', 'Product Title', 'Variant Title', 'Variant SKU', 'Variant
                  Barcode' must be unique. 'Product ID' & 'Variant ID' is{' '}
                  <span className="font-semibold text-red-900">not needed</span> when creating new
                  product, both will be generated automatically by the system.
                </div>
                <div className="bg-gray-300 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md">
                  2. Each product can have{' '}
                  <span className="font-semibold text-blue-900">many variants</span>, and each
                  variant can have <span className="font-semibold text-blue-900">many options</span>
                  , while each option can have{' '}
                  <span className="font-semibold text-blue-900">any value</span>.
                  <br />
                  <br />
                  To create Option you will have to manually add the columns in the spreadsheet such
                  as 'Option 1 Name' and it's value 'Option 1 Value', 'Option 2 Name' and it's value
                  'Option 2 Value', and so on. Add the number as you add more options.
                  <div className="bg-gray-400 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md m-3">
                    Eg. <br />
                    Product A, Variant A, Option 1 Name: Color, Option 1 Value: Red, Option 2 Name:
                    Size, Option 2 Value: M<br />
                    Product A, Variant B, Option 1 Name: Color, Option 1 Value: Orange, Option 2
                    Name: Size, Option 2 Value: M<br />
                  </div>
                  <br />
                  <br />
                  Please ensure that each variant's option have some differences in their option
                  value to differentiate between the variants (At least 1 option value difference or
                  a product would not make sense to be added as a variant if there is already an
                  existing variant with the exact same option's value).
                  <div className="bg-gray-400 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md m-3">
                    Example of Variants <span className="font-semibold text-blue-900">without</span>{' '}
                    different option <span className="font-semibold text-blue-900">value</span>{' '}
                    which is <span className="font-semibold text-red-900">invalid</span>: <br />
                    Product <span className="font-semibold text-red-900">A</span>, Variant{' '}
                    <span className="font-semibold text-blue-900">A</span>, Option 1 Name: Color,
                    Option 1 Value: <span className="font-semibold text-red-900">Red</span>, Option
                    2 Name: Size, Option 2 Value:{' '}
                    <span className="font-semibold text-red-900">M</span>
                    <br />
                    Product <span className="font-semibold text-red-900">A</span>, Variant{' '}
                    <span className="font-semibold text-blue-900">B</span>, Option 1 Name: Color,
                    Option 1 Value: <span className="font-semibold text-red-900">Red</span>, Option
                    2 Name: Size, Option 2 Value:{' '}
                    <span className="font-semibold text-red-900">M</span>
                    <br />
                    <br />
                    Example of Variants <span className="font-semibold text-blue-900">
                      with
                    </span>{' '}
                    different option <span className="font-semibold text-blue-900">value</span>{' '}
                    which is <span className="font-semibold text-green-900">valid</span>: <br />
                    Product A, Variant A, Option 1 Name: Color, Option 1 Value:{' '}
                    <span className="font-semibold text-green-900">Red</span>, Option 2 Name: Size,
                    Option 2 Value: M<br />
                    Product A, Variant B, Option 1 Name: Color, Option 1 Value:{' '}
                    <span className="font-semibold text-green-900">Green</span>, Option 2 Name:
                    Size, Option 2 Value: M<br />
                  </div>
                  <br />
                  <br />
                  Please ensure that each variant's option names are same (Different variants should
                  have same option names).
                  <div className="bg-gray-400 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md m-3">
                    Example of Variants <span className="font-semibold text-blue-900">with</span>{' '}
                    different option <span className="font-semibold text-blue-900">name</span> which
                    is <span className="font-semibold text-red-900">invalid</span>: <br />
                    Product A, Variant A, Option 1 Name: Color, Option 1 Value: Red, Option 2 Name:
                    Size, Option 2 Value: M, <br /> Option 3 Name:{' '}
                    <span className="font-semibold text-red-900">Printing</span>, Option 3 Value:{' '}
                    <span className="font-semibold text-red-900">Naruto</span>
                    <br />
                    Product A, Variant B, Option 1 Name: Color, Option 1 Value: Red, Option 2 Name:
                    Size, Option 2 Value: M, <br /> Option 3 Name:{' '}
                    <span className="font-semibold text-red-900">Style</span>, Option 3 Value:{' '}
                    <span className="font-semibold text-red-900">A</span>
                    <br />
                    Product B, Variant A, Option 1 Name: Color, Option 1 Value: Red, Option 2 Name:
                    Size, Option 2 Value: M, <br /> Option 3 Name:{' '}
                    <span className="font-semibold text-red-900">Printing</span>, Option 3 Value:{' '}
                    <span className="font-semibold text-red-900">Naruto</span>
                    <br />
                    Product B, Variant B, Option 1 Name: Color, Option 1 Value: Red, Option 2 Name:
                    Size, Option 2 Value: M <br />{' '}
                    <span className="font-semibold text-red-900"> *Does not have Option 3*</span>
                    <br />
                    <br />
                    Example of Variants <span className="font-semibold text-blue-900">
                      without
                    </span>{' '}
                    different option <span className="font-semibold text-blue-900">name</span> which
                    is <span className="font-semibold text-green-900">valid</span>: <br />
                    Product A, Variant A, Option 1 Name: Color, Option 1 Value: Red, Option 2 Name:
                    Size, Option 2 Value: M, <br /> Option 3 Name:{' '}
                    <span className="font-semibold text-green-900">Printing</span>, Option 3 Value:{' '}
                    <span className="font-semibold text-green-900">Naruto</span>
                    <br />
                    Product A, Variant B, Option 1 Name: Color, Option 1 Value: Red, Option 2 Name:
                    Size, Option 2 Value: M, <br /> Option 3 Name:{' '}
                    <span className="font-semibold text-green-900">Printing</span>, Option 3 Value:{' '}
                    <span className="font-semibold text-green-900">Dragon</span>
                    <br />
                    Product B, Variant A, Option 1 Name: Color, Option 1 Value: Red, Option 2 Name:
                    Size, Option 2 Value: M, <br /> Option 3 Name:{' '}
                    <span className="font-semibold text-green-900">Printing</span>, Option 3 Value:{' '}
                    <span className="font-semibold text-green-900">Evening</span>
                    <br />
                    Product B, Variant B, Option 1 Name: Color, Option 1 Value: Red, Option 2 Name:
                    Size, Option 2 Value: M, <br /> Option 3 Name:{' '}
                    <span className="font-semibold text-green-900">Printing</span>, Option 3 Value:{' '}
                    <span className="font-semibold text-green-900">Midnight Moon</span>
                    <br />
                  </div>
                  <br />
                  <br />
                  'Option *Number* Name' must be unique in order to create a new option for the
                  product's variant. Else it will be treated as an update to an existing option's
                  value.
                  <div className="bg-gray-400 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md m-3">
                    Eg. <br />
                    Product A, Variant A, Option 1 Name:{' '}
                    <span className="font-semibold text-blue-900">Size</span>, Option 1 Value:
                    <span className="font-semibold text-red-900">Small</span>, Option 2 Name:{' '}
                    <span className="font-semibold text-blue-900">Size</span>, Option 2 Value:{' '}
                    <span className="font-semibold text-red-900">Big</span>
                    <br />
                    Product A's Variant A's option with Name 'Size' will has it's value updated to
                    'Big'.
                    <br />
                    <br />
                    Product B, Variant A, Option 1 Name:{' '}
                    <span className="font-semibold text-blue-900">Color</span>, Option 1 Value:
                    <span className="font-semibold text-red-900">green</span>, Option 2 Name:{' '}
                    <span className="font-semibold text-blue-900">Color</span>, Option 2 Value:{' '}
                    <span className="font-semibold text-red-900">blue</span>
                    <br />
                    Product B's Variant A's option with Name 'Color' will has it's value updated to
                    'blue'.
                  </div>
                  <div>
                    <br />
                    <br />
                    Option name cannot be updated later after you created a product, you can only
                    add new option to a product. However, you can update it's value by simply
                    changing the value of an option
                    <div className="bg-gray-400 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md m-3">
                      Eg. <br />
                      Product A, Variant A, Option 1 Name:{' '}
                      <span className="font-semibold text-blue-900">Size</span>, Option 1 Value:{' '}
                      <span className="font-semibold text-blue-900">Small</span> (Assume this is the{' '}
                      <span className="font-semibold text-blue-900">initial</span> value in system){' '}
                      <br />
                      Product A, Variant A, Option 1 Name:{' '}
                      <span className="font-semibold text-blue-900">Size</span>, Option 1 Value:{' '}
                      <span className="font-semibold text-blue-900">Big</span> (update via
                      spreadsheet import)
                      <br />
                      <br />
                      Product A's Variant A's option with Name 'Size' will has it's value updated to
                      'Big'.
                    </div>
                  </div>
                </div>
                <div className="bg-gray-300 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md">
                  3. 'Product Status' can only be 'draft', 'proposed', 'published', 'rejected'.
                  Please also ensure the status are in{' '}
                  <span className="font-semibold text-red-900">lowercase</span> as 'Product Status'
                  field is case sensitive. Leave it blank when creating will default the status to
                  'proposed'.
                </div>
                <div className="bg-gray-300 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md">
                  4. Please ensure 'Brand Handle' are already created and exist (It can be created
                  through brand spreadsheet import). The system won't create it automatically if it
                  does not exist.
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="add-variant">
            <AccordionTrigger>B. Add New Product Variant</AccordionTrigger>
            <AccordionContent>
              <div className="space-y-5">
                To add new product variant, 'Product ID', 'Product Handle', 'Variant Title',
                'Variant SKU' are <span className="font-semibold text-red-900">compulsory</span>{' '}
                fields.
                <br />A product can have many Variants as mentioned earlier. 'Variant Id' will be
                generated automatically, hence, it is not needed. While 'Variant Barcode' can be
                updated later.
                <div className="bg-gray-300 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md">
                  1. To add a new product variant for a product, please ensure 'Variant SKU',
                  'Variant Title' are provided and that{' '}
                  <span className="font-semibold text-gray-900">
                    these values are all unique from existing ones for a product.
                  </span>
                  <div className="bg-gray-400 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md m-3">
                    Eg.
                    <br />
                    Product ID: prod_01A, Product Handle: cloth, Variant SKU: CLTH2930, Variant
                    Title: Casual Clothing <br />
                    Product ID: prod_02XCD, Product Handle: cloth-summer, Variant SKU: CLTHS88320,
                    Variant Title: Summer Casual Clothing <br />
                    <br />
                    Please ensure Variant SKU and Variant Title are{' '}
                    <span className="font-semibold text-red-900">unique</span>.
                  </div>
                </div>
                <div className="bg-gray-300 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md">
                  2. Please refer to A.2 for Option update/create if you wish to create new option
                  for the variant altogether at the same time, the procedure is the same.
                </div>
                <div className="bg-gray-300 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md">
                  3. The rest of the value is optional and can be updated later, you are free to
                  update any values, but just need to ensure value such as 'Product Handle',
                  'Product Title', 'Variant Title', 'Variant SKU', 'Variant Barcode' are unique as
                  mentioned in A.1.
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="add-category">
            <AccordionTrigger>C. Add/Update New Product Category</AccordionTrigger>
            <AccordionContent>
              <div className="space-y-5">
                To add new product category, 'Product ID', 'Product Handle', 'Variant Title',
                'Variant SKU', 'Product Category *Number* Handle' are{' '}
                <span className="font-semibold text-red-900">compulsory</span> fields.
                <br />A product can have zero or many Categories.
                <div className="bg-gray-300 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md">
                  1. To add a new product variant for a product, please ensure 'Product Category
                  Handle' are provided and is an existing category handle.
                </div>
                <div className="bg-gray-300 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md">
                  2. To add Category to a product you will have to manually add the columns in the
                  spreadsheet such as 'Product Category 1 Handle', 'Product Category 2 Handle' and
                  so on. Add the number as you add more options.
                </div>
                <div className="bg-gray-300 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md">
                  3. When update a product's category. Please make sure the old categories of a
                  product are also present in the submitted spreadsheet, not including them will be
                  treated as <span className="font-semibold text-red-900">removing</span> the
                  categories from the product.
                  <br />
                  <div className="bg-gray-400 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md m-3">
                    Eg.
                    <br />
                    Before Update:
                    <Separator className="bg-slate-500 mb-2" />
                    Product ID: prod_01A,
                    <br />
                    Product Category 1 Handle: baby-care,
                    <br />
                    Product Category 2 Handle: Education
                    <br />
                    <br />
                    Product ID: prod_02A,
                    <br />
                    Product Category 1 Handle:{' '}
                    <span className="font-semibold text-red-900 line-through">baby-gear</span>,{' '}
                    <br />{' '}
                    <span className="font-semibold text-red-900">
                      (assume category 1 handle value is removed from spreadsheet cell, but the
                      column still remain because prod_01A still have category 1)
                    </span>
                    <br />
                    Product Category 2 Handle: diapering-potty
                    <br />
                    <br />
                    <br />
                    After Update:
                    <Separator className="bg-slate-500 mb-2" />
                    Product ID: prod_01A,
                    <br /> Product Category 1 Handle: baby-care,
                    <br /> Product Category 2 Handle: Education
                    <br /> <br />
                    Product ID: prod_02A,
                    <br /> Product Category 1 Handle:{' '}
                    <span className="font-semibold text-red-900">
                      (leaving blank in spreadsheet is alright in future update, the system will
                      automatically skip this and move on to process next category, there is no
                      ranking in category, so order does not matter)
                    </span>
                    ,<br />
                    Product Category 2 Handle: diapering-potty
                    <br />
                    <br />
                    Now, <span className="font-semibold text-blue-900">baby-gear</span> are the only
                    Product Category of Product{' '}
                    <span className="font-semibold text-blue-900">prod_02A</span> after the update.
                  </div>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="update-product">
            <AccordionTrigger>D. Update a Product's associated Value</AccordionTrigger>
            <AccordionContent>
              <div className="space-y-5">
                To update value, 'Product Id', 'Product Handle', 'Variant Id', 'Variant Title' are{' '}
                <span className="font-semibold text-red-900">compulsory</span> fields.
                <div className="bg-gray-300 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md">
                  1. Only 'Product Title', 'Product Description', 'Product Status', 'Product
                  Thumbnail', 'Brand Handle', 'Variant Title', 'Variant SKU', 'Variant Barcode',
                  'Price MYR', an option's 'Option *Number* Value' (Not 'Option *Number* Name'),
                  'Product Category *Number* Handle', and 'Store Id' can be updated.
                </div>
                <div className="bg-gray-300 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md">
                  2. Please ensure 'Product Handle', 'Product Title', 'Variant Title', 'Variant
                  SKU', 'Variant Barcode' are unique to prevent conflict with existing value as
                  mentioned in A.1.
                </div>
                <div className="bg-gray-300 font-semibold text-gray-900 text-sm pl-5 pr-5 pt-2 pb-2 border-solid border-red-100 rounded-md">
                  3. If you are trying to update an option's value or creating new option, please
                  refer to A.2.
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
    </>
  )
}
