import { SearchProducts, useSearchProducts } from '@/hooks/products/useProducts'
import { cn } from '@/lib/utils'
import { ChevronsUpDown } from 'lucide-react'
import { isEmpty } from 'radash'
import { FC, useState } from 'react'
import { useFormContext } from 'react-hook-form'
import { FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Search } from '@/components/Form/Search'

const POPOVER_WIDTH = 'w-[250px]'

// multiple select
export const ProductComboBox: FC<{ products: SearchProducts[]; name: string }> = ({
  products,
  name
}) => {
  const [open, setOpen] = useState(false)
  const form = useFormContext()

  const handleSetActive = (product: SearchProducts) => {
    const newProducts = [...products, product]
    form.setValue(name, newProducts)
  }

  const handleSetInactive = (product: SearchProducts) => {
    const newProducts = [...products]
    const findIndex = newProducts.findIndex((c) => c.id == product.id)
    newProducts.splice(findIndex, 1)
    form.setValue(name, newProducts)
  }

  const displayName = !isEmpty(products)
    ? products.length == 1
      ? products[0].title
      : products.length
    : 'Choose a product'

  return (
    <FormItem className="flex flex-col">
      <FormLabel>Products</FormLabel>

      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <FormControl>
            <Button
              variant="outline"
              role="combobox"
              className={cn(
                'w-[200px] justify-between',
                isEmpty(products) && 'text-muted-foreground',
                POPOVER_WIDTH
              )}
            >
              {(products.length ?? 0) > 1 ? (
                <Badge variant="outline">{displayName}</Badge>
              ) : (
                <>{displayName}</>
              )}

              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </FormControl>
        </PopoverTrigger>

        <PopoverContent side="bottom" className={cn('p-0', POPOVER_WIDTH)}>
          <Search<SearchProducts, SearchProducts[]>
            multiple
            fn={useSearchProducts}
            renderFn={(prod: SearchProducts) => prod.title}
            valueFn={(prod: SearchProducts) => prod.id}
            compareFn={(prod: SearchProducts) => {
              if (isEmpty(products)) {
                return false
              }

              const findIndex = products?.findIndex((product) => {
                return product.id == prod.id
              })

              return findIndex != -1
            }}
            selectedResult={products}
            onSelectResult={handleSetActive}
            onDeselectResult={handleSetInactive}
          />
        </PopoverContent>
      </Popover>
      <FormMessage />
    </FormItem>
  )
}
