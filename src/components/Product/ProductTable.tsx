import ProductService, { ProductResponse } from '@/network/services/product'
import { ColumnDef, Row, createColumnHelper } from '@tanstack/react-table'
import { DataTable, FilterColumn } from '@/components/Table/DataTable'
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem
} from '@/components/ui/dropdown-menu'
import { useToast } from '@/components/ui/use-toast'
import { Button } from '@/components/ui/button'
import { cn, statusToColor } from '@/lib/utils'
import { Product, ProductStatus } from '@/types/Product'
import useAuth from '@/hooks/useAuth'
import { MonitorCheckIcon, MoreHorizontal, Trash2Icon } from 'lucide-react'
import { FC } from 'react'
import { useNavigate } from 'react-router-dom'
import { mutate } from 'swr'
import { serialize } from '@/network/request'
import { ProductCategory } from '@/types/ProductCategory'
import { isEmpty } from 'radash'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger
} from '../ui/alert-dialog'

const columnHelper = createColumnHelper<Product>()
const columns: ColumnDef<Product>[] = [
  {
    accessorKey: 'title',
    header: 'Title'
  },
  {
    accessorKey: 'store.name',
    header: 'Store'
  },
  {
    accessorKey: 'collection.handle',
    header: 'Brand'
  },
  {
    accessorKey: 'categories',
    header: 'Categories',
    cell: (props) => {
      const value = props.getValue<ProductCategory[]>()
      if (isEmpty(value)) {
        return '-'
      }

      return value?.map((v) => v.name).join(', ')
    }
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: (props) => {
      const value = props.getValue<string>()
      const color = statusToColor(value)

      return (
        <div className="flex items-center space-x-2">
          <div className={cn('h-1.5 w-1.5 self-center rounded-full', color)} />
          <span className="capitalize">{value}</span>
        </div>
      )
    }
  },
  columnHelper.display({
    id: 'actions',
    cell: (props) => <RowActions row={props.row} />
  })
]

const columnFilter: FilterColumn[] = [{ columnKey: 'title', header: 'Search', dataType: 'string' }]

const RowActions: FC<{ row: Row<Product> }> = ({ row }) => {
  const productId = row.original.id
  const { toast } = useToast()
  const { role } = useAuth()

  return (
    <div className="w-full flex justify-end">
      {role != 'vendor' && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0" onClick={(e) => e.stopPropagation()}>
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem
              disabled={row.original.status != ProductStatus.PROPOSED}
              onClick={async (event) => {
                event.stopPropagation()

                try {
                  await ProductService.updateProduct(productId, { status: ProductStatus.PUBLISHED })
                  mutate(
                    (key) => typeof key === 'string' && key.startsWith(ProductService.getProducts)
                  )
                  toast({
                    title: 'Product published',
                    variant: 'success'
                  })
                } catch (error) {
                  console.log(error)
                }
              }}
              className="space-x-2"
            >
              <MonitorCheckIcon size="16" />
              <span>Publish</span>
            </DropdownMenuItem>
            <DropdownMenuItem className="space-x-2">
              <AlertDialog key="delete-product">
                <AlertDialogTrigger
                  type="button"
                  onClick={(e) => e.stopPropagation()}
                  className="flex space-x-2 w-full"
                >
                  <Trash2Icon size="16" />
                  <span>Delete</span>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>
                      Are you absolutely sure to delete {row.original.title} from{' '}
                      {row.original.store?.name}?
                    </AlertDialogTitle>
                    <AlertDialogDescription>This action cannot be undone</AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={async (event) => {
                        event.stopPropagation()

                        try {
                          await ProductService.deleteProduct(productId)
                          mutate(
                            (key) =>
                              typeof key === 'string' && key.startsWith(ProductService.getProducts)
                          )
                          toast({
                            description: 'Product deleted',
                            variant: 'destructive'
                          })
                        } catch (error) {
                          console.log(error)
                        }
                      }}
                    >
                      Confirm
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    </div>
  )
}

const ProductTable = () => {
  const nav = useNavigate()
  const { role } = useAuth()
  // remove index 1 (store)
  const roleBasedColumns =
    role == 'vendor' ? [...columns].filter((_, index) => index != 1) : columns

  return (
    <DataTable<Product, unknown, ProductResponse>
      columns={roleBasedColumns}
      filterColumns={columnFilter}
      swrService={serialize(
        ProductService.getProducts,
        { expand: ['store', 'categories', 'collection'] },
        'comma'
      )}
      pageParam="offset"
      limitParam="limit"
      filterParam="q"
      sortParam="sort"
      sortColumns={['title', 'status']}
      toRow={ProductService.toRow}
      toPaginate={ProductService.toPaginate}
      onRowClick={(row: Row<Product>) => {
        nav(`/products/${row.original.id}`)
      }}
    />
  )
}

export default ProductTable
