import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>ooter, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import {
  DialogHeader,
  Dialog,
  DialogContent,
  DialogTitle,
  DialogClose
} from '@/components/ui/dialog'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from '@/components/ui/accordion'
import { Button } from '@/components/ui/button'
import { Product, ProductStatus } from '@/types/Product'
import { cn, statusToColor } from '@/lib/utils'
import { UpdateProductInput } from '@/types/CreateProduct'
import { useForm } from 'react-hook-form'
import { Form } from '@/components/ui/form'
import { Icons } from '@/components/icons'
import ProductService from '@/network/services/product'
import { toast } from '@/components/ui/use-toast'
import { FC, PropsWithChildren, useEffect, useMemo, useState } from 'react'
import { isEmpty } from 'radash'
import { mutate } from 'swr'
import { MoreHorizontal, SettingsIcon } from 'lucide-react'
import GeneralInformation from '../CreateProductForm/GeneralInformation'
import Organize from '../CreateProductForm/Organize'
import useAuth from '@/hooks/useAuth'

const Title: FC<PropsWithChildren> = ({ children }) => {
  return <div className="text-sm font-muted-foreground">{children}</div>
}

const Content: FC<PropsWithChildren> = ({ children }) => {
  return <div className="text-sm text-right font-medium">{children}</div>
}

const STATUS_LIST = [
  // ProductStatus.DRAFT,
  ProductStatus.PROPOSED,
  ProductStatus.PUBLISHED,
  ProductStatus.REJECTED
]

const ProductStatusBadge: FC<{ product: Product }> = ({ product }) => {
  const { role } = useAuth()
  const color = useMemo(() => {
    return statusToColor(product.status)
  }, [product.status])

  const statuses = useMemo(() => {
    return STATUS_LIST.filter((f) => f != product.status)
  }, [product.status])

  const updateStatus = async (product: Product, status: ProductStatus) => {
    try {
      await ProductService.updateProduct(product.id, { status })
      mutate(
        (key) => typeof key === 'string' && key.startsWith(ProductService.getProduct(product.id))
      )
      toast({
        title: `Product ${status}`,
        variant: 'success'
      })
    } catch (error) {
      console.log(error)
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="h-8">
          <div className="flex items-center space-x-2">
            <div className={cn('h-1.5 w-1.5 self-center rounded-full', color)} />
            <span className="capitalize text-xs">{product.status}</span>
          </div>
        </Button>
      </DropdownMenuTrigger>
      {role != 'vendor' && (
        <DropdownMenuContent align="end" forceMount>
          <DropdownMenuGroup>
            {statuses.map((status) => {
              return (
                <DropdownMenuItem
                  key={status}
                  onClick={async (event) => {
                    event.stopPropagation()
                    updateStatus(product, status)
                  }}
                  className="space-x-2"
                >
                  <div
                    className={cn('h-1.5 w-1.5 self-center rounded-full', statusToColor(status))}
                  />
                  <span className="capitalize text-xs">{status}</span>
                </DropdownMenuItem>
              )
            })}
          </DropdownMenuGroup>
        </DropdownMenuContent>
      )}
    </DropdownMenu>
  )
}

const GeneralInformationCard: FC<{ product: Product }> = ({ product }) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const form = useForm<UpdateProductInput>({
    shouldUseNativeValidation: false,
    defaultValues: {
      title: product.title,
      subtitle: product.subtitle,
      description: product.description,
      handle: product.handle,
      type: product.type,
      collection_id: product.collection_id,
      collection: product.collection,
      categories: product.categories
    }
  })

  useEffect(() => {
    if (isDialogOpen || product) {
      form.reset({
        title: product.title,
        subtitle: product.subtitle,
        description: product.description,
        handle: product.handle,
        type: product.type,
        collection_id: product.collection_id,
        collection: product.collection,
        categories: product.categories
      })
    }
  }, [isDialogOpen, product])

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      if (values.categories) {
        values.categories = values.categories.map((category) => {
          return { id: category.id }
        })
      }

      if (values.collection) {
        values.collection_id = values.collection.id
        delete values.collection
      }
      console.log(values)
      await ProductService.updateProduct(product.id, values)

      setIsDialogOpen(false)
      mutate(
        (key) => typeof key === 'string' && key.startsWith(ProductService.getProduct(product.id))
      )
      toast({
        title: 'Product updated',
        variant: 'success'
      })
    } catch (error) {
      console.error(error)
      form.setError('root', {})
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <CardTitle>{product.title}</CardTitle>
          <div className="flex flex-row space-x-2">
            <ProductStatusBadge {...{ product }} />

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" forceMount>
                <DropdownMenuGroup>
                  <DropdownMenuItem
                    onClick={async (event) => {
                      event.stopPropagation()
                      setIsDialogOpen(true)
                    }}
                    className="space-x-2"
                  >
                    <SettingsIcon size="16" />
                    <span>Edit</span>
                  </DropdownMenuItem>
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-2">
            <Title>Subtitle</Title>
            <Content>{product.subtitle ?? '-'}</Content>
            <Title>Handle</Title>
            <Content>{product.handle ?? '-'}</Content>
            <Title>Description</Title>
            <Content>{product.description ?? '-'}</Content>
            <Title>Store</Title>
            <Content>{product.store?.name ?? '-'}</Content>
            <Title>Brands</Title>
            <Content>{isEmpty(product.collection) ? '-' : product.collection.title}</Content>
            <Title>Category</Title>
            <Content>
              {isEmpty(product.categories)
                ? '-'
                : product.categories?.map((category, index) => <p key={index}>{category.name}</p>)}
            </Content>
          </div>
        </CardContent>
        <CardFooter></CardFooter>
      </Card>

      <Dialog
        open={isDialogOpen}
        onOpenChange={(isOpen) => {
          setIsDialogOpen(isOpen)
        }}
      >
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle className="flex justify-between items-center">
              Edit General Information
              <div className="flex space-x-2">
                <DialogClose asChild>
                  <Button variant="outline">Cancel</Button>
                </DialogClose>
                <Button
                  type="submit"
                  form="update-product-form"
                  disabled={form.formState.isSubmitting}
                  onClick={() => {
                    form.clearErrors()
                  }}
                >
                  {form.formState.isSubmitting && (
                    <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  Save
                </Button>
              </div>
            </DialogTitle>
          </DialogHeader>
          <Form {...form}>
            <form
              id="update-product-form"
              onSubmit={onSubmit}
              className={cn('flex flex-col space-y-8')}
            >
              <Accordion
                type="multiple"
                className="w-full"
                defaultValue={['general-information', 'organize']}
              >
                <AccordionItem value="general-information">
                  <AccordionTrigger>General Information</AccordionTrigger>
                  <AccordionContent>
                    <GeneralInformation />
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="organize">
                  <AccordionTrigger>Organize</AccordionTrigger>
                  <AccordionContent>
                    <Organize />
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  )
}

export default GeneralInformationCard
