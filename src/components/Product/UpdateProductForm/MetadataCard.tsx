import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>ooter, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import {
  DialogHeader,
  Dialog,
  DialogContent,
  DialogTitle,
  DialogClose,
  DialogDescription
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Product } from '@/types/Product'
import { cn } from '@/lib/utils'
import { UpdateProductInput } from '@/types/CreateProduct'
import { useForm } from 'react-hook-form'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Icons } from '@/components/icons'
import ProductService from '@/network/services/product'
import { toast } from '@/components/ui/use-toast'
import React, { FC, PropsWithChildren, useEffect, useState } from 'react'
import { mutate } from 'swr'
import { MoreHorizontal, PlusIcon, SettingsIcon } from 'lucide-react'
import { Input } from '@/components/ui/input'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger
} from '@/components/ui/alert-dialog'
import { snake } from 'radash'

const Title: FC<PropsWithChildren> = ({ children }) => {
  return <div className="text-sm font-muted-foreground">{children}</div>
}

const Content: FC<PropsWithChildren> = ({ children }) => {
  return <div className="text-sm text-right font-medium">{children}</div>
}

const MetadataCard: FC<{ product: Product }> = ({ product }) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [newKey, setNewKey] = useState<string>('')
  const form = useForm<UpdateProductInput>({
    shouldUseNativeValidation: false,
    defaultValues: {
      metadata: product.metadata
    }
  })

  const metadata = form.watch('metadata')

  useEffect(() => {
    if (isDialogOpen || product) {
      form.reset({
        metadata: product.metadata
      })
    }
  }, [isDialogOpen, product])

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      console.log(values)
      await ProductService.updateProduct(product.id, values)

      setIsDialogOpen(false)
      mutate(
        (key) => typeof key === 'string' && key.startsWith(ProductService.getProduct(product.id))
      )
      toast({
        title: 'Product metadata updated',
        variant: 'success'
      })
    } catch (error) {
      console.error(error)
      form.setError('root', {})
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <CardTitle>Metadata</CardTitle>
          <div className="flex flex-row space-x-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" forceMount>
                <DropdownMenuGroup>
                  <DropdownMenuItem
                    onClick={async (event) => {
                      event.stopPropagation()
                      setIsDialogOpen(true)
                    }}
                    className="space-x-2"
                  >
                    <SettingsIcon size="16" />
                    <span>Edit</span>
                  </DropdownMenuItem>
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-2">
            {product.metadata &&
              Object.keys(product.metadata).length > 0 &&
              Object.keys(product.metadata).map((key) => (
                <React.Fragment key={key}>
                  <Title>{key}</Title>
                  <Content>{product.metadata![key]}</Content>
                </React.Fragment>
              ))}
          </div>
        </CardContent>
        <CardFooter></CardFooter>
      </Card>

      <Dialog
        open={isDialogOpen}
        onOpenChange={(isOpen) => {
          setIsDialogOpen(isOpen)
        }}
      >
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle className="flex justify-between items-center">
              Edit Metadata
              <div className="flex space-x-2">
                <DialogClose asChild>
                  <Button variant="outline">Cancel</Button>
                </DialogClose>
                <Button
                  type="submit"
                  form="update-product-form"
                  disabled={form.formState.isSubmitting}
                  onClick={() => {
                    form.clearErrors()
                  }}
                >
                  {form.formState.isSubmitting && (
                    <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  Save
                </Button>
              </div>
            </DialogTitle>
            <DialogDescription>Leave the value empty will remove the metadata key.</DialogDescription>
          </DialogHeader>
          <Form {...form}>
            <form
              id="update-product-form"
              onSubmit={onSubmit}
              className={cn('flex flex-col space-y-8')}
            >
              {metadata &&
                (Object.keys(metadata)?.length ?? 0) > 0 &&
                Object.keys(metadata).map((key) => {
                  return (
                    <FormField
                      key={key}
                      control={form.control}
                      name={`metadata.${key}`}
                      //   rules={{ required: 'Please enter the value' }}
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>{key}</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )
                })}
              <AlertDialog key="add-metadata-key">
                <AlertDialogTrigger
                  asChild
                  onClick={(e) => e.stopPropagation()}
                  className="flex space-x-2 w-full"
                >
                  <Button>
                    <PlusIcon size="16" /> Add Metadata
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>New Key</AlertDialogTitle>
                  </AlertDialogHeader>
                  <Input placeholder="new_key" onChange={(e) => setNewKey(e.target.value)} />
                  <AlertDialogFooter>
                    <AlertDialogCancel>Close</AlertDialogCancel>
                    <AlertDialogAction
                      type="button"
                      onClick={() => {
                        if (newKey != '') {
                          const snakeKey = snake(newKey)
                          setNewKey('')
                          const tmp = form.getValues('metadata') ?? {}
                          if (!Object.keys(tmp).includes(snakeKey)) {
                            tmp[snakeKey] = ''
                          }
                          form.setValue('metadata', tmp)
                        }
                      }}
                    >
                      Add
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  )
}

export default MetadataCard
