import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>ooter, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { Product, ProductVariant } from '@/types/Product'
import { FC, useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  // DollarSignIcon,
  // EditIcon,
  MoreHorizontal,
  PlusIcon,
  SettingsIcon,
  // SettingsIcon,
  Trash2Icon
} from 'lucide-react'
import { UpdateProductCreateVariantForm } from '../VariantForm/UpdateProductCreateVariantForm'
import { Badge } from '@/components/ui/badge'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger
} from '@/components/ui/alert-dialog'
import ProductService from '@/network/services/product'
import { mutate } from 'swr'
import { UpdateProductUpdateVariantForm } from '../VariantForm/UpdateProductUpdateVariantForm'
import { UpdateProductOptionForm } from '../VariantForm/UpdateProductOptionForm'
// import { UpdateProductUpdateVariantForm } from '../VariantForm/UpdateProductUpdateVariantForm'

const VariantsCard: FC<{ product: Product }> = ({ product }) => {
  const [isCreateVariantDialogOpen, setIsCreateVariantDialogOpen] = useState(false)
  const [isEditProductrOptionDialogOpen, setIsEditProductOptionDialogOpen] = useState(false)
  const [editVariant, setEditVariant] = useState<ProductVariant | null>(null)

  const deleteProductVariant = async (variantId: string) => {
    try {
      await ProductService.deleteProductVariant(product.id, variantId)
      mutate(
        (key) => typeof key === 'string' && key.startsWith(ProductService.getProduct(product.id))
      )
    } catch (error) {
      console.log(error)
    }
  }

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <CardTitle>Variants</CardTitle>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" forceMount>
              <DropdownMenuGroup>
                <DropdownMenuItem
                  onClick={async (event) => {
                    event.stopPropagation()
                    setIsCreateVariantDialogOpen(true)
                  }}
                  className="space-x-2"
                >
                  <PlusIcon size="16" />
                  <span>Add Variant</span>
                </DropdownMenuItem>
                {/* <DropdownMenuItem
                  onClick={async (event) => {
                    event.stopPropagation()
                  }}
                  className="space-x-2"
                >
                  <DollarSignIcon size="16" />
                  <span>Edit Prices</span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={async (event) => {
                    event.stopPropagation()
                  }}
                  className="space-x-2"
                >
                  <EditIcon size="16" />
                  <span>Edit Variants</span>
                </DropdownMenuItem> */}
                <DropdownMenuItem
                  onClick={async (event) => {
                    event.stopPropagation()
                    setIsEditProductOptionDialogOpen(true)
                  }}
                  className="space-x-2"
                >
                  <SettingsIcon size="16" />
                  <span>Edit Options</span>
                </DropdownMenuItem>
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex flex-row flex-wrap space-x-6">
            {product.options?.map((opt) => {
              return (
                <div key={opt.title} className="flex flex-col space-y-1">
                  <p className="font-medium">{opt.title}</p>
                  <div className="flex flex-row space-x-1">
                    {opt.values?.map((val, index) => {
                      return (
                        <Badge key={index} variant="secondary">
                          {val.value}
                        </Badge>
                      )
                    })}
                  </div>
                </div>
              )
            })}
          </div>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Title</TableHead>
                <TableHead>Price</TableHead>
                <TableHead className="w-12"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {product.variants.map((variant) => {
                const amount = variant.prices.find((price) => !price.price_list_id)?.amount
                return (
                  <TableRow key={variant.id}>
                    <TableCell>{variant.title}</TableCell>
                    <TableCell>{amount ? `MYR ${(amount / 100).toFixed(2)}` : '-'}</TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" forceMount>
                          <DropdownMenuGroup>
                            <DropdownMenuItem
                              onClick={async (event) => {
                                event.stopPropagation()
                                // setIsUpdateVariantDialogOpen(true)
                                setEditVariant(variant)
                              }}
                              className="space-x-2"
                            >
                              <PlusIcon size="16" />
                              <span>Edit Variant</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem className="space-x-2">
                              <AlertDialog>
                                <AlertDialogTrigger
                                  onClick={async (event) => {
                                    event.stopPropagation()
                                  }}
                                  className="flex items-center space-x-2"
                                >
                                  <Trash2Icon size="16" />
                                  <span>Delete Variant</span>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                  <AlertDialogHeader>
                                    <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                                    <AlertDialogDescription>
                                      This action cannot be undone
                                    </AlertDialogDescription>
                                  </AlertDialogHeader>
                                  <AlertDialogFooter>
                                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                                    <AlertDialogAction
                                      onClick={() => {
                                        deleteProductVariant(variant.id)
                                      }}
                                    >
                                      Continue
                                    </AlertDialogAction>
                                  </AlertDialogFooter>
                                </AlertDialogContent>
                              </AlertDialog>
                            </DropdownMenuItem>
                          </DropdownMenuGroup>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
          </Table>
        </CardContent>
        <CardFooter></CardFooter>
      </Card>

      <UpdateProductCreateVariantForm
        {...{
          options: product.options,
          isDialogOpen: isCreateVariantDialogOpen,
          setIsDialogOpen: setIsCreateVariantDialogOpen,
          product
        }}
      />

      {editVariant && (
        <UpdateProductUpdateVariantForm
          {...{
            options: product.options,
            setEditVariant,
            variant: editVariant,
            product: product
          }}
        />
      )}

      <UpdateProductOptionForm
        {...{
          options: product.options,
          productId: product.id,
          isDialogOpen: isEditProductrOptionDialogOpen,
          setIsDialogOpen: setIsEditProductOptionDialogOpen
        }}
      />
    </>
  )
}

export default VariantsCard
