import * as React from 'react'

import { Product } from '@/types/Product'
import GeneralInformationCard from './GeneralInformationCard'
import ThumbnailCard from './ThumbnailCard'
import MediaCard from './MediaCard'
import VariantsCard from './VariantsCard'
import MetadataCard from './MetadataCard'

interface ProductFormProps extends React.HTMLAttributes<HTMLDivElement> {
  initialValue: Product
}

export function UpdateProductForm({ initialValue }: ProductFormProps) {
  console.log(initialValue)
  // const form = useForm<UpdateProductInput>({
  //   shouldUseNativeValidation: false,
  //   defaultValues: {
  //     title: initialValue?.title,
  //     subtitle: initialValue?.subtitle,
  //     profile_id: initialValue?.profile_id,
  //     description: initialValue?.description,
  //     is_giftcard: initialValue?.is_giftcard,
  //     discountable: initialValue?.discountable,
  //     // images: initialValue?.images.map((image) => image.url),
  //     thumbnail: initialValue?.thumbnail,
  //     handle: initialValue?.handle,
  //     status: initialValue?.status,
  //     collection_id: initialValue?.collection_id,
  //     type: initialValue?.type,
  //     tags: initialValue?.tags,
  //     options: initialValue?.options,
  //     // variants: initialValue?.variants,
  //     // sales_channels: initialValue.sales_channels,
  //     // categories?: initialValue.categories,
  //     weight: initialValue?.weight,
  //     length: initialValue?.length,
  //     height: initialValue?.height,
  //     width: initialValue?.width,
  //     hs_code: initialValue?.hs_code,
  //     origin_country: initialValue?.origin_country,
  //     mid_code: initialValue?.mid_code,
  //     material: initialValue?.material,
  //     // metadata: initialValue?.metadata,
  //     external_id: initialValue?.external_id
  //   }
  // })

  return (
    <div className="grid grid-cols-[4fr_2fr] gap-4">
      <div className="space-y-4">
        <GeneralInformationCard product={initialValue} />
        <VariantsCard product={initialValue} />
      </div>
      <div className="space-y-4">
        <ThumbnailCard product={initialValue} />
        <MediaCard product={initialValue} />
      </div>
      <div className="space-y-4">
        <MetadataCard product={initialValue} />
      </div>
    </div>
  )
}
