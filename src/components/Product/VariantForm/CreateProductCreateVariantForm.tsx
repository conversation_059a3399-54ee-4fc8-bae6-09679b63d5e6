import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from '@/components/ui/accordion'
import { Button } from '@/components/ui/button'
import { DialogClose, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Form } from '@/components/ui/form'
import { cn } from '@/lib/utils'
import { CreateProductInput, CreateProductProductVariantInput } from '@/types/CreateProduct'
import xor from 'lodash.xor'
import { isEmpty } from 'radash'
import { useForm, useFormContext } from 'react-hook-form'
import { OptionValue } from '../CreateProductForm/Variants'
import CreateProductVariantGeneralInformation from './CreateProductVariantGeneralInformation'
import VariantPricing from './VariantPricing'

interface CreateProductCreateVariantFormProps extends React.HTMLAttributes<HTMLDivElement> {
  initialValues?: CreateProductProductVariantInput
  optionValues: OptionValue[]
  index: number
  setIsDialogOpen: (value: boolean) => void
}

export function CreateProductCreateVariantForm({
  className,
  initialValues,
  optionValues,
  index,
  setIsDialogOpen
}: CreateProductCreateVariantFormProps) {
  const parentForm = useFormContext<CreateProductInput>()
  const form = useForm<CreateProductProductVariantInput>({
    defaultValues: { ...initialValues }
  })

  const onSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    // this part is for stopping parent forms to trigger their submit
    if (event) {
      // sometimes not true, e.g. React Native
      if (typeof event.preventDefault === 'function') {
        event.preventDefault()
      }
      if (typeof event.stopPropagation === 'function') {
        // prevent any outer forms from receiving the event too
        event.stopPropagation()
      }
    }

    return form.handleSubmit(async (values) => {
      if (initialValues) {
        console.log('update variant', values)
      } else {
        console.log('create variant', values)
      }

      values?.options?.forEach((opt, index) => {
        if (!opt.value || isEmpty(opt.value)) {
          form.setError(`options.${index}.value`, {
            message: 'Please select a valid variant value'
          })

          throw Error('Please select a valid variant value')
        }
      })

      // check for unique combination of option values
      const otherVariants = parentForm.getValues('variants')
      const alreadyExist = otherVariants?.some((variant, i) => {
        return (
          variant.options?.length === values.options?.length &&
          // only check the variant with different index when edit variant
          index !== i &&
          xor(
            variant.options?.map((o) => o.value.toLowerCase()),
            values.options?.map((o) => o.value.toLowerCase())
          ).length == 0
        )
      })

      if (alreadyExist) {
        form.setError(`options.99.value`, {
          message: 'A variant with these options already exists'
        })

        return
      }

      // set title to variant name if custom title is null
      if (isEmpty(values.title)) {
        values.title = values.options?.map((option) => option.value).join(' / ') ?? ''
      }

      // convert prices to number
      values.prices?.forEach((price, index) => {
        if (typeof price.amount == 'string') {
          values.prices![index].amount = parseFloat(price.amount)
        }
      })

      form.reset()

      // send final value to parent form
      parentForm.setValue(`variants.${index}`, { ...values })

      setIsDialogOpen(false)
    })(event)
  }

  return (
    <>
      <DialogContent className="max-w-3xl" onInteractOutside={(e) => e.preventDefault()}>
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            New Variant
            <div className="flex space-x-2">
              <DialogClose>
                <Button variant="outline">Cancel</Button>
              </DialogClose>
              <Button
                type="submit"
                form="create-variant-form"
                // disabled={form.formState.isSubmitting}
                onClick={() => {
                  form.clearErrors()
                }}
              >
                Save
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form
            id="create-variant-form"
            onSubmit={onSubmit}
            className={cn('flex flex-col space-y-8', className)}
          >
            <Accordion
              type="multiple"
              className="w-full"
              defaultValue={['general-information', 'pricing']}
            >
              <AccordionItem disabled value="general-information">
                <AccordionTrigger>General Information</AccordionTrigger>
                <AccordionContent forceMount>
                  <CreateProductVariantGeneralInformation {...{ optionValues }} />
                </AccordionContent>
              </AccordionItem>

              <AccordionItem disabled value="pricing">
                <AccordionTrigger>Pricing</AccordionTrigger>
                <AccordionContent forceMount>
                  <VariantPricing />
                </AccordionContent>
              </AccordionItem>

              {/* <AccordionItem value="stock-inventory">
                <AccordionTrigger>Stock & Inventory</AccordionTrigger>
                <AccordionContent></AccordionContent>
              </AccordionItem> */}

              {/* <AccordionItem value="shipping">
                <AccordionTrigger>Shipping</AccordionTrigger>
                <AccordionContent></AccordionContent>
              </AccordionItem> */}
            </Accordion>
          </form>
        </Form>
      </DialogContent>
    </>
  )
}
