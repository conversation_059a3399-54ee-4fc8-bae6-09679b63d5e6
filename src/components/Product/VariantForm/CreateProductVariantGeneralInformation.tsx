import { Input } from '@/components/ui/input'
import {
  FormControl,
  // FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import { useFormContext } from 'react-hook-form'
import { CreateProductProductVariantInput } from '@/types/CreateProduct'
import { FC } from 'react'
import { OptionValue } from '../CreateProductForm/Variants'
import { ErrorMessage } from '@hookform/error-message'
import { cn } from '@/lib/utils'

const CreateProductVariantGeneralInformation: FC<{ optionValues: OptionValue[] }> = ({ optionValues }) => {
  const form = useFormContext<CreateProductProductVariantInput>()

  return (
    <div className="flex flex-col space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="title"
          // rules={{ required: 'Please enter the title' }}
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Custom Title</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        {optionValues.map((optionValue, index) => {
          if (!optionValue) {
            return <></>
          }

          return (
            <FormField
              key={optionValue.option}
              control={form.control}
              name={`options.${index}.value`}
              // rules={{ required: 'Please enter the title' }}
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>{optionValue.option}</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose an option" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {optionValue.values.map((value) => {
                        return (
                          <SelectItem key={value} value={value}>
                            {value}
                          </SelectItem>
                        )
                      })}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          )
        })}
      </div>

      <ErrorMessage
        errors={form.formState.errors}
        name="options.99.value"
        render={({ message }) => {
          return <p className={cn('text-[0.8rem] font-medium text-destructive')}>{message}</p>
        }}
      />
    </div>
  )
}

export default CreateProductVariantGeneralInformation
