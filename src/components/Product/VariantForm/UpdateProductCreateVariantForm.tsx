import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from '@/components/ui/accordion'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import { Form } from '@/components/ui/form'
import { cn } from '@/lib/utils'
import ProductService from '@/network/services/product'
import { UpdateProductProductVariantDTO } from '@/types/CreateProduct'
import { Product, ProductOption } from '@/types/Product'
import xor from 'lodash.xor'
import { isEmpty } from 'radash'
import { useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { mutate } from 'swr'
import UpdateProductVariantGeneralInformation from './UpdateProductVariantGeneralInformation'
import VariantPricing from './VariantPricing'

interface UpdateProductCreateVariantFormProps extends React.HTMLAttributes<HTMLDivElement> {
  product: Product
  options: ProductOption[]
  isDialogOpen: boolean
  setIsDialogOpen: (value: boolean) => void
}

export function UpdateProductCreateVariantForm({
  className,
  product,
  options,
  isDialogOpen,
  setIsDialogOpen
}: UpdateProductCreateVariantFormProps) {
  const form = useForm<UpdateProductProductVariantDTO>()

  useEffect(() => {
    if (isDialogOpen) {
      form.reset()
    }
  }, [isDialogOpen])
  const onSubmit = form.handleSubmit(async (values) => {
    values?.options?.forEach((opt, index) => {
      if (!opt.value || isEmpty(opt.value)) {
        form.setError(`options.${index}.value`, {
          message: 'Please select a valid variant value'
        })

        throw Error('Please select a valid variant value')
      }
    })

    // check for unique combination of option values
    const otherVariants = product.variants
    const alreadyExist = otherVariants?.some((variant) => {
      return (
        variant.options?.length === values.options?.length &&
        xor(
          variant.options?.map((o) => o.value.toLowerCase()),
          values.options?.map((o) => o.value.toLowerCase())
        ).length == 0
      )
    })

    if (alreadyExist) {
      form.setError(`options.99.value`, {
        message: 'A variant with these options already exists'
      })

      return
    }

    // set title to variant name if custom title is null
    if (isEmpty(values.title)) {
      values.title = values.options?.map((option) => option.value).join(' / ') ?? ''
    }

    // convert prices to number
    values.prices?.forEach((price, index) => {
      if (typeof price.amount == 'string') {
        values.prices![index].amount = parseFloat(price.amount)
      }
    })

    try {
      // TODO: option values repeated
      await ProductService.createProductVariant(product.id, values)
      setIsDialogOpen(false)
      mutate(
        (key) => typeof key === 'string' && key.startsWith(ProductService.getProduct(product.id))
      )
    } catch (error) {
      console.log(error)
    }
  })

  return (
    <Dialog
      open={isDialogOpen}
      onOpenChange={(isOpen) => {
        setIsDialogOpen(isOpen)
      }}
    >
      <DialogContent className="max-w-3xl" onInteractOutside={(e) => e.preventDefault()}>
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            New Variant
            <div className="flex space-x-2">
              <DialogClose asChild>
                <Button variant="outline">Cancel</Button>
              </DialogClose>
              <Button
                type="submit"
                form="update-product-create-variant-form"
                // disabled={form.formState.isSubmitting}
                onClick={() => {
                  form.clearErrors()
                }}
              >
                Save
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form
            id="update-product-create-variant-form"
            onSubmit={onSubmit}
            className={cn('flex flex-col space-y-8', className)}
          >
            <Accordion
              type="multiple"
              className="w-full"
              defaultValue={['general-information', 'pricing']}
            >
              <AccordionItem disabled value="general-information">
                <AccordionTrigger>General Information</AccordionTrigger>
                <AccordionContent forceMount>
                  <UpdateProductVariantGeneralInformation {...{ options }} />
                </AccordionContent>
              </AccordionItem>

              <AccordionItem disabled value="pricing">
                <AccordionTrigger>Pricing</AccordionTrigger>
                <AccordionContent forceMount>
                  <VariantPricing />
                </AccordionContent>
              </AccordionItem>

              {/* <AccordionItem value="stock-inventory">
                  <AccordionTrigger>Stock & Inventory</AccordionTrigger>
                  <AccordionContent></AccordionContent>
                </AccordionItem> */}

              {/* <AccordionItem value="shipping">
                  <AccordionTrigger>Shipping</AccordionTrigger>
                  <AccordionContent></AccordionContent>
                </AccordionItem> */}
            </Accordion>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
