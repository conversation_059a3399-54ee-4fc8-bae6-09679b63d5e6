import { Button } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  DialogClose,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { toast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import ProductService from '@/network/services/product'
import ProductOptionService from '@/network/services/product_option'
import { UpdateProductProductOptions } from '@/types/CreateProduct'
import { ProductOption } from '@/types/Product'
import { PlusIcon, Trash2Icon } from 'lucide-react'
import { capitalize } from 'radash'
import React, { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { mutate } from 'swr'

interface UpdateProductOptionFormProps extends React.HTMLAttributes<HTMLDivElement> {
  options: ProductOption[]
  productId: string
  isDialogOpen: boolean
  setIsDialogOpen: (value: boolean) => void
}

export function UpdateProductOptionForm({
  className,
  options,
  productId,
  isDialogOpen,
  setIsDialogOpen
}: UpdateProductOptionFormProps) {
  const [optionsToRemove, setOptionsToRemove] = useState<{ id: string; title: string }[]>([])
  const form = useForm<UpdateProductProductOptions>({
    defaultValues: {
      options:
        options.map((option) => {
          return {
            id: option.id,
            title: option.title
          }
        }) ?? []
    }
  })

  const formOptions = form.watch('options')

  useEffect(() => {
    if (isDialogOpen || options) {
      form.reset({
        options:
          options.map((option) => {
            return {
              id: option.id,
              title: option.title
            }
          }) ?? []
      })
    }
  }, [isDialogOpen, options])

  const onSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    if (event) {
      // sometimes not true, e.g. React Native
      if (typeof event.preventDefault === 'function') {
        event.preventDefault()
      }
      if (typeof event.stopPropagation === 'function') {
        // prevent any outer forms from receiving the event too
        event.stopPropagation()
      }
    }

    return form.handleSubmit(async (values) => {
      try {
        console.log(values)
        // to remove options
        for (const option of optionsToRemove) {
          await ProductOptionService.deleteProductOption(option.id, productId)
        }

        // to create/update options
        for (const option of values.options) {
          if (option.id) {
            // update
            await ProductOptionService.updateProductOption(option.id, productId, {
              title: option.title
            })
          } else {
            // create
            await ProductOptionService.addProductOption(productId, { title: option.title })
          }
        }

        setIsDialogOpen(false)
        mutate(
          (key) => typeof key === 'string' && key.startsWith(ProductService.getProduct(productId))
        )

        toast({
          title: 'Product options updated',
          variant: 'success'
        })
      } catch (e) {
        console.log(e)
        form.setError('root', {})
        toast({
          title: 'Action failed, please try again',
          variant: 'destructive'
        })
      }
    })(event)
  }

  const addOption = () => {
    const tmp = [...formOptions]
    tmp.push({ title: '' })
    form.setValue('options', tmp)
  }

  const removeOption = (index: number) => {
    const tmp = [...formOptions]
    const items = tmp.splice(index, 1)
    for (const item of items) {
      if (item.id) {
        setOptionsToRemove([...optionsToRemove, item as any])
      }
    }
    form.setValue('options', tmp)
  }

  return (
    <Dialog
      open={isDialogOpen}
      onOpenChange={(isOpen) => {
        setIsDialogOpen(isOpen)
      }}
    >
      <DialogContent className="max-w-3xl" onInteractOutside={(e) => e.preventDefault()}>
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            Edit Options
            <div className="flex space-x-2">
              <DialogClose asChild>
                <Button variant="outline">Cancel</Button>
              </DialogClose>
              <Button
                type="submit"
                form="edit-product-options-form"
                // disabled={form.formState.isSubmitting}
                onClick={() => {
                  form.clearErrors()
                }}
              >
                Save
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form
            id="edit-product-options-form"
            onSubmit={onSubmit}
            className={cn('flex flex-col space-y-6', className)}
          >
            <Label>Product Options</Label>
            <Label className="text-xs text-gray-500">Option title</Label>
            <div className="flex flex-col space-y-2">
              {formOptions.map((option, index) => {
                return (
                  <FormField
                    key={`${index}-${option.id}`}
                    control={form.control}
                    name={`options.${index}.title`}
                    rules={{ required: 'Title cannot be empty' }}
                    render={({ field }) => {
                      field.onChange = (e) => form.setValue(field.name, capitalize(e.target.value))
                      field.onBlur = field.onChange

                      return (
                        <FormItem key={index} className="flex flex-col">
                          {/* <FormLabel>Custom Title</FormLabel> */}
                          <div key={index} className="grid grid-cols-[1fr_50px] gap-2">
                            <FormControl>
                              <Input className="bg-gray-100" placeholder="Color" {...field} />
                            </FormControl>
                            <Button
                              variant="outline"
                              className="h-full text-gray-500"
                              onClick={(e) => {
                                e.preventDefault()
                                removeOption(index)
                              }}
                            >
                              <Trash2Icon />
                            </Button>
                          </div>
                          <FormMessage />
                        </FormItem>
                      )
                    }}
                  />
                )
              })}

              <Button
                variant="outline"
                onClick={(e) => {
                  e.preventDefault()
                  addOption()
                }}
              >
                <PlusIcon /> Add an option
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
