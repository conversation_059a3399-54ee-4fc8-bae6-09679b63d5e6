import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from '@/components/ui/accordion'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import { Form } from '@/components/ui/form'
import { toast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import ProductService from '@/network/services/product'
import { UpdateProductProductVariantDTO } from '@/types/CreateProduct'
import { Product, ProductOption, ProductVariant } from '@/types/Product'
import xor from 'lodash.xor'
import { isEmpty } from 'radash'
import { useForm } from 'react-hook-form'
import { mutate } from 'swr'
import UpdateProductVariantGeneralInformation from './UpdateProductVariantGeneralInformation'
import VariantPricing from './VariantPricing'

interface UpdateProductUpdateVariantFormProps extends React.HTMLAttributes<HTMLDivElement> {
  variant: ProductVariant
  options: ProductOption[]
  product: Product
  setEditVariant: (value: ProductVariant | null) => void
}

export function UpdateProductUpdateVariantForm({
  className,
  variant,
  options,
  product,
  setEditVariant
}: UpdateProductUpdateVariantFormProps) {
  const price = variant.prices.find((price) => !price.price_list_id)
  const form = useForm<UpdateProductProductVariantDTO>({
    defaultValues: {
      title: variant.title,
      prices: [
        {
          id: price?.id ?? undefined,
          region_id: price?.region_id ?? undefined,
          currency_code: price?.currency_code ?? 'myr',
          amount: price?.amount,
          min_quantity: price?.min_quantity ?? undefined,
          max_quantity: price?.max_quantity ?? undefined
        }
      ],
      options: variant.options.map((opt) => {
        return {
          value: opt.value,
          option_id: opt.option_id
        }
      })
    }
  })

  const onSubmit = form.handleSubmit(async (values) => {
    console.log(values)
    // check for unique combination of option values
    const otherVariants = product.variants.filter((otherVariant) => otherVariant.id != variant.id)
    const alreadyExist = otherVariants?.some((variant) => {
      return (
        variant.options?.length === values.options?.length &&
        xor(
          variant.options?.map((o) => o.value.toLowerCase()),
          values.options?.map((o) => o.value.toLowerCase())
        ).length == 0
      )
    })

    if (alreadyExist) {
      form.setError(`options.99.value`, {
        message: 'A variant with these options already exists'
      })

      return
    }

    // set title to variant name if custom title is null
    if (isEmpty(values.title)) {
      values.title = values.options?.map((option) => option.value).join(' / ') ?? ''
    }

    // convert prices to number
    values.prices?.forEach((price, index) => {
      if (typeof price.amount == 'string') {
        values.prices![index].amount = parseFloat(price.amount)
      }
    })

    try {
      await ProductService.updateProductVariant(variant.product_id, variant.id, values)
      setEditVariant(null)
      // form.reset()
      mutate(
        (key) =>
          typeof key === 'string' && key.startsWith(ProductService.getProduct(variant.product_id))
      )

      toast({
        title: 'Product variant updated',
        variant: 'success'
      })
    } catch (error) {
      console.log(error)
      form.setError('root', {})
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <Dialog
      open={variant ? true : false}
      onOpenChange={(isOpen) => {
        if (!isOpen) {
          setEditVariant(null)
        }
      }}
    >
      <DialogContent className="max-w-3xl" onInteractOutside={(e) => e.preventDefault()}>
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            Update Variant
            <div className="flex space-x-2">
              <DialogClose asChild>
                <Button variant="outline">Cancel</Button>
              </DialogClose>
              <Button
                type="submit"
                form="update-product-create-variant-form"
                // disabled={form.formState.isSubmitting}
                onClick={() => {
                  form.clearErrors()
                }}
              >
                Save
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form
            id="update-product-create-variant-form"
            onSubmit={onSubmit}
            className={cn('flex flex-col space-y-8', className)}
          >
            <Accordion
              type="multiple"
              className="w-full"
              defaultValue={['general-information', 'pricing']}
            >
              <AccordionItem disabled value="general-information">
                <AccordionTrigger>General Information</AccordionTrigger>
                <AccordionContent forceMount>
                  <UpdateProductVariantGeneralInformation {...{ options }} />
                </AccordionContent>
              </AccordionItem>

              <AccordionItem disabled value="pricing">
                <AccordionTrigger>Pricing</AccordionTrigger>
                <AccordionContent forceMount>
                  <VariantPricing />
                </AccordionContent>
              </AccordionItem>

              {/* <AccordionItem value="stock-inventory">
                  <AccordionTrigger>Stock & Inventory</AccordionTrigger>
                  <AccordionContent></AccordionContent>
                </AccordionItem> */}

              {/* <AccordionItem value="shipping">
                  <AccordionTrigger>Shipping</AccordionTrigger>
                  <AccordionContent></AccordionContent>
                </AccordionItem> */}
            </Accordion>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
