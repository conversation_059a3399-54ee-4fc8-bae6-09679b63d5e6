import { Input } from '@/components/ui/input'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { useFormContext } from 'react-hook-form'
import { UpdateProductProductVariantDTO } from '@/types/CreateProduct'
import { FC } from 'react'
import { ErrorMessage } from '@hookform/error-message'
import { cn } from '@/lib/utils'
import { ProductOption } from '@/types/Product'

const UpdateProductVariantGeneralInformation: FC<{ options: ProductOption[] }> = ({ options }) => {
  const form = useFormContext<UpdateProductProductVariantDTO>()

  return (
    <div className="flex flex-col space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="title"
          // rules={{ required: 'Please enter the title' }}
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Custom Title</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        {options.map((opt, index) => {
          return (
            <div key={opt.id}>
              <FormField
                control={form.control}
                name={`options.${index}.option_id`}
                defaultValue={opt.id}
                // rules={{ required: 'Please enter the title' }}
                render={({ field }) => (
                  <FormItem className="hidden">
                    <Input {...field} />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name={`options.${index}.value`}
                rules={{ required: `Option value for ${opt.title} is required` }}
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>{opt.title}*</FormLabel>
                    <Input {...field} />
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          )
        })}
      </div>

      <ErrorMessage
        errors={form.formState.errors}
        name="options.99.value"
        render={({ message }) => {
          return <p className={cn('text-[0.8rem] font-medium text-destructive')}>{message}</p>
        }}
      />
    </div>
  )
}

export default UpdateProductVariantGeneralInformation
