import { Input } from '@/components/ui/input'
import {
  FormControl,
  // FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { useFormContext } from 'react-hook-form'
import { CreateProductProductVariantInput } from '@/types/CreateProduct'

const VariantPricing = () => {
  const form = useFormContext<CreateProductProductVariantInput>()

  return (
    <div className="flex flex-col space-y-4">
      <p>* Set Price in sen, E.g. MYR 10.50 = 1050 sen.</p>
      <div className="grid grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="prices.0.currency_code"
          defaultValue="myr"
          rules={{ required: 'Please enter the currency' }}
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Currency</FormLabel>
              <FormControl>
                <Input {...field} readOnly disabled />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="prices.0.amount"
          rules={{ required: 'Please enter the amount' }}
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Amount*</FormLabel>
              <FormControl>
                <Input type="number" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* <FormField
          control={form.control}
          name="prices.0.min_quantity"
          rules={{ required: 'Please enter the amount' }}
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Min Quantity</FormLabel>
              <FormControl>
                <Input type="number" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        /> */}
      </div>
    </div>
  )
}

export default VariantPricing
