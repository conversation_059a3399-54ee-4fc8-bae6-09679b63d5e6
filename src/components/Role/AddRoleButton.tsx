import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import {
  Sheet,
  Sheet<PERSON>lose,
  <PERSON>et<PERSON>ontent,
  <PERSON>etHeader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger
} from '@/components/ui/sheet'
import { Form } from '@/components/ui/form'
import { PlusCircleIcon } from 'lucide-react'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { CreateRoleForm } from './CreateRoleForm'
import { CreateRoleInput } from '@/types/Role'

const AddRoleButton = () => {
  const [isSheetOpen, setIsSheetOpen] = useState(false)
  const form = useForm<CreateRoleInput>({
    shouldUseNativeValidation: false
  })

  return (
    <div className="flex justify-end">
      <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
        <SheetTrigger asChild>
          <Button>
            <PlusCircleIcon size="16" className="mr-2" />
            Create Role
          </Button>
        </SheetTrigger>
        <SheetContent side="bottom" className="w-full h-full">
          <SheetHeader className="max-w-4xl mx-auto">
            <SheetTitle className="flex justify-between items-center">
              New Role
              <div className="flex space-x-2 ">
                <SheetClose asChild>
                  <Button
                    variant="outline"
                    onClick={() => {
                      form.clearErrors()
                    }}
                  >
                    Cancel
                  </Button>
                </SheetClose>
                <Button type="submit" form="create-role-form">
                  {form.formState.isSubmitting && (
                    <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  Create
                </Button>
              </div>
            </SheetTitle>
          </SheetHeader>
          <Form {...form}>
            <CreateRoleForm {...{ setIsSheetOpen }} />
          </Form>
        </SheetContent>
      </Sheet>
    </div>
  )
}

export default AddRoleButton
