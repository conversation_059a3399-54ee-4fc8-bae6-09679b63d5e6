/* eslint-disable @typescript-eslint/ban-ts-comment */
import * as React from 'react'
import { useFormContext } from 'react-hook-form'
import { mutate } from 'swr'

import { cn } from '@/lib/utils'
import {
  FormControl,
  // FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { toast } from '@/components/ui/use-toast'
import { Input } from '@/components/ui/input'
import { CreateRoleInput } from '@/types/Role'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select'
import { capitalize } from 'radash'
import AuthService, { LicenseRoles } from '@/network/services/auth'

interface RoleFormProps extends React.HTMLAttributes<HTMLDivElement> {
  setIsSheetOpen: (value: boolean) => void
}

export function CreateRoleForm({ className, setIsSheetOpen }: RoleFormProps) {
  const form = useFormContext<CreateRoleInput>()

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      await AuthService.activateAdmin(values)
      mutate((key) => typeof key === 'string' && key.startsWith(AuthService.findLicenseUsers))
      toast({
        title: 'New role created',
        variant: 'success'
      })
      form.reset()
      setIsSheetOpen(false)
    } catch (error) {
      console.error(error)

      //   if (isAxiosError(error) && error.response?.data.type == 'duplicate_error') {
      //     // likely is handle duplicated
      //     if (error.response.data.message.startsWith('Product_category with handle')) {
      //       form.setError('handle', {
      //         message: error.response.data.message.replace('_', ' ')
      //       })
      //     }
      //   } else {
      //     form.setError('root', {})
      //   }

      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <form
      id="create-role-form"
      onSubmit={onSubmit}
      className={cn('flex flex-col space-y-8 max-w-4xl mx-auto', className)}
    >
      <div className="grid grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="email"
          rules={{ required: 'Please enter the email' }}
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="group"
          rules={{ required: 'Please select a role' }}
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Role</FormLabel>
              <FormControl>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose an option" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {[
                      LicenseRoles.SALES,
                      LicenseRoles.FINANCE,
                      LicenseRoles.MARKETING,
                      LicenseRoles.CREATIVE
                    ].map((value) => {
                      return (
                        <SelectItem key={value} value={value}>
                          {capitalize(value)}
                        </SelectItem>
                      )
                    })}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="password"
          rules={{ required: 'Please enter the password' }}
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Password</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </form>
  )
}
