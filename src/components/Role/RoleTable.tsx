import { ColumnDef, Row, createColumnHelper } from '@tanstack/react-table'
import { DataTable, FilterColumn } from '@/components/Table/DataTable'
import { FC } from 'react'
import { IDataResponse, serialize } from '@/network/request'
import AuthService, { LicenseUser } from '@/network/services/auth'
import { useSWRConfig } from 'swr'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu'
import { Button } from '../ui/button'
import { MonitorCheckIcon, MoreHorizontal, XCircle } from 'lucide-react'
import { toast } from '../ui/use-toast'

const columnHelper = createColumnHelper<LicenseUser>()
const columns: ColumnDef<LicenseUser>[] = [
  // {
  //   accessorKey: 'id',
  //   header: 'Id'
  // },
  {
    accessorKey: 'email',
    header: 'Email'
  },
  {
    accessorKey: 'role',
    header: 'Role'
  },
  {
    accessorKey: 'blocked',
    header: 'Blocked'
  },
  columnHelper.display({
    id: 'actions',
    cell: (props) => <RowActions row={props.row} />
  })
]

const RowActions: FC<{ row: Row<LicenseUser> }> = ({ row }) => {
  const { mutate } = useSWRConfig()
  const user = row.original

  const updateStatus = async (user: LicenseUser) => {
    try {
      const { data } = await AuthService.updateLicenseUser(user.id, { blocked: !user.blocked })

      mutate((key) => typeof key === 'string' && key.startsWith(AuthService.findLicenseUsers))

      if (data.success) {
        toast({
          title: `${user.email} ${!user.blocked ? 'Deactivated' : 'Activated'}`,
          variant: 'success'
        })
        return
      }

      toast({
        title: `Action failed, please try again`,
        variant: 'destructive'
      })
    } catch (error) {
      toast({
        title: `Action failed, please try again`,
        variant: 'destructive'
      })
      console.log(error)
    }
  }

  return (
    <div className="w-full flex justify-end">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0" onClick={(e) => e.stopPropagation()}>
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem
            // disabled={row.original.blocked == true}
            onClick={async (e) => {
              e.stopPropagation()
              updateStatus(user)
            }}
            className="space-x-2"
          >
            {row.original.blocked ? (
              <>
                <MonitorCheckIcon size="16" />
                <span>Activate</span>
              </>
            ) : (
              <>
                <XCircle size="16" />
                <span>Deactivate</span>
              </>
            )}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}

const columnFilter: FilterColumn[] = []

const RoleTable = () => {
  //   const nav = useNavigate()

  return (
    <DataTable<LicenseUser, unknown, IDataResponse<LicenseUser>>
      columns={columns}
      filterColumns={columnFilter}
      swrService={serialize(AuthService.findLicenseUsers, {
        'role[0]': 'sales',
        'role[1]': 'finance',
        'role[2]': 'marketing',
        'role[3]': 'creative'
      })}
      pageParam="offset"
      limitParam="limit"
      toRow={AuthService.toRow}
      toPaginate={AuthService.toPaginate}
      //   onRowClick={(row: Row<LicenseUser>) => {
      //     nav(`/roles/${row.original.id}`)
      //   }}
    />
  )
}

export default RoleTable
