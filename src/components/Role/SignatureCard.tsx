import { Dropzone } from '@/components/Form/Dropzone'
import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { useToast } from '@/components/ui/use-toast'
import FileService from '@/network/services/file'
import { Image } from '@unpic/react'
import { FC, useEffect } from 'react'
import { FileWithPath } from 'react-dropzone'
import { useForm } from 'react-hook-form'
import useSWR, { mutate } from 'swr'
import { Input } from '@/components/ui/input'
import { Signature, UpdateSignatureInput } from '@/types/Signature'
import SignatureService from '@/network/services/signature'

const SignatureCard = () => {
  return (
    <>
      <SignatureView />
    </>
  )
}

const SignatureView = () => {
  const { data, isLoading, error } = useSWR(SignatureService.findSignature)

  if (error) {
    return <></>
  }

  if (isLoading) {
    return <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
  }

  const signature = data.data

  return <SignatureForm initialValue={signature} />
}

const SignatureForm: FC<{ initialValue?: Signature }> = ({ initialValue }) => {
  const { toast } = useToast()
  const form = useForm<UpdateSignatureInput>({
    shouldUseNativeValidation: false,
    defaultValues: {
      ...initialValue
    }
  })

  const signature = form.watch('signature_url')
  const signatureFile = form.watch('signature') as (FileWithPath & { preview: string }) | undefined

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      if (values.signature) {
        const { data: uploadResponse } = await FileService.upload([values.signature])

        values.signature_url = uploadResponse.uploads[0].url
        delete values.signature
      }

      console.log('submit values: ', values)

      const { data: response } = await SignatureService.createOrUpdateSignature(values)

      if (response.success) {
        toast({
          title: 'Update successfully',
          variant: 'success'
        })

        mutate((key) => typeof key === 'string' && key.startsWith(SignatureService.findSignature))
      }
    } catch (error) {
      console.log(error)
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  useEffect(() => {
    form.reset({
      ...initialValue
    })
  }, [initialValue])

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0">
        <CardTitle>My Signature</CardTitle>
        <Button
          type="submit"
          form="signature-form"
          disabled={form.formState.isSubmitting}
          onClick={() => {
            form.clearErrors()
          }}
          className="gap-1"
        >
          Save
          {form.formState.isSubmitting && <Icons.spinner className="h-4 w-4 animate-spin" />}
        </Button>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form id="signature-form" onSubmit={onSubmit} className="space-y-4">
            <div className="grid gap-3">
              <FormField
                control={form.control}
                name="signature"
                rules={{
                  //   required: 'Please upload your signature',
                  validate: (value) => {
                    if (initialValue?.signature_url || value) {
                      return true
                    }
                    return 'Please upload your signature'
                  }
                }}
                render={({ field }) => (
                  <>
                    <FormItem>
                      <FormLabel>Signature</FormLabel>
                      <FormControl>
                        <Dropzone
                          accept={{ 'image/*': ['.jpeg', '.jpg', '.png'] }}
                          description="Drop your image here, or click to browse"
                          multiple={false}
                          onDrop={(acceptedFiles) => {
                            const file = acceptedFiles[0]
                            const exist = signatureFile?.path === file.path

                            if (!exist) {
                              Object.assign(file, {
                                preview: URL.createObjectURL(file)
                              })
                            }

                            form.setValue('signature', file)
                          }}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>

                    {(signature || signatureFile?.preview) && (
                      <div className="mt-2 flex flex-col space-y-4">
                        <Image
                          src={signatureFile?.preview ?? signature ?? ''}
                          layout="constrained"
                          className="rounded-md"
                          width={320}
                          height={320}
                        />
                      </div>
                    )}
                  </>
                )}
              />
              <FormField
                control={form.control}
                name="full_name"
                rules={{ required: 'Please enter your full name' }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Full Name</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="designation"
                rules={{ required: 'Please enter your designation' }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Designation</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                rules={{ required: 'Please enter your email' }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="phone"
                rules={{ required: 'Please enter your phone number' }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone Number</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}

export default SignatureCard
