import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardContentLabel,
  CardContentTitle,
  CardHeader,
  CardTitle
} from '@/components/ui/card'
import { Dialog } from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import SettingService from '@/network/services/setting'
import Settings, { Freebies } from '@/types/Setting'
import { MoreHorizontal, SettingsIcon } from 'lucide-react'
import { FC, useState } from 'react'
import useSWR from 'swr'
import UpdateFreebiesForm from '../forms/UpdateFreebiesForm'
import { Image } from '@unpic/react'
import ReadOnlyEditorComponent from '@/components/Editor/ReadOnlyEditor'

const FreebiesCard: FC = () => {
  const { data, error, isLoading } = useSWR(SettingService.getSetting('freebies'))
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  if (isLoading || error || !data) {
    return <></>
  }

  const setting = (data?.data as Settings) ?? undefined

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <CardTitle>Freebies</CardTitle>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" forceMount>
              <DropdownMenuGroup>
                <DropdownMenuItem
                  onClick={async (event) => {
                    event.stopPropagation()
                    setIsDialogOpen(true)
                  }}
                  className="space-x-2"
                >
                  <SettingsIcon size="16" />
                  <span>Edit</span>
                </DropdownMenuItem>
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>
        </CardHeader>
        <CardContent>
          <div className="grid gap-y-8">
            <div className="grid grid-cols-2 gap-2 gap-y-2">
              <CardContentTitle>Page Description</CardContentTitle>
              <CardContentLabel>
                <ReadOnlyEditorComponent content={(setting.value as Freebies).description} />
              </CardContentLabel>
              {/* <div className="flex flex-col items-end gap-2">
                {(setting.value as Freebies).description}
              </div> */}
            </div>

            <div className="grid gap-2">
              <CardContentTitle>Primary Banner</CardContentTitle>
              <div className="flex flex-col items-end gap-2">
                {setting.value.gallery?.map((media: any, i: number) => {
                  return media.type.startsWith('image') ? (
                    <Image
                      key={i}
                      src={media.url ?? ''}
                      alt={`freebies-banner-${i + 1}`}
                      height={180}
                      width={180}
                      objectFit="contain"
                      className="rounded-md"
                    />
                  ) : media.type.startsWith('video') ? (
                    <video
                      controls
                      key={i}
                      src={media.url ?? ''}
                      height={180}
                      width={180}
                      className="rounded-md"
                    />
                  ) : (
                    <></>
                  )
                })}
              </div>
            </div>

            {(setting.value as Freebies)?.categories.map((category) => (
              <FreebiesDetail category={category} />
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Update freebies */}
      <Dialog
        open={isDialogOpen}
        onOpenChange={(isOpen) => {
          setIsDialogOpen(isOpen)
        }}
      >
        <UpdateFreebiesForm
          initialValues={setting}
          isDialogOpen={isDialogOpen}
          setIsDialogOpen={setIsDialogOpen}
        />
      </Dialog>
    </>
  )
}

const FreebiesDetail: FC<{
  category: {
    title: string
    image_url: string
  }
}> = ({ category }) => {
  return (
    <div className="grid grid-cols-2 gap-2 gap-y-2 border-t-2 pt-8">
      <CardContentTitle>Title</CardContentTitle>
      <CardContentLabel>{category.title ?? '-'}</CardContentLabel>
      <CardContentTitle>Thumbnail</CardContentTitle>
      <CardContentLabel>
        <div className="flex justify-end">
          <img className="max-w-[180px]" src={category.image_url ?? '-'} alt="brand-thumbnail" />
        </div>
      </CardContentLabel>
    </div>
  )
}

export default FreebiesCard
