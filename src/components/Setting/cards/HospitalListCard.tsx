import { FC, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../../ui/card'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../../ui/dropdown-menu'
import { MoreHorizontal, SettingsIcon, Trash2Icon } from 'lucide-react'
import { Button } from '../../ui/button'
import HospitalService, { Hospital } from '@/network/services/hospital'
import { IDataResponse } from '@/network/request'
import { DataTable, FilterColumn } from '@/components/Table/DataTable'
import { ColumnDef, Row, createColumnHelper } from '@tanstack/react-table'
import { CreateHospitalForm } from '@/components/MediaRoom/Hospital/CreateHospitalForm'
import { UpdateHospitalForm } from '@/components/MediaRoom/Hospital/UpdateHospitalForm'
import { useToast } from '@/components/ui/use-toast'
import { mutate } from 'swr'
import { title } from 'radash'

const HospitalListCard: FC = () => {
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <CardTitle>Hospitals of Delivery List</CardTitle>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" forceMount>
              <DropdownMenuGroup>
                <DropdownMenuItem
                  onClick={async (event) => {
                    event.stopPropagation()
                    setIsDialogOpen(true)
                  }}
                  className="space-x-2"
                >
                  <SettingsIcon size="16" />
                  <span>Create Hospital</span>
                </DropdownMenuItem>
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>
        </CardHeader>
        <CardContent>
          <HospitalTable />
        </CardContent>
      </Card>

      <CreateHospitalForm {...{ isDialogOpen, setIsDialogOpen }} />
    </>
  )
}

function HospitalTable() {
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [hospital, setHospital] = useState<Hospital | null>(null)

  const columnHelper = createColumnHelper<Hospital>()
  const columns: ColumnDef<Hospital>[] = [
    {
      accessorKey: 'title',
      header: 'Title'
    },
    {
      accessorKey: 'type',
      header: 'Type',
      cell: (props) => <span>{title(props.getValue<string>())}</span>
    },
    columnHelper.display({
      id: 'actions',
      cell: (props) => <RowActions row={props.row} />
    })
  ]
  const columnFilter: FilterColumn[] = [
    { columnKey: 'title', header: 'Title', dataType: 'string' },
    { columnKey: 'type', header: 'Type', dataType: 'string' }
  ]

  return (
    <>
      <DataTable<Hospital, unknown, IDataResponse<Hospital>>
        columns={columns}
        filterColumns={columnFilter}
        swrService={HospitalService.getHospitals}
        pageParam="page"
        limitParam="limit"
        sortParam="sort"
        sortColumns={['title', 'type']}
        toRow={HospitalService.toRow}
        toPaginate={HospitalService.toPaginate}
        onRowClick={(row: Row<Hospital>) => {
          setHospital(row.original)
          setIsDialogOpen(true)
        }}
      />

      {hospital && (
        <UpdateHospitalForm
          {...{
            hospital,
            isDialogOpen,
            setIsDialogOpen: (value: boolean) => {
              setIsDialogOpen(value)
              if (value == false) {
                setHospital(null)
              }
            }
          }}
        />
      )}
    </>
  )
}

const RowActions: FC<{ row: Row<Hospital> }> = ({ row }) => {
  const hospitalId = row.original.id
  const { toast } = useToast()

  return (
    <div className="w-full flex justify-end">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem
            onClick={async (event) => {
              event.stopPropagation()

              try {
                await HospitalService.deleteHospital(hospitalId)
                mutate(
                  (key) => typeof key === 'string' && key.startsWith(HospitalService.getHospitals)
                )
                toast({
                  description: 'Hospital deleted',
                  variant: 'destructive'
                })
              } catch (error) {
                console.log(error)
              }
            }}
            className="space-x-2"
          >
            <Trash2Icon size="16" />
            <span>Delete</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}

export default HospitalListCard
