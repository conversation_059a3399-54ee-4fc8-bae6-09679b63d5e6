import React, { FC, useState } from 'react'
import {
  Card,
  CardContent,
  CardContentLabel,
  CardContentTitle,
  CardHeader,
  CardTitle
} from '../../ui/card'
import { Dialog } from '../../ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../../ui/dropdown-menu'
import { MoreHorizontal, SettingsIcon } from 'lucide-react'
import { Button } from '../../ui/button'
import UpdateMemberBenefitsForm from '../forms/UpdateMemberBenefitsForm'
import useSWR from 'swr'
import SettingService from '@/network/services/setting'
import Settings, { MemberBenefit } from '@/types/Setting'
import ReadOnlyEditorComponent from '@/components/Editor/ReadOnlyEditor'

const MemberBenefitsCard: FC = () => {
  const { data, error, isLoading } = useSWR(SettingService.getSetting('member_benefits'))
  const [isDialogO<PERSON>, setIsDialogOpen] = useState(false)

  if (isLoading || error || !data) {
    return <></>
  }

  const setting = (data?.data as Settings) ?? undefined

  if (!setting) {
    return <></>
  }

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <CardTitle>Friend Benefits</CardTitle>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" forceMount>
              <DropdownMenuGroup>
                <DropdownMenuItem
                  onClick={async (event) => {
                    event.stopPropagation()
                    setIsDialogOpen(true)
                  }}
                  className="space-x-2"
                >
                  <SettingsIcon size="16" />
                  <span>Edit</span>
                </DropdownMenuItem>
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-2">
            {(setting.value as MemberBenefit[]).map((benefit, i) => (
              <React.Fragment key={i}>
                {i > 0 && (
                  <>
                    <div className="flex"></div>
                    <div className="flex"></div>
                  </>
                )}
                <CardContentTitle>Title</CardContentTitle>
                <CardContentLabel>{benefit.title ?? '-'}</CardContentLabel>
                <CardContentTitle>Description</CardContentTitle>
                <CardContentLabel>{benefit.description ?? '-'}</CardContentLabel>
                <CardContentTitle>Image</CardContentTitle>
                <CardContentLabel>
                  <div className="flex justify-end">
                    <img
                      className="max-w-[180px]"
                      src={benefit.image_url ?? '-'}
                      alt="brand-thumbnail"
                    />
                  </div>
                </CardContentLabel>
              </React.Fragment>
            ))}
            <CardContentTitle>User Agreements</CardContentTitle>
            <CardContentLabel>
              {setting.metadata?.user_agreements?.map((user_agreement: string) => {
                return <ReadOnlyEditorComponent className="text-right" content={user_agreement} />
              }) ?? '-'}
            </CardContentLabel>
          </div>
        </CardContent>
      </Card>

      {/* Update customer general */}
      <Dialog
        open={isDialogOpen}
        onOpenChange={(isOpen) => {
          setIsDialogOpen(isOpen)
        }}
      >
        <UpdateMemberBenefitsForm
          initialValues={setting}
          isDialogOpen={isDialogOpen}
          setIsDialogOpen={setIsDialogOpen}
        />
      </Dialog>
    </>
  )
}

export default MemberBenefitsCard
