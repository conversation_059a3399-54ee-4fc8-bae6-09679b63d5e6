import EditorComponent from '@/components/Editor/Editor'
import { Dropzone } from '@/components/Form/Dropzone'
import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import { DialogClose, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { toast } from '@/components/ui/use-toast'
import { bytesToSize } from '@/lib/utils'
import FileService from '@/network/services/file'
import SettingService from '@/network/services/setting'
import Settings, { CreateFreebies, Freebies } from '@/types/Setting'
import { Image } from '@unpic/react'
import { cx } from 'class-variance-authority'
import { MoreHorizontal, Trash2Icon } from 'lucide-react'
import { title } from 'radash'
import { FC, useEffect } from 'react'
import { FileWithPath } from 'react-dropzone'
import { useFieldArray, useForm } from 'react-hook-form'
import { mutate } from 'swr'

const SETTING_KEY = 'freebies'

const UpdateFreebiesForm: FC<{
  initialValues?: Settings
  isDialogOpen: boolean
  setIsDialogOpen: (value: boolean) => void
}> = ({ initialValues, isDialogOpen, setIsDialogOpen }) => {
  const form = useForm<CreateFreebies>({
    shouldUseNativeValidation: false,
    defaultValues: {
      value: {
        description: (initialValues?.value as Freebies).description,
        gallery: (initialValues?.value as Freebies).gallery,
        categories: (initialValues?.value as Freebies).categories ?? [{ title: '', image_url: '' }]
      }
    }
  })

  const gallery = form.watch('value.gallery')

  const { fields } = useFieldArray({
    name: 'value.categories',
    control: form.control
  })

  useEffect(() => {
    if (isDialogOpen) {
      form.reset({
        value: {
          description: (initialValues?.value as Freebies).description,
          gallery: (initialValues?.value as Freebies).gallery,
          categories: (initialValues?.value as Freebies).categories ?? [
            { title: '', image_url: '' }
          ]
        }
      })
    }
  }, [isDialogOpen])

  const onSubmit = form.handleSubmit(async (values) => {
    const galleryMedia: File[] = []
    const galleryMediaIndices = []

    for (const index in values.value.gallery) {
      const item = values.value.gallery[index]
      if (!item.media_file) continue
      galleryMedia.push(item.media_file)
      galleryMediaIndices.push(index)
    }

    if (galleryMedia.length) {
      const { data } = await FileService.upload(galleryMedia)
      for (const fileIndex in galleryMediaIndices) {
        const valueIndex = Number(galleryMediaIndices[fileIndex])
        values.value.gallery[valueIndex].url = data.uploads[fileIndex].url
        delete values.value.gallery[valueIndex].media_file
      }
    }

    const categoryMedia: File[] = []
    const categoryMediaIndices = []

    for (const index in values.value.categories) {
      const category = values.value.categories[index]
      if (!category.image) continue
      categoryMedia.push(category.image)
      categoryMediaIndices.push(index)
    }

    if (categoryMedia.length) {
      const { data } = await FileService.upload(categoryMedia)
      for (const fileIndex in categoryMediaIndices) {
        const valueIndex = Number(categoryMediaIndices[fileIndex])
        values.value.categories[valueIndex].image_url = data.uploads[fileIndex].url
        delete values.value.categories[valueIndex].image
      }
    }

    console.log('submit values', values)
    // setIsDialogOpen(false)

    // block if no changes
    // if (isEqual(values, initialValues)) {
    //   console.log('same')
    //   toast({
    //     title: 'No changes applied',
    //     variant: 'destructive'
    //   })

    //   return
    // }

    // upload image

    try {
      const { data } = await SettingService.updateSetting(SETTING_KEY, values)
      console.log('response', data)

      if (data.success) {
        setIsDialogOpen(false)
        mutate(
          (key) => typeof key === 'string' && key.includes(SettingService.getSetting(SETTING_KEY))
        )
        toast({
          title: 'Freebies updated',
          variant: 'success'
        })
      } else {
        toast({
          title: 'Action failed, please try again',
          variant: 'destructive'
        })
      }
    } catch (e) {
      console.log(e)
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <DialogContent className="max-h-[80vh] max-w-3xl overflow-y-auto">
      <DialogHeader>
        <DialogTitle className="flex items-center justify-between">
          Edit Freebies Page
          <div className="flex space-x-2">
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button
              type="submit"
              form="update-freebies-form"
              disabled={form.formState.isSubmitting}
              onClick={() => {
                form.clearErrors()
              }}
            >
              {form.formState.isSubmitting && (
                <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
              )}
              Save
            </Button>
          </div>
        </DialogTitle>
      </DialogHeader>

      <Form {...form}>
        <form id="update-freebies-form" className="grid gap-2" onSubmit={onSubmit}>
          <FormField
            control={form.control}
            name={`value.description`}
            rules={{ required: 'This is required' }}
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Page Description</FormLabel>
                <FormControl>
                  <EditorComponent content={field.value} onChange={field.onChange} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="value.gallery"
            // rules={{ required: 'Please insert the image' }}
            render={({ field }) => (
              <>
                <FormItem>
                  <FormLabel>Primary banners</FormLabel>
                  {/* <FormDescription>Replace the thumbnail</FormDescription> */}
                  <FormControl>
                    <Dropzone
                      accept={{ 'image/*': ['.jpeg', '.jpg', '.png'], 'video/*': ['.mp4'] }}
                      description="Drop your videos or images here, or click to browse"
                      multiple={true}
                      onDrop={(acceptedFiles) => {
                        console.log('ondrop')
                        const galleryTmp = gallery ? [...gallery] : []

                        // remove existing already uploaded file
                        const acceptedFiles2 = acceptedFiles.filter((acceptedFile) =>
                          galleryTmp?.every((file) => file.name != acceptedFile.name)
                        )
                        for (const index in acceptedFiles2) {
                          const file = acceptedFiles2[index]
                          const findIndex = gallery?.findIndex(
                            (f) => f.media_file?.path == file.path
                          )

                          if ((findIndex ?? -1) == -1) {
                            const preview = Object.assign(file, {
                              preview: URL.createObjectURL(file)
                            })

                            galleryTmp.push({
                              name: preview.name,
                              type: preview.type.split('/')[0] as 'video' | 'image',
                              media_file: preview
                            })
                          }
                        }

                        form.setValue('value.gallery', galleryTmp, {
                          shouldValidate: true
                        })
                      }}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>

                {(gallery ?? []).length > 0 && (
                  <div className="flex flex-col space-y-4 mt-2">
                    {gallery?.map((file, index) => {
                      return (
                        <div
                          key={index}
                          className="grid grid-cols-[150px_1fr_36px] space-x-2 items-center"
                        >
                          {file.type.startsWith('image') && (
                            <Image
                              key={index}
                              src={file.url ?? file.media_file?.preview ?? ''}
                              height={150}
                              width={150}
                              objectFit="contain"
                              className="rounded-md"
                            />
                          )}
                          {file.type.startsWith('video') && (
                            <video
                              // controls
                              key={index}
                              src={file.url ?? file.media_file?.preview ?? ''}
                              height={150}
                              width={150}
                              className="rounded-md"
                            />
                          )}
                          <div className="flex flex-col">
                            {file.url ? (
                              <Label className="text-xs font-normal">
                                {title(file.type)} uploaded
                              </Label>
                            ) : (
                              <>
                                <Label className="text-xs font-normal">
                                  {file.media_file?.path}
                                </Label>
                                <Label className="text-xs font-normal text-gray-500">
                                  {file.media_file?.size && bytesToSize(file.media_file!.size!)}
                                </Label>
                              </>
                            )}
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.stopPropagation()
                                  const galleryTmp = [...gallery]

                                  galleryTmp?.splice(index, 1)
                                  form.setValue('value.gallery', galleryTmp, {
                                    shouldValidate: true
                                  })
                                }}
                                className="space-x-2"
                              >
                                <Trash2Icon size="16" />
                                <span>Delete</span>
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      )
                    })}
                  </div>
                )}
              </>
            )}
          />

          <FormItem className="flex flex-col pt-4 mt-4 border-t-2">
            <FormLabel className="text-lg">Categories</FormLabel>
          </FormItem>

          {fields.map((item, i) => {
            const imageFile = form.watch(`value.categories.${i}.image`)
            const imageFileWithPath = imageFile
              ? (imageFile as unknown as FileWithPath & { preview: string })
              : undefined

            return (
              <div key={item.id} className={cx('grid gap-2', i > 0 && 'border-t-2 mt-4')}>
                {i > 0 && <FormItem className="flex flex-col"></FormItem>}
                <FormLabel className="flex gap-2">
                  {/* {fields.length > 1 && (
                    <Trash2Icon
                      size={16}
                      color="red"
                      onClick={() => {
                        remove(i)
                      }}
                    />
                  )} */}
                </FormLabel>
                <FormField
                  control={form.control}
                  name={`value.categories.${i}.title`}
                  rules={{ required: 'This is required' }}
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Title</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Title" disabled />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name={`value.categories.${i}.image`}
                  // rules={{ required: 'This is required' }}
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Image</FormLabel>
                      <FormControl>
                        <Dropzone
                          multiple={false}
                          onDrop={(acceptedFiles) => {
                            if (imageFileWithPath?.path != acceptedFiles[0].path) {
                              const preview = Object.assign(acceptedFiles[0], {
                                preview: URL.createObjectURL(acceptedFiles[0])
                              })

                              form.setValue(`value.categories.${i}.image`, preview as File, {
                                shouldValidate: true
                              })
                            }
                          }}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                      {imageFileWithPath && (
                        <div className="mt-2 flex flex-col space-y-4">
                          <div className="grid grid-cols-[150px_1fr_36px] items-center space-x-2">
                            {imageFileWithPath.type.startsWith('image') && (
                              <Image
                                key={imageFileWithPath.path}
                                src={imageFileWithPath.preview ?? ''}
                                height={150}
                                width={150}
                                objectFit="contain"
                                className="rounded-md"
                              />
                            )}
                            {imageFileWithPath.type.startsWith('video') && (
                              <video
                                // controls
                                key={imageFileWithPath.path}
                                src={imageFileWithPath.preview ?? ''}
                                height={150}
                                width={150}
                                className="rounded-md"
                              />
                            )}
                            <div className="flex flex-col">
                              <Label className="text-xs font-normal">
                                {imageFileWithPath.path}
                              </Label>
                              <Label className="text-xs font-normal text-gray-500">
                                {bytesToSize(imageFileWithPath.size)}
                              </Label>
                            </div>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <span className="sr-only">Open menu</span>
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem
                                  onClick={(e) => {
                                    e.stopPropagation()

                                    form.setValue(`value.categories.${i}.image`, undefined)
                                  }}
                                  className="space-x-2"
                                >
                                  <Trash2Icon size="16" />
                                  <span>Delete</span>
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </div>
                      )}
                      {!imageFileWithPath && item.image_url && (
                        <div className="mt-2 flex flex-col space-y-4">
                          <div className="grid grid-cols-[150px_1fr_36px] items-center space-x-2">
                            <Image
                              key={item.id}
                              src={item.image_url ?? ''}
                              height={150}
                              width={150}
                              objectFit="contain"
                              className="rounded-md"
                            />
                            <Label className="text-xs font-normal">Uploaded</Label>
                          </div>
                        </div>
                      )}
                    </FormItem>
                  )}
                />
              </div>
            )
          })}
        </form>
      </Form>
    </DialogContent>
  )
}

export default UpdateFreebiesForm
