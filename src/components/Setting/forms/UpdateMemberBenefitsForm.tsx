import EditorComponent, { PlainTextEditorComponent } from '@/components/Editor/Editor'
import TextEditorComponent from '@/components/Editor/TextEditor'
import { Dropzone } from '@/components/Form/Dropzone'
import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import { DialogClose, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { toast } from '@/components/ui/use-toast'
import { bytesToSize } from '@/lib/utils'
import FileService from '@/network/services/file'
import SettingService from '@/network/services/setting'
import Settings, { CreateMemberBenefit, MemberBenefit } from '@/types/Setting'
import { Image } from '@unpic/react'
import { MoreHorizontal, Trash2Icon } from 'lucide-react'
import React, { FC, useEffect } from 'react'
import { FileWithPath } from 'react-dropzone'
import { useFieldArray, useForm } from 'react-hook-form'
import { mutate } from 'swr'

const SETTING_KEY = 'member_benefits'

const UpdateMemberBenefitsForm: FC<{
  initialValues?: Settings
  isDialogOpen: boolean
  setIsDialogOpen: (value: boolean) => void
}> = ({ isDialogOpen, initialValues, setIsDialogOpen }) => {
  const form = useForm<CreateMemberBenefit>({
    shouldUseNativeValidation: false,
    defaultValues: {
      value: initialValues?.value
        ? initialValues.value.map((i: MemberBenefit) => {
            return { ...i, image: undefined }
          })
        : [{ title: '', description: '', image_url: '', image: undefined }],
      metadata: initialValues?.metadata ?? { user_agreements: [''], hospital_list: [''] }
    }
  })

  const {
    fields: benefits,
    append: appendBenefit,
    remove: removeBenefit
  } = useFieldArray({ name: 'value', control: form.control })
  const {
    fields: userAgreements,
    append: appendAgreement,
    remove: removeAgreement
  } = useFieldArray({ name: 'metadata.user_agreements', control: form.control })

  const {
    fields: hospitalList,
    append: appendHospital,
    remove: removeHospital
  } = useFieldArray({ name: 'metadata.hospital_list', control: form.control })

  useEffect(() => {
    if (isDialogOpen) {
      form.reset({
        value: initialValues?.value
          ? initialValues.value.map((i: MemberBenefit) => {
              return { ...i, image: undefined }
            })
          : [{ title: '', description: '', image_url: '', image: undefined }],
        metadata: initialValues?.metadata ?? { user_agreements: [''] }
      })
    }
  }, [isDialogOpen])

  const onSubmit = form.handleSubmit(async (values) => {
    const files: File[] = []
    const fileIndices = []
    for (const index in values.value) {
      const benefit = values.value[index]
      if (!benefit.image) continue
      files.push(benefit.image)
      fileIndices.push(index)
    }

    if (files.length) {
      const { data } = await FileService.upload(files)
      for (const fileIndex in fileIndices) {
        const valueIndex = Number(fileIndices[fileIndex])
        values.value[valueIndex].image_url = data.uploads[fileIndex].url
        delete values.value[valueIndex].image
      }
    }

    console.log('submit values', values)
    // setIsDialogOpen(false)

    // block if no changes
    // if (isEqual(values, initialValues)) {
    //   console.log('same')
    //   toast({
    //     title: 'No changes applied',
    //     variant: 'destructive'
    //   })

    //   return
    // }

    // upload image

    try {
      const { data } = await SettingService.updateSetting(SETTING_KEY, values)
      console.log('response', data)

      if (data.success) {
        setIsDialogOpen(false)
        mutate(
          (key) => typeof key === 'string' && key.includes(SettingService.getSetting(SETTING_KEY))
        )
        toast({
          title: 'Member benefits updated',
          variant: 'success'
        })
      } else {
        toast({
          title: 'Action failed, please try again',
          variant: 'destructive'
        })
      }
    } catch (e) {
      console.log(e)
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle className="flex justify-between items-center">
          Edit Member Benefits
          <div className="flex space-x-2">
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button
              type="submit"
              form="update-member-benefits-form"
              disabled={form.formState.isSubmitting}
              onClick={() => {
                form.clearErrors()
              }}
            >
              {form.formState.isSubmitting && (
                <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
              )}
              Save
            </Button>
          </div>
        </DialogTitle>
      </DialogHeader>

      <Form {...form}>
        <form id="update-member-benefits-form" className="grid gap-4" onSubmit={onSubmit}>
          {benefits.map((item, i) => {
            const imageFile = form.watch(`value.${i}.image`)
            const imageFileWithPath = imageFile
              ? (imageFile as unknown as FileWithPath & { preview: string })
              : undefined

            return (
              <React.Fragment key={item.id}>
                {i > 0 && <FormItem className="flex flex-col col-span-2"></FormItem>}
                <FormLabel className="flex gap-2">
                  <span>Benefit {i + 1}</span>
                  {benefits.length > 1 && (
                    <Trash2Icon size={16} color="red" onClick={() => removeBenefit(i)} />
                  )}
                </FormLabel>
                <FormField
                  control={form.control}
                  name={`value.${i}.title`}
                  rules={{ required: 'This is required' }}
                  render={({ field }) => {
                    return (
                      <FormItem className="flex flex-col col-span-2">
                        <FormLabel>Title</FormLabel>
                        <TextEditorComponent content={field.value} onChange={field.onChange} />
                        <FormMessage />
                      </FormItem>
                    )
                  }}
                />
                <FormField
                  control={form.control}
                  name={`value.${i}.description`}
                  rules={{ required: 'This is required' }}
                  render={({ field }) => (
                    <FormItem className="flex flex-col col-span-2">
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <PlainTextEditorComponent content={field.value} onChange={field.onChange} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name={`value.${i}.image`}
                  // rules={{ required: 'This is required' }}
                  render={({ field }) => (
                    <FormItem className="flex flex-col col-span-2">
                      <FormLabel>Image</FormLabel>
                      <FormControl>
                        <Dropzone
                          multiple={false}
                          onDrop={(acceptedFiles) => {
                            if (imageFileWithPath?.path != acceptedFiles[0].path) {
                              const preview = Object.assign(acceptedFiles[0], {
                                preview: URL.createObjectURL(acceptedFiles[0])
                              })

                              form.setValue(`value.${i}.image`, preview as File, {
                                shouldValidate: true
                              })
                            }
                          }}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                      {imageFileWithPath && (
                        <div className="flex flex-col space-y-4 mt-2">
                          <div className="grid grid-cols-[150px_1fr_36px] space-x-2 items-center">
                            {imageFileWithPath.type.startsWith('image') && (
                              <Image
                                key={imageFileWithPath.path}
                                src={imageFileWithPath.preview ?? ''}
                                height={150}
                                width={150}
                                objectFit="contain"
                                className="rounded-md"
                              />
                            )}
                            {imageFileWithPath.type.startsWith('video') && (
                              <video
                                // controls
                                key={imageFileWithPath.path}
                                src={imageFileWithPath.preview ?? ''}
                                height={150}
                                width={150}
                                className="rounded-md"
                              />
                            )}
                            <div className="flex flex-col">
                              <Label className="text-xs font-normal">
                                {imageFileWithPath.path}
                              </Label>
                              <Label className="text-xs font-normal text-gray-500">
                                {bytesToSize(imageFileWithPath.size)}
                              </Label>
                            </div>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <span className="sr-only">Open menu</span>
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem
                                  onClick={(e) => {
                                    e.stopPropagation()

                                    form.setValue(`value.${i}.image`, undefined)
                                  }}
                                  className="space-x-2"
                                >
                                  <Trash2Icon size="16" />
                                  <span>Delete</span>
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </div>
                      )}
                      {!imageFileWithPath && item.image_url && (
                        <div className="flex flex-col space-y-4 mt-2">
                          <div className="grid grid-cols-[150px_1fr_36px] space-x-2 items-center">
                            <Image
                              key={item.id}
                              src={item.image_url ?? ''}
                              height={150}
                              width={150}
                              objectFit="contain"
                              className="rounded-md"
                            />
                          </div>
                        </div>
                      )}
                    </FormItem>
                  )}
                />
              </React.Fragment>
            )
          })}
          <FormItem className="flex flex-col col-span-2">
            <Button
              type="button"
              onClick={() =>
                appendBenefit({ title: '', description: '', image_url: '', image: undefined })
              }
            >
              Add Benefit
            </Button>
          </FormItem>

          {userAgreements.map((item, i) => {
            return (
              <React.Fragment key={item.id}>
                {i > 0 && <FormItem className="flex flex-col col-span-2"></FormItem>}
                <FormLabel className="flex gap-2">
                  <span>User Agreement {i + 1}</span>
                  {userAgreements.length > 1 && (
                    <Trash2Icon size={16} color="red" onClick={() => removeAgreement(i)} />
                  )}
                </FormLabel>
                <FormField
                  control={form.control}
                  name={`metadata.user_agreements.${i}`}
                  rules={{ required: 'This is required' }}
                  render={({ field }) => {
                    return (
                      <FormItem className="flex flex-col col-span-2">
                        <EditorComponent content={field.value} onChange={field.onChange} />
                        <FormMessage />
                      </FormItem>
                    )
                  }}
                />
              </React.Fragment>
            )
          })}

          <FormItem className="flex flex-col col-span-2">
            <Button type="button" onClick={() => appendAgreement('')}>
              Add User Agreement
            </Button>
          </FormItem>

          {hospitalList.map((item, i) => {
            return (
              <React.Fragment key={item.id}>
                {i > 0 && <FormItem className="flex flex-col col-span-2"></FormItem>}
                <FormLabel className="flex gap-2">
                  <span>Hospital {i + 1}</span>
                  {hospitalList.length > 1 && (
                    <Trash2Icon size={16} color="red" onClick={() => removeHospital(i)} />
                  )}
                </FormLabel>
                <FormField
                  control={form.control}
                  name={`metadata.hospital_list.${i}`}
                  rules={{ required: 'This is required' }}
                  render={({ field }) => {
                    return (
                      <FormItem className="flex flex-col col-span-2">
                        <Input {...field} />
                        <FormMessage />
                      </FormItem>
                    )
                  }}
                />
              </React.Fragment>
            )
          })}

          <FormItem className="flex flex-col col-span-2">
            <Button type="button" onClick={() => appendHospital('')}>
              Add Hospital
            </Button>
          </FormItem>
        </form>
      </Form>
    </DialogContent>
  )
}

export default UpdateMemberBenefitsForm
