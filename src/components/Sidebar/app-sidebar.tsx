'use client'

import * as React from 'react'
import {
  BarChart4,
  BookPlusIcon,
  BriefcaseIcon,
  ImageIcon,
  LayoutDashboardIcon,
  MailQuestionIcon,
  PlayIcon,
  SettingsIcon,
  ShirtIcon,
  ShoppingBasketIcon,
  SlackIcon,
  StoreIcon,
  TagIcon,
  UserCircleIcon,
  UsersIcon
} from 'lucide-react'

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem
} from '@/components/ui/sidebar'
import { NavMain } from './nav-main'
import { Link } from 'react-router-dom'
import useAuth from '@/hooks/useAuth'
import { UserNav } from '../Nav/UserNav'

const data = {
  navMain: [
    {
      title: 'Dashboard',
      url: '/dashboard',
      icon: LayoutDashboardIcon
    },
    {
      title: 'Media Room',
      url: '/media-room',
      icon: PlayIcon
    },
    {
      title: 'Statistics',
      url: '/statistics',
      icon: BarChart4
    },
    {
      title: 'Products',
      url: '/products',
      icon: TagIcon
    },
    {
      title: 'Categories',
      url: '/categories',
      icon: ShirtIcon
    },
    {
      title: 'Brands',
      url: '/brands',
      icon: SlackIcon
    },
    {
      title: 'Events',
      url: '/events',
      icon: ShoppingBasketIcon
    },
    {
      title: 'Inquiries',
      url: '/inquiries',
      icon: MailQuestionIcon
    },
    {
      title: 'Job Applications',
      url: '/job-applications',
      icon: BriefcaseIcon
    }
  ],
  navAdmin: [
    {
      title: 'Friends',
      url: '/friends',
      icon: UsersIcon
    },
    {
      title: 'Companies',
      url: '/companies',
      icon: StoreIcon
    },
    {
      title: 'Campaigns',
      url: '/campaigns',
      icon: BookPlusIcon
    },
    {
      title: 'Memories',
      url: '/memories',
      icon: ImageIcon
    }
  ],
  navSuperAdmin: [
    {
      title: 'Roles',
      url: '/roles',
      icon: UserCircleIcon
    }
  ],
  navSecondary: [
    {
      title: 'Settings',
      url: '/settings',
      icon: SettingsIcon
    }
  ]
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { role, store } = useAuth()

  return (
    <Sidebar collapsible="offcanvas" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton asChild className="data-[slot=sidebar-menu-button]:!p-1.5">
              <div className="flex h-16 items-center">
                <Link to="/">
                  <svg xmlns="http://www.w3.org/2000/svg" width="200" viewBox="0 0 260 36.38">
                    <path
                      id="Logo"
                      d="M10.573,2.879a9.347,9.347,0,0,0-6.915,2.49,9.347,9.347,0,0,0-2.49,6.915A3.163,3.163,0,0,0,1.6,14.22a2.278,2.278,0,0,0,1.783.523l5.409-.062V13.206a9.061,9.061,0,0,1,.43-3.073q.43-1.167,1.107-1.168,1.536,0,1.537,5.717v1.6a4.328,4.328,0,0,1-.123,1.23.483.483,0,0,1-.492.368A4.343,4.343,0,0,1,9.712,17.2l-.184-.123a4.578,4.578,0,0,0-2.644-.8A5.911,5.911,0,0,0,1.844,18.8,11.213,11.213,0,0,0,0,25.562a11.26,11.26,0,0,0,1.967,6.854,6.061,6.061,0,0,0,5.1,2.674,5.03,5.03,0,0,0,3.811-1.721l.8-.861a1.375,1.375,0,0,1,.922-.492q.43,0,.615.676l.123.492a1.629,1.629,0,0,0,1.721,1.291h4.426v-20.1q0-6.024-2.121-8.76T10.573,2.879M11.8,27.191a1.918,1.918,0,0,1-1.66.891,1.993,1.993,0,0,1-1.69-.891,3.517,3.517,0,0,1-.646-2.121,3.323,3.323,0,0,1,.646-2,2,2,0,0,1,1.69-.891,1.92,1.92,0,0,1,1.66.891,3.756,3.756,0,0,1,0,4.119M48.858,4.17a1.407,1.407,0,0,1,.368,1.167L48.3,34.475H38.039a1.557,1.557,0,0,1-1.107-.4A1.6,1.6,0,0,1,36.441,33l-.246-6.393a1.118,1.118,0,0,0-.246-.768.728.728,0,0,0-.553-.277q-.738,0-.8,1.045L34.1,34.475H24.7a1.7,1.7,0,0,1-1.905-1.783L21.688,3.74h6.823a1.649,1.649,0,0,1,1.168.4,1.476,1.476,0,0,1,.43,1.137l-.43,23.113a1.676,1.676,0,0,0,.185.861.517.517,0,0,0,.43.307.589.589,0,0,0,.461-.307,1.768,1.768,0,0,0,.277-.8l2.09-17.335h5.1L39.7,28.39a1.84,1.84,0,0,0,.277.861.586.586,0,0,0,.461.307.555.555,0,0,0,.492-.307,1.681,1.681,0,0,0,.185-.861L41.051,3.74H47.69a1.583,1.583,0,0,1,1.168.43M62.327,3.125A9.286,9.286,0,0,0,54.275,7.4q-2.95,4.272-2.95,11.71t2.92,11.71a9.263,9.263,0,0,0,8.083,4.272,9.356,9.356,0,0,0,6.608-2.244,7.841,7.841,0,0,0,2.428-6.055,2.381,2.381,0,0,0-.308-1.322,1.383,1.383,0,0,0-1.229-.461l-5.717.061a5.689,5.689,0,0,1-.43,2.644A1.576,1.576,0,0,1,62.2,28.759q-2.152,0-2.766-6.327l12.293-.312q.185-2.336.185-5.041,0-6.762-2.49-10.358a8.12,8.12,0,0,0-7.1-3.6m1.66,13.708-4.734.184a19.144,19.144,0,0,1,.738-5.532q.676-2.028,1.721-2.029,1.166,0,1.721,1.66a14.638,14.638,0,0,1,.554,4.487Zm30.084,2.551a2.722,2.722,0,0,1,.738,2.059V34.475H90.444a.8.8,0,0,1-.676-.246,2.136,2.136,0,0,1-.307-.8l-.062-.369a1.176,1.176,0,0,0-.246-.584.615.615,0,0,0-.492-.215,1.55,1.55,0,0,0-1.045.553L87.432,33a8.138,8.138,0,0,1-2.059,1.6,5.5,5.5,0,0,1-2.366.43,7.13,7.13,0,0,1-4.949-2,12.957,12.957,0,0,1-3.381-5.594,26.135,26.135,0,0,1-1.2-8.268q0-7.622,3.074-11.895A9.934,9.934,0,0,1,85.1,3a8.975,8.975,0,0,1,6.762,2.674,9.705,9.705,0,0,1,2.582,7.039,3.738,3.738,0,0,1-.368,2.029,1.68,1.68,0,0,1-1.414.492H86.695a16.05,16.05,0,0,0,.061-1.844,7.2,7.2,0,0,0-.522-2.828q-.523-1.229-1.445-1.229-1.475,0-2.243,2.336a22.783,22.783,0,0,0-.769,6.762q0,9.589,3.688,9.589a1.961,1.961,0,0,0,1.444-.584,3.323,3.323,0,0,0,.83-1.629l.061-.246a3.306,3.306,0,0,0,.061-.738,1.779,1.779,0,0,0-.43-1.26,1.448,1.448,0,0,0-1.107-.461h-.308a.271.271,0,0,1-.307-.307V18.739l6.27-.061a2.9,2.9,0,0,1,2.091.707M107.619,3.125a9.257,9.257,0,0,0-5.871,2,12.867,12.867,0,0,0-3.965,5.655,23.144,23.144,0,0,0-1.414,8.391q0,7.376,2.827,11.648a8.826,8.826,0,0,0,7.746,4.272,9.255,9.255,0,0,0,5.87-2,12.868,12.868,0,0,0,3.965-5.655,23.174,23.174,0,0,0,1.414-8.391q0-7.376-2.828-11.648a8.829,8.829,0,0,0-7.745-4.272M109.187,26.3q-.708,2.459-1.875,2.459T105.406,25.9a30.809,30.809,0,0,1-.738-7.468,23.431,23.431,0,0,1,.738-6.486q.738-2.489,1.967-2.49,1.166,0,1.844,2.828a33.469,33.469,0,0,1,.676,7.5,24.367,24.367,0,0,1-.707,6.516M131.294,3.185a9.259,9.259,0,0,0-5.87,2,12.865,12.865,0,0,0-3.965,5.656,23.155,23.155,0,0,0-1.414,8.391q0,7.376,2.828,11.649a8.825,8.825,0,0,0,7.745,4.272,9.256,9.256,0,0,0,5.871-2,12.868,12.868,0,0,0,3.965-5.656,23.158,23.158,0,0,0,1.414-8.391q0-7.376-2.828-11.649a8.829,8.829,0,0,0-7.746-4.272m1.568,23.174q-.707,2.46-1.875,2.459t-1.905-2.859a30.8,30.8,0,0,1-.738-7.468,23.427,23.427,0,0,1,.738-6.485q.738-2.489,1.967-2.49,1.167,0,1.844,2.828a33.435,33.435,0,0,1,.676,7.5,24.337,24.337,0,0,1-.707,6.516M162.852.122,156.521,0l.554,5.225q.122.922-.369.922a.882.882,0,0,1-.615-.307l-.185-.185a6.906,6.906,0,0,0-4.794-2.4,5.047,5.047,0,0,0-4,2.121,15.5,15.5,0,0,0-2.7,5.9,34.682,34.682,0,0,0-.983,8.7,29.2,29.2,0,0,0,.953,7.838,12.79,12.79,0,0,0,2.674,5.287,5.213,5.213,0,0,0,3.934,1.875q2.827,0,5.287-2.889a1.342,1.342,0,0,1,.8-.492q.368,0,.676.676l.369.983a1.741,1.741,0,0,0,1.721,1.229h4.672V13.092L164.7,2.028A1.914,1.914,0,0,0,164.2.614a1.829,1.829,0,0,0-1.352-.492M156,25.479q-.83,2.552-2.183,2.551-1.046,0-1.721-2.4a23.716,23.716,0,0,1-.676-6.269,23.219,23.219,0,0,1,.707-6.454q.706-2.336,1.936-2.336,1.291,0,2.029,2.183a19.138,19.138,0,0,1,.738,5.932A22.6,22.6,0,0,1,156,25.479M190,5.091a5.22,5.22,0,0,0-3.9-1.905,4.368,4.368,0,0,0-1.967.492,22.558,22.558,0,0,0-3.073,2.09l-.246.185a1.357,1.357,0,0,1-.615.307c-.246,0-.369-.224-.369-.676a10.382,10.382,0,0,1,.123-1.107l.246-1.352q.122-.982.123-1.229,0-1.782-1.475-1.783l-6.577.123.246,32.088a2.273,2.273,0,0,0,.553,1.629,2.093,2.093,0,0,0,1.6.584h3.75a19.4,19.4,0,0,1,.8-2.183c.2-.43.43-.645.676-.645s.594.226,1.045.676l.307.308a6.529,6.529,0,0,0,4.61,2.213,5.327,5.327,0,0,0,4.181-2.121,14.727,14.727,0,0,0,2.735-6.024,38.515,38.515,0,0,0,.953-9.067,25.845,25.845,0,0,0-.984-7.407A13.111,13.111,0,0,0,190,5.091m-5.163,20.163q-.707,2.337-2.059,2.336-1.414,0-2.029-2.644a27.959,27.959,0,0,1-.614-6.27A18.642,18.642,0,0,1,181,12.62q.86-2.489,1.967-2.489,1.167,0,1.875,2.366a22.173,22.173,0,0,1,.707,6.178,23.816,23.816,0,0,1-.707,6.578M205.777,2.939a9.343,9.343,0,0,0-6.915,2.49,9.344,9.344,0,0,0-2.49,6.915,3.164,3.164,0,0,0,.43,1.936,2.279,2.279,0,0,0,1.783.522l5.41-.061V13.267a9.064,9.064,0,0,1,.43-3.074q.43-1.167,1.106-1.168,1.537,0,1.537,5.717v1.6a4.3,4.3,0,0,1-.123,1.229.482.482,0,0,1-.492.369,4.353,4.353,0,0,1-1.536-.676l-.184-.123a4.576,4.576,0,0,0-2.643-.8,5.911,5.911,0,0,0-5.041,2.52,11.208,11.208,0,0,0-1.844,6.762,11.265,11.265,0,0,0,1.967,6.854,6.062,6.062,0,0,0,5.1,2.674,5.031,5.031,0,0,0,3.811-1.721l.8-.861a1.376,1.376,0,0,1,.922-.492q.429,0,.615.676l.123.492a1.629,1.629,0,0,0,1.721,1.291h4.426v-20.1q0-6.024-2.121-8.76t-6.792-2.736m1.229,24.312a1.918,1.918,0,0,1-1.66.891,1.993,1.993,0,0,1-1.691-.891,3.515,3.515,0,0,1-.645-2.121,3.325,3.325,0,0,1,.645-2,2,2,0,0,1,1.691-.891,1.92,1.92,0,0,1,1.66.891,3.756,3.756,0,0,1,0,4.119m27.7-22.16a5.222,5.222,0,0,0-3.9-1.905,4.369,4.369,0,0,0-1.967.492,22.594,22.594,0,0,0-3.073,2.09l-.246.185a1.356,1.356,0,0,1-.614.307c-.246,0-.369-.224-.369-.676a10.329,10.329,0,0,1,.123-1.107l.246-1.352q.122-.982.123-1.229,0-1.782-1.475-1.783l-6.578.123.246,32.088a2.273,2.273,0,0,0,.553,1.629,2.094,2.094,0,0,0,1.6.584h3.75a19.411,19.411,0,0,1,.8-2.183q.307-.646.676-.645t1.045.676l.307.308a6.527,6.527,0,0,0,4.61,2.213,5.326,5.326,0,0,0,4.18-2.121,14.719,14.719,0,0,0,2.736-6.024,38.489,38.489,0,0,0,.953-9.067,25.864,25.864,0,0,0-.983-7.407,13.119,13.119,0,0,0-2.736-5.194m-5.163,20.163q-.708,2.337-2.06,2.336-1.414,0-2.028-2.644a27.951,27.951,0,0,1-.615-6.27,18.643,18.643,0,0,1,.861-6.055q.86-2.489,1.967-2.489,1.167,0,1.875,2.366a22.194,22.194,0,0,1,.707,6.178,23.838,23.838,0,0,1-.707,6.578M259.992,5.4,258.64,21.5l-.246,6.7q-.124,4.3-2.275,6.239t-6.762,1.937a11.42,11.42,0,0,1-6.577-1.691,5.279,5.279,0,0,1-2.458-4.579,4.031,4.031,0,0,1,.584-2.428,2,2,0,0,1,1.813-.707l5.348.123a13.821,13.821,0,0,0-.061,1.475,3.72,3.72,0,0,0,.369,1.783,1.189,1.189,0,0,0,1.107.676q1.536,0,1.537-4V25.991a.815.815,0,0,0-.922-.923l-5.163-.061a3.63,3.63,0,0,1-2.521-.83,3.407,3.407,0,0,1-1.045-2.367L239.707,3.8h6.516a1.565,1.565,0,0,1,1.107.4,1.611,1.611,0,0,1,.492,1.076l.8,12.909a4.465,4.465,0,0,0,.4,1.813q.338.646.768.646t.769-.646a4.348,4.348,0,0,0,.4-1.752L252,3.8h6.516a1.478,1.478,0,0,1,1.137.43,1.463,1.463,0,0,1,.338,1.168"
                      transform="translate(0 0.001)"
                      fill="#0f172a"
                    />
                  </svg>
                </Link>
              </div>
            </SidebarMenuButton>
            <div className="px-2 mt-4">
              <p className="text-sm">Store</p>
              <h2 className="text-lg font-semibold tracking-tight">{store?.name}</h2>
            </div>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        {(role == 'admin' || role == 'super_admin') && (
          <NavMain items={data.navAdmin} title="Admin" />
        )}
        {role == 'super_admin' && <NavMain items={data.navSuperAdmin} title="Super Admin" />}
        {/* <NavSecondary items={data.navSecondary} className="mt-auto" /> */}
      </SidebarContent>
      <SidebarFooter>
        <UserNav />
      </SidebarFooter>
    </Sidebar>
  )
}
