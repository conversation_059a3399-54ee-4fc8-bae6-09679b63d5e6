'use client'

import { type LucideIcon } from 'lucide-react'

import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem
} from '@/components/ui/sidebar'
import { NavLink, useLocation } from 'react-router-dom'

export function NavMain({
  items,
  title
}: {
  items: {
    title: string
    url: string
    icon?: LucideIcon
  }[]
  title?: string
}) {
  const location = useLocation()

  return (
    <SidebarGroup className="group-data-[collapsible=icon]:hidden">
      <SidebarGroupContent className="flex flex-col gap-2">
        {title && <SidebarGroupLabel>{title}</SidebarGroupLabel>}
        <SidebarMenu>
          {items.map((item) => (
            <SidebarMenuItem key={item.title}>
              <NavLink to={item.url}>
                {({ isActive }) => {
                  // memories is special
                  if (item.title === 'Memories') {
                    return (
                      <SidebarMenuButton
                        tooltip={item.title}
                        isActive={
                          isActive ||
                          //location.pathname.startsWith('/memory-medias') ||
                          location.pathname.startsWith('/memory-collections')
                        }
                      >
                        {item.icon && <item.icon />}
                        <span>{item.title}</span>
                      </SidebarMenuButton>
                    )
                  }

                  return (
                    <SidebarMenuButton tooltip={item.title} isActive={isActive}>
                      {item.icon && <item.icon />}
                      <span>{item.title}</span>
                    </SidebarMenuButton>
                  )
                }}
              </NavLink>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  )
}
