import { Separator } from '@/components/ui/separator'
import { SidebarTrigger } from '@/components/ui/sidebar'
import { ReactNode } from 'react'
import { Breadcrumb, BreadcrumbItem } from '@/components/ui/breadcrumb'

interface SiteHeaderProps {
  crumbs: ReactNode[]
}

export function SiteHeader({ crumbs }: SiteHeaderProps) {
  return (
    <header className="group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 flex h-12 shrink-0 items-center gap-2 border-b transition-[width,height] ease-linear">
      <div className="flex w-full items-center gap-1 px-4 lg:gap-2 lg:px-6">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mx-2 data-[orientation=vertical]:h-4" />
        {crumbs && (
          <Breadcrumb>
            {crumbs.map((crumb, index) => {
              return (
                <BreadcrumbItem key={index} isCurrentPage={index == crumbs.length}>
                  {crumb}
                </BreadcrumbItem>
              )
            })}
          </Breadcrumb>
        )}

        {/* <Breadcrumb className="flex-1">
          <BreadcrumbList>
            {crumbs.map((crumb, index) => {
              const isLast = index === crumbs.length - 1

              console.log(crumb)

              return (
                <div key={index} className="flex items-center">
                  <BreadcrumbItem>
                    {isLast ? <BreadcrumbPage>{crumb}</BreadcrumbPage> : crumb}
                  </BreadcrumbItem>
                  {!isLast && <BreadcrumbSeparator />}
                </div>
              )
            })}
          </BreadcrumbList>
        </Breadcrumb> */}
      </div>
    </header>
  )
}
