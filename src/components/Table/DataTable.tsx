import * as React from 'react'
import {
  ColumnDef,
  ColumnFiltersState,
  PaginationState,
  Row,
  RowSelectionState,
  SortingState,
  Table as TableType,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'

import { DataTablePagination } from '@/components/Table/DataTablePagination'
import { DataTableToolbar } from '@/components/Table/DataTableToolbar'
import { useContext, useEffect, useState } from 'react'
import { useTablefy } from '@/hooks/useTablefy'
import { cn, combineObjectAndArray } from '@/lib/utils'
import { ColumnFacetedOption } from './DataTableFacetedFilter'
import { serialize } from '@/network/request'
import { useDebounce } from 'use-debounce'
import { ChevronDown, ChevronUp } from 'lucide-react'
import { PageSizeContext } from './TablePageSizeProvider'

export type FilterColumn = {
  columnKey: string // column access key (must be same as column declaration)
  header: string // placeholder
  dataType?: 'string' | 'number' | 'faceted' | 'date' // To determine which type of filter to use
  options?: ColumnFacetedOption[] // list of faceted filter options
}

interface DataTableProps<TData, TValue, TResponseType> {
  columns: ColumnDef<TData, TValue>[]
  filterColumns?: FilterColumn[] // list of filterable columns
  swrService: string
  pageParam?: string
  limitParam?: string
  sortParam?: string
  sortColumns?: string[]
  defaultSort?: SortingState
  filterParam?: string
  toRow: (data: TResponseType | undefined) => TData[]
  toPaginate: (data: TResponseType | undefined) => {
    total: number
    lastPage: number
  }
  setSelectedRows?: (selectedRows: number[] | string[]) => void
  idDataType?: 'string' | 'number'
  initialSelected?: number[] | string[]
  onRowClick?: (row: Row<TData>, table: TableType<TData>) => void

  // UI props for custom feature
  triggerState?: boolean
  triggerButton?: React.ReactNode
  actionButton?: React.ReactNode
}

export function DataTable<TData, TValue, TResponseType>({
  columns,
  filterColumns,
  swrService,
  pageParam = 'page', // API page query key
  limitParam = 'limit', // API limit query key
  filterParam,
  sortParam,
  sortColumns,
  defaultSort,
  toRow,
  toPaginate,
  setSelectedRows,
  idDataType = 'number',
  initialSelected,
  onRowClick,
  triggerState,
  triggerButton,
  actionButton
}: DataTableProps<TData, TValue, TResponseType>) {
  const contextPageSize = useContext(PageSizeContext)

  const [pageSize, setPageSize] = useState(contextPageSize?.size ?? 20)
  const [pageIndex, setPageIndex] = useState(0)
  const [tableFilters, setTableFilters] = useState<ColumnFiltersState>([])
  const [sorting, setSorting] = React.useState<SortingState>(defaultSort ? defaultSort : [])

  const defaultSelected = initialSelected
    ? Object.fromEntries(initialSelected.map((item) => [item, true]))
    : {}

  // Debounce to prevent instant filter
  const [debouncedFilter] = useDebounce(tableFilters, 1000)

  const { tableData, totalPage, isLoading, error } = useTablefy<TData, TResponseType>({
    swrService: serialize(
      swrService,
      combineObjectAndArray(
        {
          [limitParam]: pageSize ?? 5,
          [pageParam]: pageParam === 'offset' ? pageIndex ?? 0 : pageIndex + 1 ?? 1,
          ...(sortParam &&
            sorting.length > 0 && {
              [sortParam]: `${sorting[0].id}:${sorting[0].desc ? 'DESC' : 'ASC'}`
            })
        },
        filterParam
          ? [{ id: filterParam, value: debouncedFilter[0]?.value ?? '' }]
          : debouncedFilter
      )
    ),
    toRow: toRow as (data: TResponseType | undefined) => TData[],
    toPaginate: toPaginate as (data: TResponseType | undefined) => {
      total: number
      lastPage: number
    }
  })

  /// Table states
  const [rowSelection, setRowSelection] = React.useState<RowSelectionState>(defaultSelected)
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({})
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [{ pageIndex: tablePageIndex, pageSize: tablePageSize }, setPagination] =
    React.useState<PaginationState>({
      pageIndex: pageIndex,
      pageSize: pageSize
    })
  /// Table states

  // to change table pagination state
  const pagination = React.useMemo(
    () => ({
      pageIndex: tablePageIndex,
      pageSize: tablePageSize
    }),
    [tablePageIndex, tablePageSize]
  )

  const table = useReactTable({
    data: tableData,
    columns: columns,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
      pagination // to update the table pagination state
    },
    manualSorting: true,
    manualPagination: true, // true since api handling pagination
    pageCount: totalPage ?? -1, // total pages from API (-1 if unknown / failed)
    enableRowSelection: true, // not working(?)
    enableSorting: false, // disable default sorting

    onPaginationChange: setPagination,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,

    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getRowId: (row) => row['id' as keyof TData] as unknown as string // required for server pagination + row selection
  })

  // Change API limit
  useEffect(() => {
    setPageSize(tablePageSize)
  }, [tablePageSize])

  // Change API page
  useEffect(() => {
    setPageIndex(pageParam === 'offset' ? tablePageSize * tablePageIndex : tablePageIndex)
  }, [tablePageIndex])

  // Table column filters
  useEffect(() => {
    console.log('filter change', columnFilters)
    setTableFilters(columnFilters)
  }, [columnFilters])

  // Pass back an array of TData.id
  useEffect(() => {
    if (setSelectedRows != null) {
      // convert map into array > setstate
      setSelectedRows(
        idDataType === 'number' ? Object.keys(rowSelection).map(Number) : Object.keys(rowSelection)
      )
    }
  }, [rowSelection])

  useEffect(() => {
    if (!triggerState && triggerButton && actionButton) {
      table.resetRowSelection()
    }
  }, [triggerState])

  if (isLoading) {
    return <></>
  }

  if (error) {
    return (
      <div className="space-y-4">
        <DataTableToolbar table={table} filterColumns={filterColumns ?? []} />
        <div className="rounded-md border">
          <span>No result</span>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Trigger button */}
      {triggerButton && triggerButton}

      {/* Table filter */}
      <DataTableToolbar table={table} filterColumns={filterColumns ?? []} />

      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead
                      key={header.id}
                      onClick={() => {
                        if (sortColumns?.includes(header.column.id)) {
                          if (
                            table.getState().sorting.length > 0 &&
                            table.getState().sorting[0].id === header.column.id
                          ) {
                            // if same column sorting
                            switch (sorting[0].desc) {
                              case false:
                                table.setSorting([{ id: header.column.id, desc: true }])
                                break
                              case true:
                              default:
                                table.setSorting([])
                                break
                            }
                            // table.setSorting()
                          } else {
                            table.setSorting([{ id: header.column.id as string, desc: false }])
                          }
                        }
                      }}
                    >
                      <div className="flex flex-row items-center gap-x-2">
                        {header.isPlaceholder
                          ? null
                          : flexRender(header.column.columnDef.header, header.getContext())}
                        {sortColumns?.includes(header.column.id) && (
                          <div className="flex flex-col">
                            <ChevronUp
                              size={12}
                              {...(sorting[0]?.id === header.column.id && {
                                color: sorting[0].desc ? 'black' : undefined
                              })}
                            />
                            <ChevronDown
                              size={12}
                              {...(sorting[0]?.id === header.column.id && {
                                color: !sorting[0].desc ? 'black' : undefined
                              })}
                            />
                          </div>
                        )}
                      </div>
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                  onClick={() => {
                    onRowClick && onRowClick(row, table)
                  }}
                  className={cn(onRowClick != null ? 'cursor-pointer' : '')}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Table Pagination */}
      <DataTablePagination table={table} />

      {/* Action button */}
      {triggerState && actionButton && actionButton}
    </div>
  )
}
