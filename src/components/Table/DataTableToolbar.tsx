import { Cross2Icon } from '@radix-ui/react-icons'
import { Table } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { DataTableFacetedFilter } from '@/components/Table/DataTableFacetedFilter'
import { DataTableViewOptions } from '@/components/Table//DataTableViewOptions'
import { CalendarDatePicker } from '@/components/DatePicker/CalendarDatePicker'
import { FilterColumn } from './DataTable'
import { DateTime } from 'luxon'

interface DataTableToolbarProps<TData> {
  table: Table<TData>
  filterColumns: FilterColumn[]
}

export function DataTableToolbar<TData>({ table, filterColumns }: DataTableToolbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0

  return (
    <div className="flex items-center justify-between">
      <div className="flex flex-1 items-center space-x-2">
        {filterColumns.map((filterColumn) => {
          switch (filterColumn.dataType) {
            case 'string':
              return (
                <Input
                  key={filterColumn.columnKey}
                  placeholder={filterColumn.header}
                  value={
                    (table.getColumn(filterColumn.columnKey)?.getFilterValue() as string) ?? ''
                  }
                  onChange={(event) =>
                    table.getColumn(filterColumn.columnKey)?.setFilterValue(event.target.value)
                  }
                  className="h-8 w-[150px] lg:w-[250px]"
                />
              )

            case 'number':
              return <p>number</p>

            case 'date':
              return (
                <CalendarDatePicker
                  className="w-[180px]"
                  key={filterColumn.columnKey}
                  buttonLabel={
                    DateTime.fromISO(
                      table.getColumn(filterColumn.columnKey)?.getFilterValue() as string
                    ).toISODate() ?? filterColumn.header
                  }
                  mode="single"
                  onSelect={(e) => {
                    table
                      .getColumn(filterColumn.columnKey)
                      ?.setFilterValue(DateTime.fromISO(e?.toISOString() ?? '').toISO())
                    console.log('date changed', DateTime.fromISO(e?.toISOString() ?? '').toISO())
                  }}
                />
              )

            case 'faceted':
              return (
                table.getColumn(filterColumn.columnKey) && (
                  <DataTableFacetedFilter
                    key={filterColumn.columnKey}
                    column={table.getColumn(filterColumn.columnKey)}
                    title={filterColumn.header}
                    options={filterColumn.options ?? []}
                  />
                )
              )

            default:
              return <p>unknown</p>
          }
        })}
        {/* Map the filters according to filterColumns */}
        {/* String variant */}
        {/* Number variant */}
        {/* Date variant */}
        {/* Faceted */}

        {/* String filters (string filter) */}
        {/* <Input
          placeholder="Filter"
          value={(table.getColumn('title')?.getFilterValue() as string) ?? ''}
          onChange={(event) => table.getColumn('title')?.setFilterValue(event.target.value)}
          className="h-8 w-[150px] lg:w-[250px]"
        />

        <Input
          placeholder="Location"
          value={(table.getColumn('location')?.getFilterValue() as string) ?? ''}
          onChange={(event) => table.getColumn('location')?.setFilterValue(event.target.value)}
          className="h-8 w-[150px] lg:w-[250px]"
        />

        <Input
          type="date"
          placeholder="Start Date"
          value={(table.getColumn('from')?.getFilterValue() as string) ?? ''}
          onChange={(event) => table.getColumn('from')?.setFilterValue(event.target.value)}
          className="h-8 w-[150px] lg:w-[250px]"
        /> */}

        {/* Faceted filters (for data with limited options) */}
        {/* {table.getColumn('selected') && (
          <DataTableFacetedFilter
            column={table.getColumn('selected')}
            title="Selected"
            options={filterColumns[0].options ?? []}
          />
        )} */}

        {/* {table.getColumn("priority") && (
          <DataTableFacetedFilter
            column={table.getColumn("priority")}
            title="Priority"
            options={priorities}
          />
        )} */}
        {isFiltered && (
          <Button
            variant="ghost"
            onClick={() => table.resetColumnFilters()}
            className="h-8 px-2 lg:px-3"
          >
            Reset
            <Cross2Icon className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
      <DataTableViewOptions table={table} />
    </div>
  )
}
