import { createContext, <PERSON>spatch, FC, PropsWithChildren, SetStateAction } from 'react'
import { useLocalStorage } from 'usehooks-ts'

// export the set cursor variant function globally
export const PageSizeContext = createContext<{
  size: number
  setSize: Dispatch<SetStateAction<number>>
} | null>(null)

const TablePageSizeProvider: FC<PropsWithChildren> = ({ children }) => {
  const [size, setSize] = useLocalStorage('page-size', 20)

  return (
    <PageSizeContext.Provider value={{ size: size, setSize: setSize }}>
      {children}
    </PageSizeContext.Provider>
  )
}

export default TablePageSizeProvider
