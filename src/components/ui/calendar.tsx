import * as React from 'react'
import { ChevronLeftIcon, ChevronRightIcon } from '@radix-ui/react-icons'
import { DayPicker } from 'react-day-picker'

import { cn } from '@/lib/utils'
import { buttonVariants } from '@/components/ui/button'
import { DateTime } from 'luxon'

export type CalendarProps = React.ComponentProps<typeof DayPicker>

function Calendar({
  className,
  classNames,
  showOutsideDays = true,
  onChange,
  value,
  includeTime = false,
  ...props
}: CalendarProps & { onChange?: (date: Date) => void; value?: string; includeTime?: boolean }) {
  let date: Date | undefined = undefined
  let time: string = '00:00'
  if (value) {
    const v = DateTime.fromISO(value)
    date = v.toJSDate()
    time = v.toFormat('HH:mm')
  }
  const [selected, setSelected] = React.useState<Date | undefined>(date)
  const [timeValue, setTimeValue] = React.useState<string>(time)

  const handleTimeChange: React.ChangeEventHandler<HTMLInputElement> = (e) => {
    const time = e.target.value
    if (!selected) {
      setTimeValue(time)
      return
    }
    const [hours, minutes] = time.split(':').map((str) => parseInt(str, 10))
    const newSelectedDate = new Date(
      selected.getFullYear(),
      selected.getMonth(),
      selected.getDate(),
      hours,
      minutes
    )
    setSelected(newSelectedDate)
    setTimeValue(time)
  }

  const handleDaySelect = (date: Date | undefined) => {
    if (!timeValue || !date) {
      setSelected(date)
      return
    }
    const [hours, minutes] = timeValue.split(':').map((str) => parseInt(str, 10))
    const newDate = new Date(date.getFullYear(), date.getMonth(), date.getDate(), hours, minutes)
    setSelected(newDate)
  }

  React.useEffect(() => {
    if (onChange) {
      onChange(selected!)
    }
  }, [selected])

  return (
    // @ts-ignore - ignore onSelect checking, remove this line for testing if there's other properties
    <DayPicker
      showOutsideDays={showOutsideDays}
      className={cn('p-3', className)}
      classNames={{
        months: 'flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0',
        month: 'space-y-4',
        caption: 'flex justify-center pt-1 relative items-center',
        caption_label: 'text-sm font-medium',
        nav: 'space-x-1 flex items-center',
        nav_button: cn(
          buttonVariants({ variant: 'outline' }),
          'h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100'
        ),
        nav_button_previous: 'absolute left-1',
        nav_button_next: 'absolute right-1',
        table: 'w-full border-collapse space-y-1 flex flex-col',
        head_row: 'flex justify-center',
        head_cell: 'text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]',
        row: 'flex w-full mt-2 flex justify-center',
        cell: cn(
          'relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent',
          props.mode === 'range'
            ? '[&:has(>.day-range-end)]:rounded-r-md [&:has(>.day-range-start)]:rounded-l-md first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md'
            : '[&:has([aria-selected])]:rounded-md'
        ),
        day: cn(
          buttonVariants({ variant: 'ghost' }),
          'h-8 w-8 p-0 font-normal aria-selected:opacity-100'
        ),
        day_range_start: 'day-range-start',
        day_range_end: 'day-range-end',
        day_selected:
          'bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground',
        day_today: 'bg-accent text-accent-foreground',
        day_outside: 'text-muted-foreground opacity-50',
        day_disabled: 'text-muted-foreground opacity-50',
        day_range_middle: 'aria-selected:bg-accent aria-selected:text-accent-foreground',
        day_hidden: 'invisible',
        ...classNames
      }}
      components={{
        IconLeft: () => <ChevronLeftIcon className="h-4 w-4" />,
        IconRight: () => <ChevronRightIcon className="h-4 w-4" />
      }}
      {...(includeTime && {
        onSelect: (v: Date) => handleDaySelect(v as Date),
        footer: (
          <>
            <p>
              Pick a time: <input type="time" value={timeValue} onChange={handleTimeChange} />
            </p>
            <p>Selected date: {selected ? selected.toLocaleString() : 'none'}</p>
          </>
        )
      })}
      {...props}
    />
  )
}
Calendar.displayName = 'Calendar'

export { Calendar }
