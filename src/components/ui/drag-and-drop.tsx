// import EventEmitter from 'events'
// import { FC, ReactNode } from 'react'
// import { useDrag, useDragDropManager, useDrop } from 'react-dnd'

// interface DraggableProps {
//   children: ReactNode
//   name: string
//   index: number
//   eventName: string
//   eventEmitter: EventEmitter
// }

// type DragData = {
//   index: number
// }
// // type DropData = DragData

// const DraggableComponent: FC<DraggableProps> = ({
//   children,
//   name,
//   index,
//   eventName,
//   eventEmitter
// }) => {
//   const dndManager = useDragDropManager()
//   const [dragProps, drag, _dragPreview] = useDrag(() => ({
//     // "type" is required. It is used by the "accept" specification of drop targets.
//     type: name,
//     // The collect function utilizes a "monitor" instance (see the Overview for what this is)
//     // to pull important pieces of state from the DnD system.
//     item: {
//       index
//     },
//     collect: (monitor) => ({
//       isDragging: !!monitor.isDragging()
//     })
//   }))

//   const [dropProps, drop] = useDrop(() => ({
//     accept: name,
//     collect: (monitor) => {
//       return {
//         isOver: !!monitor.isOver({ shallow: true })
//       }
//     },
//     drop: (item: DragData) => {
//       // when from index not equal to to index
//       if (item.index !== index) {
//         // emit event, from, to
//         eventEmitter.emit(eventName, item.index, index)
//         // const tmp = data ? [...data] : []
//         // const [removeItem] = tmp.splice(item.index, 1)
//         // tmp.splice(index, 0, removeItem)
//         // form.setValue(fieldName, tmp)
//       }
//     }
//   }))

//   return (
//     <div ref={(node) => drag(drop(node))} style={{ height: '100%' }}>
//       <div>
//         {dndManager.getMonitor().getItem()?.index !== index &&
//           dndManager.getMonitor().getItem()?.index > index &&
//           dropProps.isOver && (
//             <div
//               style={{
//                 width: '100%',
//                 border: '1px solid',
//                 color: 'red',
//                 marginTop: '5px',
//                 marginBottom: '5px'
//               }}
//             />
//           )}
//         <div
//           style={{
//             opacity: dragProps.isDragging ? 0.5 : 1
//           }}
//         >
//           {children}
//         </div>
//         {dndManager.getMonitor().getItem()?.index !== index &&
//           dndManager.getMonitor().getItem()?.index < index &&
//           dropProps.isOver && (
//             <div
//               style={{
//                 width: '100%',
//                 border: '1px solid',
//                 color: 'red',
//                 marginTop: '5px',
//                 marginBottom: '5px'
//               }}
//             />
//           )}
//       </div>
//     </div>
//   )
// }

// export default DraggableComponent

import { FC, PropsWithChildren } from 'react'
import type { UniqueIdentifier } from '@dnd-kit/core'
import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { TableRow } from './table'

const SortableItem: FC<PropsWithChildren<{ id: UniqueIdentifier; className?: string }>> = (
  props
) => {
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ id: props.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition
  }

  return (
    <div className={props.className} ref={setNodeRef} style={style} {...attributes} {...listeners}>
      {props.children}
    </div>
  )
}

export const SortableTableRow: FC<PropsWithChildren<{ id: UniqueIdentifier; className?: string }>> = (
  props
) => {
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ id: props.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition
  }

  return (
    <TableRow
      className={props.className}
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
    >
      {props.children}
    </TableRow>
  )
}

export default SortableItem
