import { isRouteErrorResponse, useRouteError } from 'react-router-dom'
import Navbar from '@/components/Nav/Navbar'
import { Sidebar } from '@/components/Nav/Sidebar'

export default function ErrorPage() {
  const error = useRouteError()
  console.error(error)

  if (isRouteErrorResponse(error)) {
    return (
      <div>
        <h2>{error.status}</h2>
        <p>{error.statusText}</p>
        {error.data?.message && <p>{error.data.message}</p>}
      </div>
    )
  } else {
    return (
      <div className="block">
        <Navbar />
        <div className="border-t">
          <div className="bg-background">
            <div className="grid lg:grid-cols-5">
              <Sidebar className="col-span-4 block lg:col-span-1" />
              <div className="col-span-4 flex h-full items-center justify-center">
                Something went wrong
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }
}
