import { IDataResponse, serialize } from '@/network/request'
import CampaignService from '@/network/services/campaign'
import { Campaign } from '@/types/Campaign'
import { useEffect, useState } from 'react'
import useSWR from 'swr'

export const useDefaultCampaigns = (fetchAll: boolean = false, limit?: number, pageNo?: number) => {
  const [page, setPage] = useState<number>(fetchAll ? 1 : pageNo ?? 1)
  const LIMIT = fetchAll ? 100 : limit ?? null
  const { data, error, isLoading } = useSWR<IDataResponse<Campaign>>(
    serialize(CampaignService.getDefaultCampaigns, { page: page ?? 1, limit: LIMIT ?? null })
  )
  const [campaigns, setCampaigns] = useState<Campaign[]>([])
  const total = data?.meta?.total ?? 0

  useEffect(() => {
    if (data && data?.data) {
      const tmp = [...campaigns, ...data.data]
      setCampaigns(tmp)

      if (tmp.length < total) {
        setPage((page) => page + 1)
      }
    }
  }, [data])

  return {
    campaigns,
    isLoading,
    error
  }
}