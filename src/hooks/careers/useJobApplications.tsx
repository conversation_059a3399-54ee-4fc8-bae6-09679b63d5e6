import { serialize } from '@/network/request'
import CareerService from '@/network/services/career'
import useSWR from 'swr'

export const useJobApplication = (applicationId: string | number) => {
  const { data, error, isLoading } = useSWR(
    serialize(CareerService.getJobApplication(applicationId), {})
  )
  const application = data?.data

  return {
    application,
    isLoading,
    error
  }
}
