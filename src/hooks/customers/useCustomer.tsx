import { serialize } from '@/network/request'
import CustomerService from '@/network/services/customer'
import { Customer, LicenseCustomer } from '@/types/Customer'
import useSWR from 'swr'

export const useCustomer = (customerId: string | number) => {
  const {
    data: core,
    error: coreError,
    isLoading: coreLoading
  } = useSWR(serialize(CustomerService.getCustomer(customerId), { expand: 'shipping_addresses,shipping_addresses.country,children' }))
  const customer = core?.customer as Customer

  const {
    data: license,
    error: licenseError,
    isLoading: licenseLoading
  } = useSWR(serialize(CustomerService.getLicenseCustomer(customerId), {}))
  const licenseCustomer = license?.data as LicenseCustomer

  return {
    customer,
    licenseCustomer,
    coreLoading,
    coreError,
    licenseError,
    licenseLoading
  }
}
