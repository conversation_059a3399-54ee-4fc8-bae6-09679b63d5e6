import { serialize } from '@/network/request'
import EventService from '@/network/services/event'
import { Event } from '@/types/Event'
import useSWR from 'swr'

export const useEvent = (id: string | number) => {
  const { data, error, isLoading } = useSWR<{ data: Event }>(
    serialize(EventService.getEvent(id), {})
  )
  
  const event = data?.data

  return {
    event,
    isLoading,
    error
  }
}
