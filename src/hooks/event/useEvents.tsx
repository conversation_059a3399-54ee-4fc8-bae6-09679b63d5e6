import { IDataResponse, serialize } from '@/network/request'
import EventService from '@/network/services/event'
import { GeneralEventInfo } from '@/types/Event'
import useSWR from 'swr'

export const useEvents = (limit?: number, page?: number) => {
  const { data, error, isLoading } = useSWR<IDataResponse<GeneralEventInfo>>(
    serialize(EventService.getEvents, {
      page: page ?? 1,
      limit: limit ?? null
    })
  )

  const events = EventService.toRow(data)
  const pagination = EventService.toPaginate(data)

  return {
    events,
    pagination,
    isLoading,
    error
  }
}
