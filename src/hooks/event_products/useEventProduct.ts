import { serialize } from '@/network/request'
import EventProductService from '@/network/services/event_product'
import useSWR from 'swr'

export const useEventProduct = (
  id: string | number,
  params?: unknown | [],
  arrayFormat: 'indices' | 'brackets' | 'repeat' | 'comma' = 'indices'
) => {
  const { data, error, isLoading } = useSWR(
    serialize(EventProductService.getEventProduct(id), params, arrayFormat)
  )
  const eventProduct = data?.data

  return {
    eventProduct,
    isLoading,
    error
  }
}
