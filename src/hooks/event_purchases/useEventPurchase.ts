import { serialize } from '@/network/request'
import EventPurchaseService from '@/network/services/event_purchase'
import { EventPurchase } from '@/types/EventPurchase'
import useSWR from 'swr'

export const useEventPurchase = (eventPurchaseId: string) => {
  const {
    data: core,
    error: coreError,
    isLoading: coreLoading
  } = useSWR(serialize(EventPurchaseService.getEventPurchase(eventPurchaseId), {}))
  const eventPurchase = core?.data as EventPurchase

  return {
    eventPurchase,
    coreLoading,
    coreError
  }
}
