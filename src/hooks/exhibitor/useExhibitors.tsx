import { serialize } from '@/network/request'
import ExhibitorService from '@/network/services/exhibitor'
import useSWR from 'swr'

export const useExhibitors = () => {
  const { data, error, isLoading } = useSWR(
    serialize(ExhibitorService.getExhibitors, {
      limit: 5
    })
  )
  const exhibitors = ExhibitorService.toRow(data)
  const pagination = ExhibitorService.toPaginate(data)
  console.log('useExhi', data)

  return {
    exhibitors,
    pagination,
    isLoading,
    error
  }
}
