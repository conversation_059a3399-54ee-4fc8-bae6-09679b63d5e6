import { serialize } from '@/network/request'
import InquiryService from '@/network/services/inquiry'
import { Inquiry } from '@/types/Inquiry'
import useSWR from 'swr'

export const useInquiry = (inquiryId: string) => {
  const {
    data: response,
    error,
    isLoading
  } = useSWR(serialize(InquiryService.getInquiry(inquiryId), {}))
  const inquiry = response?.data as Inquiry

  return {
    inquiry,
    isLoading,
    error
  }
}
