import { serialize } from '@/network/request'
import MemoryService from '@/network/services/memory'
import useSWR from 'swr'

export const useSearchMemoriesUnlinked = (query?: string) => {
  const { data, error, isLoading } = useSWR(
    serialize(MemoryService.findMemories, { name: query, limit: 20, type: 'unlinked-memory-id' }),
    {
      keepPreviousData: true
    }
  )

  const memory = data?.data

  const pagination = MemoryService.toPaginate(data)

  return {
    data: memory,
    pagination,
    isLoading,
    error
  }
}

export const useSearchMemoriesAll = (query?: string) => {
  const { data, error, isLoading } = useSWR(
    serialize(MemoryService.findMemories, { name: query, limit: 20 }),
    {
      keepPreviousData: true
    }
  )

  const memory = data?.data

  const pagination = MemoryService.toPaginate(data)

  return {
    data: memory,
    pagination,
    isLoading,
    error
  }
}
