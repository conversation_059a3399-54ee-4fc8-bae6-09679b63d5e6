import { serialize } from '@/network/request'
import MemoryCollectionService from '@/network/services/memory_collection'
import useSWR from 'swr'

export const useMemoryCollection = (memoryCollectionId: string | number) => {
  const { data, error, isLoading } = useSWR(
    serialize(MemoryCollectionService.findMemoryCollection(memoryCollectionId), {})
  )
  const memoryCollection = data?.data

  return {
    memoryCollection,
    isLoading,
    error
  }
}
