import { serialize } from '@/network/request'
import MemoryCollectionService from '@/network/services/memory_collection'
import useSWR from 'swr'

export const useSearchMemoryCollections = (query?: string) => {
  const { data, error, isLoading } = useSWR(
    serialize(MemoryCollectionService.findMemoryCollections, { name: query, limit: 20 }),
    {
      keepPreviousData: true
    }
  )

  const memoryCollections = data?.data

  const pagination = MemoryCollectionService.toPaginate(data)

  return {
    data: memoryCollections,
    pagination,
    isLoading,
    error
  }
}
