import { serialize } from '@/network/request'
import MemoryMediaService from '@/network/services/memory_media'
MemoryMediaService
import useSWR from 'swr'

export const useMemoryMedia = (memoryMediaId: string | number) => {
  const { data, error, isLoading } = useSWR(
    serialize(MemoryMediaService.findMemoryMedia(memoryMediaId), {})
  )
  const memoryMedia = data?.data

  return {
    memoryMedia,
    isLoading,
    error
  }
}
