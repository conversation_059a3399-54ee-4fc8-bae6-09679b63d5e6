import { IMedusaDataResponse, serialize } from '@/network/request'
import ProductService from '@/network/services/product'
import { PriceList } from '@/types/Product'
import { useEffect, useState } from 'react'
import useSWR from 'swr'

interface PriceListResponse extends IMedusaDataResponse {
  price_lists: PriceList[]
}

export const usePriceLists = (
  eventId: number,
  campaignId: number,
  fetchAll: boolean = false,
  limit?: number,
  pageNo?: number
) => {
  const [page, setPage] = useState<number>(fetchAll ? 1 : pageNo ?? 1)
  const LIMIT = fetchAll ? 100 : limit ?? 10
  const { data, error, isLoading } = useSWR<PriceListResponse>(
    serialize(ProductService.getPriceLists, {
      status: 'active',
      type: 'sale',
      event_id: eventId,
      campaign_id: campaignId,
      limit: LIMIT,
      offset: (page - 1) * LIMIT
    })
  )
  const [priceLists, setPriceLists] = useState<PriceList[]>([])
  const total = data?.count ?? 0

  useEffect(() => {
    if (data && data?.price_lists) {
      const tmp = [...priceLists, ...data.price_lists]
      setPriceLists(tmp)

      if (tmp.length < total) {
        setPage((page) => page + 1)
      }
    }
  }, [data])

  return {
    priceLists,
    isLoading,
    error
  }
}
