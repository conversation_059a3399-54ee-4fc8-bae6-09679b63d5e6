import { serialize } from '@/network/request'
import ProductCategoryService from '@/network/services/product_category'
import useSWR from 'swr'

export const useProductCategory = (id: string, params: unknown) => {
  const { data, error, isLoading } = useSWR(
    serialize(ProductCategoryService.getProductCategory(id), params)
  )
  const category = data?.product_category

  return {
    category,
    isLoading,
    error
  }
}
