import { serialize } from '@/network/request'
import ProductService from '@/network/services/product'
import { Product } from '@/types/Product'
import useSWR from 'swr'

export const useProduct = (
  id: string | number,
  params?: unknown | [],
  arrayFormat: 'indices' | 'brackets' | 'repeat' | 'comma' = 'indices'
): { product: Product; isLoading: boolean; error: any } => {
  const { data, error, isLoading } = useSWR(
    serialize(ProductService.getProduct(id), params, arrayFormat)
  )
  const product = data?.product

  return {
    product,
    isLoading,
    error
  }
}
