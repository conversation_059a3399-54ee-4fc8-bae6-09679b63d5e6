import { serialize } from '@/network/request'
import ProductCollectionService from '@/network/services/product_collection'
import { ProductCollection } from '@/types/ProductCollection'
import useSWR from 'swr'

export const useProductBrand = (brand_id: string) => {
  const { data, error, isLoading } = useSWR<{ collection: ProductCollection }>(
    serialize(
      ProductCollectionService.getProductCollection(brand_id),
      {
        expand: ['images', 'related_collections']
      },
      'comma'
    )
  )
  const brand = data?.collection

  return {
    brand,
    isLoading,
    error
  }
}
