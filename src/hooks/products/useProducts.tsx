import { serialize } from '@/network/request'
import ProductService from '@/network/services/product'
import ProductCategoryService from '@/network/services/product_category'
import ProductCollectionService from '@/network/services/product_collection'
import useSWR from 'swr'

export const useProducts = (params: Record<string, unknown>) => {
  const { data, error, isLoading } = useSWR(
    serialize(ProductService.getProducts, {
      offset: 1,
      limit: 5,
      ...params
    })
  )
  const products = ProductService.toRow(data)
  const pagination = ProductService.toPaginate(data)

  return {
    products,
    pagination,
    isLoading,
    error
  }
}

export interface SearchProducts {
  id: string
  created_at: string
  updated_at: string
  title: string
}

export const useSearchProducts = (query?: string) => {
  const { data, error, isLoading } = useSWR(serialize(ProductService.getProducts, { q: query }), {
    keepPreviousData: true
  })
  const products = data?.products
  const pagination = ProductService.toPaginate(data)

  return {
    data: products,
    pagination,
    isLoading,
    error
  }
}

export interface SearchProductType {
  id: string
  created_at: string
  updated_at: string
  value: string
}

export const useProductTypes = (query?: string) => {
  const { data, error, isLoading } = useSWR(
    serialize(ProductService.getProductTypes, { q: query }),
    {
      keepPreviousData: true
    }
  )
  const productTypes = data?.product_types
  const pagination = ProductService.toPaginate(data)

  return {
    data: productTypes,
    pagination,
    isLoading,
    error
  }
}

export interface SearchProductCategories {
  id: string
  name: string
  handle: string
  mpath: string
  is_internal: boolean
  is_active: boolean
  // category_children: [{}]
  parent_category: SearchProductCategories
  parent_category_id: string
  created_at: string
  updated_at: string
}

export const useProductCategories = (query?: string) => {
  const { data, error, isLoading } = useSWR(
    serialize(ProductCategoryService.getProductCategories, {
      q: query,
      expand: 'parent_category'
      // is_active: true
    }),
    {
      keepPreviousData: true
    }
  )
  const productCategories = data?.product_categories
  const pagination = ProductService.toPaginate(data)
  console.log(data)

  return {
    data: productCategories,
    pagination,
    isLoading,
    error
  }
}

export interface SearchProductCollections {
  id: string
  title: string
  description: string
  handle: string
  status: string
  thumbnail_url: string
  store_id: string
  deleted_at: string
  created_at: string
  updated_at: string
}

export const useProductCollections = (query?: string) => {
  const { data, error, isLoading } = useSWR(
    serialize(ProductCollectionService.getProductCollections, { q: query, status: 'published' }),
    {
      keepPreviousData: true
    }
  )
  const productCollections = data?.collections
  const pagination = ProductCollectionService.toPaginate(data)

  return {
    data: productCollections,
    pagination,
    isLoading,
    error
  }
}
