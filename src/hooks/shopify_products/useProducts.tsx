// import useSWR from "swr"
// import ProductService from "@/network/services/product"
// import { graphqlFetcher } from "network/request"
// import {
//   CountryCode,
//   Maybe,
//   Product,
//   ProductConnection,
// } from "@shopify/hydrogen-react/storefront-api-types"
// import { flattenConnection } from "@shopify/hydrogen-react"

// interface ProductFilter {
//   filter?: string | null
//   endCursor?: Maybe<string> | undefined
//   length?: number
//   isReverse?: boolean
//   country?: string
// }

// const useProducts = (
//   props: ProductFilter = { length: 12, isReverse: false, country: "MY" }
// ): [{ products: ProductConnection } | undefined, Product[] | undefined, () => void] => {
//   const { filter, endCursor, length = 12, isReverse = false, country = "MY" } = props
//   const {
//     data: productsResponse,
//     error,
//     mutate,
//   } = useSWR<{ products: ProductConnection }>(
//     [
//       ProductService.productsQuery,
//       {
//         query: filter ?? null,
//         cursor: endCursor ?? null,
//         length: length ?? 12,
//         reverse: isReverse,
//         country,
//       },
//     ],
//     graphqlFetcher
//   )

//   const products = productsResponse?.products && flattenConnection(productsResponse?.products)

//   return [productsResponse, products, mutate]
// }

// export default useProducts
