// import useSWR from 'swr'
// import ProductService from '@/network/services/product'
// import { graphqlFetcher } from 'network/request'
// import { Collection, CollectionConnection } from '@shopify/hydrogen-react/storefront-api-types'
// import { flattenConnection } from '@shopify/hydrogen-react'

// export interface ProductCategory {
//   title: string
//   value: string
// }

// const useProductCategory = (): [
//   { collections: CollectionConnection } | undefined,
//   Collection[] | undefined,
//   () => void
// ] => {
//   const {
//     data: collectionResponse,
//     error,
//     mutate
//   } = useSWR<{ collections: CollectionConnection }>(
//     [ProductService.productCollectionQuery],
//     graphqlFetcher
//   )

//   const collectionCategories =
//     collectionResponse?.collections && flattenConnection(collectionResponse?.collections)

//   return [collectionResponse, collectionCategories, mutate]
// }

// export default useProductCategory
