import * as React from 'react'
import { useCallbackRef } from '@radix-ui/react-use-callback-ref'

/**
 * Listens for when the Enter key is down
 */
function useEnterKeydown(
  onEnterKeyDownProp?: (event: KeyboardEvent) => void,
  reference?: React.RefObject<HTMLInputElement>
) {
  const onEnterKeyDown = useCallbackRef(onEnterKeyDownProp)

  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Enter') {
        onEnterKeyDown(event)
      }
    }

    const input = reference?.current
    if (input) {
      input?.addEventListener('keydown', handleKeyDown)
      return () => input?.removeEventListener('keydown', handleKeyDown)
    } else {
      const doc = globalThis?.document
      doc.addEventListener('keydown', handleKeyDown)
      return () => doc.removeEventListener('keydown', handleKeyDown)
    }
  }, [onEnterKeyDown, reference])
}

export { useEnterKeydown }
