import useSWR from 'swr'

export type TablefyType<T, TResponseType> = {
  swrService: string
  toRow: (data: TResponseType | undefined) => T[]
  toPaginate: (data: TResponseType | undefined) => {
    total: number
    lastPage: number
  }
}

export const useTablefy = <T, TResponseType>(props: TablefyType<T, TResponseType>) => {
  const { swrService, toRow, toPaginate } = props

  const { data, error, isLoading } = useSWR<TResponseType>(swrService)

  const processedData = toRow(data)
  const totalRow = toPaginate(data).total
  const totalPage = toPaginate(data).lastPage

  return {
    tableData: processedData,
    totalRow,
    totalPage,
    isLoading,
    error
  }
}
