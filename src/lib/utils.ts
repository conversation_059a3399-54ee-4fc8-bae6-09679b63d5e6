import { ColumnFiltersState } from '@tanstack/react-table'
import { type ClassValue, clsx } from 'clsx'
import { format } from 'date-fns'
import React from 'react'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function getValidChildren(children: React.ReactNode) {
  return React.Children.toArray(children).filter((child) =>
    React.isValidElement(child)
  ) as React.ReactElement[]
}

export function dateFormatter(date: Date | undefined, formatString?: string) {
  if (date) {
    return format(date, formatString ?? 'LLL dd, y')
  }

  return '-'
}

export function constructDateRangeString(
  startDate: Date | undefined,
  endDate: Date | undefined,
  formatString?: string
) {
  if (!startDate || !endDate) return '-'

  const tempStart = format(startDate, formatString ?? 'LLL dd, y')
  const tempEnd = format(endDate, formatString ?? 'LLL dd, y')

  return `${tempStart} - ${tempEnd}`
}

// Combine object & array and output as single object
// Used for DataTable dynamic filter
export function combineObjectAndArray(obj: object, arr?: ColumnFiltersState) {
  let combinedObj = { ...obj }

  for (const element of arr ?? []) {
    const tempObj = { [element.id]: element.value }
    combinedObj = { ...combinedObj, ...tempObj }
  }

  return combinedObj
}

export const convertValuesToFormData = (formData: FormData, values: any, keyPrefix?: string) => {
  for (const key in values) {
    const data = values[key]
    if (data === undefined || data === null) continue
    if (typeof data === 'object' && Array.isArray(data)) {
      for (const i in data) {
        const item = data[i]
        if (typeof item === 'object' && item instanceof File) {
          formData.append(`${keyPrefix ? `${keyPrefix}.` : ''}${key}[${i}]`, item)
        } else if (typeof item === 'object' && Object.keys(item).includes('originFileObj')) {
          // for ant upload only
          formData.append(`${keyPrefix ? `${keyPrefix}.` : ''}${key}[${i}]`, item.originFileObj)
        } else if (typeof item === 'object' && item instanceof Date) {
          formData.append(`${keyPrefix ? `${keyPrefix}.` : ''}${key}[${i}]`, item.toISOString())
        } else if (typeof item === 'object') {
          convertValuesToFormData(
            formData,
            item,
            keyPrefix ? `${keyPrefix}.${key}[${i}]` : `${key}[${i}]`
          )
        } else {
          formData.append(`${keyPrefix ? `${keyPrefix}.` : ''}${key}[${i}]`, item)
        }
      }
    } else if (typeof data === 'object' && Object.keys(data).includes('originFileObj')) {
      // for ant upload only
      formData.set(`${keyPrefix ? `${keyPrefix}.` : ''}${key}`, data.originFileObj)
    } else if (typeof data === 'object' && data instanceof File) {
      console.log('here')
      formData.set(`${keyPrefix ? `${keyPrefix}.` : ''}${key}`, data)
    } else if (typeof data === 'object' && data instanceof Date) {
      formData.set(`${keyPrefix ? `${keyPrefix}.` : ''}${key}`, data.toISOString())
    } else if (typeof data === 'object') {
      convertValuesToFormData(formData, data, keyPrefix ? `${keyPrefix}.${key}` : key)
    } else {
      formData.set(`${keyPrefix ? `${keyPrefix}.` : ''}${key}`, data)
    }
  }
}

export function bytesToSize(bytes: number) {
  if (bytes === 0) return '0 Bytes'
  const k = 1024,
    sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'],
    i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

export function statusToColor(status: string | boolean) {
  let color = 'bg-primary'

  switch (status) {
    case 'pending':
    case 'draft':
      color = 'bg-muted-foreground'
      break
    case 'created':
      color = 'bg-blue-500'
      break
    case 'proposed':
    case 'pre_processed':
    case 'processing':
    case 'interview_1':
    case 'interview_2':
      color = 'bg-yellow-500'
      break
    case 'published':
    case 'confirmed':
    case 'completed':
    case 'approved':
    case 'passed':
    case true:
      color = 'bg-success'
      break
    case 'rejected':
    case 'canceled':
    case 'failed':
    case false:
      color = 'bg-destructive'
      break
  }

  return color
}

export const getUrlMediaType = (mediaUrl: string) => {
  const urls = mediaUrl.split('.')
  const extname = urls[urls.length - 1]
  const videoExtnames = ['mp4']
  const imageExtnames = ['png', 'jpg', 'jpeg', 'webp']
  const mediaType = imageExtnames.includes(extname)
    ? 'image'
    : videoExtnames.includes(extname)
    ? 'video'
    : undefined

  return mediaType
}

export function useValueKey(value: string | undefined | null): string | number {
  const [prevValue, setPrevValue] = React.useState(value)
  const [key, setKey] = React.useState(0)

  if (value !== prevValue) {
    setPrevValue(value)
    setKey((k) => k + 1)
  }

  return key
}
