import client, { serialize } from '@/network/request'
import { CustomerAnalytics, PurchaseAnalytics } from '@/types/Analytics'
import { DateTime } from 'luxon'

const getCustomerAnalytic = (startsAt?: Date, endsAt?: Date) => {
  return serialize('/store/admin/analytics/customers', {
    starts_at: startsAt?.toISOString(),
    ends_at: endsAt?.toISOString()
  })
}

const exportCustomerAnalytic = (startsAt?: Date, endsAt?: Date) => {
  return client.get<CustomerAnalytics>(
    serialize('/store/admin/analytics/customers', {
      starts_at: startsAt?.toISOString(),
      ends_at: endsAt?.toISOString(),
      export: true
    })
  )
}

const getCampaignWishlistAnaytic = (campaignIds?: string[]) => {
  return serialize('/store/admin/analytics/wishlists', {
    campaign_ids: campaignIds && campaignIds.length > 0 ? campaignIds?.join(',') : undefined
  })
}

const getCampaignsOverview = (startsAt?: DateTime, endsAt?: DateTime) => {
  return serialize('/core/admin/analytics/campaigns', {
    starts_at: startsAt?.toISODate() ?? undefined,
    ends_at: endsAt?.toISODate() ?? undefined
  })
}

const getCampaignAnalytic = (campaignId: string, startsAt?: DateTime, endsAt?: DateTime) => {
  return serialize(`/core/admin/analytics/campaigns/${campaignId}`, {
    starts_at: startsAt?.toISODate() ?? undefined,
    ends_at: endsAt?.toISODate() ?? undefined
  })
}

const exportCampaignAnalytic = (campaignId: string, startsAt?: Date, endsAt?: Date) => {
  return client.get<CustomerAnalytics>(
    serialize(`/core/admin/analytics/campaigns/${campaignId}`, {
      starts_at: startsAt?.toISOString(),
      ends_at: endsAt?.toISOString(),
      export: true
    })
  )
}

const getPurchaseAnalytic = (eventId: number) => {
  return serialize(`/core/admin/analytics/event-purchases`, {
    event_id: eventId
  })
}

const exportPurchaseAnalytic = (event_id: number) => {
  return client.get<PurchaseAnalytics>(
    serialize(`/core/admin/analytics/event-purchases`, { event_id, export_csv: true })
  )
}

const AnalyticService = {
  getCustomerAnalytic,
  exportCustomerAnalytic,
  getCampaignWishlistAnaytic,
  getCampaignsOverview,
  getCampaignAnalytic,
  exportCampaignAnalytic,
  getPurchaseAnalytic,
  exportPurchaseAnalytic
}
export default AnalyticService
