import { CreateRoleInput } from '@/types/Role'
import client, { IDataResponse } from '../request' // , { IDataResponse }

export type RegisterState = {
  email: string
  password: string
  password_confirmation: string
  auth_code: string
}

export type LoginState = {
  email: string
  password: string
  remember_me: boolean
}

export type ResetPasswordState = {
  password: string
  new_password: string
  new_password_confirmation: string
}

export type ForgotPassword = {
  email: string
  password: string
  password_confirmation: string
  auth_code: string
}

export enum LicenseRoles {
  SUPER_ADMIN = 'super_admin',
  ADMIN = 'admin',
  VENDOR = 'vendor',
  SALES = 'sales',
  FINANCE = 'finance',
  MARKETING = 'marketing',
  CREATIVE = 'creative'
}

export type LicenseUser = {
  id: string
  username: string
  email: string
  role: LicenseRoles
  blocked: boolean
}

/// swr
const findMyself = '/license/me'
const findLicenseUsers = '/license/admin/users'
/// swr

/// axios
const activateAdmin = (data: CreateRoleInput) => {
  return client.post('/license/admin/activate-account', data)
}

const updateLicenseUser = (userId: string, data: { blocked: boolean }) => {
  return client.put(`/license/admin/users/${userId}`, data)
}

const bulkBlockLicenseUsers = (data: { ids: string[] }) => {
  return client.post(`/license/admin/users/bulk-block`, data)
}

const softDelete = (id: string) => {
  return client.delete(`/license/admin/users/${id}`)
}
// const verifyUser = (data: Record<string, string>) => {
//   return client.post('/users/available', data)
// }

// const register = (data: RegisterState) => {
//   return client.post('/auth/register', data)
// }

const clientFindMyself = () => {
  return client.get('/license/me')
}

const login = (data: LoginState) => {
  return client.post('/license/login-admin', data)
}

const logout = () => {
  // TODO
  return client.get('/license/me/hello-world?session_logout=true')
}

// const changePassword = (data: ResetPasswordState) => {
//   return client.post('/auth/change-password', data)
// }

// const getAuth = (data: Record<string, string>) => {
//   return client.post('/auth/code', data)
// }

// const verifyAuth = (data: Record<string, string>) => {
//   return client.post('/auth/code/verify', data)
// }

// const forgotPassword = (data: ForgotPassword) => {
//   return client.post('/auth/forgot-password', data)
// }

// const forgotPasswordGetAuth = (data: Record<string, string>) => {
//   return client.post('/auth/forgot-password/code', data)
// }
/// axios

// processing
const toRow = (data: IDataResponse<LicenseUser> | undefined): LicenseUser[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...element,
        key: element.id
      }
    })
  }

  return []
}

const toPaginate = (data: IDataResponse<LicenseUser> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}
// processing

const AuthService = {
  findLicenseUsers,
  login,
  logout,
  activateAdmin,
  updateLicenseUser,
  bulkBlockLicenseUsers,
  softDelete,
  // register,
  // verifyUser,
  // getAuth,
  // verifyAuth,
  // forgotPassword,
  // forgotPasswordGetAuth,
  findMyself,
  clientFindMyself,
  // changePassword,
  toRow,
  toPaginate
}

export default AuthService
