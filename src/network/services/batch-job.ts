import { Batch<PERSON>ob, CreateBatchJobInput } from '@/types/BatchJob'
import client, { IMedusaDataResponse } from '../request'

export interface BatchJobResponse extends IMedusaDataResponse {
  batch_jobs: Array<BatchJob>
}

/// swr
const getBatchJob = (id: string) => `/store/admin/batch-jobs/${id}`
const getBatchJobs = `/store/admin/batch-jobs`
/// swr

/// axios
const createBatchJob = (data: CreateBatchJobInput) => {
  return client.post(`/store/admin/batch-jobs`, data)
}

/// Processing
const toRow = (data: BatchJobResponse | undefined): BatchJob[] => {
  console.log(data)
  if (data?.batch_jobs && data?.batch_jobs?.length > 0) {
    return data?.batch_jobs?.map((element) => {
      return {
        ...element
      }
    })
  }

  return []
}

const toPaginate = (data: BatchJobResponse | undefined) => {
  return {
    total: data?.count ?? 0,
    lastPage: data ? Math.ceil(data?.count / data?.limit) : 1
  }
}
/// Processing

const BatchJobService = {
  getBatchJob,
  getBatchJobs,
  createBatchJob,
  toRow,
  toPaginate
}

export default BatchJobService
