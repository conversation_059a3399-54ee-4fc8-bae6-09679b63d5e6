import { DateTime } from 'luxon'
import client, { IDataResponse } from '../request'
import { CreateEventBoothInput, UpdateBoothInput } from '@/types/Booth'

/// swr
const getBooths = `/core/admin/event-stores`
const getBooth = (id: string) => `/core/admin/event-stores/${id}`
const getBoothOptions = `/core/admin/event-store-options`
const getEligibleBoothStores = (eventId: string | number) =>
  `/core/admin/event/${eventId}/eligible-stores`
/// swr

/// axios
const createBooths = (data: CreateEventBoothInput) => {
  return client.post(`/core/admin/event-stores`, data)
}

const updateBooth = (id: number | string, data: Partial<UpdateBoothInput>) => {
  return client.put(`/core/admin/event-stores/${id}`, data)
}

const deleteBooth = (id: number | string) => {
  return client.delete(`/core/admin/event-stores/${id}`)
}

/// Processing
const toRow = <T>(data: IDataResponse<T> | undefined): T[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element: any) => {
      return {
        ...element,
        key: element.id,
        created_at: element.created_at && DateTime.fromISO(element.created_at as string),
        updated_at: element.updated_at && DateTime.fromISO(element.updated_at as string)
      }
    })
  }

  return []
}

const toPaginate = <T>(data: IDataResponse<T> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}
/// Processing

const BoothService = {
  getBooths,
  getBooth,
  getBoothOptions,
  getEligibleBoothStores,
  createBooths,
  updateBooth,
  deleteBooth,
  toRow,
  toPaginate
}

export default BoothService
