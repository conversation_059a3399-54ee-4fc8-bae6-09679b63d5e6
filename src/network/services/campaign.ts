import { Campaign, CampaignFormField } from '@/types/Campaign'
import client, { IDataResponse, serialize } from '../request'
import {
  CreateCampaignFieldInput,
  CreateCampaignInput,
  UpdateCampaignFieldInput,
  UpdateCampaignInput,
  UpdateCampaignProductFreeGiftsInput
} from '@/types/CreateCampaign'
import { AxiosPromise } from 'axios'

/// swr
const getCampaigns = '/core/admin/campaigns'
const getCampaign = (id: string | number) => `/core/admin/campaigns/${id}`
const getCampaignEventProductOptions = (eventId: string | number) =>
  `/core/admin/event/${eventId}/products`
const getDefaultCampaigns = '/core/vendor/campaigns'
/// swr

/// axios
const bulkImportCampaign = (data: FormData) => {
  return client.post(`/core/admin/campaigns/bulk-import-campaigns`, data)
}

const bulkImportCampaignProduct = (campaignId: number | string, data: FormData) => {
  return client.post(`/core/admin/campaigns/${campaignId}/bulk-import-campaign-products`, data)
}

const createCampaign = (data: CreateCampaignInput | FormData) => {
  return client.post(`/core/admin/campaigns`, data)
}

const updateCampaign = (
  id: string | number,
  data: UpdateCampaignInput | FormData
): AxiosPromise<{ success: boolean; data: Campaign }> => {
  return client.put(`/core/admin/campaigns/${id}`, data)
}

const addOrRemoveCampaignProducts = (
  id: string | number,
  data: UpdateCampaignInput
): AxiosPromise<{ success: boolean; data: Campaign }> => {
  return client.put(`/core/admin/campaigns/${id}/campaign-products`, data)
}

const updateCampaignProduct = (
  id: string | number,
  data: UpdateCampaignProductFreeGiftsInput
): AxiosPromise<{ success: boolean; data: Campaign }> => {
  return client.put(`/core/admin/campaign-products/${id}`, data)
}

const deleteCampaign = (id: string | number) => {
  return client.delete(`/core/admin/campaigns/${id}`)
}

const sortDealCampaign = (id: string | number, data: UpdateCampaignInput) => {
  return client.post(`/core/admin/campaigns/sort/${id}`, data)
}

const createCampaignField = (
  id: string | number,
  data: CreateCampaignFieldInput
): AxiosPromise<{ data: CampaignFormField }> => {
  return client.post(`/core/admin/campaigns/${id}/fields`, data)
}

const updateCampaignField = (
  id: string,
  field_id: string | number,
  data: UpdateCampaignFieldInput
): AxiosPromise<{ data: CampaignFormField }> => {
  return client.put(`/core/admin/campaigns/${id}/fields/${field_id}`, data)
}

const deleteCampaignField = (
  id: string,
  field_id: string | number
): AxiosPromise<{ data: CampaignFormField }> => {
  return client.delete(`/core/admin/campaigns/${id}/fields/${field_id}`)
}

const generateBooklet = (id: string | number) => {
  return client.get(`/core/admin/campaigns/${id}/booklet`)
}

const generateProductSocialMediaImage = (
  id: string | number,
  campaignProductId: string | number
) => {
  return client.get(`/core/admin/campaigns/${id}/social-medias/${campaignProductId}`)
}

const generateProductSocialMediaImageBulk = (id: string | number) => {
  return client.get(`/core/admin/campaigns/${id}/social-medias-bulk`)
}

const getProductSocialMediaImageBulkRequest = (id: string | number) => {
  return serialize(`/pdf/admin/social-medias-bulk`, {
    campaign_id: id
  })
}

const generateCampaignProductCsv = (id: string | number) => {
  return client.post(`/core/admin/campaigns/${id}/product-csv`)
}
/// axios

/// Processing
const toRow = (data: IDataResponse<Campaign> | undefined): Campaign[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...element
      }
    })
  }

  return []
}

const toPaginate = (data: IDataResponse<Campaign> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}
/// Processing

const CampaignService = {
  getCampaigns,
  getCampaign,
  getDefaultCampaigns,
  getCampaignEventProductOptions,
  createCampaign,
  bulkImportCampaign,
  bulkImportCampaignProduct,
  updateCampaign,
  addOrRemoveCampaignProducts,
  updateCampaignProduct,
  deleteCampaign,
  sortDealCampaign,
  createCampaignField,
  updateCampaignField,
  deleteCampaignField,
  generateBooklet,
  generateProductSocialMediaImage,
  generateCampaignProductCsv,
  generateProductSocialMediaImageBulk,
  getProductSocialMediaImageBulkRequest,
  toRow,
  toPaginate
}

export default CampaignService
