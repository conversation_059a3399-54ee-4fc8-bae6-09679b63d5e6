import { IDataResponse } from '../request'
import { CampaignSubmission } from '@/types/CampaignSubmission'

/// swr
const getCampaignSubmissions = (id: string | number) => `/core/admin/campaigns/${id}/submissions`
/// swr

/// Processing
const toRow = (data: IDataResponse<CampaignSubmission> | undefined): CampaignSubmission[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...element
      }
    })
  }

  return []
}

const toPaginate = (data: IDataResponse<CampaignSubmission> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}
/// Processing

const CampaignSubmissionService = {
  getCampaignSubmissions,
  toRow,
  toPaginate
}

export default CampaignSubmissionService
