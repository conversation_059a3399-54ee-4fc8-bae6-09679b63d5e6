import { DateTime } from 'luxon'
import client, { IDataResponse } from '../request'
import { JobApplication } from '@/types/Career'

/// swr
const getJobApplications = '/cms/admin/job-applications'
const getJobApplication = (id: string | number) => `/cms/admin/job-applications/${id}`
/// swr

/// axios
const updateApplication = (id: string | number, data: any) => {
    return client.put(`/cms/admin/job-applications/${id}`, data)
}

const toRow = (data: IDataResponse<JobApplication> | undefined): JobApplication[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...element,
        key: element.id,
        created_at: element.created_at && DateTime.fromISO(element.created_at as string),
        updated_at: element.updated_at && DateTime.fromISO(element.updated_at as string)
      }
    })
  }

  return []
}

const toPaginate = (data: IDataResponse<JobApplication> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}
/// Processing

const CareerService = {
  getJobApplications,
  getJobApplication,
  updateApplication,
  toRow,
  toPaginate
}

export default CareerService
