import { DateTime } from 'luxon'
import client, { IDataResponse } from '../request'
import { Contract, CreateBoothContractInput, CreateBoothInvoiceInput } from '@/types/Contract'

/// swr
const getContracts = `/pdf/admin/contracts`
const getContractByFilters = `/pdf/admin/find-contract-by-filter`
const getContractFormData = (eventId: string | number, storeId: string | number) =>
  `/core/admin/event/${eventId}/store/${storeId}/contract-data`
/// swr

/// axios
const createContract = (data: CreateBoothContractInput) => {
  return client.post(`/pdf/admin/contracts`, data)
}

const updateStatus = (
  id: string | number,
  data: { status: 'pending' | 'accepted' | 'rejected' | 'void' }
) => {
  return client.put(`/pdf/admin/contracts/${id}/status`, data)
}

const updatePaymentFrequency = (id: string | number, data: CreateBoothInvoiceInput) => {
  return client.put(`/pdf/admin/contracts/${id}/payment-frequency`, data)
}

const voidInvoices = (id: string | number) => {
  return client.delete(`/pdf/admin/contracts/${id}/payments`)
}

const download = (filename: string) => {
  return client.get(`/pdf/admin/signed/${filename}`)
}

/// Processing
const toRow = (data: IDataResponse<Contract> | undefined): Contract[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...element,
        key: element.id,
        created_at: element.created_at && DateTime.fromISO(element.created_at as string),
        updated_at: element.updated_at && DateTime.fromISO(element.updated_at as string)
      }
    })
  }

  return []
}

const toPaginate = (data: IDataResponse<Contract> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}
/// Processing

const ContractService = {
  getContracts,
  getContractByFilters,
  getContractFormData,
  createContract,
  updateStatus,
  updatePaymentFrequency,
  voidInvoices,
  download,
  toRow,
  toPaginate
}

export default ContractService
