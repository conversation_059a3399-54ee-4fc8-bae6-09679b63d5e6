import { Customer, CustomerResponse, LicenseCustomer, UpdateCustomerInput } from '@/types/Customer'
import client, { IDataResponse } from '../request'
import { DateTime } from 'luxon'

/// swr
const getLicenseCustomers = '/license/admin/customers'
const getLicenseCustomer = (customerId: string | number) => `/license/admin/customers/${customerId}`
const getCustomers = '/store/admin/customers'
const getCustomer = (customerId: string | number) => `/store/admin/customers/${customerId}`
/// swr

/// axios
const updateCustomer = (customerId: string | number, data: UpdateCustomerInput) => {
  return client.put(`/store/admin/customers/${customerId}`, data)
}
/// axios

/// processing
const toRow = (data: CustomerResponse | undefined): Customer[] => {
  if (data?.customers && data?.customers?.length > 0) {
    return data?.customers?.map((element) => {
      return {
        ...element
      }
    })
  }

  return []
}

const licenseToRow = (data: IDataResponse<LicenseCustomer> | undefined): LicenseCustomer[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...element,
        key: element.id,
        created_at: element.created_at && DateTime.fromISO(element.created_at as string),
        updated_at: element.updated_at && DateTime.fromISO(element.updated_at as string)
      }
    })
  }

  return []
}

const toPaginate = (data: CustomerResponse | undefined) => {
  return {
    total: data?.count ?? 0,
    lastPage: data ? Math.ceil((data?.count ?? 0) / (data?.limit ?? 0)) : 1
  }
}

const licenseToPaginate = (data: IDataResponse<LicenseCustomer> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}
/// processing

const CustomerService = {
  getLicenseCustomers,
  getLicenseCustomer,
  getCustomers,
  getCustomer,
  updateCustomer,
  toRow,
  licenseToRow,
  toPaginate,
  licenseToPaginate
}

export default CustomerService
