import { Child, ChildResponse } from '@/types/CustomerChild'

/// swr
const getChildren = (custId: string) => `/store/admin/customer-children/${custId}`
/// swr

/// axios

/// axios

/// processing
const toPaginate = (data: ChildResponse | undefined) => {
  return {
    total: data?.count ?? 0,
    lastPage: data ? Math.ceil((data?.count ?? 0) / (data?.limit ?? 0)) : 1
  }
}

const toRow = (data: ChildResponse | undefined): Child[] => {
  console.log(data)
  if (data?.children && data?.children?.length > 0) {
    return data?.children?.map((element) => {
      return {
        id: element.id,
        dob: new Date(element.dob).toLocaleDateString(),
        full_name: element.full_name,
        gender: element.gender
      }
    })
  }

  return []
}
/// processing

const CustomerChildService = {
  getChildren,
  toPaginate,
  toRow
}

export default CustomerChildService
