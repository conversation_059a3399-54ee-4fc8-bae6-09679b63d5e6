import { DateTime } from 'luxon'
import client, { IDataResponse, IPostResponse } from '../request'
import { AxiosPromise } from 'axios'
import {
  CreateEventInput,
  SortEventExploreInput,
  UpdateEventExploreInput,
  UpdateEventInfoInput,
  UpdateEventInput
} from '@/types/CreateEvent'
import { Event, EventExplore, EventVisitUs, GeneralEventInfo } from '@/types/Event'
import { CreateVisitUsInput } from '@/types/VisitUs'

/// swr
const getEvents = '/core/admin/events'
const getEvent = (id: string | number) => `/core/admin/events/${id}`
const getEventVisitUs = '/core/admin/visits'
const getEventExplores = '/core/admin/explore-cards'
const getActiveEvent = '/core/admin/default-event'

const getVendorEventProducts = '/core/admin/event-products'
const getVendorEventProductsOptions = '/core/admin/product-options'
/// swr

/// axios

const bulkImportEvent = (data: FormData) => {
  return client.post(`/core/admin/events/bulk-import-events`, data)
}

const createEvent = (data: CreateEventInput): AxiosPromise<IPostResponse<Event>> => {
  return client.post(`/core/admin/events`, data)
}

const createVisitUs = (
  data: Omit<CreateVisitUsInput, 'shuttle_van_service'> & {
    shuttle_van_service: { image_url: string; description: string } | undefined
  }
): AxiosPromise<IPostResponse<Event>> => {
  return client.post('/core/admin/visits', data)
}

const updateVisitUs = (
  id: number,
  data: Omit<CreateVisitUsInput, 'shuttle_van_service'> & {
    shuttle_van_service: { image_url?: string; description: string } | undefined
  }
): AxiosPromise<IPostResponse<Event>> => {
  return client.put(`/core/admin/visits/${id}`, data)
}

const createExplore = (data: FormData): AxiosPromise<IPostResponse<EventExplore>> => {
  return client.post('/core/admin/explore-cards', data)
}

const updateExplore = (id: number, data: FormData): AxiosPromise<IPostResponse<EventExplore>> => {
  return client.put(`/core/admin/explore-cards/${id}`, data)
}

const updateEvent = (
  id: string | number,
  data: UpdateEventInput
): AxiosPromise<IPostResponse<Event>> => {
  return client.put(`/core/admin/events/${id}`, data)
}

const updateEventStatus = (id: number) => {
  return client.put(`/core/admin/selected-event/${id}`)
}

const createEventInfo = (eventId: number, data: UpdateEventInfoInput | FormData) => {
  const payload: any = data
  payload.append('event_id', eventId)
  return client.post(`/core/admin/event-info`, payload)
}

const updateEventInfo = (eventInfoId: number, data: UpdateEventInfoInput | FormData) => {
  // const apiBody = omit(data, ['image_file'])

  return client.put(`/core/admin/event-info/${eventInfoId}`, data)
}

const updateEventExplore = (eventId: number, data: UpdateEventExploreInput) => {
  return client.put(`/core/admin/events/${eventId}/event-explores`, data)
}

const sortEventExplore = (eventId: number, data: SortEventExploreInput) => {
  return client.post(`/core/admin/events/${eventId}/event-explores/sort`, data)
}

const updateEventVisit = (
  eventId: number,
  data: { visit_us: number }
): AxiosPromise<IPostResponse<EventVisitUs>> => {
  return client.put(`/core/admin/event/${eventId}/event-visits`, data)
}

const updateVendorEventProducts = (data: number[]) => {
  return client.post(`/core/admin/event-products`, { products: data })
}

const joinEvent = () => {
  return client.post('/core/admin/join-event')
}
/// axios

/// Processing
const toEventObj = (data: { data: Event } | undefined): Event | null => {
  // Process to simplified Event object
  if (data?.data) {
    const response = data?.data

    return {
      ...response,
      from:
        response.from && DateTime.fromISO(response.from as string).toFormat('yyyy-MM-dd HH:mm:ss'),
      to: response.to && DateTime.fromISO(response.to as string).toFormat('yyyy-MM-dd HH:mm:ss'),
      cut_off:
        response.cut_off &&
        DateTime.fromISO(response.cut_off as string).toFormat('yyyy-MM-dd HH:mm:ss'),
      created_at:
        response.created_at &&
        DateTime.fromISO(response.created_at as string).toFormat('yyyy-MM-dd HH:mm:ss'),
      updated_at:
        response.updated_at &&
        DateTime.fromISO(response.updated_at as string).toFormat('yyyy-MM-dd HH:mm:ss')
    }
  }

  return null
}

const toRow = (data: IDataResponse<GeneralEventInfo> | undefined): GeneralEventInfo[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...element,
        key: element.id,
        from: element.from && DateTime.fromISO(element.from as string),
        to: element.to && DateTime.fromISO(element.to as string),
        created_at: element.created_at && DateTime.fromISO(element.created_at as string),
        updated_at: element.updated_at && DateTime.fromISO(element.updated_at as string)
      }
    })
  }

  return []
}

const toPaginate = (data: IDataResponse<GeneralEventInfo> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}

const eventDurationString = (event: GeneralEventInfo | undefined): string => {
  const fromDate = DateTime.fromISO(event?.from as string).toFormat('dd')
  const toDate = DateTime.fromISO(event?.to as string).toFormat('dd LLL yyyy')

  return `${fromDate} - ${toDate} @ ${event?.location}`
}
/// Processing

const EventService = {
  getEvents,
  getEvent,
  getEventVisitUs,
  getEventExplores,
  getActiveEvent,
  getVendorEventProducts,
  getVendorEventProductsOptions,
  createEvent,
  bulkImportEvent,
  updateEvent,
  updateEventStatus,
  createVisitUs,
  updateVisitUs,
  createExplore,
  updateExplore,
  createEventInfo,
  updateEventInfo,
  updateEventExplore,
  sortEventExplore,
  updateEventVisit,
  updateVendorEventProducts,
  joinEvent,
  toEventObj,
  toRow,
  toPaginate,
  eventDurationString
}

export default EventService
