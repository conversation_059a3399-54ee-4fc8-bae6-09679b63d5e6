import { IMedusaDataResponse } from '../request'
import { EventProduct } from '@/types/Event'

export interface EventProductResponse extends IMedusaDataResponse {
  event_products: Array<EventProduct>
}

/// swr
const getEventProduct = (id: string | number) => `/core/admin/event-products/${id}`
/// swr

/// axios
/// axios

/// Processing
const toRow = (data: EventProductResponse | undefined): EventProduct[] => {
  if (data?.event_products && data?.event_products?.length > 0) {
    return data?.event_products?.map((element) => {
      return {
        ...element
      }
    })
  }

  return []
}

const toPaginate = (data: EventProductResponse | undefined) => {
  return {
    total: data?.count ?? 0,
    lastPage: data ? Math.ceil(data?.count / data?.limit) : 1
  }
}
/// Processing

const EventProductService = {
  getEventProduct,
  toRow,
  toPaginate
}

export default EventProductService
