import { EventPurchase, UpdateEventPurchaseStatusInput } from '@/types/EventPurchase'
import client, { IDataResponse } from '../request'

/// swr
const getEventPurchases = (id?: number | string) => `/core/admin/event-purchases/event/${id}`
const getEventPurchase = (id?: number | string) => `/core/admin/event-purchases/${id}`
/// swr

// axios
const updateEventPurchaseStatus = (
  eventPurchaseId: string | number,
  data: UpdateEventPurchaseStatusInput
) => {
  return client.put(`core/admin/event-purchases/${eventPurchaseId}`, data)
}
// axios

/// Processing
const toRow = (data: IDataResponse<EventPurchase> | undefined): EventPurchase[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...element
      }
    })
  }

  return []
}

const toPaginate = (data: IDataResponse<EventPurchase> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}
/// Processing

const EventPurchaseService = {
  getEventPurchase,
  getEventPurchases,
  updateEventPurchaseStatus,
  toRow,
  toPaginate
}

export default EventPurchaseService
