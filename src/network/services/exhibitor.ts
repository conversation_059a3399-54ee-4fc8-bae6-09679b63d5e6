import client, { IPostResponse } from '@/network/request'
import { Exhibitor, ExhibitorResponse, ExhibitorSignupFormType } from '@/types/Exhibitor'
import { AxiosPromise } from 'axios'
import { omit } from 'radash'

/// swr
const getStore = (storeId: string | number) => `/core/admin/stores/${storeId}`

const getExhibitors = `/store/admin/exhibitors`
const getExhibitor = (exhibitorId: string | number) => `/store/admin/exhibitors/${exhibitorId}`
/// swr

/// Axios
const exhibitorSignup = (data: ExhibitorSignupFormType): AxiosPromise<IPostResponse<Exhibitor>> => {
  // Processing to combime mobile prefix & postfix
  const tempBody: ExhibitorSignupFormType = {
    ...data,
    contact_mobile_number: `${data.contact_mobile_prefix}-${data.contact_mobile_postfix}`,
    finance_mobile_number: `${data.finance_mobile_prefix}-${data.finance_mobile_postfix}`,
    marketing_mobile_number: `${data.marketing_mobile_prefix}-${data.marketing_mobile_postfix}`
  }

  // Omit unwanted fields
  const apiBody = omit(tempBody, [
    'contact_mobile_prefix',
    'contact_mobile_postfix',
    'finance_mobile_prefix',
    'finance_mobile_postfix',
    'marketing_mobile_prefix',
    'marketing_mobile_postfix'
  ])

  return client.post(`/store/admin/exhibitors/register`, apiBody)
}

const updateExhibitor = (storeID: string, data: Exhibitor) => {
  return client.put(`/store/admin/exhibitors/${storeID}`, data)
}

const exhibitorApproval = (storeID: string, approved: boolean) => {
  return client.post('/store/admin/exhibitors/approval', {
    store_id: storeID,
    approve: approved
  })
}

const deleteExhibitor = (storeID: string) => {
  return client.delete(`/store/admin/exhibitors/${storeID}`)
}

/// Axios

/// Processing
const toRow = (data: ExhibitorResponse | undefined): Exhibitor[] => {
  if (data?.stores && data?.stores?.length > 0) {
    return data?.stores?.map((element) => {
      return {
        ...element
      }
    })
  }

  return []
}

const toPaginate = (data: ExhibitorResponse | undefined) => {
  return {
    total: data?.count ?? 0,
    lastPage: data ? Math.ceil((data?.count ?? 0) / (data?.limit ?? 0)) : 1
  }
}
/// Processing

const ExhibitorService = {
  getStore,
  getExhibitors,
  getExhibitor,
  exhibitorSignup,
  updateExhibitor,
  deleteExhibitor,
  exhibitorApproval,
  toRow,
  toPaginate
}

export default ExhibitorService
