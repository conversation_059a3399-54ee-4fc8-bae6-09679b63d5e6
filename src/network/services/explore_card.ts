import { DateTime } from 'luxon'
import client, { IDataResponse, IPostResponse } from '../request'
import { AxiosPromise } from 'axios'
import {
  CreateExploreInput,
  ExploreCard,
  UpdateExploreInput
} from '@/types/ExploreCard'

/// swr
const getExploreCards = '/core/admin/explore-cards'
/// swr

/// axios

const createExploreCard = (data: CreateExploreInput): AxiosPromise<IPostResponse<ExploreCard>> => {
  return client.post(`/core/admin/explore-cards`, data)
}

const updateExploreCard = (
  id: string | number,
  data: UpdateExploreInput
): AxiosPromise<IPostResponse<ExploreCard>> => {
  return client.put(`/core/admin/explore-cards/${id}`, data)
}

const deleteExploreCard = (id: string | number): AxiosPromise<IPostResponse<ExploreCard>> => {
  return client.delete(`/core/admin/explore-cards/${id}`)
}

/// axios

/// Processing

const toRow = (data: IDataResponse<ExploreCard> | undefined): ExploreCard[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...element,
        key: element.id,
        created_at: element.created_at && DateTime.fromISO(element.created_at as string),
        updated_at: element.updated_at && DateTime.fromISO(element.updated_at as string)
      }
    })
  }

  return []
}

const toPaginate = (data: IDataResponse<ExploreCard> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}

/// Processing

const ExploreCardService = {
  getExploreCards,
  createExploreCard,
  updateExploreCard,
  deleteExploreCard,
  toRow,
  toPaginate
}

export default ExploreCardService
