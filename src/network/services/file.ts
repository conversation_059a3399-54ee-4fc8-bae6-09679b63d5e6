import { AxiosPromise } from 'axios'
import client, { IPostResponse } from '../request'

export type FileResponse = {
  key: string
  url: string
}

/// swr
/// swr

/// axios
// upload multiples (public)
const uploadPrivate = (data: File[]): AxiosPromise<{ uploads: FileResponse[] }> => {
  const formData = new FormData()

  data.forEach((d) => {
    formData.append('files', d)
  })

  return client.post('/store/admin/uploads/protected', formData)
}

const upload = (data: File[]): AxiosPromise<{ uploads: FileResponse[] }> => {
  const formData = new FormData()

  data.forEach((d) => {
    formData.append('files', d)
  })

  return client.post('/core/admin/uploads', formData)
}

// upload single (public)
const uploadImage = (data: FormData): AxiosPromise<IPostResponse<FileResponse>> => {
  return client.post('/core/admin/upload', data)
}

const getDownloadUrl = (file_key: string) => {
  return client.post('/store/admin/uploads/download-url', { file_key })
}
/// axios

/// Processing
/// Processing

const FileService = {
  uploadPrivate,
  upload,
  uploadImage,
  getDownloadUrl
}

export default FileService
