import client, { IDataResponse } from '../request'

export enum HospitalType {
  PUBLIC = 'public',
  PRIVATE = 'private'
}

export type Hospital = {
  id: number
  title: string
  type: HospitalType
}

/// swr
const getHospitals = '/cms/admin/hospitals'
/// swr

/// axios
const createHospitals = (data: { title: string[]; type: HospitalType }) => {
  return client.post(`/cms/admin/hospitals`, data)
}

const updateHospital = (
  key: string | number,
  data: {
    title: string
    type: HospitalType
  }
) => {
  return client.put(`/cms/admin/hospitals/${key}`, data)
}

const deleteHospital = (key: number | string) => {
  return client.delete(`/cms/admin/hospitals/${key}`)
}
/// axios

/// processing
const toRow = (data: IDataResponse<Hospital> | undefined): Hospital[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...element
      }
    })
  }

  return []
}

const toPaginate = (data: IDataResponse<Hospital> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}
/// processing

const HospitalService = {
  getHospitals,
  createHospitals,
  updateHospital,
  deleteHospital,
  toRow,
  toPaginate
}

export default HospitalService
