import { Inquiry } from '@/types/Inquiry'
import client, { IMedusaDataResponse } from '../request'
export interface InquiryResponse extends IMedusaDataResponse {
  data: Array<Inquiry>
}

/// swr
const getInquiries = `/cms/admin/inquiries`
const getInquiry = (id: string) => `/cms/admin/inquiries/${id}`
/// swr

/// axios
const deleteInquiry = (id: string | number) => {
  return client.delete(`/cms/admin/inquiries/${id}`)
}
/// axios

/// Processing
const toRow = (data: InquiryResponse | undefined): Inquiry[] => {
  if (data?.data && data?.data.length > 0) {
    return data?.data.map((element) => {
      return {
        ...element
      }
    })
  }

  return []
}

const toPaginate = (data: InquiryResponse | undefined) => {
  return {
    total: data?.count ?? 0,
    lastPage: data ? Math.ceil(data?.count / data?.limit) : 1
  }
}
/// Processing

const InquiryService = {
  getInquiries,
  getInquiry,
  deleteInquiry,
  toRow,
  toPaginate
}

export default InquiryService
