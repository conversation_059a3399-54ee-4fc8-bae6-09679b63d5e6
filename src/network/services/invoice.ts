import { DateTime } from 'luxon'
import client, { IDataResponse } from '../request'
import { GenerateInvoice, Invoice } from '@/types/Contract'

/// swr
const getInvoices = `/pdf/admin/invoices`
/// swr

/// axios
const generateNextInvoice = (contractId: string | number, data: GenerateInvoice) => {
  return client.post(`/pdf/admin/contract/${contractId}/invoices`, data)
}

const updateStatus = (id: string | number, data: { status: 'paid' | 'pending' }) => {
  return client.put(`/pdf/admin/invoices/${id}/status`, data)
}

/// Processing
const toRow = (data: IDataResponse<Invoice> | undefined): Invoice[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...element,
        key: element.id,
        created_at: element.created_at && DateTime.fromISO(element.created_at as string),
        updated_at: element.updated_at && DateTime.fromISO(element.updated_at as string)
      }
    })
  }

  return []
}

const toPaginate = (data: IDataResponse<Invoice> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}
/// Processing

const InvoiceService = {
  getInvoices,
  generateNextInvoice,
  updateStatus,
  toRow,
  toPaginate
}

export default InvoiceService
