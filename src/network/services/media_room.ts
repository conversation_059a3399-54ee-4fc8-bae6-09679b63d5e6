import {
  CreateApplicationForm<PERSON>ieldInput,
    CreateFaqGroupInput,
  CreateFaqInput,
  CreateJobCategoryInput,
  CreateJobInput,
  CreateSearchInput,
  SortApplicationFormFieldInput,
  SortFaqInput,
  SortFooterInput,
  SortJobCategoryInput,
  SortJobInput,
  SortSearchInput,
  UpdateApplicationFormFieldInput,
  UpdateFaqGroupInput,
  UpdateFaqInput,
  UpdateFooterInput,
  UpdateJobCategoryInput,
  UpdateJobInput,
  UpdatePageCmsInput,
  UpdateSearchInput
} from '@/types/Cms'
import client, { IDataResponse } from '../request'

// swr
const findPage = (slug: string) => `/cms/admin/pages/slug/${slug}`
const findFaqs = `/cms/admin/faqs`
const findFaqGroups = `/cms/admin/faq-groups`
const findSearches = `/cms/admin/searches`
const findFooters = `/cms/admin/footers`
const findJobCategories = `/cms/admin/job-categories`
const findJobs = `/cms/admin/jobs`
const findApplicationFormFields = `/cms/admin/form-fields`
// swr

// axios
const updatePage = (id: string | number, data: UpdatePageCmsInput) => {
  return client.put(`/cms/admin/pages/${id}`, data)
}

const createFaqGroup = (data: CreateFaqGroupInput) => {
  return client.post(`/cms/admin/faq-groups`, data)
}

const updateFaqGroup = (id: string | number, data: UpdateFaqGroupInput) => {
  return client.put(`/cms/admin/faq-groups/${id}`, data)
}

const sortFaqGroup = (data: SortFaqInput) => {
  return client.post(`/cms/admin/faq-groups/sort`, data)
}

const deleteFaqGroup = (id: string | number) => {
  return client.delete(`/cms/admin/faq-groups/${id}`)
}

const createFaq = (data: CreateFaqInput) => {
  return client.post(`/cms/admin/faqs`, data)
}

const updateFaq = (id: string | number, data: UpdateFaqInput) => {
  return client.put(`/cms/admin/faqs/${id}`, data)
}

const sortFaq = (data: SortFaqInput) => {
  return client.post(`/cms/admin/faqs/sort`, data)
}

const deleteFaq = (id: string | number) => {
  return client.delete(`/cms/admin/faqs/${id}`)
}

const createSearch = (data: CreateSearchInput) => {
  return client.post(`/cms/admin/searches`, data)
}

const updateSearch = (id: string | number, data: UpdateSearchInput) => {
  return client.put(`/cms/admin/searches/${id}`, data)
}

const sortSearch = (data: SortSearchInput) => {
  return client.post(`/cms/admin/searches/sort`, data)
}

const deleteSearch = (id: string | number) => {
  return client.delete(`/cms/admin/searches/${id}`)
}

const createFooter = (data: CreateSearchInput) => {
  return client.post(`/cms/admin/footers`, data)
}

const updateFooter = (id: string | number, data: UpdateFooterInput) => {
  return client.put(`/cms/admin/footers/${id}`, data)
}

const sortFooter = (data: SortFooterInput) => {
  return client.post(`/cms/admin/footers/sort`, data)
}

const deleteFooter = (id: string | number) => {
  return client.delete(`/cms/admin/footers/${id}`)
}

const createJobCategory = (data: CreateJobCategoryInput) => {
  return client.post(`/cms/admin/job-categories`, data)
}

const updateJobCategory = (id: string | number, data: UpdateJobCategoryInput) => {
  return client.put(`/cms/admin/job-categories/${id}`, data)
}

const sortJobCategory = (data: SortJobCategoryInput) => {
  return client.post(`/cms/admin/job-categories/sort`, data)
}

const deleteJobCategory = (id: string | number) => {
  return client.delete(`/cms/admin/job-categories/${id}`)
}

const createJob = (data: CreateJobInput) => {
  return client.post(`/cms/admin/jobs`, data)
}

const updateJob = (id: string | number, data: UpdateJobInput) => {
  return client.put(`/cms/admin/jobs/${id}`, data)
}

const sortJob = (data: SortJobInput) => {
  return client.post(`/cms/admin/jobs/sort`, data)
}

const deleteJob = (id: string | number) => {
  return client.delete(`/cms/admin/jobs/${id}`)
}

const createApplicationFormField = (data: CreateApplicationFormFieldInput) => {
  return client.post(`/cms/admin/form-fields`, data)
}

const updateApplicationFormField = (id: string | number, data: UpdateApplicationFormFieldInput) => {
  return client.put(`/cms/admin/form-fields/${id}`, data)
}

const sortApplicationFormField = (data: SortApplicationFormFieldInput) => {
  return client.post(`/cms/admin/form-fields/sort`, data)
}

const deleteApplicationFormField = (id: string | number) => {
  return client.delete(`/cms/admin/form-fields/${id}`)
}
// axios

/// Processing
const toRow = <T>(data: IDataResponse<T> | undefined): T[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...element
      }
    })
  }

  return []
}

const toPaginate = <T>(data: IDataResponse<T> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}
/// Processing

const MediaRoomService = {
  findPage,
  findFaqs,
  findFaqGroups,
  findSearches,
  findFooters,
  findJobCategories,
  findJobs,
  findApplicationFormFields,
  updatePage,
  createFaqGroup,
  updateFaqGroup,
  sortFaqGroup,
  deleteFaqGroup,
  createFaq,
  updateFaq,
  sortFaq,
  deleteFaq,
  createSearch,
  updateSearch,
  sortSearch,
  deleteSearch,
  createFooter,
  updateFooter,
  sortFooter,
  deleteFooter,
  createJobCategory,
  updateJobCategory,
  sortJobCategory,
  deleteJobCategory,
  createJob,
  updateJob,
  sortJob,
  deleteJob,
  createApplicationFormField,
  updateApplicationFormField,
  sortApplicationFormField,
  deleteApplicationFormField,
  toRow,
  toPaginate
}

export default MediaRoomService
