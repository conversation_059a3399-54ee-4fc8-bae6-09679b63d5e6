import client, { IPostResponse } from '@/network/request'
import { AxiosPromise } from 'axios'
import { omit } from 'radash'
import { DateTime } from 'luxon'

export type MemberSignupFormType = {
  current_stage: string
  first_name: string
  last_name: string
  email: string
  password: string
  password_confirmation: string
  mobile_prefix: string
  mobile_postfix: string
  mobile_number: string
  dob: DateTime
  gender: string
  race: string
  expected_delivery_date: string
  hospital_of_delivery: string
  no_of_child: number
  child_dob: string[]
  tnc1: boolean
  tnc2: boolean
}

export type Member = {
  id: string
  billing_address_id: string | null
  current_stage: string
  first_name: string
  last_name: string
  dob: string
  email: string
  mobile_number: string
  expected_delivery_date: string | DateTime
  gender: string
  has_account: boolean
  hospital_of_delivery: string
  no_of_child: number
  child_dob: string[] | DateTime[]
  race: string
  phone: string | null
  deleted_at: string | DateTime
  metadata: any | null
  created_at: string | DateTime
  updated_at: string | DateTime
}

/// swr
/// swr

/// Axios
const memberSignup = (data: MemberSignupFormType): AxiosPromise<IPostResponse<Member>> => {
  // Processing to combime mobile prefix & postfix
  const tempBody: MemberSignupFormType = {
    ...data,
    mobile_number: `${data.mobile_prefix}-${data.mobile_postfix}`,
    expected_delivery_date: DateTime.fromISO(data.expected_delivery_date).toISO() ?? '',
    child_dob: data.child_dob.map(
      (dob, index) => (data.child_dob[index] = DateTime.fromISO(dob).toISO() ?? '')
    )
  }

  // Omit unwanted fields
  const apiBody = omit(tempBody, ['mobile_prefix', 'mobile_postfix', 'tnc1', 'tnc2'])

  return client.post(`/store/customers/register`, apiBody)
}
/// Axios

const MemberService = {
  memberSignup
}

export default MemberService
