import client, { IDataResponse } from '../request'
import { BulkCreateMemoryInput, CreateMemoryInput, UpdateMemoryInput } from '@/types/Memory'

// swr
const findMemory = (id: string | number) => `/cms/admin/memories/${id}`
const findMemories = `/cms/admin/memories`
// swr

// axios
const getMemory = (id: string | number) => {
  return client.get(`/cms/admin/memories/${id}`)
}

const createMemory = (data: CreateMemoryInput) => {
  const formData = new FormData()
  data.name && formData.append('name', data.name)
  data.thumbnail_file && formData.append('thumbnail_file', data.thumbnail_file)
  data.thumbnail_url && formData.append('thumbnail_url', data.thumbnail_url)
  data.all_rank && formData.append('all_rank', data.all_rank + '')
  formData.append('collection_rank', data.collection_rank + '')

  return client.post(`/cms/admin/memories`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

const bulkCreateMemory = (data: BulkCreateMemoryInput) => {
  const formData = new FormData()
  data.memory_thumbnail_files?.forEach((file) => {
    formData.append('memory_thumbnail_files[]', file)
  })
  data.memory_media_files?.forEach((file) => {
    formData.append('memory_media_files[]', file)
  })
  data.memory_collection_id &&
    formData.append('memory_collection_id', data.memory_collection_id + '')

  return client.post(`/cms/admin/memories/bulk`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

const updateMemory = (id: string | number, data: UpdateMemoryInput) => {
  const formData = new FormData()
  data.name && formData.append('name', data.name)
  data.thumbnail_file && formData.append('thumbnail_file', data.thumbnail_file)
  data.thumbnail_url && formData.append('thumbnail_url', data.thumbnail_url)
  data.all_rank && formData.append('all_rank', data.all_rank + '')
  formData.append('collection_rank', data.collection_rank + '')

  return client.put(`/cms/admin/memories/${id}`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

const deleteMemory = (id: string | number) => {
  return client.delete(`/cms/admin/memories/${id}`)
}
// axios

/// Processing
const toRow = <T>(data: IDataResponse<T> | undefined): T[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...element
      }
    })
  }

  return []
}

const toPaginate = <T>(data: IDataResponse<T> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}
/// Processing

const MemoryService = {
  findMemory,
  findMemories,
  getMemory,
  createMemory,
  bulkCreateMemory,
  updateMemory,
  deleteMemory,
  toRow,
  toPaginate
}

export default MemoryService
