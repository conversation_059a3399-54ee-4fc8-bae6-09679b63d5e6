import { CreateMemoryCollectionInput, UpdateMemoryCollectionInput } from '@/types/MemoryCollection'
import client, { IDataResponse } from '../request'

// swr
const findMemoryCollection = (id: string | number) => `/cms/admin/memory-collections/${id}`
const findMemoryCollections = `/cms/admin/memory-collections`
// swr

// axios
const getMemoryCollection = (id: string | number) => {
  return client.get(`/cms/admin/memory-collections/${id}`)
}

const createMemoryCollection = (data: CreateMemoryCollectionInput) => {
  return client.post(`/cms/admin/memory-collections`, data)
}

const updateMemoryCollection = (id: string | number, data: UpdateMemoryCollectionInput) => {
  return client.put(`/cms/admin/memory-collections/${id}`, data)
}

const deleteMemoryCollection = (id: string | number) => {
  return client.delete(`/cms/admin/memory-collections/${id}`)
}
// axios

/// Processing
const toRow = <T>(data: IDataResponse<T> | undefined): T[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...element
      }
    })
  }

  return []
}

const toPaginate = <T>(data: IDataResponse<T> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}
/// Processing

const MemoryCollectionService = {
  findMemoryCollection,
  findMemoryCollections,
  getMemoryCollection,
  createMemoryCollection,
  updateMemoryCollection,
  deleteMemoryCollection,
  toRow,
  toPaginate
}

export default MemoryCollectionService
