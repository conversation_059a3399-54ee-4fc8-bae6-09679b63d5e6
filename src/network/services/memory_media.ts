import { CreateMemoryMediaInput, UpdateMemoryMediaInput } from '@/types/MemoryMedia'
import client, { IDataResponse } from '../request'

// swr
const findMemoryMedia = (id: string | number) => `/cms/admin/memory-medias/${id}`
const findMemoryMedias = `/cms/admin/memory-medias`
// swr

// axios
const createMemoryMedia = (data: CreateMemoryMediaInput) => {
  const formData = new FormData()
  data.media_url && formData.append('media_url', data.media_url)
  data.media_file && formData.append('media_file', data.media_file)
  formData.append('memory_id', data.memory_id + '')
  formData.append('type', data.type)
  formData.append('status', data.status + '')

  return client.post(`/cms/admin/memory-medias`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

const updateMemoryMedia = (id: string | number, data: UpdateMemoryMediaInput) => {
  const formData = new FormData()
  data.media_url && formData.append('media_url', data.media_url)
  data.media_file && formData.append('media_file', data.media_file)
  formData.append('memory_id', data.memory_id + '')
  data.type && formData.append('type', data.type)
  data.status && formData.append('status', data.status + '')
  return client.put(`/cms/admin/memory-medias/${id}`, data, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

const deleteMemoryMedia = (id: string | number) => {
  return client.delete(`/cms/admin/memory-medias/${id}`)
}
// axios

/// Processing
const toRow = <T>(data: IDataResponse<T> | undefined): T[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...element
      }
    })
  }

  return []
}

const toPaginate = <T>(data: IDataResponse<T> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}
/// Processing

const MemoryMediaService = {
  findMemoryMedia,
  findMemoryMedias,
  createMemoryMedia,
  updateMemoryMedia,
  deleteMemoryMedia,
  toRow,
  toPaginate
}

export default MemoryMediaService
