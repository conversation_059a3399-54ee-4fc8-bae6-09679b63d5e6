import { Address, Customer, Region } from '@/types/Customer'
import { Currency, ProductVariant } from '@/types/Product'

export enum OrderStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  ARCHIVED = 'archived',
  CANCELED = 'canceled',
  REQUIRES_ACTION = 'requires_action'
}

export enum FulfillmentStatus {
  NOT_FULFILLED = 'not_fulfilled',
  PARTIALLY_FULFILLED = 'partially_fulfilled',
  FULFILLED = 'fulfilled',
  PARTIALLY_SHIPPED = 'partially_shipped',
  SHIPPED = 'shipped',
  PARTIALLY_RETURNED = 'partially_returned',
  RETURNED = 'returned',
  CANCELED = 'canceled',
  REQUIRES_ACTION = 'requires_action'
}

export enum PaymentStatus {
  NOT_PAID = 'not_paid',
  AWAITING = 'awaiting',
  CAPTURED = 'captured',
  PARTIALLY_REFUNDED = 'partially_refunded',
  REFUNDED = 'refunded',
  CANCELED = 'canceled',
  REQUIRES_ACTION = 'requires_action'
}

export type Order = {
  status: OrderStatus
  fulfillment_status: FulfillmentStatus
  payment_status: PaymentStatus
  display_id: number
  cart_id: string
  //   cart: Cart
  customer_id: string
  customer: Customer
  email: string
  billing_address_id: string
  billing_address: Address
  shipping_address_id: string
  shipping_address: Address
  region_id: string
  region: Region
  currency_code: string
  currency: Currency
  tax_rate: number | null
  //   discounts: Discount[]
  //   gift_cards: GiftCard[]
  //   shipping_methods: ShippingMethod[]
  //   payments: Payment[]
  //   fulfillments: Fulfillment[]
  //   returns: Return[]
  //   claims: ClaimOrder[]
  //   refunds: Refund[]
  //   swaps: Swap[]
  draft_order_id: string
  //   draft_order: DraftOrder
  //   edits: OrderEdit[]
  items: LineItem[]
  //   gift_card_transactions: GiftCardTransaction[]
  canceled_at: Date
  metadata: Record<string, unknown>
  no_notification: boolean
  idempotency_key: string
  external_id: string | null
  sales_channel_id: string | null
  //   sales_channel: SalesChannel
  shipping_total: number
  discount_total: number
  raw_discount_total: number
  tax_total: number | null
  refunded_total: number
  total: number
  subtotal: number
  paid_total: number
  refundable_amount: number
  gift_card_total: number
  gift_card_tax_total: number
  returnable_items?: LineItem[]
}

export type LineItem = {
  cart_id: string
  //   cart: Cart
  order_id: string | null
  order: Order
  swap_id: string
  //   swap: Swap
  claim_order_id: string
  //   claim_order: ClaimOrder
  //   tax_lines: LineItemTaxLine[]
  //   adjustments: LineItemAdjustment[]
  original_item_id?: string | null
  order_edit_id?: string | null
  //   order_edit?: OrderEdit | null
  title: string
  description: string | null
  thumbnail: string | null
  is_return: boolean
  is_giftcard: boolean
  should_merge: boolean
  allow_discounts: boolean
  has_shipping: boolean | null
  unit_price: number
  variant_id: string | null
  variant: ProductVariant
  product_id: string | null
  quantity: number
  fulfilled_quantity: number | null
  returned_quantity: number | null
  shipped_quantity: number | null
  metadata: Record<string, unknown>
  includes_tax: boolean
  refundable?: number | null
  subtotal?: number | null
  tax_total?: number | null
  total?: number | null
  original_total?: number | null
  original_tax_total?: number | null
  discount_total?: number | null
  raw_discount_total?: number | null
  gift_card_total?: number | null
}
