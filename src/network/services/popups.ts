import client, { IDataResponse } from '@/network/request'
import { CreatePopUp, PopUp } from '@/types/Cms'

const findPopUps = `/cms/admin/pop-ups`

const findOnePopUp = (id: string | number) => {
  return `/cms/admin/pop-ups/${id}`
}

//axios
const createPopUp = (data: CreatePopUp) => {
  return client.post(`/cms/admin/pop-ups`, data)
}

const updatePopUp = (id: string | number, data: CreatePopUp) => {
  return client.put(`/cms/admin/pop-ups/${id}`, data)
}

const deletePopUp = (id: string | number) => {
  return client.delete(`/cms/admin/pop-ups/${id}`)
}

/// Processing
const toRow = (data: IDataResponse<PopUp> | undefined) => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...element
      }
    })
  }

  return []
}

const toPaginate = (data: IDataResponse<PopUp> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}
/// Processing

const PopUpService = {
  findPopUps,
  findOnePopUp,
  createPopUp,
  updatePopUp,
  deletePopUp,
  toRow,
  toPaginate
}

export default PopUpService
