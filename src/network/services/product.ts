import { Product } from '@/types/Product'
import client, { IMedusaDataResponse } from '../request'
import {
  CreateProductInput,
  UpdateProductInput,
  UpdateProductProductVariantDTO
} from '@/types/CreateProduct'
import { AxiosPromise } from 'axios'

export interface ProductResponse extends IMedusaDataResponse {
  products: Array<Product>
}

/// swr
const getProducts = '/store/admin/products'
const getProduct = (id: string | number) => `/store/admin/products/${id}`

const getProductTypes = `/store/admin/product-types`
const getPriceLists = `/store/admin/price-lists`
/// swr

/// axios
const createProduct = (data: CreateProductInput) => {
  return client.post(`/store/admin/products`, data)
}

const updateProduct = (
  id: string,
  data: UpdateProductInput
): AxiosPromise<{ product: Product }> => {
  return client.post(`/store/admin/products/${id}`, data)
}

const updatePriceList = (id: string, data: any) => {
  return client.post(`/store/admin/price-lists/${id}`, data)
}

const deleteProduct = (id: string) => {
  return client.delete(`/store/admin/products/${id}`)
}

const createProductVariant = (id: string, values: UpdateProductProductVariantDTO) => {
  return client.post(`/store/admin/products/${id}/variants`, values)
}

const updateProductVariant = (
  id: string,
  variantId: string,
  values: UpdateProductProductVariantDTO
) => {
  return client.post(`/store/admin/products/${id}/variants/${variantId}`, values)
}

const deleteProductVariant = (id: string, variantId: string) => {
  return client.delete(`/store/admin/products/${id}/variants/${variantId}`)
}
/// axios

/// Processing
const toRow = (data: ProductResponse | undefined): Product[] => {
  if (data?.products && data?.products?.length > 0) {
    return data?.products?.map((element) => {
      return {
        ...element
      }
    })
  }

  return []
}

const toPaginate = (data: ProductResponse | undefined) => {
  return {
    total: data?.count ?? 0,
    lastPage: data ? Math.ceil(data?.count / data?.limit) : 1
  }
}
/// Processing

const ProductService = {
  getProducts,
  getProduct,
  getProductTypes,
  getPriceLists,
  createProduct,
  updateProduct,
  deleteProduct,
  updatePriceList,
  createProductVariant,
  updateProductVariant,
  deleteProductVariant,
  toRow,
  toPaginate
}

export default ProductService
