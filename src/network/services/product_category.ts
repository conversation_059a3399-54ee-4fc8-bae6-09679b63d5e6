import client, { IMedusaDataResponse } from '../request'
import {
  CreateProductCategoryInput,
  ProductCategory,
  ProductCategoryBatchProduct,
  UpdateProductCategoryInput
} from '@/types/ProductCategory'

export interface ProductCategoryResponse extends IMedusaDataResponse {
  product_categories: Array<ProductCategory>
}

/// swr
const getProductCategories = `/store/admin/product-categories`
const getProductCategory = (id: string) => `/store/admin/product-categories/${id}`
/// swr

/// axios
const createProductCategory = (data: CreateProductCategoryInput) => {
  return client.post(`/store/admin/product-categories`, data)
}

const updateProductCategory = (id: string, data: UpdateProductCategoryInput) => {
  return client.post(`/store/admin/product-categories/${id}`, data)
}

const addProductsToProductCategory = (id: string, data: ProductCategoryBatchProduct) => {
  return client.post(`/store/admin/product-categories/${id}/products/batch`, data)
}

const deleteProductsFromProductCategory = (id: string, data: ProductCategoryBatchProduct) => {
  return client.delete(`/store/admin/product-categories/${id}/products/batch`, { data })
}

const deleteProductCategory = (id: string) => {
  return client.delete(`/store/admin/product-categories/${id}`)
}
/// axios

/// Processing
const toRow = (data: ProductCategoryResponse | undefined): ProductCategory[] => {
  if (data?.product_categories && data?.product_categories?.length > 0) {
    return data?.product_categories?.map((element) => {
      return {
        ...element
      }
    })
  }

  return []
}

const toPaginate = (data: ProductCategoryResponse | undefined) => {
  return {
    total: data?.count ?? 0,
    lastPage: data ? Math.ceil(data?.count / data?.limit) : 1
  }
}
/// Processing

const ProductCategoryService = {
  getProductCategories,
  getProductCategory,
  createProductCategory,
  updateProductCategory,
  addProductsToProductCategory,
  deleteProductsFromProductCategory,
  deleteProductCategory,
  toRow,
  toPaginate
}

export default ProductCategoryService
