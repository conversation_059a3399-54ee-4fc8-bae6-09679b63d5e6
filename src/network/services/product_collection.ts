import client, { IMedusaDataResponse } from '../request'
import {
  ProductCollection,
  CreateProductCollectionInput,
  UpdateProductCollectionInput
  // ProductCollectionBatchProduct
} from '@/types/ProductCollection'

export interface ProductCollectionResponse extends IMedusaDataResponse {
  collections: Array<ProductCollection>
}

/// swr
const getProductCollections = `/store/admin/collections`
const getProductCollection = (id: string) => `/store/admin/collections/${id}`
/// swr

/// axios
const createProductCollection = (data: CreateProductCollectionInput) => {
  return client.post(`/store/admin/collections`, data)
}

const updateProductCollection = (id: string, data: UpdateProductCollectionInput) => {
  return client.put(`/store/admin/collections/${id}`, data)
}

const deleteProductCollection = (id: string) => {
  return client.delete(`/store/admin/collections/${id}`)
}

// const addProductsToProductCollection = (id: string, data: ProductCollectionBatchProduct) => {
//   return client.post(`/store/admin/collections/${id}/products/batch`, data)
// }

// const deleteProductsFromProductCollection = (id: string, data: ProductCollectionBatchProduct) => {
//   return client.delete(`/store/admin/collections/${id}/products/batch`, { data })
// }
/// axios

/// Processing
const toRow = (data: ProductCollectionResponse | undefined): ProductCollection[] => {
  if (data?.collections && data?.collections?.length > 0) {
    return data?.collections?.map((element) => {
      return {
        ...element
      }
    })
  }

  return []
}

const toPaginate = (data: ProductCollectionResponse | undefined) => {
  return {
    total: data?.count ?? 0,
    lastPage: data ? Math.ceil(data?.count / data?.limit) : 1
  }
}
/// Processing

const ProductCollectionService = {
  getProductCollections,
  getProductCollection,
  createProductCollection,
  updateProductCollection,
  deleteProductCollection,
  // addProductsToProductCollection,
  // deleteProductsFromProductCollection,
  toRow,
  toPaginate
}

export default ProductCollectionService
