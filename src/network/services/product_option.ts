import { Product } from '@/types/Product'
import client from '../request'
import { AxiosPromise } from 'axios'

/// axios
const addProductOption = (
  productId: string,
  data: { title: string }
): AxiosPromise<{ product: Product }> => {
  return client.post(`/store/admin/products/${productId}/options`, data)
}

const updateProductOption = (
  id: string, // option id
  productId: string,
  data: { title: string }
): AxiosPromise<{ product: Product }> => {
  return client.post(`/store/admin/products/${productId}/options/${id}`, data)
}

const deleteProductOption = (id: string, productId: string) => {
  return client.delete(`/store/admin/products/${productId}/options/${id}`)
}
/// axios

const ProductOptionService = {
  addProductOption,
  updateProductOption,
  deleteProductOption
}

export default ProductOptionService
