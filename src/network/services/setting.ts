import { Customer, CustomerResponse } from '@/types/Customer'
import client from '../request'

/// swr
const getSettings = '/core/admin/settings'
const getSetting = (key: string) => `/cms/admin/settings/${key}`
/// swr

/// axios
const updateSetting = (key: string, data: any) => {
  return client.put(`/cms/admin/settings/${key}`, data)
}
/// axios

/// processing
const toRow = (data: CustomerResponse | undefined): Customer[] => {
  console.log(data)
  if (data?.customers && data?.customers?.length > 0) {
    return data?.customers?.map((element) => {
      return {
        ...element
      }
    })
  }

  return []
}

const toPaginate = (data: CustomerResponse | undefined) => {
  return {
    total: data?.count ?? 0,
    lastPage: data ? Math.ceil((data?.count ?? 0) / (data?.limit ?? 0)) : 1
  }
}
/// processing

const SettingService = {
  getSettings,
  getSetting,
  updateSetting,
  toRow,
  toPaginate
}

export default SettingService
