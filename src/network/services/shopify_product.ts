// Fetch collections of store
const productCollectionQuery = `query getCollections {
  collections(first: 10, query: "-title:Shop-All") {
    edges {
      node {
        handle
        title
      }
    }
  }
}`

const productsQuery = `query getProducts($query: String, $cursor: String, $length: Int!, $sortKey: ProductSortKeys, $country: CountryCode) @inContext(country: $country) {
  products(after: $cursor, first: $length, query: $query, sortKey: $sortKey) {
    pageInfo {
      hasNextPage
      endCursor
    }
    edges {
      node {
        id
        title
        handle
        description
        vendor
        featuredImage {
          url
        }
        productType
        images(first: 2) {
          edges {
            node {
              url
            }
          }
        }
        publishedAt
        priceRange {
          minVariantPrice {
            amount
            currencyCode
          }
        }
        compareAtPriceRange {
          minVariantPrice {
            amount
            currencyCode
          }
        }
        options {
          name
          values
        }
        variants(first: 10) {
          edges {
            node {
              id
              price {
                amount
                currencyCode
              }
              compareAtPrice {
                amount
                currencyCode
              }
              selectedOptions {
                name
                value
              }
            }
          }
        }
        tags
        availableForSale
      }
    }
  }
}`

const productQuery = `query getProduct($handle: String!, $country: CountryCode) @inContext(country: $country) {
  productByHandle(handle: $handle) {
    id
    title
    vendor
    description
    images(first: 3) {
          edges {
            node {
              url
            }
          }
        }
    variants(first: 10) {
      nodes {
        id
        title
        quantityAvailable
        sku
        selectedOptions {
          name
          value
        }
        price {
          amount
          currencyCode
        }
        compareAtPrice {
          amount
          currencyCode
        }
      }
    }
    tags
  }
}`

const productsByCollectionHandle = `query getProducts($handle: String!, $cursor: String, $length: Int!, $sortKey: ProductCollectionSortKeys, $reverse: Boolean, $productType: String, $country: CountryCode) @inContext(country: $country) {
    collection(handle: $handle) {
      products(after: $cursor, first: $length, sortKey: $sortKey, reverse: $reverse, filters: {productType: $productType}) {
        pageInfo {
          hasNextPage
          endCursor
        }
        edges {
          node {
            id
            title
            handle
            description
            featuredImage {
              url
            }
            images(first: 2) {
              edges {
                node {
                  url
                }
              }
            }
            publishedAt
            priceRange {
              minVariantPrice {
                amount
                currencyCode
              }
            }
            compareAtPriceRange {
              minVariantPrice {
                amount
                currencyCode
              }
            }
            tags
            availableForSale
          }
        }
      }
    }
  }`

const ShopifyProductService = {
  productCollectionQuery,
  productsQuery,
  productQuery,
  productsByCollectionHandle
}

export default ShopifyProductService
