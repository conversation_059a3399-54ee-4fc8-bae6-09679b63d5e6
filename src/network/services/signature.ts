import client, { IDataResponse } from '../request'
import { UpdateSignatureInput } from '@/types/Signature'

// swr
const findSignature = `/pdf/admin/my-signatures`
// swr

// axios
const createOrUpdateSignature = (data: UpdateSignatureInput) => {
  return client.post(`/pdf/admin/signatures`, data)
}
// axios

/// Processing
const toRow = <T>(data: IDataResponse<T> | undefined): T[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...element
      }
    })
  }

  return []
}

const toPaginate = <T>(data: IDataResponse<T> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}
/// Processing

const SignatureService = {
  findSignature,
  createOrUpdateSignature,
  toRow,
  toPaginate
}

export default SignatureService
