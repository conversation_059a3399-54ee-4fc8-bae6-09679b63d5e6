import client, { IDataResponse, IPostResponse } from '../request'
import { AxiosPromise } from 'axios'
import {
  SortTestimonialInput,
  Testimonials,
  UpdateTestimonialInput
} from '@/types/CreateTestimonial'

/// swr
const getTestimonials = '/cms/admin/testimonials'
/// swr

/// axios

const createTestimonial = (data: Testimonials): AxiosPromise<IPostResponse<Testimonials>> => {
  return client.post(`/cms/admin/testimonials`, data)
}

const updateTestimonial = (
  id: string | number,
  data: UpdateTestimonialInput
): AxiosPromise<IPostResponse<Testimonials>> => {
  return client.put(`/cms/admin/testimonials/${id}`, data)
}

const sortTestimonials = (
  data: SortTestimonialInput
): AxiosPromise<IPostResponse<Testimonials>> => {
  return client.post(`/cms/admin/testimonials/sort`, data)
}

const deleteTestimonial = (id: string | number): AxiosPromise<IPostResponse<Testimonials>> => {
  return client.delete(`/cms/admin/testimonials/${id}`)
}

/// axios

/// Processing

const toRow = (data: IDataResponse<Testimonials> | undefined): Testimonials[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...element,
        key: element.id
      }
    })
  }

  return []
}

const toPaginate = (data: IDataResponse<Testimonials> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}

/// Processing

const TestimonialService = {
  getTestimonials,
  createTestimonial,
  updateTestimonial,
  sortTestimonials,
  deleteTestimonial,
  toRow,
  toPaginate
}

export default TestimonialService
