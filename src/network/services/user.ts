import { Exhibitor } from '@/types/Exhibitor'
import { LicenseRoles } from './auth'

export declare enum UserRoles {
  ADMIN = 'admin',
  MEMBER = 'member',
  DEVELOPER = 'developer'
}

export type User = {
  id: string
  role: UserRoles
  email: string
  first_name: string
  last_name: string
  password_hash: string
  api_token: string
  metadata: Record<string, unknown>
  store_id?: string
  store?: Exhibitor
  custom_role: LicenseRoles
}
