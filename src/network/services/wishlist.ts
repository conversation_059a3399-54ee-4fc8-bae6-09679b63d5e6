import { PricedWishlist, WishlistResponse } from '@/types/Wishlist'
import client from '../request'

/// swr
const getWishlists = (campaignId: number | string) =>
  `/store/admin/wishlists/find-by-campaign/${campaignId}`
/// swr

/// axios
const generateWishlist = async (data: any) => {
  return await client.post(`/pdf/admin/wishlists`, data)
}
/// axios

/// processing
const toRow = (data: WishlistResponse | undefined): PricedWishlist[] => {
  if (data?.wishlists && data?.wishlists?.length > 0) {
    return data?.wishlists?.map((element) => {
      return {
        ...element
      }
    })
  }

  return []
}

const toPaginate = (data: WishlistResponse | undefined) => {
  return {
    total: data?.count ?? 0,
    lastPage: data ? Math.ceil((data?.count ?? 0) / (data?.limit ?? 0)) : 1
  }
}
/// processing

const WishlistService = {
  getWishlists,
  generateWishlist,
  toRow,
  toPaginate
}

export default WishlistService
