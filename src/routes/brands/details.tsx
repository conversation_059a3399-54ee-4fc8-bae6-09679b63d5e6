import { useParams } from 'react-router-dom'
import { FC } from 'react'
import { UpdateProductBrandForm } from '@/components/Brands/UpdateProductBrandForm'
import { useProductBrand } from '@/hooks/products/useProductBrand'

const BrandDetails = () => {
  const { brand_id } = useParams()

  if (!brand_id) {
    return <></>
  }

  return <View {...{ brand_id }} />
}

const View: FC<{ brand_id: string }> = ({ brand_id }) => {
  const { brand, isLoading, error } = useProductBrand(brand_id)

  if (!brand) return <></>
  if (error) return <></>

  return (
    <div className="space-y-6">
      <div className="p-8">
        <div className="flex flex-col space-y-4">
          {!isLoading && <UpdateProductBrandForm initialValue={brand} products={brand.products} />}
        </div>
      </div>
    </div>
  )
}

export default BrandDetails
