import { AddBrandButton } from '@/components/Brands/AddBrandButton'
import BrandTable from '@/components/Brands/BrandTable'
import BrandExportTable from '@/components/Brands/ImportExportComponents/BrandExportTable'
import BrandImportTable from '@/components/Brands/ImportExportComponents/BrandImportTable'
import { ExportBrandButton } from '@/components/Brands/ImportExportComponents/ExportBrandButton'
import { ImportBrandButton } from '@/components/Brands/ImportExportComponents/ImportBrandButton'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

const Brands = () => {
  return (
    <div className="h-full flex-1 flex-col space-y-8 p-8 md:flex">
      <Tabs defaultValue="table" className="space-y-4">
        <TabsList>
          <TabsTrigger value="table">Brands</TabsTrigger>
          <TabsTrigger value="import">Import</TabsTrigger>
          <TabsTrigger value="export">Export</TabsTrigger>
        </TabsList>

        <TabsContent value="table">
          <div className="space-y-4">
            <div className="flex justify-end space-x-2">
              <AddBrandButton />
            </div>
            <BrandTable />
          </div>
        </TabsContent>

        <TabsContent value="import">
          <div className="space-y-4">
            <div className="flex justify-end space-x-2">
              <ImportBrandButton />
            </div>
            <BrandImportTable />
          </div>
        </TabsContent>

        <TabsContent value="export">
          <div className="space-y-4">
            <div className="flex justify-end space-x-2">
              <ExportBrandButton />
            </div>
            <BrandExportTable />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
export default Brands
