import { useParams } from 'react-router-dom'
import { FC } from 'react'
import { UpdateCampaignForm } from '@/components/Campaign/UpdateCampaignForm'
import { useCampaign } from '@/hooks/campaigns/useCampaign'

const CampaignDetails = () => {
  const { campaign_id } = useParams()

  if (!campaign_id) {
    return <></>
  }

  return <View {...{ campaign_id }} />
}

const View: FC<{ campaign_id: string }> = ({ campaign_id }) => {
  // find campaign with campaign_id
  const { campaign, isLoading } = useCampaign(campaign_id)

  return (
    <div className="space-y-6">
      <div className="p-8">{!isLoading && <UpdateCampaignForm initialValue={campaign} />}</div>
    </div>
  )
}

export default CampaignDetails
