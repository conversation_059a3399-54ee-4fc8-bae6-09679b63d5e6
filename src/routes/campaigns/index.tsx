import AddCampaignButton from '@/components/Campaign/AddCampaignButton'
import CampaignTable from '@/components/Campaign/CampaignTable'
// import CampaignExportTable from '@/components/Campaign/ImportExportComponents/CampaignExportTable'
// import CampaignImportTable from '@/components/Campaign/ImportExportComponents/CampaignImportTable'
// import { ExportCampaignButton } from '@/components/Campaign/ImportExportComponents/ExportCampaignButton'
// import { ImportCampaignButton } from '@/components/Campaign/ImportExportComponents/ImportCampaignButton'
import FreebiesCard from '@/components/Setting/cards/FreebiesCard'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

const Campaigns = () => {
  return (
    <div className="h-full flex-1 flex-col space-y-8 p-8 md:flex">
      <Tabs defaultValue="media-room" className="space-y-4">
        <TabsList>
          <TabsTrigger value="media-room">Freebies Media Room</TabsTrigger>
          <TabsTrigger value="table">Campaigns</TabsTrigger>
          {/* <TabsTrigger value="import">Import</TabsTrigger> */}
          {/* <TabsTrigger value="export">Export</TabsTrigger> */}
        </TabsList>

        <TabsContent value="media-room">
          <div className="space-y-4">
            <FreebiesCard />
          </div>
        </TabsContent>

        <TabsContent value="table">
          <div className="space-y-4">
            <div className="flex justify-end space-x-2">
              {/* <ImportCampaignButton /> */}
              <AddCampaignButton type="freebies" />
            </div>

            <CampaignTable type="freebies" />
          </div>
        </TabsContent>

        {/* <TabsContent value="import">
          <div className="space-y-4">
            <div className="flex justify-end space-x-2">
              <ImportCampaignButton />
            </div>
            <CampaignImportTable />
          </div>
        </TabsContent> */}

        {/* <TabsContent value="export">
          <div className="space-y-4">
            <div className="flex justify-end space-x-2">
              <ExportCampaignButton />
            </div>
            <CampaignExportTable />
          </div>
        </TabsContent> */}
      </Tabs>
    </div>
  )
}

export default Campaigns
