import CampaignSubmissionTable from '@/components/Campaign/CampaignSubmissionTable'
import CustomTooltip from '@/components/Charts/CustomTooltip'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle, Content, Title } from '@/components/ui/card'
import { ChartColors } from '@/constants/colors'
import { useCampaign } from '@/hooks/campaigns/useCampaign'
import AnalyticService from '@/network/services/analytic'
import { CampaignAnalytic } from '@/types/Analytics'
import { useMemo } from 'react'
import { useParams } from 'react-router-dom'
import { Cell, Pie, PieChart, ResponsiveContainer, Tooltip } from 'recharts'
import useSWR from 'swr'

const CampaignStatistics = () => {
  const { campaign_id } = useParams()

  if (!campaign_id) {
    return <></>
  }

  return (
    <div className="space-y-6">
      <CampaignStatisticsForm campaignId={campaign_id} />
    </div>
  )
}

const CampaignStatisticsForm = ({ campaignId }: { campaignId: string }) => {
  const { campaign, isLoading } = useCampaign(campaignId)
  const { data } = useSWR<CampaignAnalytic>(AnalyticService.getCampaignAnalytic(campaignId))

  const handleExport = async () => {
    try {
      const { data: result } = await AnalyticService.exportCampaignAnalytic(campaignId)
      if (result.download_url) {
        window.open(result.download_url, '_blank')
        return
      }
      console.log('error, no download url')
    } catch (e) {
      console.log('error export customer analytics', e)
    }
  }

  const total = useMemo(() => {
    return {
      gender: data?.gender.reduce((prev, curr) => (prev += parseInt(`${curr.count}`)), 0),
      address: data?.address.reduce((prev, curr) => (prev += parseInt(`${curr.count}`)), 0),
      age: data?.age.reduce((prev, curr) => (prev += parseInt(`${curr.count}`)), 0),
      stage: data?.stage.reduce((prev, curr) => (prev += parseInt(`${curr.count}`)), 0)
    }
  }, [data])

  if (isLoading || !campaign) {
    return <></>
  }

  return (
    <div>
      <div className="h-full space-y-8 p-8 mb-4">
        <CardTitle>{campaign.title}</CardTitle>
        <div className="flex flex-row items-end">
          <Card className="flex flex-col">
            <CardHeader className="flex flex-row space-y-0 items-center justify-between">
              <CardTitle>Total Submissions</CardTitle>
            </CardHeader>
            <CardContent>
              <div>{data?.total_submissions}</div>
            </CardContent>
          </Card>

          <Button className="ml-4" onClick={handleExport}>
            Export To CSV
          </Button>
        </div>

        <Card className="h-fit">
          <CardHeader className="flex flex-row space-y-0 items-center justify-between">
            <CardTitle>Participant Analytic</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 xl:grid-cols-2">
              <div className="h-[250px]">
                <Title>Gender</Title>
                <Content>
                  {data?.gender && data.gender.length > 0 ? (
                    <div className="h-[250px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Tooltip content={<CustomTooltip total={total.gender} />} />
                          <Pie
                            data={data.gender.map((item) => ({
                              name: item.gender,
                              value: parseInt(`${item.count}`)
                            }))}
                            dataKey="value"
                            nameKey="name"
                            cx="50%"
                            cy="50%"
                            outerRadius={50}
                            fill="#8884d8"
                            label={({ x, y, cx, index }) => (
                              <text
                                x={x}
                                y={y}
                                fill="black"
                                textAnchor={x > cx ? 'start' : 'end'}
                                dominantBaseline="central"
                              >
                                {`${data.gender[index].gender ?? 'NULL'} (${(
                                  (parseInt(`${data.gender[index].count}`) / (total.gender ?? 1)) *
                                  100
                                ).toFixed(1)}%)`}
                              </text>
                            )}
                          >
                            {data.gender.map((_, index) => (
                              <Cell
                                key={`k-${index}`}
                                fill={ChartColors[index % ChartColors.length]}
                              />
                            ))}
                          </Pie>
                        </PieChart>
                      </ResponsiveContainer>
                    </div>
                  ) : (
                    <div className="text-start">No Data</div>
                  )}
                </Content>
              </div>

              <div className="h-[250px]">
                <Title>Stage</Title>
                <Content>
                  {data?.stage && data.stage.length > 0 ? (
                    <div className="h-[250px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Tooltip content={<CustomTooltip total={total.stage} />} />
                          <Pie
                            data={data.stage.map((item) => ({
                              name: item.current_stage,
                              value: parseInt(`${item.count}`)
                            }))}
                            dataKey="value"
                            nameKey="name"
                            cx="50%"
                            cy="50%"
                            outerRadius={50}
                            fill="#8884d8"
                            label={({ x, y, cx, index }) => (
                              <text
                                x={x}
                                y={y}
                                fill="black"
                                textAnchor={x > cx ? 'start' : 'end'}
                                dominantBaseline="central"
                              >
                                {`${data.stage[index].current_stage ?? 'NULL'} (${(
                                  (parseInt(`${data.stage[index].count}`) / (total.stage ?? 1)) *
                                  100
                                ).toFixed(1)}%)`}
                              </text>
                            )}
                          >
                            {data.stage.map((_, index) => (
                              <Cell
                                key={`k-${index}`}
                                fill={ChartColors[index % ChartColors.length]}
                              />
                            ))}
                          </Pie>
                        </PieChart>
                      </ResponsiveContainer>
                    </div>
                  ) : (
                    <div className="text-start">No Data</div>
                  )}
                </Content>
              </div>

              <div className="h-[250px]">
                <Title>Address</Title>
                <Content>
                  {data?.address && data.address.length > 0 ? (
                    <div className="h-[250px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Tooltip content={<CustomTooltip total={total.address} />} />
                          <Pie
                            data={data.address.map((item) => ({
                              name: item.province,
                              value: parseInt(`${item.count}`)
                            }))}
                            dataKey="value"
                            nameKey="name"
                            cx="50%"
                            cy="50%"
                            outerRadius={50}
                            fill="#8884d8"
                            label={({ x, y, cx, index }) => (
                              <text
                                x={x}
                                y={y}
                                fill="black"
                                textAnchor={x > cx ? 'start' : 'end'}
                                dominantBaseline="central"
                              >
                                {`${data.address[index].province ?? 'NULL'} (${(
                                  (parseInt(`${data.address[index].count}`) /
                                    (total.address ?? 1)) *
                                  100
                                ).toFixed(1)}%)`}
                              </text>
                            )}
                          >
                            {data.address.map((_, index) => (
                              <Cell
                                key={`k-${index}`}
                                fill={ChartColors[index % ChartColors.length]}
                              />
                            ))}
                          </Pie>
                        </PieChart>
                      </ResponsiveContainer>
                    </div>
                  ) : (
                    <div className="text-start">No Data</div>
                  )}
                </Content>
              </div>

              <div className="h-[250px]">
                <Title>Age group</Title>
                <Content>
                  {data?.age && data.age.length > 0 ? (
                    <div className="h-[250px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Tooltip content={<CustomTooltip total={total.age} />} />
                          <Pie
                            data={data.age.map((item) => ({
                              name: item.age_group,
                              value: parseInt(`${item.count}`)
                            }))}
                            dataKey="value"
                            nameKey="name"
                            cx="50%"
                            cy="50%"
                            outerRadius={50}
                            fill="#8884d8"
                            label={({ x, y, cx, index }) => (
                              <text
                                x={x}
                                y={y}
                                fill="black"
                                textAnchor={x > cx ? 'start' : 'end'}
                                dominantBaseline="central"
                              >
                                {`${data.age[index].age_group ?? 'NULL'} (${(
                                  (parseInt(`${data.age[index].count}`) / (total.age ?? 1)) *
                                  100
                                ).toFixed(1)}%)`}
                              </text>
                            )}
                          >
                            {data.age.map((_, index) => (
                              <Cell
                                key={`k-${index}`}
                                fill={ChartColors[index % ChartColors.length]}
                              />
                            ))}
                          </Pie>
                        </PieChart>
                      </ResponsiveContainer>
                    </div>
                  ) : (
                    <div className="text-start">No Data</div>
                  )}
                </Content>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row space-y-0 items-center justify-between">
            <CardTitle>Participants</CardTitle>
          </CardHeader>
          <CardContent>
            <CampaignSubmissionTable campaignId={campaignId} />
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default CampaignStatistics
