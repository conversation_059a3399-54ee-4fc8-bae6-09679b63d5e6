import { useParams } from 'react-router-dom'
import { FC } from 'react'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { ExclamationTriangleIcon } from '@radix-ui/react-icons'
import { useJobApplication } from '@/hooks/careers/useJobApplications'
import JobApplicationInfoCard from '@/components/Career/JobApplicationInfoCard'

const JobApplicationDetails = () => {
  const { application_id } = useParams()

  if (!application_id) {
    return <></>
  }

  return <View {...{ application_id }} />
}

const View: FC<{ application_id: string | number }> = ({ application_id }) => {
  // find exhibitor with exhibitor_id
  const { application, isLoading, error } = useJobApplication(application_id)

  if (error) {
    return (
      <div className="p-8 space-y-6">
        <Alert variant="destructive">
          <ExclamationTriangleIcon className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            Failed to retrieve job applications. Please try again later.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex h-full flex-1 flex-col space-y-8 p-8">
        {!isLoading && (
          <>
            <div className="grid grid-cols-1 gap-4">
              <JobApplicationInfoCard application={application} />
            </div>
            <div className="grid grid-cols-3 gap-4"></div>
          </>
        )}
      </div>
    </div>
  )
}

export default JobApplicationDetails
