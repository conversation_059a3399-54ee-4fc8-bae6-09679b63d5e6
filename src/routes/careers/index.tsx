import JobApplicationTable from '@/components/Career/JobApplicationTable'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'

const Careers = () => {
  return (
    <div className="hidden h-full flex-1 flex-col space-y-8 p-8 md:flex">
      <Tabs defaultValue="job-applications" className="space-y-4">
        <TabsList>
          <TabsTrigger value="job-applications">Job Applications</TabsTrigger>
        </TabsList>

        <TabsContent value="job-applications">
          <div className="space-y-4">
            <JobApplicationTable />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default Careers
