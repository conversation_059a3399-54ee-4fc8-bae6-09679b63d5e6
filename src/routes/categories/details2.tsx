import { useParams } from 'react-router-dom'
import { FC } from 'react'
import { useProductCategory } from '@/hooks/product_categories/useProductCategory'
import { UpdateProductCategoryForm } from '@/components/Category/UpdateProductCategoryForm'
import useSWR from 'swr'
import { serialize } from '@/network/request'
import ProductService from '@/network/services/product'
import CategoryTable from '@/components/Category/CategoryTable'
import { AddProductCategoryButton } from '@/components/Category/AddCategoryButton'

const SubCategoryDetails = () => {
  const { parent_category_id, sub_category_id } = useParams()

  if (!parent_category_id || !sub_category_id) {
    return <></>
  }

  return <View {...{ parent_category_id, sub_category_id }} />
}

const View: FC<{ parent_category_id: string; sub_category_id: string }> = ({ parent_category_id, sub_category_id }) => {
  const { category, isLoading } = useProductCategory(sub_category_id, {})
  const { data, isLoading: isLoadingProduct } = useSWR(
    serialize(ProductService.getProducts, { category_id: [sub_category_id] })
  )

  return (
    <div className="space-y-6">
      <div className="p-8 space-y-8">
        <div className="flex flex-col space-y-4">
          {!isLoading && !isLoadingProduct && (
            <UpdateProductCategoryForm initialValue={category} products={data.products} />
          )}
        </div>
        <div className="flex flex-col space-y-4">
          <div className="flex justify-end space-x-2">
            <AddProductCategoryButton categoryId={sub_category_id} />
          </div>
          <CategoryTable parentCategoryId={parent_category_id} subCategoryId={sub_category_id} />
        </div>
      </div>
    </div>
  )
}

export default SubCategoryDetails
