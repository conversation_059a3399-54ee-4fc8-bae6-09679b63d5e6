import { useParams } from 'react-router-dom'
import { FC } from 'react'
import { useProductCategory } from '@/hooks/product_categories/useProductCategory'
import { UpdateProductCategoryForm } from '@/components/Category/UpdateProductCategoryForm'
import useSWR from 'swr'
import { serialize } from '@/network/request'
import ProductService from '@/network/services/product'

const SubCategory2Details = () => {
  const { sub_category_id2 } = useParams()

  if (!sub_category_id2) {
    return <></>
  }

  return <View {...{ category_id: sub_category_id2 }} />
}

const View: FC<{ category_id: string }> = ({ category_id }) => {
  const { category, isLoading } = useProductCategory(category_id, {})
  const { data, isLoading: isLoadingProduct } = useSWR(
    serialize(ProductService.getProducts, { category_id: [category_id] })
  )

  return (
    <div className="space-y-6">
      <div className="p-8 space-y-8">
        <div className="flex flex-col space-y-4">
          {!isLoading && !isLoadingProduct && (
            <UpdateProductCategoryForm initialValue={category} products={data.products} />
          )}
        </div>
      </div>
    </div>
  )
}

export default SubCategory2Details
