import { AddProductCategoryButton } from '@/components/Category/AddCategoryButton'
import CategoryTable from '@/components/Category/CategoryTable'
import { ImportProductCategoryButton } from '@/components/Category/ImportExportComponents/ImportProductCategoryButton'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import ProductCategoryImportTable from '@/components/Category/ImportExportComponents/ProductCategoryImportTable'
import { ExportProductCategoryButton } from '@/components/Category/ImportExportComponents/ExportProductCategoryButton'
import ProductCategoryExportTable from '@/components/Category/ImportExportComponents/ProductCategoryExportTable'

const Categories = () => {
  return (
    <div className="h-full flex-1 flex-col space-y-8 p-8 md:flex">
      <Tabs defaultValue="table" className="space-y-4">
        <TabsList>
          <TabsTrigger value="table">Categories</TabsTrigger>
          <TabsTrigger value="import">Import</TabsTrigger>
          <TabsTrigger value="export">Export</TabsTrigger>
        </TabsList>

        <TabsContent value="table">
          <div className="space-y-4">
            <div className="flex justify-end space-x-2">
              <AddProductCategoryButton />
            </div>
            <CategoryTable />
          </div>
        </TabsContent>

        <TabsContent value="import">
          <div className="space-y-4">
            <div className="flex justify-end space-x-2">
              <ImportProductCategoryButton />
            </div>
            <ProductCategoryImportTable />
          </div>
        </TabsContent>

        <TabsContent value="export">
          <div className="space-y-4">
            <div className="flex justify-end space-x-2">
              <ExportProductCategoryButton />
            </div>
            <ProductCategoryExportTable />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
export default Categories
