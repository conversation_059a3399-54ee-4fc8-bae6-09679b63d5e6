import { useParams } from 'react-router-dom'
import { FC } from 'react'
import { useCustomer } from '@/hooks/customers/useCustomer'
import CustomerGeneralInfoCard from '@/components/Customer/CustomerGeneralInfoCard'
import CustomerShippingCard from '@/components/Customer/CustomerShippingCard'
import CustomerChildCard from '@/components/Customer/CustomerChildCard'
import { Separator } from '@/components/ui/separator'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ImportChildButton } from '@/components/Customer/CustomerChild/ImportExportComponents/ImportChildButton'
import ChildImportTable from '@/components/Customer/CustomerChild/ImportExportComponents/ChildImportTable'

const CustomerDetails = () => {
  const { customer_id } = useParams()

  if (!customer_id) {
    return <></>
  }

  return <View {...{ customer_id }} />
}

const View: FC<{ customer_id: string | number }> = ({ customer_id }) => {
  const { customer, coreLoading, licenseCustomer, licenseLoading } = useCustomer(customer_id)

  return (
    <div className="space-y-6">
      <div className="flex h-full flex-1 flex-col space-y-8 p-8">
        {!coreLoading && (
          <>
            <div className="grid grid-cols-2 gap-4">
              {!licenseLoading && (
                <CustomerGeneralInfoCard customer={customer} licenseCustomer={licenseCustomer} />
              )}

              {/* TODO: Shipping Card */}
              <CustomerShippingCard customer={customer} />

              <div className="col-span-2 space-y-4">
                <Separator />

                <Tabs defaultValue="table" className="space-y-4">
                  <TabsList>
                    <TabsTrigger value="table">Children</TabsTrigger>
                    {/* <TabsTrigger value="import">Import</TabsTrigger> */}
                    {/* <TabsTrigger value="export">Export</TabsTrigger> */}
                  </TabsList>

                  <TabsContent value="table">
                    <div className="space-y-4">
                      <div className="flex justify-end space-x-2">
                        {/* <AddChildrenButton /> */}
                      </div>
                      <CustomerChildCard customer={customer} />
                    </div>
                  </TabsContent>

                  <TabsContent value="import">
                    <div className="space-y-4">
                      <div className="flex justify-end space-x-2">
                        <ImportChildButton />
                      </div>
                      <ChildImportTable />
                    </div>
                  </TabsContent>

                  {/* <TabsContent value="export">
                    <div className="space-y-4">
                      <div className="flex justify-end space-x-2">
                        <ExportChildButton />
                      </div>
                      <ChildExportTable />
                    </div>
                  </TabsContent> */}
                </Tabs>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  )
}

export default CustomerDetails
