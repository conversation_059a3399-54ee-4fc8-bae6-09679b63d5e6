import CustomerTable from '@/components/Customer/CustomerTable'
import CustomerExportTable from '@/components/Customer/ImportExportComponents/CustomerExportTable'
import CustomerImportTable from '@/components/Customer/ImportExportComponents/CustomerImportTable'
import { ExportCustomerButton } from '@/components/Customer/ImportExportComponents/ExportCustomerButton'
import { ImportCustomerButton } from '@/components/Customer/ImportExportComponents/ImportCustomerButton'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

const Customers = () => {
  return (
    <div className="hidden h-full flex-1 flex-col space-y-8 p-8 md:flex">
      <Tabs defaultValue="table" className="space-y-4">
        <TabsList>
          <TabsTrigger value="table">Friends</TabsTrigger>
          <TabsTrigger value="import">Import</TabsTrigger>
          <TabsTrigger value="export">Export</TabsTrigger>
        </TabsList>

        <TabsContent value="table">
          <div className="space-y-4">
            <CustomerTable />
          </div>
        </TabsContent>

        <TabsContent value="import">
          <div className="space-y-4">
            <div className="flex justify-end space-x-2">
              <ImportCustomerButton />
            </div>
            <CustomerImportTable />
          </div>
        </TabsContent>

        <TabsContent value="export">
          <div className="space-y-4">
            <div className="flex justify-end space-x-2">
              <ExportCustomerButton />
            </div>
            <CustomerExportTable />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default Customers
