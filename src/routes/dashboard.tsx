/* eslint-disable @typescript-eslint/ban-ts-comment */
// import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from '@/components/ui/tabs'
// import { Button } from '@/components/ui/button'
// import { Card, CardHeader, CardTitle, CardContent, CardDescription } from '@/components/ui/card'
// import { CalendarDatePicker } from '@/components/DatePicker/CalendarDatePicker'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import axios from 'axios'
import { first } from 'radash'
import { useMemo } from 'react'
import useSWR from 'swr'

// eslint-disable-next-line @typescript-eslint/no-explicit-any, react-refresh/only-export-components
export const vercelFetcher = async (...args: any) => {
  const url = args[0]

  return axios
    .get(url, { headers: { Authorization: `Bear<PERSON> ${import.meta.env.VITE_VERCEL_TOKEN}` } })
    .then((res) => res.data)
}

const Dashboard = () => {
  const { data, mutate } = useSWR(
    'https://api.vercel.com/v6/deployments?teamId=team_5j3mD2RdBpZyC6DZ7sXJZ8cF&projectId=prj_CPFqgd6EyonMacXHXL8zfGKnIA2H&limit=1&target=production',
    vercelFetcher
  )

  const isDeploymentRunning = useMemo(() => {
    const deployment = first(data?.deployments ?? [])

    if (
      // @ts-ignore
      deployment?.state == 'BUILDING' ||
      // @ts-ignore
      deployment?.state == 'INITIALIZING' ||
      // @ts-ignore
      deployment?.state == 'QUEUED'
    ) {
      return true
    }

    return false
  }, [data])

  return (
    <>
      <div className="flex-1 space-y-4 p-8 pt-6">
        <div className="flex items-center justify-between space-y-2">
          <h2 className="text-3xl font-bold tracking-tight">Dashboard</h2>
          {/* <div className="flex items-center space-x-2">
            <CalendarDatePicker mode="range" numberOfMonths={2} />
            <Button>Download</Button>
          </div> */}
        </div>
        {/* <Tabs defaultValue="overview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="reports">Reports</TabsTrigger>
            <TabsTrigger value="notifications">Notifications</TabsTrigger>
          </TabsList>
          <TabsContent value="overview" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    className="h-4 w-4 text-muted-foreground"
                  >
                    <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
                  </svg>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">$45,231.89</div>
                  <p className="text-xs text-muted-foreground">+20.1% from last month</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Subscriptions</CardTitle>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    className="h-4 w-4 text-muted-foreground"
                  >
                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                    <circle cx="9" cy="7" r="4" />
                    <path d="M22 21v-2a4 4 0 0 0-3-3.87M16 3.13a4 4 0 0 1 0 7.75" />
                  </svg>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">+2350</div>
                  <p className="text-xs text-muted-foreground">+180.1% from last month</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Sales</CardTitle>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    className="h-4 w-4 text-muted-foreground"
                  >
                    <rect width="20" height="14" x="2" y="5" rx="2" />
                    <path d="M2 10h20" />
                  </svg>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">+12,234</div>
                  <p className="text-xs text-muted-foreground">+19% from last month</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Active Now</CardTitle>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    className="h-4 w-4 text-muted-foreground"
                  >
                    <path d="M22 12h-4l-3 9L9 3l-3 9H2" />
                  </svg>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">+573</div>
                  <p className="text-xs text-muted-foreground">+201 since last hour</p>
                </CardContent>
              </Card>
            </div>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
              <Card className="col-span-4">
                <CardHeader>
                  <CardTitle>Overview</CardTitle>
                </CardHeader>
                <CardContent className="pl-2"></CardContent>
              </Card>
              <Card className="col-span-3">
                <CardHeader>
                  <CardTitle>Recent Sales</CardTitle>
                  <CardDescription>You made 265 sales this month.</CardDescription>
                </CardHeader>
                <CardContent></CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs> */}

        <Card className="col-span-3">
          <CardHeader>
            <CardTitle>If you need to refresh content after modifying contents found in</CardTitle>
          </CardHeader>
          <CardContent>
            <CardDescription>Header</CardDescription>
            <CardDescription>Footer</CardDescription>
            <Button
              className="mt-2"
              variant="destructive"
              onClick={async () => {
                await axios.post(
                  'https://api.vercel.com/v1/integrations/deploy/prj_CPFqgd6EyonMacXHXL8zfGKnIA2H/1MwKHzKADT'
                )
                mutate({ deployments: [{ state: 'QUEUED' }] })
              }}
              disabled={isDeploymentRunning}
            >
              {isDeploymentRunning ? 'Redeploying...' : 'Refresh Frontend Website'}
            </Button>
          </CardContent>
        </Card>
      </div>
    </>
  )
}

export default Dashboard
