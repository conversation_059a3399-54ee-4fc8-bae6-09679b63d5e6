import { FC } from 'react'
import { Card, CardContent, CardHeader, CardTitle, Content, Title } from '@/components/ui/card'
import { useParams } from 'react-router-dom'
import { useEventPurchase } from '@/hooks/event_purchases/useEventPurchase'
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuGroup
} from '@/components/ui/dropdown-menu'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { EventPurchaseStatus } from '@/types/EventPurchase'
import EventPurchaseService from '@/network/services/event_purchase'
import { mutate } from 'swr'
import { toast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import { DateTime } from 'luxon'

const EventPurchaseDetails = () => {
  const { event_purchase_id } = useParams()

  if (!event_purchase_id) {
    return <></>
  }

  return <EventPurchaseCard {...{ event_purchase_id }} />
}

const EventPurchaseCard: FC<{
  event_purchase_id: string
}> = ({ event_purchase_id }) => {
  const { eventPurchase, coreLoading } = useEventPurchase(event_purchase_id)

  const updateEventPurchaseStatus = async (newStatus: EventPurchaseStatus) => {
    try {
      const { data: response } = await EventPurchaseService.updateEventPurchaseStatus(
        eventPurchase.id,
        {
          status: newStatus
        }
      )

      if (response != null) {
        mutate(
          (key) =>
            typeof key === 'string' &&
            key.startsWith(EventPurchaseService.getEventPurchase(eventPurchase.id))
        )
        toast({
          title: `Event Purchase ${newStatus}`,
          variant: 'success'
        })
      }

      console.log(response)
    } catch (e) {
      console.log(e)

      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  }

  const eventPurchaseItems = eventPurchase?.purchased_items

  if (!eventPurchaseItems || coreLoading) {
    return <></>
  }

  return (
    <div className="flex h-full flex-1 flex-col p-8 space-y-4">
      <Card>
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <CardTitle>Event Purchase Details</CardTitle>
          <div className="flex flex-row space-x-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="h-8">
                  <div className="flex items-center space-x-2">
                    <div
                      className={cn(
                        'h-1.5 w-1.5 self-center rounded-full',
                        eventPurchase.status == EventPurchaseStatus.APPROVED
                          ? 'bg-success'
                          : 'bg-destructive'
                      )}
                    />
                    <span className="capitalize text-xs">
                      {eventPurchase.status == EventPurchaseStatus.APPROVED
                        ? 'Approved'
                        : 'Rejected'}
                    </span>
                  </div>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" forceMount>
                <DropdownMenuGroup>
                  <DropdownMenuItem
                    onClick={async (e) => {
                      e.stopPropagation()
                      updateEventPurchaseStatus(
                        eventPurchase.status == EventPurchaseStatus.APPROVED
                          ? EventPurchaseStatus.REJECTED
                          : EventPurchaseStatus.APPROVED
                      )
                    }}
                    className="space-x-2"
                  >
                    <div
                      className={cn(
                        'h-1.5 w-1.5 self-center rounded-full',
                        eventPurchase.status == EventPurchaseStatus.APPROVED
                          ? 'bg-destructive'
                          : 'bg-success'
                      )}
                    />
                    <span className="capitalize text-xs">
                      {eventPurchase.status == EventPurchaseStatus.APPROVED ? 'Reject' : 'Approve'}
                    </span>
                  </DropdownMenuItem>
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-2">
            <Title>ID</Title>
            <Content>{eventPurchase?.id ?? '-'}</Content>
            {/* <Title>Event Id</Title>
            <Content>{eventPurchase?.event_id ?? '-'}</Content> */}
            <Title>Transaction ID</Title>
            <Content>{eventPurchase?.transaction_id ?? '-'}</Content>
            <Title>Total</Title>
            <Content>{eventPurchase?.total ?? '-'}</Content>
            <Title>Purchase Date</Title>
            <Content>
              {eventPurchase?.buy_date
                ? DateTime.fromISO(eventPurchase.buy_date as string).toFormat('yyyy-MM-dd')
                : ''}
            </Content>
            <Title>Created Date</Title>
            <Content>
              {eventPurchase?.created_at
                ? DateTime.fromISO(eventPurchase.created_at as string).toFormat('yyyy-MM-dd HH:mm:ss')
                : ''}
            </Content>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row space-y-0 items-center justify-between">
          <CardTitle>Purchase Items</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Brand</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Price</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {eventPurchaseItems?.map((item, index) => (
                <TableRow key={index}>
                  <TableCell>{item?.brand_name ?? '-'}</TableCell>
                  <TableCell>{item?.category_name ?? '-'}</TableCell>
                  <TableCell>RM {item?.price ?? '-'}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}

export default EventPurchaseDetails
