import { useLocation, useParams } from 'react-router-dom'
import { FC } from 'react'
import { useEvent } from '@/hooks/event/useEvent'
import { UpdateEventForm } from '@/components/Event/UpdateEventForm'
import useAuth from '@/hooks/useAuth'
import { useActiveEvent } from '@/hooks/event/useActiveEvent'

const EventDetails = () => {
  const { event_id } = useParams()
  const hash = useLocation().hash.replace('#', '')

  if (!event_id) {
    return <></>
  }

  return <View {...{ event_id, hash }} />
}

const VendorView = () => {
  const { event, isLoading, error } = useActiveEvent()

  if (!event) return <></>
  if (error) return <></>

  return (
    <div className="space-y-6">
      <div className="flex h-full flex-1 flex-col space-y-8">
        <div className="p-8">{!isLoading && <UpdateEventForm initialValue={event} />}</div>
      </div>
    </div>
  )
}

const AdminView: FC<{ event_id: string | number; hash: string }> = ({ event_id, hash }) => {
  const { event, isLoading, error } = useEvent(event_id)

  if (!event) return <></>
  if (error) return <></>

  return (
    <div className="space-y-6">
      <div className="flex h-full flex-1 flex-col space-y-8">
        <div className="p-8">
          {!isLoading && <UpdateEventForm initialValue={event} initialTab={hash} />}
        </div>
      </div>
    </div>
  )
}

const View: FC<{ event_id: string | number; hash: string }> = ({ event_id, hash }) => {
  const { role } = useAuth()

  if (role === 'vendor') {
    return <VendorView />
  }

  return <AdminView {...{ event_id, hash }} />
}

export default EventDetails
