import EventPanel from '@/components/Event/EventPanel'
import EventTable from '@/components/Event/EventTable'
import { Separator } from '@/components/ui/separator'
import useAuth from '@/hooks/useAuth'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
// import { ImportEventButton } from '@/components/Event/ImportExportComponents/ImportEventButton'

const Events = () => {
  const { role } = useAuth()

  return (
    <div className="hidden h-full flex-1 flex-col space-y-4 p-8 md:flex">
      <Tabs defaultValue="table" className="space-y-4">
        <TabsList>
          <TabsTrigger value="table">Events</TabsTrigger>
          {/* <TabsTrigger value="import">Import</TabsTrigger> */}
          {/* <TabsTrigger value="export">Export</TabsTrigger> */}
        </TabsList>

        <TabsContent value="table">
          <div className="space-y-4">
            <div className="flex justify-end space-x-2">
              {/* <ImportEventButton /> */}
              <EventPanel />
            </div>

            {role != 'vendor' && (
              <>
                <Separator />
                <EventTable />
              </>
            )}
          </div>
        </TabsContent>

        {/* <TabsContent value="import">
          <div className="space-y-4">
            <div className="flex justify-end space-x-2">
              <ImportCampaignButton />
            </div>
            <CampaignImportTable />
          </div>
        </TabsContent> */}

        {/* <TabsContent value="export">
          <div className="space-y-4">
            <div className="flex justify-end space-x-2">
              <ExportCampaignButton />
            </div>
            <CampaignExportTable />
          </div>
        </TabsContent> */}
      </Tabs>
    </div>
  )
}

export default Events
