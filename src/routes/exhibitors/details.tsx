import { useParams } from 'react-router-dom'
import { FC } from 'react'
import { useExhibitor } from '@/hooks/exhibitor/useExhibitor'
import ExhibitorGeneralInfoCard from '@/components/Exhibitor/ExhibitorGeneralInfoCard'
// import ExhibitorBrandCard from '@/components/Exhibitor/ExhibitorBrandCard'
import ExhibitorContactCard from '@/components/Exhibitor/ExhibitorContactCard'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { ExclamationTriangleIcon } from '@radix-ui/react-icons'
import ExhibitorBrandCard from '@/components/Exhibitor/ExhibitorBrandCard'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import ExhibitorContractCard from '@/components/Exhibitor/ExhibitorContractCard'

const ExhibitorDetails = () => {
  const { exhibitor_id } = useParams()

  if (!exhibitor_id) {
    return <></>
  }

  return <View {...{ exhibitor_id }} />
}

const View: FC<{ exhibitor_id: string | number }> = ({ exhibitor_id }) => {
  // find exhibitor with exhibitor_id
  const { exhibitor, isLoading, error } = useExhibitor(exhibitor_id)

  if (error) {
    return (
      <div className="p-8 space-y-6">
        <Alert variant="destructive">
          <ExclamationTriangleIcon className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>Failed to retrieve exhibitor. Please try again later.</AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex h-full flex-1 flex-col space-y-8 p-8">
        {!isLoading && (
          <Tabs defaultValue="company" className="space-y-4">
            <TabsList>
              <TabsTrigger value="company">Company</TabsTrigger>
              <TabsTrigger value="contracts">Contracts</TabsTrigger>
            </TabsList>

            <TabsContent value="company">
              <div className="flex h-full flex-1 flex-col space-y-8">
                <div className="grid grid-cols-1 gap-4">
                  <ExhibitorGeneralInfoCard exhibitor={exhibitor} />
                  {/* <ExhibitorBrandCard exhibitor={exhibitor} /> */}
                </div>
                <div className="grid grid-cols-3 gap-4">
                  <ExhibitorContactCard exhibitor={exhibitor} />
                  <ExhibitorContactCard exhibitor={exhibitor} variant="marketing" />
                  <ExhibitorContactCard exhibitor={exhibitor} variant="finance" />
                </div>
                <div className="grid grid-cols-1 gap-4">
                  <ExhibitorBrandCard exhibitor={exhibitor} />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="contracts">
              <ExhibitorContractCard exhibitorId={exhibitor.id} />
            </TabsContent>
          </Tabs>
        )}
      </div>
    </div>
  )
}

export default ExhibitorDetails
