import ExhibitorTable from '@/components/Exhibitor/ExhibitorTable'
import ExhibitorExportTable from '@/components/Exhibitor/ImportExportComponents/ExhibitorExportTable'
import ExhibitorImportTable from '@/components/Exhibitor/ImportExportComponents/ExhibitorImportTable'
import { ExportExhibitorButton } from '@/components/Exhibitor/ImportExportComponents/ExportExhibitorButton'
import { ImportExhibitorButton } from '@/components/Exhibitor/ImportExportComponents/ImportExhibitorButton'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

const Exhibitor = () => {
  return (
    <div className="hidden h-full flex-1 flex-col space-y-8 p-8 md:flex">
      <Tabs defaultValue="table" className="space-y-4">
        <TabsList>
          <TabsTrigger value="table">Companies</TabsTrigger>
          <TabsTrigger value="import">Import</TabsTrigger>
          <TabsTrigger value="export">Export</TabsTrigger>
        </TabsList>

        <TabsContent value="table">
          <div className="space-y-4">
            <ExhibitorTable />
          </div>
        </TabsContent>

        <TabsContent value="import">
          <div className="space-y-4">
            <div className="flex justify-end space-x-2">
              <ImportExhibitorButton />
            </div>
            <ExhibitorImportTable />
          </div>
        </TabsContent>

        <TabsContent value="export">
          <div className="space-y-4">
            <div className="flex justify-end space-x-2">
              <ExportExhibitorButton />
            </div>
            <ExhibitorExportTable />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default Exhibitor
