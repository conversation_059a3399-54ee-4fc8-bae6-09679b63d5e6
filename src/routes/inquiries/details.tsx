import { useParams } from 'react-router-dom'
import { FC } from 'react'
import { useInquiry } from '@/hooks/inquiries/useInquiry'
import InquiryInfoCard from '@/components/Inquiries/InquiryInfoCard'

const InquiryDetails = () => {
  const { inquiry_id } = useParams()

  if (!inquiry_id) {
    return <></>
  }

  return <View {...{ inquiry_id }} />
}

const View: FC<{ inquiry_id: string }> = ({ inquiry_id }) => {
  const { inquiry, isLoading } = useInquiry(inquiry_id)

  return (
    <div className="space-y-6">
      <div className="flex h-full flex-1 flex-col space-y-8 p-8">
        {!isLoading && <InquiryInfoCard inquiry={inquiry} />}
      </div>
    </div>
  )
}

export default InquiryDetails
