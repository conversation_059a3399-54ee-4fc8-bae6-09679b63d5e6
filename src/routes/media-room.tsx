import { FC } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import TermsOfUseCard from '@/components/MediaRoom/TermsOfUse/TermsOfUseCard'
import AboutUsCard from '@/components/MediaRoom/AboutUs/AboutUsCard'
import PrivacyPolicyCard from '@/components/MediaRoom/PrivacyPolicy/PrivacyPolicyCard'
import ReturnAndRefundPolicyCard from '@/components/MediaRoom/ReturnAndRefundPolicy.tsx/ReturnAndRefundPolicyCard'
import FaqGroupCard from '@/components/MediaRoom/Faq/FaqGroupCard'
import SearchCard from '@/components/MediaRoom/Search/SearchCard'
import ShopCard from '@/components/MediaRoom/Footer/ShopCard'
import LearnCard from '@/components/MediaRoom/Footer/LearnCard'
import SupportCard from '@/components/MediaRoom/Footer/SupportCard'
import FabCard from '@/components/MediaRoom/Footer/FabCard'
import FaqPageCard from '@/components/MediaRoom/Faq/FaqPageCard'
import CareerPageCard from '@/components/MediaRoom/Career/CareerPageCard'
import JobCategoryCard from '@/components/MediaRoom/Career/JobCategoryCard'
import HomeCard from '@/components/MediaRoom/Home/HomeCard'
import MemberBenefitsCard from '@/components/Setting/cards/MemberBenefitsCard'
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area'
import ExhibitorBenefitsCard from '@/components/Setting/cards/ExhibitorBenefitsCard'
import SocialCard from '@/components/MediaRoom/Footer/SocialCard'
import ContactUsCard from '@/components/MediaRoom/ContactUs/ContactUsCard'
import ShopPageCard from '@/components/MediaRoom/Shop/ShopPageCard'
import MemoryPageCard from '@/components/MediaRoom/Memory/MemoryPageCard'
import BlogPageCard from '@/components/MediaRoom/Blog/BlogPageCard'
import EventPageCard from '@/components/MediaRoom/Event/EventPageCard'
import DealsPageCard from '@/components/MediaRoom/Deals/DealsPageCard'
import FreebiesPageCard from '@/components/MediaRoom/Freebies/FreebiesPageCard'
import BrandsPageCard from '@/components/MediaRoom/Brands/BrandsPageCard'
import SignUpPageCard from '@/components/MediaRoom/SignUp/SignUpPageCard'
import HospitalListCard from '@/components/Setting/cards/HospitalListCard'
import PopUpListCard from '@/components/MediaRoom/PopUp/PopUpListCard'
import TestimonialsCard from '@/components/MediaRoom/Testimonial/TestimonialsCard'

const MediaRoom: FC = () => {
  return (
    <div className="space-y-6">
      <div className="flex h-full flex-1 flex-col space-y-8 p-8">
        <Tabs defaultValue="home" className="space-y-4">
          <ScrollArea>
            <div className="max-w-[calc(100vw-8rem)] relative h-10">
              <TabsList>
                {/* <TabsTrigger value="general">General</TabsTrigger> */}
                <TabsTrigger value="home">Home</TabsTrigger>
                <TabsTrigger value="testimonial">Testimonial Headlines</TabsTrigger>
                <TabsTrigger value="sign-up">Sign Up</TabsTrigger>
                <TabsTrigger value="about-us">About us</TabsTrigger>
                <TabsTrigger value="terms-of-use">Terms of use</TabsTrigger>
                <TabsTrigger value="privacy-policy">Privacy policy</TabsTrigger>
                <TabsTrigger value="contact-us">Contact Us</TabsTrigger>
                <TabsTrigger value="faq">FAQ</TabsTrigger>
                <TabsTrigger value="return-refund">Return and refund policy</TabsTrigger>
                <TabsTrigger value="search">Search</TabsTrigger>
                <TabsTrigger value="footer">Footer</TabsTrigger>
                <TabsTrigger value="career">Career</TabsTrigger>
                <TabsTrigger value="shop">Shop</TabsTrigger>
                <TabsTrigger value="memory">Memory</TabsTrigger>
                <TabsTrigger value="blog">Blog</TabsTrigger>
                <TabsTrigger value="event">Event</TabsTrigger>
                <TabsTrigger value="brands">Brand</TabsTrigger>
                <TabsTrigger value="deals">Deals</TabsTrigger>
                <TabsTrigger value="freebies">Freebies</TabsTrigger>
                <TabsTrigger value="hospitals">Hospitals</TabsTrigger>
                <TabsTrigger value="popups">Pop-ups</TabsTrigger>
              </TabsList>
            </div>
            <ScrollBar orientation="horizontal" />
          </ScrollArea>
          {/* <TabsContent value="general">
            <div className="grid grid-cols-2 gap-4">
              <div className="col-span-2 space-y-4"></div>
            </div>
          </TabsContent> */}

          <TabsContent value="home">
            <div className="grid grid-cols-2 gap-4">
              <div className="col-span-2 space-y-4">
                <HomeCard />
              </div>
            </div>
          </TabsContent>
          <TabsContent value="testimonial">
            <div className="grid grid-cols-2 gap-4">
              <div className="col-span-2 space-y-4">
                <TestimonialsCard />
              </div>
            </div>
          </TabsContent>
          <TabsContent value="sign-up">
            <div className="grid grid-cols-2 gap-4">
              <div className="col-span-2 space-y-4">
                <SignUpPageCard />
                <MemberBenefitsCard />
                <ExhibitorBenefitsCard />
              </div>
            </div>
          </TabsContent>
          <TabsContent value="search">
            <div className="grid grid-cols-2 gap-4">
              <div className="col-span-2 space-y-4">
                <SearchCard />
              </div>
            </div>
          </TabsContent>
          <TabsContent value="about-us">
            <div className="grid grid-cols-2 gap-4">
              <div className="col-span-2 space-y-4">
                <AboutUsCard />
              </div>
            </div>
          </TabsContent>
          <TabsContent value="terms-of-use">
            <div className="grid grid-cols-2 gap-4">
              <div className="col-span-2 space-y-4">
                <TermsOfUseCard />
              </div>
            </div>
          </TabsContent>
          <TabsContent value="privacy-policy">
            <div className="grid grid-cols-2 gap-4">
              <div className="col-span-2 space-y-4">
                <PrivacyPolicyCard />
              </div>
            </div>
          </TabsContent>
          <TabsContent value="contact-us">
            <div className="grid grid-cols-2 gap-4">
              <div className="col-span-2 space-y-4">
                <ContactUsCard />
              </div>
            </div>
          </TabsContent>
          <TabsContent value="faq">
            <div className="grid grid-cols-2 gap-4">
              <div className="col-span-2 space-y-4">
                <FaqPageCard />
                <FaqGroupCard />
              </div>
            </div>
          </TabsContent>
          <TabsContent value="return-refund">
            <div className="grid grid-cols-2 gap-4">
              <div className="col-span-2 space-y-4">
                <ReturnAndRefundPolicyCard />
              </div>
            </div>
          </TabsContent>
          <TabsContent value="footer">
            <div className="grid grid-cols-2 gap-4">
              <div className="col-span-2 space-y-4">
                <ShopCard />
                <LearnCard />
                <SupportCard />
                <SocialCard />
                <FabCard />
              </div>
            </div>
          </TabsContent>
          <TabsContent value="career">
            <div className="grid grid-cols-2 gap-4">
              <div className="col-span-2 space-y-4">
                <CareerPageCard />
                <JobCategoryCard />
                {/* <ApplicationFormCard /> */}
              </div>
            </div>
          </TabsContent>
          <TabsContent value="shop">
            <div className="grid grid-cols-2 gap-4">
              <div className="col-span-2 space-y-4">
                <ShopPageCard />
              </div>
            </div>
          </TabsContent>
          <TabsContent value="memory">
            <div className="grid grid-cols-2 gap-4">
              <div className="col-span-2 space-y-4">
                <MemoryPageCard />
              </div>
            </div>
          </TabsContent>
          <TabsContent value="blog">
            <div className="grid grid-cols-2 gap-4">
              <div className="col-span-2 space-y-4">
                <BlogPageCard />
              </div>
            </div>
          </TabsContent>
          <TabsContent value="event">
            <div className="grid grid-cols-2 gap-4">
              <div className="col-span-2 space-y-4">
                <EventPageCard />
              </div>
            </div>
          </TabsContent>
          <TabsContent value="brands">
            <div className="grid grid-cols-2 gap-4">
              <div className="col-span-2 space-y-4">
                <BrandsPageCard />
              </div>
            </div>
          </TabsContent>
          <TabsContent value="deals">
            <div className="grid grid-cols-2 gap-4">
              <div className="col-span-2 space-y-4">
                <DealsPageCard />
              </div>
            </div>
          </TabsContent>
          <TabsContent value="freebies">
            <div className="grid grid-cols-2 gap-4">
              <div className="col-span-2 space-y-4">
                <FreebiesPageCard />
              </div>
            </div>
          </TabsContent>
          <TabsContent value="hospitals">
            <div className="grid grid-cols-2 gap-4">
              <div className="col-span-2 space-y-4">
                <HospitalListCard />
              </div>
            </div>
          </TabsContent>
          <TabsContent value="popups">
            <div className="grid grid-cols-2 gap-4">
              <div className="col-span-2 space-y-4">
                <PopUpListCard />
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

export default MediaRoom
