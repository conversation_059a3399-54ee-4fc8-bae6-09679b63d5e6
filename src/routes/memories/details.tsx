import { useNavigate, useParams } from 'react-router-dom'
import { FC, useState } from 'react'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { ExclamationTriangleIcon } from '@radix-ui/react-icons'
import { useMemory } from '@/hooks/memories/useMemory'
import MemoryInfoCard from '@/components/Memory/MemoryInfoCard'
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from '@/components/ui/alert-dialog'
import { EditIcon, TrashIcon } from 'lucide-react'
import MemoryService from '@/network/services/memory'
import { toast } from '@/components/ui/use-toast'
import { Button } from '@/components/ui/button'
import { ToastAction } from '@/components/ui/toast'
import { UpdateMemoryForm } from '@/components/Memory/UpdateMemoryForm'

const MemoryDetails = () => {
  const { memory_id } = useParams()

  if (!memory_id) {
    return <></>
  }

  return <View {...{ memory_id }} />
}

const View: FC<{ memory_id: string | number }> = ({ memory_id }) => {
  // find exhibitor with exhibitor_id
  const { memory, isLoading, error } = useMemory(memory_id)

  if (error) {
    return (
      <div className="p-8 space-y-6">
        <Alert variant="destructive">
          <ExclamationTriangleIcon className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>Failed to retrieve memory. Please try again later.</AlertDescription>
        </Alert>
      </div>
    )
  }

  const [isOpen, setIsOpen] = useState(false)
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false)

  const navigate = useNavigate()

  const handleDelete = async () => {
    try {
      await MemoryService.deleteMemory(memory.id)

      toast({
        title: 'Success',
        description: `Delete memory successfully.`,
        action: <ToastAction altText="OK">OK</ToastAction>
      })

      navigate('/memories')
    } catch (error) {
      console.error(error)
      toast({
        title: 'Failed',
        description: 'Something went wrong',
        variant: 'destructive'
      })
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex h-full flex-1 flex-col space-y-4 p-4 md:p-8">
        {!isLoading && (
          <>
            <div className="flex w-full justify-end space-x-3">
              <Button onClick={() => setIsUpdateDialogOpen(true)}>
                <EditIcon size="16" className="mr-2" />
                Edit Memory
              </Button>

              <Button onClick={() => setIsOpen(true)}>
                <TrashIcon size="16" className="mr-2" />
                Delete Memory
              </Button>

              <AlertDialog open={isOpen}>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Delete memory</AlertDialogTitle>
                    <AlertDialogDescription className="text-red-500">
                      *NOTE: this action cannot be undone
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <Button onClick={() => setIsOpen(false)}>Cancel</Button>

                    <Button onClick={handleDelete}>Confirm</Button>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>

            <UpdateMemoryForm
              isDialogOpen={isUpdateDialogOpen}
              setIsDialogOpen={setIsUpdateDialogOpen}
              memory={memory}
            />

            <MemoryInfoCard memory={memory} />
          </>
        )}
      </div>
    </div>
  )
}

export default MemoryDetails
