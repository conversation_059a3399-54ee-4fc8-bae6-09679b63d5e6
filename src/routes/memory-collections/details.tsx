import { useNavigate, useParams } from 'react-router-dom'
import { FC, useState } from 'react'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { ExclamationTriangleIcon } from '@radix-ui/react-icons'
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from '@/components/ui/alert-dialog'
import { TrashIcon } from 'lucide-react'
import { toast } from '@/components/ui/use-toast'
import { Button } from '@/components/ui/button'
import { ToastAction } from '@/components/ui/toast'
import { useMemoryCollection } from '@/hooks/memory_collections/useMemoryCollection'
import MemoryCollectionService from '@/network/services/memory_collection'
import MemoryCollectionInfoCard from '@/components/MemoryCollection/MemoryCollectionInfoCard'
import { UpdateMemoryCollectionButton } from '@/components/MemoryCollection/UpdateMemoryCollectionButton'

const MemoryCollectionDetails = () => {
  const { memory_collection_id } = useParams()

  if (!memory_collection_id) {
    return <></>
  }

  return <View {...{ memory_collection_id }} />
}

const View: FC<{ memory_collection_id: string | number }> = ({ memory_collection_id }) => {
  const { memoryCollection, isLoading, error } = useMemoryCollection(memory_collection_id)

  if (error) {
    return (
      <div className="p-8 space-y-6">
        <Alert variant="destructive">
          <ExclamationTriangleIcon className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            Failed to retrieve memory collection. Please try again later.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  const [isOpen, setIsOpen] = useState(false)

  const navigate = useNavigate()

  const handleDelete = async () => {
    try {
      await MemoryCollectionService.deleteMemoryCollection(memoryCollection.id)

      toast({
        title: 'Success',
        description: `Delete memory collection successfully.`,
        action: <ToastAction altText="OK">OK</ToastAction>
      })

      navigate('/memory-collections')
    } catch (error) {
      console.error(error)
      toast({
        title: 'Failed',
        description: 'Something went wrong',
        variant: 'destructive'
      })
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex h-full flex-1 flex-col space-y-4 p-4 md:p-8">
        {!isLoading && (
          <>
            <div className="flex w-full justify-end space-x-3">
              <UpdateMemoryCollectionButton memoryCollection={memoryCollection} />

              <Button onClick={() => setIsOpen(true)}>
                <TrashIcon size="16" className="mr-2" />
                Delete Memory Collection
              </Button>

              <AlertDialog open={isOpen}>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Delete memory collection</AlertDialogTitle>
                    <AlertDialogDescription className="text-red-500">
                      *NOTE: this action cannot be undone
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <Button onClick={() => setIsOpen(false)}>Cancel</Button>

                    <Button onClick={handleDelete}>Confirm</Button>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>

            <MemoryCollectionInfoCard memoryCollection={memoryCollection} />
          </>
        )}
      </div>
    </div>
  )
}

export default MemoryCollectionDetails
