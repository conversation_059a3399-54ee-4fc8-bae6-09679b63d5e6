import { useNavigate, useParams } from 'react-router-dom'
import { FC, useState } from 'react'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { ExclamationTriangleIcon } from '@radix-ui/react-icons'
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from '@/components/ui/alert-dialog'
import { EditIcon, TrashIcon } from 'lucide-react'
import { toast } from '@/components/ui/use-toast'
import { Button } from '@/components/ui/button'
import { ToastAction } from '@/components/ui/toast'
import { useMemoryMedia } from '@/hooks/memory_medias/useMemoryMedia'
import MemoryMediaService from '@/network/services/memory_media'
import MemoryMediaInfoCard from '@/components/MemoryMedia/MemoryMediaInfoCard'
import { UpdateMemoryMediaForm } from '@/components/MemoryMedia/UpdateMemoryMediaForm'

const MemoryMediaDetails = () => {
  const { memory_media_id } = useParams()

  if (!memory_media_id) {
    return <></>
  }

  return <View {...{ memory_media_id }} />
}

const View: FC<{ memory_media_id: string | number }> = ({ memory_media_id }) => {
  // find exhibitor with exhibitor_id
  const { memoryMedia, isLoading, error } = useMemoryMedia(memory_media_id)

  if (error) {
    return (
      <div className="p-8 space-y-6">
        <Alert variant="destructive">
          <ExclamationTriangleIcon className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>Failed to retrieve memory. Please try again later.</AlertDescription>
        </Alert>
      </div>
    )
  }

  const [isOpen, setIsOpen] = useState(false)
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false)

  const navigate = useNavigate()

  const handleDelete = async () => {
    try {
      await MemoryMediaService.deleteMemoryMedia(memoryMedia.id)

      toast({
        title: 'Success',
        description: `Delete memory media successfully.`,
        action: <ToastAction altText="OK">OK</ToastAction>
      })

      navigate('/memory-medias')
    } catch (error) {
      console.error(error)
      toast({
        title: 'Failed',
        description: 'Something went wrong',
        variant: 'destructive'
      })
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex h-full flex-1 flex-col space-y-4 p-4 md:p-8">
        {!isLoading && (
          <>
            <div className="flex w-full justify-end space-x-3">
              <Button onClick={() => setIsUpdateDialogOpen(true)}>
                <EditIcon size="16" className="mr-2" />
                Edit Memory Media
              </Button>

              <Button onClick={() => setIsOpen(true)}>
                <TrashIcon size="16" className="mr-2" />
                Delete Memory Media
              </Button>

              <AlertDialog open={isOpen}>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Delete memory media</AlertDialogTitle>
                    <AlertDialogDescription className="text-red-500">
                      *NOTE: this action cannot be undone
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <Button onClick={() => setIsOpen(false)}>Cancel</Button>

                    <Button onClick={handleDelete}>Confirm</Button>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>

            <UpdateMemoryMediaForm
              isDialogOpen={isUpdateDialogOpen}
              setIsDialogOpen={setIsUpdateDialogOpen}
              memoryMedia={memoryMedia}
            />

            <MemoryMediaInfoCard memoryMedia={memoryMedia} />
          </>
        )}
      </div>
    </div>
  )
}

export default MemoryMediaDetails
