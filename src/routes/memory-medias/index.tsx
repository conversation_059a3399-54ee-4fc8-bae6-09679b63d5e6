import { AddBulkMemoryButton } from '@/components/Memory/AddBulkMemoryButton'
import { AddMemoryButton } from '@/components/Memory/AddMemoryButton'
import MemoryTable from '@/components/Memory/MemoryTable'
import { AddMemoryCollectionButton } from '@/components/MemoryCollection/AddMemoryCollectionButton'
import MemoryCollectionTable from '@/components/MemoryCollection/MemoryCollectionTable'
import { AddMemoryMediaButton } from '@/components/MemoryMedia/AddMemoryMediaButton'
import MemoryMediaTable from '@/components/MemoryMedia/MemoryMediaTable'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useNavigate } from 'react-router-dom'
const MemoryMedias = () => {
  const nav = useNavigate()
  return (
    <div className="hidden h-full flex-1 flex-col space-y-8 p-8 md:flex">
      <Tabs defaultValue="media" className="space-y-4">
        <TabsList>
          <TabsTrigger
            value="memory"
            onClick={() => {
              nav('/memories')
            }}
          >
            Memories
          </TabsTrigger>
          {/* <TabsTrigger
            value="media"
            onClick={() => {
              nav('/memory-medias')
            }}
          >
            Memory Media
          </TabsTrigger> */}
          <TabsTrigger
            value="collection"
            onClick={() => {
              nav('/memory-collections')
            }}
          >
            Memory Collection
          </TabsTrigger>
        </TabsList>

        <TabsContent value="memory">
          <div className="space-y-4">
            <div className="flex justify-end space-x-2">
              <AddMemoryButton />
              <AddBulkMemoryButton />
            </div>
            <MemoryTable />
          </div>
        </TabsContent>

        <TabsContent value="media">
          <div className="space-y-4">
            <div className="flex justify-end space-x-2">
              <AddMemoryMediaButton />
            </div>
            <MemoryMediaTable />
          </div>
        </TabsContent>

        <TabsContent value="collection">
          <div className="space-y-4">
            <div className="flex justify-end space-x-2">
              <AddMemoryCollectionButton />
            </div>
            <MemoryCollectionTable />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default MemoryMedias
