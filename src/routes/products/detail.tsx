import { useParams } from 'react-router-dom'
import { FC } from 'react'
import { ExclamationTriangleIcon } from '@radix-ui/react-icons'
import { UpdateProductForm } from '@/components/Product/UpdateProductForm'
import { useProduct } from '@/hooks/products/useProduct'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'

const ProductDetails = () => {
  const { product_id } = useParams()

  if (!product_id) {
    return <></>
  }

  return <View {...{ product_id }} />
}

const View: FC<{ product_id: string | number }> = ({ product_id }) => {
  // find product with product_id
  const { product, isLoading, error } = useProduct(
    product_id,
    {
      expand: [
        'variants',
        'variants.options',
        'variants.prices',
        'options',
        'options.values',
        'store',
        'collection',
        'categories',
        'images'
      ]
    },
    'comma'
  )

  if (error) {
    return (
      <div className="p-8 space-y-6">
        <Alert variant="destructive">
          <ExclamationTriangleIcon className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>Failed to retrieve product. Please try again later.</AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="p-8">{!isLoading && <UpdateProductForm initialValue={product} />}</div>
    </div>
  )
}

export default ProductDetails
