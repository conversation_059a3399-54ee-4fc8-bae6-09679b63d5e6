import { AddProductButton } from '@/components/Product/AddProductButton'
import { ExportProductButton } from '@/components/Product/ExportProductButton'
import { ImportProductButton } from '@/components/Product/ImportProductButton'
import ProductExportTable from '@/components/Product/ProductExportTable'
import ProductImportTable from '@/components/Product/ProductImportTable'
import ProductTable from '@/components/Product/ProductTable'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

const Products = () => {
  return (
    <div className="h-full flex-1 flex-col p-8 md:flex">
      <Tabs defaultValue="table" className="space-y-4">
        <TabsList>
          <TabsTrigger value="table">Products</TabsTrigger>
          <TabsTrigger value="import">Import</TabsTrigger>
          <TabsTrigger value="export">Export</TabsTrigger>
        </TabsList>

        <TabsContent value="table">
          <div className="space-y-4">
            <div className="flex justify-end space-x-2">
              <AddProductButton />
            </div>
            <ProductTable />
          </div>
        </TabsContent>

        <TabsContent value="import">
          <div className="space-y-4">
            <div className="flex justify-end space-x-2">
              <ImportProductButton />
            </div>
            <ProductImportTable />
          </div>
        </TabsContent>

        <TabsContent value="export">
          <div className="space-y-4">
            <div className="flex justify-end space-x-2">
              <ExportProductButton />
            </div>
            <ProductExportTable />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
export default Products
