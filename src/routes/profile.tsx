import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardContentLabel,
  CardContentTitle
} from '@/components/ui/card'
import useAuth from '@/hooks/useAuth'

const Profile = () => {
  const { user, store } = useAuth()

  return (
    <div className="h-full flex-1 flex-col space-y-8 p-8 md:flex">
      <div className="grid grid-cols-[4fr_2fr] gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0">
            <CardTitle>Profile</CardTitle>
          </CardHeader>
          <CardContent className="flex flex-col space-y-4">
            <div className="grid grid-cols-[200px_1fr] gap-2">
              <CardContentTitle>Email</CardContentTitle>
              <CardContentLabel>{user?.email}</CardContentLabel>
              <CardContentTitle>Role</CardContentTitle>
              <CardContentLabel>{user?.role}</Card<PERSON>ontentLabel>
              <CardContentTitle>Store</CardContentTitle>
              <CardContentLabel>{store?.name}</CardContentLabel>
            </div>
          </CardContent>
          <CardFooter></CardFooter>
        </Card>
        {/* TODO: change pasword */}
      </div>
    </div>
  )
}

export default Profile
