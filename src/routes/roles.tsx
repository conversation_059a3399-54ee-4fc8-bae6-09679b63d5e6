import AddRoleButton from '@/components/Role/AddRoleButton'
import RoleTable from '@/components/Role/RoleTable'
import SignatureCard from '@/components/Role/SignatureCard'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import useAuth from '@/hooks/useAuth'
import { LicenseRoles } from '@/network/services/auth'

const Roles = () => {
  const { user } = useAuth()

  return (
    <div className="h-full flex-1 flex-col space-y-8 p-8 md:flex">
      <Tabs defaultValue="roles" className="space-y-4">
        <TabsList>
          <TabsTrigger value="roles">Roles</TabsTrigger>
          {user?.role === LicenseRoles.SUPER_ADMIN && (
            <TabsTrigger value="signature">My Signature</TabsTrigger>
          )}
        </TabsList>

        <TabsContent value="roles">
          <div className="space-y-4">
            <div className="flex justify-end space-x-2">
              <AddRoleButton />
            </div>
            <RoleTable />
          </div>
        </TabsContent>

        <TabsContent value="signature">
          <div className="space-y-4">
            <SignatureCard />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
export default Roles
