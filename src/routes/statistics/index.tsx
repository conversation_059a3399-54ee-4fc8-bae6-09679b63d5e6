import CampaignAnalytic from '@/components/Analytic/CampaignAnalytic'
import CustomerAnalytic from '@/components/Analytic/CustomerAnalytic'
import DealAnalytic from '@/components/Analytic/DealsAnalytic'
import PurchaseAnalytic from '@/components/Analytic/PurchasesAnalytic'
import { CalendarDatePicker } from '@/components/DatePicker/CalendarDatePicker'
import { Button } from '@/components/ui/button'
import { Title } from '@/components/ui/card'
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ArrowRight } from 'lucide-react'
import { DateTime } from 'luxon'
import { useState } from 'react'

const Statistics = () => {
  const [startsAt, setStartsAt] = useState<DateTime | undefined>(DateTime.now().minus({ month: 1 }))
  const [endsAt, setEndsAt] = useState<DateTime | undefined>(DateTime.now())
  const [mode, setMoode] = useState<'general' | 'past30'>('general')
  const [hideDateFilter, setHideDateFilter] = useState<Boolean>(false)
  return (
    <div className="space-y-6">
      <div className="flex h-full flex-1 flex-col space-y-8 p-8">
        <div className="grid grid-cols-2 gap-4">
          <div className="col-span-2 space-y-4">
            {!hideDateFilter && <Title>Analytic Timeframe</Title>}
            <div className="flex items-center">
              {mode == 'general' && !hideDateFilter && (
                <>
                  <CalendarDatePicker
                    className="w-[200px]"
                    buttonLabel={startsAt?.toFormat('yyyy-MM-dd')}
                    placeholder="Starts at"
                    value={endsAt?.toFormat('yyyy-MM-dd')}
                    mode="single"
                    onSelect={(v) => {
                      if (!v) return
                      setStartsAt(DateTime.fromJSDate(v))
                      console.log(v)
                    }}
                  />
                  <ArrowRight className="mx-8" />
                  <CalendarDatePicker
                    className="w-[200px]"
                    buttonLabel={endsAt?.toFormat('yyyy-MM-dd')}
                    placeholder="Ends at"
                    value={endsAt?.toFormat('yyyy-MM-dd')}
                    mode="single"
                    onSelect={(v) => {
                      if (!v) return
                      setEndsAt(DateTime.fromJSDate(v))
                      console.log(v)
                    }}
                  />
                  <Button
                    className="mx-8"
                    onClick={() => {
                      setStartsAt(undefined)
                      setEndsAt(undefined)
                    }}
                  >
                    Clear Timeframe
                  </Button>
                </>
              )}

              {!hideDateFilter && (
                <Button
                  onClick={() => {
                    setStartsAt(undefined)
                    setEndsAt(undefined)
                    setMoode(mode == 'past30' ? 'general' : 'past30')
                  }}
                >
                  {mode == 'past30' ? 'Statistics By Timeframe' : 'Comparison Against Past 30 Days'}
                </Button>
              )}
            </div>

            <Tabs defaultValue="friends" className="space-y-4">
              <TabsList>
                <TabsTrigger
                  value="friends"
                  onClick={() => {
                    setHideDateFilter(false)
                  }}
                >
                  Friends
                </TabsTrigger>
                <TabsTrigger
                  value="campaigns"
                  onClick={() => {
                    setHideDateFilter(false)
                  }}
                >
                  Campaigns
                </TabsTrigger>
                <TabsTrigger
                  value="deals"
                  onClick={() => {
                    setHideDateFilter(false)
                  }}
                >
                  Deals
                </TabsTrigger>
                <TabsTrigger
                  value="purchases"
                  onClick={() => {
                    setHideDateFilter(true)
                  }}
                >
                  Purchases
                </TabsTrigger>
              </TabsList>

              <TabsContent value="friends">
                <div className="space-y-4">
                  <CustomerAnalytic mode={mode} startsAt={startsAt} endsAt={endsAt} />
                </div>
              </TabsContent>

              <TabsContent value="campaigns">
                <div className="space-y-4">
                  <CampaignAnalytic startsAt={startsAt} endsAt={endsAt} />
                </div>
              </TabsContent>

              <TabsContent value="deals">
                <DealAnalytic startsAt={startsAt} endsAt={endsAt} />
              </TabsContent>

              <TabsContent value="purchases">
                <div className="space-y-4">
                  <PurchaseAnalytic />
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Statistics
