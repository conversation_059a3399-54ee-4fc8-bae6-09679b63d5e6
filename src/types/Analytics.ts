export interface CustomerAnalytics {
  grow_30_days: number
  total_customer: number
  gender: {
    gender: string
    count: number
  }[]
  stage: {
    current_stage: string
    count: number
  }[]
  address: {
    province: string
    count: number
  }[]
  age: {
    age_group: string
    count: number
  }[]
  download_url?: string
}

export interface CampaignWishlistAnalytics {
  campaign_id: number
  by_product: { count: number; medusa_product_id: string; medusa_product_title: string }[]
  by_category: { count: number; medusa_category_id: string; medusa_category_name: string }[]
  by_collection: { count: number; medusa_collection_id: string; medusa_collection_title: string }[]
}

export interface CampaignsOverview {
  total_submissions: number
}

export interface CampaignAnalytic {
  total_submissions: number
  gender: {
    gender: string
    count: number
  }[]
  stage: {
    current_stage: string
    count: number
  }[]
  address: {
    province: string
    count: number
  }[]
  age: {
    age_group: string
    count: number
  }[]
}

export interface PurchaseAnalytics {
  total_revenue: number
  brands: { brand_name: string; total_revenue: number; sales_count: number }[]
  categories: { category_name: string; total_revenue: number; sales_count: number }[]
  download_url_brand?: string
  download_url_category?: string
}
