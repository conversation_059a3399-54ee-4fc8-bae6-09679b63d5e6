import { DateTime } from 'luxon'

export type BatchJobUploadInput = {
  files: File
}

export type CreateBatchJobInput = {
  type:
    | 'product-export'
    | 'product-import'
    | 'product-category-export'
    | 'product-category-import'
    | /** Brand is named as collection in Medusa*/ 'product-collection-export'
    | /** Brand is named as collection in Medusa*/ 'product-collection-import'
    | 'customer-export'
    | 'customer-import'
    | /** Exhibitor is named as store in Medusa*/ 'store-export'
    | /** Exhibitor is named as store in Medusa*/ 'store-import'
    | 'campaign-export'
    | 'campaign-import'
    | 'event-export'
    | 'event-import'
    | 'child-export'
    | 'child-import'
  context: {
    // for product export
    filterable_fields?: {
      export?: boolean
    }
    // for product import
    fileKey?: string
    dry_run?: boolean
    result?: {
      file_key?: string
    }
  }
}

export type BatchJob = {
  dry_run: boolean
  type: string
  created_by: string // user_id
  id: string
  status: string
  result: {
    count: number
    advancement_count: number
    progress: number
    errors: {
      message: string
      code: string | number
      err: []
    }
    stat_descriptions: {
      key: string
      name: string
      message: string
    }
    file_key: string
    file_size: number
  }
  created_at: DateTime | string
  updated_at: DateTime | string
}
