import { DateTime } from 'luxon'
import { Event } from './Event'
import { CoreExhibitor } from './Exhibitor'

export type Booth = {
  id: number
  event_id: number
  store_id: number
  status: string
  booth_no: string
  booth_size: string
  hall_no: string
  contract_no: string
  event: Event
  store: CoreExhibitor
  updated_at: DateTime | string
  created_at: DateTime | string
}

export type CreateEventBoothInput = {
  event_id: number
  store_id: number
  booth_no: string
  hall_no: string
  booth_size: string
  collection_id_1: number
  collection_id_2?: number
}

export type UpdateBoothInput = {
  booth_no: string | null
  booth_size: string | null
  status: 'blocked' | 'active'
}
