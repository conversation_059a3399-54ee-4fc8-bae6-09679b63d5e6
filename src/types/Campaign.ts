import { FileWithPath } from 'react-dropzone'
import { EffectiveDate, RedemptionTab } from './CreateCampaign'
import { Event } from './Event'
import { CoreProduct } from './Product'

export type GalleryMedia = {
  url: string
  name: string
  type: 'video' | 'image'
  metadata?: Record<string, any>
  media_file?: FileWithPath & { preview: string }
}

// form in backend
export type Campaign = {
  id: number
  published: boolean
  slug: string
  title: string
  abstract?: string
  description?: string
  thumbnail_url?: string // featured image or video
  category?: string
  gender?: 'male' | 'female' | 'any'
  gallery: GalleryMedia[]
  rewards: string
  application: string
  mission: string
  redemption: RedemptionTab[]
  terms_and_conditions?: string
  rank?: number // for ordering
  event_id?: number // relate to separate db
  deleted: boolean
  fields?: CampaignFormField[]
  event?: Event
  campaign_products?: CampaignProduct[]
  metadata?: Record<string, any> | null
  total_submission?: number | null
}

export type CampaignProduct = {
  id: number
  campaign_id: number
  product_id?: number
  shopify_product_id?: number
  free_gifts?: string
  free_gift_thumbnail?: string
  free_gifts_worth?: number
  unit_per_day: number
  effective_dates?: EffectiveDate[] | null
  tags: string[] | null
  labels: string[] | null
  // start_time?: string
  // end_time?: string
  product?: CoreProduct
  shopify_product?: any // TODO

  campaign: Campaign
}

export type CampaignFormField = {
  id: number
  name: string
  campaign_id: string
  type: FieldTypes
  form_index: number
  required: boolean
  form: Campaign
  props: FormFieldObj
}

export type FormFieldObj = {
  text_props?: {
    textarea?: boolean
    min?: number
    max?: number
  }
  file_props?: {
    extensions?: string[]
    size_limit?: number
  }
  number_props?: {
    min?: number
    max?: number
  }
  option_props?: {
    options?: string[]
  }
}

export type BulkUpdateCampaignProductInput = {
  csv_file: File
}

export type ProductSocialMediaImageBulkRequest = {
  id: number
  campaign_id: number
  payload: any[]
  result: {
    key: string
    file_name: string
  }[]
  download_links: {
    key: string
    file_name: string
    download_url: string
  }[]
  total_completed: number
  total_failed: number
  total: number
  completed_at?: string | null
  created_at: string
  updated_at: string
}

export enum FieldTypes {
  EMAIL = 'email',
  TEXT = 'text',
  NUMBER = 'number',
  DATETIME = 'datetime',
  FILE = 'file',
  OPTIONS = 'options',
  CHECKBOX = 'checkbox'
}
