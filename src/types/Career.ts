import { DateTime } from 'luxon'
import { Job } from './Cms'

export type JobApplication = {
  id: string | number
  answer: { q: string; a: string }[]
  status: ApplicationStatus//'pending' | 'passed' | 'interview_1' | 'interview_2' | 'rejected'
  metadata?: any
  created_at: string | DateTime
  updated_at: string | DateTime

  job_id: string | number
  job: Job
}

export enum ApplicationStatus {
    PENDING = 'pending',
    PASSED = 'passed',
    INTERVIEW_1 = 'interview_1',
    INTERVIEW_2 = 'interview_2',
    REJECTED = 'rejected',
  }
