import { DateTime } from 'luxon'
import { HospitalType } from '@/network/services/hospital'
import { FieldTypes, FormFieldObj } from './Campaign'
import { FileWithPath } from 'react-dropzone'

export type PageCms = {
  id: number
  title: string
  subtitle?: string
  slug: string
  description: string
  banners?: PageBanner[]
  brands?: string[]
  metadata?: any
}

export type PageBanner = {
  desktop: {
    url?: string
    name: string
    type: 'video' | 'image'
    metadata?: Record<string, any>
    media_file?: FileWithPath & { preview: string }
  }
  mobile: {
    url?: string
    name: string
    type: 'video' | 'image'
    metadata?: Record<string, any>
    media_file?: FileWithPath & { preview: string }
  }
}

export type CreatePageCmsInput = {
  title: string
  subtitle?: string
  slug: string
  description: string
  banners?: (Omit<PageBanner, 'desktop' | 'mobile'> & {
    desktop?: Omit<PageBanner['desktop'], 'url'> & { url?: string }
    mobile?: Omit<PageBanner['mobile'], 'url'> & { url?: string }
  })[]
  media?: File[]
  brands?: string[]
  metadata?: any

  tmp_seo_media?: File
}

export type UpdatePageCmsInput = CreatePageCmsInput & { id: number }

export type FaqGroup = {
  id: number
  title: string
  faqs: Faq[]
}

export type CreateFaqGroupInput = {
  title: string
  ordering: number
}

export type UpdateFaqGroupInput = CreateFaqGroupInput

export type Faq = {
  id: number
  title: string
  subtitle?: string
  description: string
  ordering: number
  metadata?: any
}

export type CreateFaqInput = {
  faq_group_id: number
  title: string
  subtitle?: string
  description: string
  metadata?: any
}

export type UpdateFaqInput = Omit<CreateFaqInput, 'faq_group_id'>

export type SortFaqInput = {
  ids: number[]
}

export type Search = {
  id: number
  title: string
  link: string
  ordering: number
  metadata?: any
}

export type CreateSearchInput = {
  title: string
  link: string
  metadata?: any
}

export type UpdateSearchInput = CreateSearchInput

export type SortSearchInput = {
  ids: number[]
}

export type Footer = {
  id: number
  slug: string
  title: string
  link: string
  type: 'shop' | 'learn' | 'support' | 'fab' | 'social'
  metadata?: any
}

export type CreateFooterInput = {
  title: string
  link: string
  type: 'shop' | 'learn' | 'support' | 'social'
  metadata?: any
}

export type UpdateFooterInput = CreateFooterInput

export type SortFooterInput = {
  ids: number[]
}

export type JobCategory = {
  id: number
  title: string
  slug: string
  description: string
  metadata?: any

  jobs: Job[]
}

export type CreateJobCategoryInput = {
  title: string
}

export type UpdateJobCategoryInput = CreateFaqGroupInput

export type SortJobCategoryInput = {
  ids: number[]
}

export enum JobNature {
  FULL_TIME = 'full_time',
  PART_TIME = 'part_time',
  INTERNSHIP = 'internship'
}

export type Job = {
  id: number
  title: string
  slug: string
  description: string
  nature: JobNature
  state: string
  country: string
  status: 'open' | 'close'
  vacancy_no: number
  metadata?: any
}

export type CreateJobInput = {
  category_id: number
  title: string
  description: string
  nature: JobNature
  state: string
  country: string
  status: 'open' | 'close'
  vacancy_no: number
  metadata: JobMetadata
}

type JobMetadata = {
  roles_and_responsibilities: string
  other_responsibilities: string
  be_awegood: string
  perks: string
  disclaimer: string
}

export type UpdateJobInput = CreateJobInput

export type SortJobInput = {
  ids: number[]
}

export type ApplicationFormField = {
  id: number
  name: string
  type: FieldTypes
  required: boolean
  form_index: number
  props: FormFieldObj
  deleted: boolean
}

export type CreateApplicationFormFieldInput = {
  name: string
  type: FieldTypes
  required: boolean
  form_index: number
  props: FormFieldObj
  deleted: boolean
}

export type UpdateApplicationFormFieldInput = CreateApplicationFormFieldInput

export type SortApplicationFormFieldInput = {
  ids: number[]
}

export type CreateHospitalInput = {
  title: string
  type: HospitalType
}

export type PopUp = {
  id: number
  title: string
  date: DateTime | string
  description: string
  media: string
  show: boolean
}

export type CreatePopUp = {
  title: string
  date: DateTime | string
  description: string
  media: string
  media_file?: File
  show: boolean
}
