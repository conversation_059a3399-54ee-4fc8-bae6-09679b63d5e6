import { DateTime } from 'luxon'

export type Contract = {
  id: number
  contract_no: string
  event_id: number
  event_name: string
  exhibitor_name: string
  filename: string
  file_url: string
  status: 'pending' | 'accepted' | 'rejected' | 'void'
  sent: boolean
  payment_frequency: PaymentFrequency[]
  metadata: CreateBoothContractInput
  created_at: DateTime | string
  updated_at: DateTime | string

  invoices: Invoice[]

  // computed attributes
  generate_next_invoice?: boolean
}

export type PaymentFrequency = {
  percentage: number
  amount: number
  // issued_date: DateTime
  // due_date: DateTime
  items: {
    description: string
    amount: number
  }[]
}

export type CreateBoothContractInput = {
  store_id: number
  // event_store: {
  //   event_id: number
  //   hall_no: string
  //   booth_no: string
  //   store_id: number
  //   store_name: string
  // }
  contract_date: string | DateTime
  attention: {
    target: 'contact' | 'finance' | 'marketing' | 'custom'
    full_name: string
    designation: string
    contact_number: string
    email: string
    fax?: string
  }
  company: {
    name: string
    reg_no: string
    address_1: string
    address_2: string
    contact_number: string
    city: string
    postcode: string
    state: string
    country: string
    fax?: string
  }
  event: {
    id: string | number
    title: string
    event_date: string
    venue: string
  }
  brands: {
    event_space_type: {
      line_1: string
      line_2: string
      line_3: string
    }
    title: string
  }[]
  participation_fees: number
  business_nature: string
}

export type UpdateBoothContractInput = CreateBoothContractInput & { id: number }

export type ContractFormData = {
  attention: ContractAttentionData
  company: ContractCompanyData
  event: ContractEventData
  booths: ContractBoothData[]
}

export type ContractAttentionData = {
  contact: {
    email: string
    full_name: string
    contact_number: string
  }
  finance: {
    email: string
    full_name: string
    contact_number: string
  }
  marketing: {
    email: string
    full_name: string
    contact_number: string
  }
}

export type ContractCompanyData = {
  name: string
  reg_no: string
  contact_number: string
  address_1: string
  address_2: string
  city: string
  state: string
  postcode: string
  country: string
}

export type ContractEventData = {
  id: string
  title: string
  event_date: string
  venue: string
}

export type ContractBoothData = {
  no: string
  size: string
  hall_no: string
  brand1: string
  brand2: string
}

export type CreateBoothInvoiceInput = {
  contract_id: number
  payment_frequency: string
  payments: InvoicePaymentData[]
}

export type InvoicePaymentData = {
  percentage: number
  amount: number
  // issued_date: string
  due_date: string | DateTime
  // items: InvoiceItem[]
}

export type InvoiceItem = {
  description: string
  amount: number
}

export type Invoice = {
  id: number
  contract_id: number
  contract_no: string
  invoice_no: string
  filename: string
  file_url: string
  issued_date: DateTime | string
  due_date: DateTime | string
  status: 'pending' | 'paid' | 'overdue'
  sent: boolean
  metadata: PaymentFrequency
  created_at: DateTime | string
  updated_at: DateTime | string

  contract: Contract
}

export type GenerateInvoice = {
  issued_date: string | DateTime
  term: string
  // due_date: string | DateTime
}
