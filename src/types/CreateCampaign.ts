import { FieldTypes, FormFieldObj, GalleryMedia } from './Campaign'

export type CreateCampaignInput = {
  title: string
  fields: CreateCampaignFieldInput[]
  published?: boolean
  slug?: string
  abstract?: string
  // description?: string
  thumbnail?: File // featured image or video
  thumbnail_url?: string // featured image or video (in url format)
  gender?: 'male' | 'female' | 'any'
  category?: string
  gallery?: (Omit<GalleryMedia, 'url'> & { url?: string })[]
  media?: File[]
  terms_and_conditions?: string
  rank?: number // for ordering
  rewards?: string
  description?: string
  application?: string
  mission?: string
  redemption?: RedemptionTab[]
  event_id?: number // relate to separate db
  products?: number[]
  shopify_products?: number[]
  metadata?: any | null
}

export type UpdateCampaignInput = Omit<CreateCampaignInput, 'title' | 'fields'> & {
  title?: string
  tmp_seo_media?: File
}

export type CreateCampaignFieldInput = {
  type: FieldTypes
  name: string
  form_index?: number
  required?: boolean
  props?: FormFieldObj
}

export type UpdateCampaignFieldInput = {
  type: FieldTypes
  name: string
  form_index?: number
  required?: boolean
  props?: FormFieldObj
}

export type RedemptionTab = {
  image?: File
  image_url?: string
  description: string
}

export type EffectiveDate = {
  start: string
  end: string
}

export type UpdateCampaignProductFreeGiftsInput = {
  free_gifts?: string
  free_gift_thumbnail?: string
  free_gifts_worth?: number
  thumbnail?: File
  unit_per_day: number
  effective_dates?: EffectiveDate[]
  tags?: string[]
  labels?: string[]
  // start_time?: string
  // end_time?: string
}
