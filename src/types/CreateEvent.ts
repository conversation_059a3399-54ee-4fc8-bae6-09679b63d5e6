import { DateTime } from 'luxon'
import { GalleryMedia } from './Campaign'

export type CreateEventInput = {
  event: {
    title: string
    from: string | DateTime
    to: string | DateTime
    cut_off: string | DateTime
    location: string
    deal_description: string
    layout_description: string
    visit_description: string
    explore_description: string
  }
  event_info: {
    image_file: File | undefined // image before upload
    image_url?: string // uploaded url
    banner_text: string
    description: string
    gallery?: (Omit<GalleryMedia, 'url'> & { url?: string })[]
    media?: File[]
  }
  visit_us: number // visit us id
  explore: {
    cards: number[] // List of explore ids
  }
  pop_up: UpdatePopUp
}

export type UpdateEventGeneralInput = {
  title: string
  from: string | DateTime
  to: string | DateTime
  cut_off: string | DateTime
  location: string
}

export type UpdateEventInfoInput = {
  image_file: File | undefined // image before upload
  image_url?: string
  banner_text: string
  description: string
  gallery?: (Omit<GalleryMedia, 'url'> & { url?: string })[]
  media?: File[]
}

export type UpdateEventExploreInput = {
  cards: number[]
}

export type SortEventExploreInput = {
  ids: number[]
}

export type UpdateEventInput = {
  event: {
    title: string
    from: string | DateTime
    to: string | DateTime
    cut_off: string | DateTime
    location: string
    deal_description: string
    layout_description: string
    visit_description: string
    explore_description: string
    pop_up: UpdatePopUp
    metadata?: any
  }
  event_info: {
    id: number
    event_id: number
    description: string
    image_url: string
    banner_text: string
    created_at: DateTime
    updated_at: DateTime
  }
}

export type UpdatePopUp = {
  description: string
  media_url: string
  media?: File
  show: boolean
}
