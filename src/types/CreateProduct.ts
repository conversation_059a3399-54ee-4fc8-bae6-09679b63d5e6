import { FileWithPath } from 'react-dropzone'
import { ProductStatus } from './Product'

export type CreateProductInput = {
  title: string
  subtitle?: string
  profile_id?: string
  description?: string
  is_giftcard?: boolean
  discountable?: boolean
  images?: string[]
  thumbnail?: string
  handle?: string
  status?: ProductStatus
  type?: CreateProductProductTypeInput
  collection_id: string
  collection?: CreateProductCollectionOption
  tags?: CreateProductProductTagInput[]
  options?: CreateProductProductOption[]
  variants?: CreateProductProductVariantInput[]
  sales_channels?: CreateProductProductSalesChannelInput[]
  categories?: CreateProductProductCategoryInput[]
  weight?: number
  length?: number
  height?: number
  width?: number
  hs_code?: string
  origin_country?: string
  mid_code?: string
  material?: string
  metadata?: Record<string, string>
  external_id?: string | null
  // custom attribute
  imageFiles?: (FileWithPath & { preview: string })[]
  thumbnailFile?: File
}
export type CreateProductProductTagInput = {
  id?: string
  value: string
}
export type CreateProductProductSalesChannelInput = {
  id: string
}
export type CreateProductProductCategoryInput = {
  id: string
}
export type CreateProductProductTypeInput = {
  id?: string
  value: string
}
export type CreateProductProductVariantInput = {
  title: string
  sku?: string
  ean?: string
  upc?: string
  barcode?: string
  hs_code?: string
  inventory_quantity?: number
  allow_backorder?: boolean
  manage_inventory?: boolean
  weight?: number
  length?: number
  height?: number
  width?: number
  origin_country?: string
  mid_code?: string
  material?: string
  // metadata?: Record<string, unknown>
  prices?: CreateProductProductVariantPriceInput[]
  options?: {
    value: string
  }[]
}
export type UpdateProductProductVariantDTO = {
  id?: string
  title?: string
  sku?: string
  ean?: string
  upc?: string
  barcode?: string
  hs_code?: string
  inventory_quantity?: number
  allow_backorder?: boolean
  manage_inventory?: boolean
  weight?: number
  length?: number
  height?: number
  width?: number
  origin_country?: string
  mid_code?: string
  material?: string
  // metadata?: Record<string, unknown>
  prices?: (CreateProductProductVariantPriceInput & { id?: string })[]
  options?: {
    value: string
    option_id: string
  }[]
}
export type CreateProductProductOption = {
  title: string
}
export type CreateProductCollectionOption = {
  id: string
  // title: string
}

export type UpdateProductProductOptions = {
  options: {
    id?: string
    title: string
  }[]
}

export type CreateProductProductVariantPriceInput = {
  region_id?: string
  currency_code?: string
  amount: number
  min_quantity?: number
  max_quantity?: number
}

export type UpdateProductInput = Omit<Partial<CreateProductInput>, 'variants'> & {
  variants?: UpdateProductProductVariantDTO[]
}

export type UpdateProductVariantSalePrices = {
  price_lists: {
    id: string
    prices: {
      variant_id: string
      currency_code: 'myr'
      amount: number | undefined
      start_time?: string
      end_time?: string
      id: string | undefined
    }[]
  }[]
}
