import { IMedusaDataResponse } from '@/network/request'
import { Order } from '@/network/services/order'
import { C<PERSON><PERSON>cy, PriceList, Product, ProductType, ShippingOption } from '@/types/Product'
import { DateTime } from 'luxon'

export type LicenseCustomer = {
  id: string
  username: string
  email: string
  role: string
  public_key: string
  blocked: boolean
  created_at: DateTime | string
  updated_at: DateTime | string
}

export type Customer = {
  email: string
  first_name: string
  last_name: string
  billing_address_id: string | null
  billing_address: Address
  shipping_addresses: Address[]
  password_hash: string
  phone: string
  has_account: boolean
  orders: Order[]
  groups: CustomerGroup[]
  metadata: Record<string, unknown>

  id: string
  no_of_child: number
  mobile_number: string
  gender: string
  race: string
  current_stage: string
  dob: string
  expected_delivery_date: string
  hospital_of_delivery: string
  household_income_range: string
  friend_code: string

  children: Child[]
}

export type Child = {
  id: string
  full_name: string
  gender: string
  dob: string
  customer_id: string
}

export type CustomerGroup = {
  name: string
  customers: Customer[]
  price_lists: PriceList[]
  metadata: Record<string, unknown>
}

export type Address = {
  customer_id: string | null
  customer: Customer | null
  company: string | null
  first_name: string | null
  last_name: string | null
  address_1: string | null
  address_2: string | null
  city: string | null
  country_code: string | null
  country: Country | null
  province: string | null
  postal_code: string | null
  phone: string | null
  metadata: Record<string, unknown>
  default: boolean
}

export type Country = {
  id: number
  iso_2: string
  iso_3: string
  num_code: number
  name: string
  display_name: string
  region_id: string | null
  region: Region
}

export type Region = {
  name: string
  currency_code: string
  currency: Currency
  tax_rate: number
  tax_rates: TaxRate[] | null
  tax_code: string
  gift_cards_taxable: boolean
  automatic_taxes: boolean
  countries: Country[]
  tax_provider_id: string | null
  //   tax_provider: TaxProvider
  //   payment_providers: PaymentProvider[]
  //   fulfillment_providers: FulfillmentProvider[]
  metadata: Record<string, unknown>
  includes_tax: boolean
}

export type TaxRate = {
  rate: number | null
  code: string | null
  name: string
  region_id: string
  region: Region
  metadata: Record<string, unknown>
  products: Product[]
  product_types: ProductType[]
  shipping_options: ShippingOption[]
  product_count?: number
  product_type_count?: number
  shipping_option_count?: number
}

export interface CustomerResponse extends IMedusaDataResponse {
  customers: Array<Customer>
}

export type AddressPayload = {
  first_name?: string
  last_name?: string
  phone?: string
  metadata?: Record<string, unknown>
  company?: string
  address_1?: string
  address_2?: string
  city?: string
  country_code?: string
  province?: string
  postal_code?: string
}

export type UpdateCustomerInput = Omit<
  Partial<Customer>,
  'billing_address' | 'shipping_addresses' | 'orders' | 'groups'
>
