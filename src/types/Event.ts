import { DateTime } from 'luxon'
import { ExploreCard } from './ExploreCard'
import { VisitUs } from './VisitUs'
import { CoreProduct } from './Product'
import { CoreExhibitor } from './Exhibitor'
import { GalleryMedia } from './Campaign'

export type Event = {
  event_info: EventInfo
  // explore: EventExplore
  deal_description: string
  layout_description: string
  visit_description: string
  explore_description: string
  pop_up_description: string
  pop_up_media: string
  show_pop_up: boolean
  event_visit: EventVisitUs
  event_explores: EventExplore[]
  // event_testimonials: Testimonials[]

  // eventDeals: EventDeals[] // TODO
  metadata?: any | null
} & GeneralEventInfo

export type GeneralEventInfo = {
  id: number
  title: string
  location: string
  selected: boolean
  ended: boolean
  joined: boolean
  cut_off: string | DateTime
  from: string | DateTime
  to: string | DateTime
  created_at: string | DateTime
  updated_at: string | DateTime
  // layoutMap: string // TODO
  // deal: string // TODO
}

export type EventInfo = {
  id: number
  event_id: number
  description: string
  image_url: string
  banner_text: string
  gallery: GalleryMedia[]
  created_at: DateTime
  updated_at: DateTime
}

export type EventVisitUs = {
  id: number
  event_id: number
  visit_id: number
  visit?: VisitUs
  created_at: DateTime
  updated_at: DateTime
}

export type EventExplore = {
  id: number
  description: string
  explore_card: ExploreCard
  explore_card_id: number
}

export type EventProduct = {
  id: number
  event_id: number
  store_id: number
  product_id: number
  event?: Event
  store?: CoreExhibitor
  product?: CoreProduct
}
