export enum EventPurchaseStatus {
  APPROVED = 'approved',
  PENDING = 'pending',
  REJECTED = 'rejected'
}

export type EventPurchase = {
  id: number
  user_id: string
  event_id: string
  status: EventPurchaseStatus
  total: number
  buy_date: Date | string
  created_at: Date | string
  updated_at: Date | string
  transaction_id: string
  purchased_items: Array<{
    price: number
    brand_id: string
    brand_name: string
    category_id: string
    category_name: string
  }>
}

export type UpdateEventPurchaseStatusInput = Omit<
  Partial<EventPurchase>,
  | 'id'
  | 'user_id'
  | 'event_id'
  | 'total'
  | 'buy_date'
  | 'created_at'
  | 'updated_at'
  | 'transaction_id'
  | 'purchased_items'
>
