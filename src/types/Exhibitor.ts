import { IMedusaDataResponse } from '@/network/request'
import { User } from '@/network/services/user'
import { Currency } from '@/types/Product'
import { DateTime } from 'luxon'
import { CoreProductCollection } from './ProductCollection'

export type ExhibitorSignupFormType = {
  name: string
  company_reg_no: string
  gst_no: string
  address_1: string
  address_2: string
  state: string
  country: string
  website: string
  facebook_or_instagram: string

  contact_full_name: string
  contact_email: string
  contact_mobile_prefix: string
  contact_mobile_postfix: string
  contact_mobile_number: string

  finance_full_name: string
  finance_email: string
  finance_mobile_prefix: string
  finance_mobile_postfix: string
  finance_mobile_number: string

  marketing_full_name: string
  marketing_email: string
  marketing_mobile_prefix: string
  marketing_mobile_postfix: string
  marketing_mobile_number: string

  brands: string
  brand_description: string
}

export interface StoreTypeTData<T> {
  stores: T[]
  count: number
}

// AKA 'store'
export type Exhibitor = {
  company_reg_no: string
  gst_no: string
  e_invoice: string
  address_1: string
  address_2: string
  city: string
  postcode: string
  state: string
  country: string
  website: string
  facebook_or_instagram: string
  contact_full_name: string
  contact_email: string
  contact_mobile_number: string
  finance_full_name: string
  finance_email: string
  finance_mobile_number: string
  marketing_full_name: string
  marketing_email: string
  marketing_mobile_number: string
  brands: string
  brand_description: string
  type: 'exhibitor' | 'partner'
  admin_store: boolean
  name: string
  default_currency_code: string
  currencies: Currency[] // Assuming Currency type here
  id: string
  swap_link_template: string | null
  payment_link_template: string | null
  invite_link_template: string | null
  default_location_id: string | null
  metadata: string | null
  default_sales_channel_id: string | null
  status: 'pending' | 'approved' | 'rejected'
  created_at: DateTime
  updated_at: DateTime
  users: User[]
}

export type CoreExhibitor = {
  id: number
  medusa_store_id: string
  name: string
  brands: string
  contact_email: string
  contact_mobile_number: string
  blocked: boolean
  product_collections?: CoreProductCollection
}

export interface ExhibitorResponse extends IMedusaDataResponse {
  stores: Array<Exhibitor>
}
