import { DateTime } from 'luxon'

export type ExploreCard = {
  id: number
  event_id: number
  explore_id: number
  type: 'shop' | 'eat' | 'stay' | 'sight' | undefined
  title: string
  description: string
  image_url: string
  rank: number
  created_at: DateTime | string
  updated_at: DateTime | string
}

export type CreateExploreInput = {
  type: 'shop' | 'eat' | 'stay' | 'sight' | undefined
  title: string
  description: string
  image: File | undefined
}

export type UpdateExploreInput = CreateExploreInput

export type SortExploreInput = {
  ids: number[]
}
