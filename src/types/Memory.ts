import { IDataResponse } from '@/network/request'
import { DateTime } from 'luxon'
import { MemoryMedia } from './MemoryMedia'
import { FileWithPath } from 'react-dropzone'

export type Memory = {
  id: number
  name: string
  slug: string
  all_rank: number
  collection_rank: number
  created_at: DateTime | string
  updated_at: DateTime | string
  medias: MemoryMedia[]
  thumbnail: string
}

export type CreateMemoryInput = {
  name: string
  all_rank?: number
  collection_rank?: number
  thumbnail_url?: string
  thumbnail_file?: File
}

export type UpdateMemoryInput = {
  name?: string
  all_rank?: number
  collection_rank?: number
  thumbnail_url?: string
  thumbnail_file?: File
}

export type BulkCreateMemoryInput = {
  memory_thumbnail_files?: (FileWithPath & { preview: string })[]
  memory_media_files?: (FileWithPath & { preview: string })[]
  memory_collection_id: number | string
}

export interface MemoryResponse extends IDataResponse<Memory> {
  data: Array<Memory>
}
