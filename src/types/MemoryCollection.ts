import { IDataResponse } from '@/network/request'
import { DateTime } from 'luxon'
import { Memory } from './Memory'

export type PreloadMemory = {
  id: number
  name: string
  slug: string
  all_rank: number
  collection_rank: number
  created_at: DateTime | string
  updated_at: DateTime | string
  thumbnail: string
}

export type MemoryCollection = {
  id: number
  title: string
  slug: string
  thumbnail: string
  description: string
  created_at: DateTime | string
  updated_at: DateTime | string
  memories: PreloadMemory[]
}

export type CreateMemoryCollectionInput = {
  title: string
  memory_id_arr: Memory[] | number[]
  thumbnail: string
  description?: string
}

export type UpdateMemoryCollectionInput = {
  title?: string
  memory_id_arr?: Memory[] | number[]
  thumbnail?: string
  description?: string
}

export interface MemoryCollectionResponse extends IDataResponse<MemoryCollection> {
  data: Array<MemoryCollection>
}
