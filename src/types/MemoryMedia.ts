import { IDataResponse } from '@/network/request'
import { DateTime } from 'luxon'

export type PreloadMemory = {
  id: number
  name: string
  slug: string
  all_rank: number
  collection_rank: number
  created_at: DateTime | string
  updated_at: DateTime | string
}

export type PreloadUser = {
  id: string
  username: string
  status: string
  created_at: DateTime | string
  updated_at: DateTime | string
}

export enum MediaType {
  VIDEO = 'video',
  IMAGE = 'image'
}

export enum MediaStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive'
}

export type MemoryMedia = {
  id: number
  memory_id: number
  url: string
  type: MediaType
  status: MediaStatus
  created_at: DateTime | string
  updated_at: DateTime | string
  memory: PreloadMemory
  liked_by_users: PreloadUser[]
}

export type CreateMemoryMediaInput = {
  memory_id: number
  media_file?: File
  media_url?: string
  type: MediaType
  status: MediaStatus
}

export type UpdateMemoryMediaInput = {
  memory_id?: number
  media_file?: File
  media_url?: string
  type?: MediaType
  status?: MediaStatus
}

export interface MemoryMediaResponse extends IDataResponse<MemoryMedia> {
  data: Array<MemoryMedia>
}
