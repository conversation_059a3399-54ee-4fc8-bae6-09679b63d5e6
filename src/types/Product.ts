import { ProductCategory } from './ProductCategory'
import { CustomerGroup, Region } from './Customer'
import { CoreExhibitor, Exhibitor } from './Exhibitor'
import { CoreProductCollection, ProductCollection } from './ProductCollection'

export enum ProductStatus {
  DRAFT = 'draft',
  PROPOSED = 'proposed',
  PUBLISHED = 'published',
  REJECTED = 'rejected'
}

export enum PriceListType {
  SALE = 'sale',
  OVERRIDE = 'override'
}

export enum PriceListStatus {
  ACTIVE = 'active',
  DRAFT = 'draft'
}

export enum ShippingProfileType {
  DEFAULT = 'default',
  GIFT_CARD = 'gift_card',
  CUSTOM = 'custom'
}

export enum ShippingOptionPriceType {
  FLAT_RATE = 'flat_rate',
  CALCULATED = 'calculated'
}

export type Currency = {
  code: string
  symbol: string
  symbol_native: string
  name: string
  includes_tax?: boolean
}

export type PriceList = {
  id: string
  name: string
  description: string
  type: PriceListType
  status: PriceListStatus
  starts_at: Date | null
  ends_at: Date | null
  customer_groups: CustomerGroup[]
  prices: MoneyAmount[]
  includes_tax: boolean
  event_id: number
  campaign_id: number
}

export type ProductOptionValue = {
  value: string
  option_id: string
  option: ProductOption
  variant_id: string
  variant: ProductVariant
  metadata: Record<string, unknown>
}

export type ProductOption = {
  id: string
  title: string
  values: ProductOptionValue[]
  product_id: string
  product: Product
  metadata: Record<string, unknown>
}

export type ProductType = {
  value: string
  metadata: Record<string, unknown>
}

export type ProductTag = {
  value: string
  metadata: Record<string, unknown>
}

export type ProductImage = {
  url: string
  metadata: Record<string, string>
}

export type ShippingProfile = {
  name: string
  type: ShippingProfileType
  products: Product[]
  shipping_options: ShippingOption[]
  metadata: Record<string, unknown>
}

export type ShippingOption = {
  name: string
  region_id: string
  region: Region
  profile_id: string
  profile: ShippingProfile
  provider_id: string
  //   provider: FulfillmentProvider
  price_type: ShippingOptionPriceType
  amount: number | null
  is_return: boolean
  admin_only: boolean
  //   requirements: ShippingOptionRequirement[]
  data: Record<string, unknown>
  metadata: Record<string, unknown>
  includes_tax: boolean
}

export type ProductVariant = {
  id: string
  title: string
  product_id: string
  product: Product
  prices: MoneyAmount[]
  sku: string
  barcode: string
  ean: string
  upc: string
  variant_rank: number
  inventory_quantity: number
  allow_backorder: boolean
  manage_inventory: boolean
  hs_code: string
  origin_country: string
  mid_code: string
  material: string
  weight: number
  length: number
  height: number
  width: number
  options: ProductOptionValue[]
  inventory_items: ProductVariantInventoryItem[]
  metadata: Record<string, unknown>
  purchasable?: boolean
}

export type ProductVariantInventoryItem = {
  inventory_item_id: string
  variant_id: string
  variant: ProductVariant
  required_quantity: number
}

export type MoneyAmount = {
  id: string
  currency_code: string
  currency?: Currency
  amount: number
  start_time?: string
  end_time?: string
  min_quantity: number | null
  max_quantity: number | null
  price_list_id: string | null
  price_list: PriceList | null
  variants: ProductVariant[]
  variant: ProductVariant
  variant_id: string
  region_id: string
  region?: Region
}

export type Product = {
  id: string
  title: string
  subtitle?: string
  description?: string
  handle?: string
  is_giftcard: boolean
  status: ProductStatus
  images: ProductImage[]
  thumbnail?: string
  options: ProductOption[]
  variants: ProductVariant[]
  categories: ProductCategory[]
  profile_id: string
  profile: ShippingProfile
  profiles: ShippingProfile[]
  weight?: number
  length?: number
  height?: number
  width?: number
  hs_code?: string
  origin_country?: string
  mid_code?: string
  material?: string
  collection_id?: string
  collection: ProductCollection
  type_id?: string
  type: ProductType
  tags: ProductTag[]
  discountable: boolean
  external_id?: string
  metadata?: Record<string, string>
  // sales_channels: SalesChannel[]
  store_id?: string
  store?: Exhibitor
}

export type CoreProduct = {
  id: number
  medusa_product_id: string
  store_id: number
  title: string
  thumbnail: string
  product_collection_id: number
  store?: CoreExhibitor
  product_collection?: CoreProductCollection
}
