import { Product } from './Product'

export type ProductCategory = {
  id: string
  productCategoryProductJoinTable: string
  treeRelations: string[]
  name: string
  description: string
  handle: string
  is_active: boolean
  is_internal: boolean
  parent_category: ProductCategory | null
  parent_category_id: string | null
  category_children: ProductCategory[]
  rank: number
  products: Product[]
}

type ProductCategoryInput = {
  handle?: string
  is_internal?: boolean
  is_active?: boolean
  description?: string
  parent_category_id?: string | null
  // parent_category?: ProductCategory | null
  rank?: number
}

export type CreateProductCategoryInput = ProductCategoryInput & {
  name: string
}

export type UpdateProductCategoryInput = ProductCategoryInput & {
  name?: string
  handle?: string
}

// export declare class AdminProductCategoriesReqBase {
//   description?: string
//   handle?: string
//   is_internal?: boolean
//   is_active?: boolean
//   parent_category_id?: string | null
// }

export declare class ProductCategoryBatchProduct {
  product_ids: {
    id: string
  }[]
}

// export type ReorderConditions = {
//   targetCategoryId: string
//   originalParentId: string | null
//   targetParentId: string | null | undefined
//   originalRank: number
//   targetRank: number | undefined
//   shouldChangeParent: boolean
//   shouldChangeRank: boolean
//   shouldIncrementRank: boolean
//   shouldDeleteElement: boolean
// }

// export {}
