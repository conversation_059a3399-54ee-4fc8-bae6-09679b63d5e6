import { FileWithPath } from 'react-dropzone'
import { Exhibitor } from './Exhibitor'
import { Product } from './Product'

export type ProductCollection = {
  id: string
  rank?: number
  title: string
  description: string
  handle: string
  thumbnail_url: string
  logo_url?: string
  status: 'published' | 'proposed' | 'rejected'
  store_id: string
  store: Exhibitor
  products: Product[]
  images?: ProductCollectionImage[]
  // website: string
  // instagram: string
  // facebook: string
  related_collections?: ProductCollection[]
  metadata: Record<string, string>
}

export type ProductCollectionImage = {
  url: string
  metadata: Record<string, string>
}

export type CoreProductCollection = {
  id: number
  medusa_product_collection_id: string
  title: string
  thumbnail_url: string
  logo_url?: string
  status: string
  store_id: number
  medusa_store_id: string
}

type ProductCollectionInput = {
  handle?: string
  description?: string
  rank?: number
  thumbnail?: File
  thumbnail_url?: string
  logo?: File
  logo_url?: string
  images?: string[]
  image_files?: (FileWithPath & { preview: string })[]
  // website: string
  // instagram: string
  // facebook: string
  related_collections?: any[] // ProductCollection
  metadata: Record<string, string>
}

export type CreateProductCollectionInput = ProductCollectionInput & {
  title: string
}

export type UpdateProductCollectionInput = Partial<ProductCollectionInput> & {
  title?: string
  status?: string
}

export declare class ProductCollectionBatchProduct {
  product_ids: string[]
}
