import { GalleryMedia } from "./Campaign"

type Settings = {
  id: number
  key: string
  value: MemberBenefit[] | Freebies | any
  metadata?: any
}

// same as ExhibitorBenefit
export type MemberBenefit = {
  title: string
  description: string
  image_url: string
}

export type CreateMemberBenefit = {
  value: { title: string; description: string; image_url?: string; image?: File }[]
  metadata?: any
}

export type Freebies = {
  description: string
  gallery: GalleryMedia[]
  categories: { title: string; image_url: string }[]
}

export type CreateFreebies = {
  value: {
    description: string
    gallery: (Omit<GalleryMedia, 'url'> & { url?: string })[]
    categories: {
      title: string
      image_url: string
      image?: File
    }[]
  }
}

export default Settings
