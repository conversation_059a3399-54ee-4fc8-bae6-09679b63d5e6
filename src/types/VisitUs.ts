import { DateTime } from 'luxon'

export type VisitUs = {
  id: number
  event_id: number
  location_url: string
  venue_name: string
  venue_address: string
  contact_mobile_number: string
  contact_email: string
  website_url: string
  instagram_url?: string
  facebook_url?: string
  waze_url?: string
  getting_here: GettingHereMethod[]
  parking_and_rate: ParkingInfo[]
  shuttle_van_service: ShuttleVanService
  created_at: DateTime
  updated_at: DateTime
}

export type GettingHereMethod = {
  method: string
  description: string
}

export type ParkingInfo = {
  label: string
  waze_url: string
  first_hour: number
  every_subsequent_hour: number
  maximum_parking_fee_per_day: number
}

export type ShuttleVanService = {
  // image_url: string
  description: string
  image_url: string
  image?: File
}

export type CreateVisitUsInput = {
  venue_name: string
  venue_address: string
  contact_mobile_number: string
  contact_email: string
  location_url: string
  website_url: string
  facebook_url?: string
  instagram_url?: string
  getting_here: GettingHereMethod[]
  parking_and_rate: ParkingInfo[]
  shuttle_van_service: ShuttleVanService | undefined
}
