import { IMedusaDataResponse } from '@/network/request'
import { MoneyAmount, ProductVariant } from '@/types/Product'
import { Customer } from './Customer'

export type PricedWishlist = {
  id: string
  title: string
  event_id: number
  campaign_id: number
  customer_id: number

  customer: Customer
  wishlist_variants: WishlistProductVariant[]

  hall_and_booth: {
    [key: string]: (ProductVariant & {
      prices: MoneyAmount[]
      original_price: number
      calculated_price: number
      calculated_price_type: string
      quantity: number
    })[]
  }
  total: {
    items_count: string
    subtotal: number
    payable: number
    save: number
  }
}

export type WishlistProductVariant = {
  wishlist_id: string
  product_variant_id: string
  quantity: number
  hall_no: string
  booth_no: string
  wishlist: PricedWishlist
  variant: ProductVariant
}

export interface WishlistResponse extends IMedusaDataResponse {
  wishlists: Array<PricedWishlist>
}
